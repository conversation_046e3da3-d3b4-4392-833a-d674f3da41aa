#!/bin/bash

# 🚀 HVAC CRM Integration Test Script
# Tests communication between GoBackend-Kratos and hvac-remix

echo "🌟 HVAC CRM Integration Test - Let's make this work perfectly! 🌟"
echo "=================================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
BACKEND_URL="http://localhost:8081"
FRONTEND_URL="http://localhost:3000"
TRPC_ENDPOINT="$BACKEND_URL/api/trpc"

echo -e "${BLUE}🔧 Testing Configuration:${NC}"
echo -e "  Backend URL: $BACKEND_URL"
echo -e "  Frontend URL: $FRONTEND_URL"
echo -e "  tRPC Endpoint: $TRPC_ENDPOINT"
echo ""

# Function to test endpoint
test_endpoint() {
    local url=$1
    local name=$2
    local method=${3:-GET}

    echo -e "${YELLOW}Testing $name...${NC}"

    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "%{http_code}" -X POST \
            -H "Content-Type: application/json" \
            -d '{"input": {}}' \
            "$url" -o /tmp/response.json)
    else
        response=$(curl -s -w "%{http_code}" "$url" -o /tmp/response.json)
    fi

    http_code="${response: -3}"

    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 201 ]; then
        echo -e "  ${GREEN}✅ $name: SUCCESS (HTTP $http_code)${NC}"
        if [ -f /tmp/response.json ]; then
            echo -e "  ${BLUE}Response preview:${NC}"
            head -c 200 /tmp/response.json | jq . 2>/dev/null || head -c 200 /tmp/response.json
            echo ""
        fi
        return 0
    else
        echo -e "  ${RED}❌ $name: FAILED (HTTP $http_code)${NC}"
        if [ -f /tmp/response.json ]; then
            echo -e "  ${RED}Error response:${NC}"
            cat /tmp/response.json
            echo ""
        fi
        return 1
    fi
}

# Test 1: Backend Health Check
echo -e "${BLUE}🏥 Testing Backend Health...${NC}"
test_endpoint "$BACKEND_URL/api/v1/health" "Backend Health Check"

# Test 2: tRPC Health Check
echo -e "${BLUE}🔗 Testing tRPC Bridge...${NC}"
test_endpoint "$TRPC_ENDPOINT/health" "tRPC Health Check"

# Test 3: tRPC Customer List
echo -e "${BLUE}👥 Testing Customer API...${NC}"
test_endpoint "$TRPC_ENDPOINT/customer.list" "Customer List" "POST"

# Test 4: tRPC System Health
echo -e "${BLUE}⚙️ Testing System API...${NC}"
test_endpoint "$TRPC_ENDPOINT/system.health" "System Health" "POST"

# Test 5: Check if processes are running
echo -e "${BLUE}🔍 Checking Running Processes...${NC}"

backend_pid=$(pgrep -f "server" | head -1)
if [ ! -z "$backend_pid" ]; then
    echo -e "  ${GREEN}✅ Backend process running (PID: $backend_pid)${NC}"
else
    echo -e "  ${YELLOW}⚠️ Backend process not detected${NC}"
fi

frontend_pid=$(pgrep -f "remix" | head -1)
if [ ! -z "$frontend_pid" ]; then
    echo -e "  ${GREEN}✅ Frontend process running (PID: $frontend_pid)${NC}"
else
    echo -e "  ${YELLOW}⚠️ Frontend process not detected${NC}"
fi

# Test 6: Database Connection Test
echo -e "${BLUE}🗄️ Testing Database Connection...${NC}"
if command -v psql &> /dev/null; then
    if psql "**********************************************************" -c "SELECT 1;" &> /dev/null; then
        echo -e "  ${GREEN}✅ Database connection: SUCCESS${NC}"
    else
        echo -e "  ${RED}❌ Database connection: FAILED${NC}"
    fi
else
    echo -e "  ${YELLOW}⚠️ psql not available, skipping database test${NC}"
fi

echo ""
echo -e "${BLUE}🎯 Integration Test Summary:${NC}"
echo "=================================================="

# Count successful tests
success_count=0
total_tests=4

# Re-run quick tests for summary
curl -s "$BACKEND_URL/api/v1/health" > /dev/null && ((success_count++))
curl -s "$TRPC_ENDPOINT/health" > /dev/null && ((success_count++))
curl -s -X POST -H "Content-Type: application/json" -d '{"input": {}}' "$TRPC_ENDPOINT/customer.list" > /dev/null && ((success_count++))
curl -s -X POST -H "Content-Type: application/json" -d '{"input": {}}' "$TRPC_ENDPOINT/system.health" > /dev/null && ((success_count++))

if [ $success_count -eq $total_tests ]; then
    echo -e "${GREEN}🎉 ALL TESTS PASSED! ($success_count/$total_tests)${NC}"
    echo -e "${GREEN}🚀 HVAC CRM is ready for action!${NC}"
    exit 0
elif [ $success_count -gt 0 ]; then
    echo -e "${YELLOW}⚠️ PARTIAL SUCCESS ($success_count/$total_tests tests passed)${NC}"
    echo -e "${YELLOW}🔧 Some components need attention${NC}"
    exit 1
else
    echo -e "${RED}❌ ALL TESTS FAILED (0/$total_tests)${NC}"
    echo -e "${RED}🚨 System needs troubleshooting${NC}"
    exit 2
fi
