/**
 * GoBackend-Kratos Authentication Adapter
 * Replaces Prisma-based authentication with GoBackend API calls
 */

import { goBackendClient } from "./gobackend-client";

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'ADMIN' | 'MANAGER' | 'TECHNICIAN' | 'CUSTOMER_SERVICE';
  isActive: boolean;
  emailVerified: boolean;
  companyId?: string;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: User['role'];
  companyId?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

/**
 * Mock users for demo - in production this would come from GoBackend
 */
const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Jan',
    lastName: '<PERSON><PERSON><PERSON>',
    role: 'ADMI<PERSON>',
    isActive: true,
    emailVerified: true,
    companyId: 'fulmark-1',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
  },
  {
    id: '2',
    email: '<EMAIL>',
    firstName: 'Piotr',
    lastName: 'Nowak',
    role: 'TECHNICIAN',
    isActive: true,
    emailVerified: true,
    companyId: 'fulmark-1',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
  },
  {
    id: '3',
    email: '<EMAIL>',
    firstName: 'Anna',
    lastName: 'Wiśniewska',
    role: 'MANAGER',
    isActive: true,
    emailVerified: true,
    companyId: 'fulmark-1',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
  }
];

/**
 * Get user by ID
 */
export async function getUserById(id: string): Promise<User | null> {
  try {
    // In production, this would call GoBackend API
    // const response = await goBackendClient.get(`/api/v1/users/${id}`);
    // return response.data;
    
    // For demo, use mock data
    return mockUsers.find(u => u.id === id) || null;
  } catch (error) {
    console.error('Error fetching user by ID:', error);
    return null;
  }
}

/**
 * Get user by email
 */
export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    // In production, this would call GoBackend API
    // const response = await goBackendClient.get(`/api/v1/users/by-email/${email}`);
    // return response.data;
    
    // For demo, use mock data
    return mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase()) || null;
  } catch (error) {
    console.error('Error fetching user by email:', error);
    return null;
  }
}

/**
 * Verify user login credentials
 */
export async function verifyLogin(email: string, password: string): Promise<User | null> {
  try {
    // In production, this would call GoBackend authentication API
    // const response = await goBackendClient.post('/api/v1/auth/login', {
    //   email,
    //   password
    // });
    // return response.data.user;
    
    // For demo, simple password check
    const user = await getUserByEmail(email);
    if (!user || !user.isActive) {
      return null;
    }
    
    // Demo passwords
    const demoPasswords: Record<string, string> = {
      '<EMAIL>': 'admin123',
      '<EMAIL>': 'technik123',
      '<EMAIL>': 'manager123'
    };
    
    if (demoPasswords[email] === password) {
      // Update last login
      user.lastLoginAt = new Date();
      return user;
    }
    
    return null;
  } catch (error) {
    console.error('Error verifying login:', error);
    return null;
  }
}

/**
 * Create new user
 */
export async function createUser(userData: CreateUserData): Promise<User | null> {
  try {
    // In production, this would call GoBackend API
    // const response = await goBackendClient.post('/api/v1/users', userData);
    // return response.data;
    
    // For demo, add to mock data
    const newUser: User = {
      id: String(mockUsers.length + 1),
      email: userData.email.toLowerCase(),
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: userData.role || 'TECHNICIAN',
      isActive: true,
      emailVerified: false,
      companyId: userData.companyId || 'fulmark-1',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    mockUsers.push(newUser);
    return newUser;
  } catch (error) {
    console.error('Error creating user:', error);
    return null;
  }
}

/**
 * Update user data
 */
export async function updateUser(id: string, updates: Partial<User>): Promise<User | null> {
  try {
    // In production, this would call GoBackend API
    // const response = await goBackendClient.patch(`/api/v1/users/${id}`, updates);
    // return response.data;
    
    // For demo, update mock data
    const userIndex = mockUsers.findIndex(u => u.id === id);
    if (userIndex === -1) return null;
    
    mockUsers[userIndex] = {
      ...mockUsers[userIndex],
      ...updates,
      updatedAt: new Date(),
    };
    
    return mockUsers[userIndex];
  } catch (error) {
    console.error('Error updating user:', error);
    return null;
  }
}

/**
 * Get users by role
 */
export async function getUsersByRole(role: User['role']): Promise<User[]> {
  try {
    // In production, this would call GoBackend API
    // const response = await goBackendClient.get(`/api/v1/users?role=${role}`);
    // return response.data;
    
    // For demo, filter mock data
    return mockUsers.filter(u => u.role === role && u.isActive);
  } catch (error) {
    console.error('Error fetching users by role:', error);
    return [];
  }
}

/**
 * Search users
 */
export async function searchUsers(query: string, limit = 10): Promise<User[]> {
  try {
    // In production, this would call GoBackend API
    // const response = await goBackendClient.get(`/api/v1/users/search?q=${query}&limit=${limit}`);
    // return response.data;
    
    // For demo, search mock data
    const filtered = mockUsers
      .filter(u => 
        u.isActive && (
          u.firstName.toLowerCase().includes(query.toLowerCase()) ||
          u.lastName.toLowerCase().includes(query.toLowerCase()) ||
          u.email.toLowerCase().includes(query.toLowerCase())
        )
      )
      .slice(0, limit);
    
    return filtered;
  } catch (error) {
    console.error('Error searching users:', error);
    return [];
  }
}

/**
 * Check if user has permission
 */
export function hasPermission(user: User, permission: string): boolean {
  const rolePermissions: Record<User['role'], string[]> = {
    ADMIN: ['*'], // Admin has all permissions
    MANAGER: [
      'users.read',
      'users.create',
      'users.update',
      'customers.read',
      'customers.create',
      'customers.update',
      'jobs.read',
      'jobs.create',
      'jobs.update',
      'reports.read',
      'analytics.read',
    ],
    TECHNICIAN: [
      'customers.read',
      'jobs.read',
      'jobs.update',
      'devices.read',
      'devices.update',
    ],
    CUSTOMER_SERVICE: [
      'customers.read',
      'customers.create',
      'customers.update',
      'jobs.read',
      'jobs.create',
    ],
  };

  const userPermissions = rolePermissions[user.role] || [];
  
  return userPermissions.includes('*') || userPermissions.includes(permission);
}

/**
 * Get user statistics
 */
export async function getUserStats() {
  try {
    // In production, this would call GoBackend API
    // const response = await goBackendClient.get('/api/v1/users/stats');
    // return response.data;
    
    // For demo, calculate from mock data
    const activeUsers = mockUsers.filter(u => u.isActive);
    
    return {
      total: mockUsers.length,
      active: activeUsers.length,
      inactive: mockUsers.length - activeUsers.length,
      byRole: {
        admin: activeUsers.filter(u => u.role === 'ADMIN').length,
        manager: activeUsers.filter(u => u.role === 'MANAGER').length,
        technician: activeUsers.filter(u => u.role === 'TECHNICIAN').length,
        customerService: activeUsers.filter(u => u.role === 'CUSTOMER_SERVICE').length,
      },
      recentLogins: activeUsers.filter(u => 
        u.lastLoginAt && u.lastLoginAt > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      ).length,
    };
  } catch (error) {
    console.error('Error fetching user stats:', error);
    return {
      total: 0,
      active: 0,
      inactive: 0,
      byRole: { admin: 0, manager: 0, technician: 0, customerService: 0 },
      recentLogins: 0,
    };
  }
}

/**
 * Logout user (invalidate session)
 */
export async function logoutUser(userId: string): Promise<boolean> {
  try {
    // In production, this would call GoBackend API to invalidate session
    // const response = await goBackendClient.post(`/api/v1/auth/logout`, { userId });
    // return response.data.success;
    
    // For demo, just return success
    return true;
  } catch (error) {
    console.error('Error logging out user:', error);
    return false;
  }
}
