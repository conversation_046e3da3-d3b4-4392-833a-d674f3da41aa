import { json, redirect } from '@remix-run/node';
import type { LoaderFunctionArgs, ActionFunctionArgs } from '@remix-run/node';
import { v4 as uuidv4 } from 'uuid';
import { createRequestLogger, logger } from '~/services/logging.server';

/**
 * Generate a unique request ID
 */
function generateRequestId(): string {
  return uuidv4();
}

/**
 * Extract user ID from the request
 */
function extractUserId(request: Request): string | undefined {
  // This is a placeholder - in a real app, you would extract the user ID
  // from the session or authentication token
  return undefined;
}

/**
 * Create a request-scoped logger for the current request
 */
export function createLoggerForRequest(request: Request): ReturnType<typeof createRequestLogger> {
  const requestId = generateRequestId();
  const userId = extractUserId(request);
  return createRequestLogger(requestId, userId);
}

/**
 * Middleware to add logging to loader functions
 */
export function withLogging<LoaderData>(
  loaderFn: (args: LoaderFunctionArgs & { logger: ReturnType<typeof createRequestLogger> }) => Promise<LoaderData>
) {
  return async (args: LoaderFunctionArgs): Promise<LoaderData> => {
    const { request, params } = args;
    const requestLogger = createLoggerForRequest(request);
    
    const url = new URL(request.url);
    requestLogger.info('Loader request started', {
      path: url.pathname,
      method: request.method,
      params,
      query: Object.fromEntries(url.searchParams.entries()),
    });
    
    try {
      const result = await loaderFn({ ...args, logger: requestLogger });
      
      requestLogger.info('Loader request completed', {
        path: url.pathname,
        method: request.method,
        status: 'success',
      });
      
      return result;
    } catch (error) {
      const isRedirect = error instanceof Response && error.status >= 300 && error.status < 400;
      
      if (isRedirect) {
        // Don't log redirects as errors
        requestLogger.info('Loader request redirected', {
          path: url.pathname,
          method: request.method,
          redirectUrl: error.headers.get('Location'),
        });
        throw error;
      }
      
      // Log the error
      requestLogger.error(
        `Loader request failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error : new Error(String(error)),
        {
          path: url.pathname,
          method: request.method,
        }
      );
      
      // Re-throw the error to be handled by the error boundary
      throw error;
    }
  };
}

/**
 * Middleware to add logging to action functions
 */
export function withActionLogging<ActionData>(
  actionFn: (args: ActionFunctionArgs & { logger: ReturnType<typeof createRequestLogger> }) => Promise<ActionData>
) {
  return async (args: ActionFunctionArgs): Promise<ActionData> => {
    const { request, params } = args;
    const requestLogger = createLoggerForRequest(request);
    
    const url = new URL(request.url);
    requestLogger.info('Action request started', {
      path: url.pathname,
      method: request.method,
      params,
    });
    
    try {
      // Clone the request to read the form data for logging
      const clonedRequest = request.clone();
      let formData: FormData | null = null;
      
      try {
        formData = await clonedRequest.formData();
      } catch (e) {
        // Ignore errors reading form data
      }
      
      // Log form data keys (not values for privacy/security)
      if (formData) {
        const formDataKeys = Array.from(formData.keys());
        requestLogger.debug('Action form data keys', { formDataKeys });
      }
      
      const result = await actionFn({ ...args, logger: requestLogger });
      
      requestLogger.info('Action request completed', {
        path: url.pathname,
        method: request.method,
        status: 'success',
      });
      
      return result;
    } catch (error) {
      const isRedirect = error instanceof Response && error.status >= 300 && error.status < 400;
      
      if (isRedirect) {
        // Don't log redirects as errors
        requestLogger.info('Action request redirected', {
          path: url.pathname,
          method: request.method,
          redirectUrl: error.headers.get('Location'),
        });
        throw error;
      }
      
      // Log the error
      requestLogger.error(
        `Action request failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error : new Error(String(error)),
        {
          path: url.pathname,
          method: request.method,
        }
      );
      
      // Re-throw the error to be handled by the error boundary
      throw error;
    }
  };
}

/**
 * Create a JSON response with logging
 */
export function jsonWithLogging<Data>(
  data: Data,
  init?: ResponseInit & { logger?: ReturnType<typeof createRequestLogger> }
): Response {
  const { logger: requestLogger, ...restInit } = init || {};
  
  if (requestLogger) {
    requestLogger.debug('Sending JSON response', {
      status: restInit?.status || 200,
    });
  }
  
  return json(data, restInit);
}

/**
 * Create a redirect response with logging
 */
export function redirectWithLogging(
  url: string,
  init?: ResponseInit & { logger?: ReturnType<typeof createRequestLogger> }
): Response {
  const { logger: requestLogger, ...restInit } = init || {};
  
  if (requestLogger) {
    requestLogger.info('Redirecting', {
      url,
      status: restInit?.status || 302,
    });
  }
  
  return redirect(url, restInit);
}

// Export the default logger for use in non-request contexts
export { logger };
