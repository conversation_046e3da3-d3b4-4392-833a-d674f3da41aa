import type { User } from "@prisma/client";
import { redirect } from "@remix-run/node";
import { getUserById } from "~/models/user.server";
import { hasPermission, type Permission, type UserRole } from "~/types/shared";

/**
 * Middleware to check if a user has the required permission
 * @param userId - The user ID to check
 * @param requiredPermission - The permission required to access the resource
 * @param redirectTo - Where to redirect if unauthorized (default: /dashboard)
 * @returns The user if authorized, otherwise redirects
 */
export async function requirePermission(
  userId: string,
  requiredPermission: Permission,
  redirectTo: string = "/dashboard"
): Promise<User> {
  const user = await getUserById(userId);
  
  if (!user) {
    throw redirect("/login");
  }
  
  const userRole = user.role as UserRole;
  
  if (!hasPermission(userRole, requiredPermission)) {
    throw redirect(redirectTo);
  }
  
  return user;
}

/**
 * Middleware to check if a user has one of the required roles
 * @param userId - The user ID to check
 * @param allowedRoles - Array of roles that can access the resource
 * @param redirectTo - Where to redirect if unauthorized (default: /dashboard)
 * @returns The user if authorized, otherwise redirects
 */
export async function requireRole(
  userId: string,
  allowedRoles: UserRole[],
  redirectTo: string = "/dashboard"
): Promise<User> {
  const user = await getUserById(userId);
  
  if (!user) {
    throw redirect("/login");
  }
  
  const userRole = user.role as UserRole;
  
  if (!allowedRoles.includes(userRole)) {
    throw redirect(redirectTo);
  }
  
  return user;
}

/**
 * Helper function to create a loader that requires a specific permission
 * @param permission - The permission required to access the resource
 * @returns A function that can be used in route loaders
 */
export function createPermissionLoader(permission: Permission) {
  return async ({ request }: { request: Request }) => {
    // Extract the user ID from the session
    const userId = await getUserIdFromSession(request);
    
    if (!userId) {
      throw redirect("/login");
    }
    
    return requirePermission(userId, permission);
  };
}

/**
 * Helper function to create a loader that requires a specific role
 * @param allowedRoles - Array of roles that can access the resource
 * @returns A function that can be used in route loaders
 */
export function createRoleLoader(allowedRoles: UserRole[]) {
  return async ({ request }: { request: Request }) => {
    // Extract the user ID from the session
    const userId = await getUserIdFromSession(request);
    
    if (!userId) {
      throw redirect("/login");
    }
    
    return requireRole(userId, allowedRoles);
  };
}

/**
 * Helper function to get the user ID from the session
 * This is a placeholder - you should implement this based on your session management
 */
async function getUserIdFromSession(request: Request): Promise<string | null> {
  // Import your session utilities
  const { getUserId } = await import("~/session.server");
  return getUserId(request) || null;
}