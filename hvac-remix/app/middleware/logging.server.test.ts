import { json, redirect } from '@remix-run/node';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createRequestLogger } from '~/services/logging.server';
import { withLogging, withActionLogging, jsonWithLogging, redirectWithLogging } from './logging.server';

// Mock the createRequestLogger function
vi.mock('~/services/logging.server', () => {
  const mockLogger = {
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    critical: vi.fn(),
  };
  
  return {
    createRequestLogger: vi.fn(() => mockLogger),
    logger: {
      info: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      critical: vi.fn(),
    },
  };
});

// Mock uuid
vi.mock('uuid', () => ({
  v4: () => 'test-uuid',
}));

describe('Logging Middleware', () => {
  let mockRequest: Request;
  let mockParams: Record<string, string>;
  
  beforeEach(() => {
    // Setup mock request
    mockRequest = new Request('https://example.com/test?query=value', {
      method: 'GET',
    });
    
    mockParams = { id: '123' };
    
    // Reset mocks
    vi.resetAllMocks();
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  describe('withLogging', () => {
    it('should log successful loader execution', async () => {
      // Mock loader function
      const mockLoader = vi.fn().mockResolvedValue({ data: 'test' });
      
      // Create wrapped loader
      const wrappedLoader = withLogging(mockLoader);
      
      // Execute wrapped loader
      const result = await wrappedLoader({ request: mockRequest, params: mockParams, context: {} });
      
      // Verify result
      expect(result).toEqual({ data: 'test' });
      
      // Verify logger was created
      expect(createRequestLogger).toHaveBeenCalled();
      
      // Get the mock logger
      const mockLogger = vi.mocked(createRequestLogger).mock.results[0].value;
      
      // Verify start log
      expect(mockLogger.info).toHaveBeenCalledWith('Loader request started', expect.objectContaining({
        path: '/test',
        method: 'GET',
        params: { id: '123' },
        query: { query: 'value' },
      }));
      
      // Verify completion log
      expect(mockLogger.info).toHaveBeenCalledWith('Loader request completed', expect.objectContaining({
        path: '/test',
        method: 'GET',
        status: 'success',
      }));
      
      // Verify loader was called with logger
      expect(mockLoader).toHaveBeenCalledWith({
        request: mockRequest,
        params: mockParams,
        context: {},
        logger: mockLogger,
      });
    });
    
    it('should log loader errors', async () => {
      // Mock error
      const mockError = new Error('Test error');
      
      // Mock loader function that throws
      const mockLoader = vi.fn().mockRejectedValue(mockError);
      
      // Create wrapped loader
      const wrappedLoader = withLogging(mockLoader);
      
      // Execute wrapped loader and expect it to throw
      await expect(wrappedLoader({ request: mockRequest, params: mockParams, context: {} }))
        .rejects.toThrow(mockError);
      
      // Get the mock logger
      const mockLogger = vi.mocked(createRequestLogger).mock.results[0].value;
      
      // Verify error log
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Loader request failed: Test error',
        mockError,
        expect.objectContaining({
          path: '/test',
          method: 'GET',
        })
      );
    });
    
    it('should handle redirects', async () => {
      // Mock redirect response
      const redirectResponse = redirect('/new-location');
      
      // Mock loader function that redirects
      const mockLoader = vi.fn().mockRejectedValue(redirectResponse);
      
      // Create wrapped loader
      const wrappedLoader = withLogging(mockLoader);
      
      // Execute wrapped loader and expect it to throw the redirect
      await expect(wrappedLoader({ request: mockRequest, params: mockParams, context: {} }))
        .rejects.toEqual(redirectResponse);
      
      // Get the mock logger
      const mockLogger = vi.mocked(createRequestLogger).mock.results[0].value;
      
      // Verify redirect log
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Loader request redirected',
        expect.objectContaining({
          path: '/test',
          method: 'GET',
          redirectUrl: '/new-location',
        })
      );
      
      // Verify error log was not called
      expect(mockLogger.error).not.toHaveBeenCalled();
    });
  });
  
  describe('withActionLogging', () => {
    it('should log successful action execution', async () => {
      // Mock action function
      const mockAction = vi.fn().mockResolvedValue({ success: true });
      
      // Create wrapped action
      const wrappedAction = withActionLogging(mockAction);
      
      // Execute wrapped action
      const result = await wrappedAction({ request: mockRequest, params: mockParams, context: {} });
      
      // Verify result
      expect(result).toEqual({ success: true });
      
      // Get the mock logger
      const mockLogger = vi.mocked(createRequestLogger).mock.results[0].value;
      
      // Verify start log
      expect(mockLogger.info).toHaveBeenCalledWith('Action request started', expect.objectContaining({
        path: '/test',
        method: 'GET',
        params: { id: '123' },
      }));
      
      // Verify completion log
      expect(mockLogger.info).toHaveBeenCalledWith('Action request completed', expect.objectContaining({
        path: '/test',
        method: 'GET',
        status: 'success',
      }));
    });
  });
  
  describe('jsonWithLogging', () => {
    it('should create JSON response with logging', () => {
      // Mock logger
      const mockLogger = {
        debug: vi.fn(),
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        critical: vi.fn(),
      };
      
      // Create response
      const response = jsonWithLogging({ data: 'test' }, { status: 201, logger: mockLogger as any });
      
      // Verify response
      expect(response.status).toBe(201);
      
      // Verify logging
      expect(mockLogger.debug).toHaveBeenCalledWith('Sending JSON response', { status: 201 });
    });
  });
  
  describe('redirectWithLogging', () => {
    it('should create redirect response with logging', () => {
      // Mock logger
      const mockLogger = {
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        critical: vi.fn(),
      };
      
      // Create response
      const response = redirectWithLogging('/new-location', { status: 301, logger: mockLogger as any });
      
      // Verify response
      expect(response.status).toBe(301);
      expect(response.headers.get('Location')).toBe('/new-location');
      
      // Verify logging
      expect(mockLogger.info).toHaveBeenCalledWith('Redirecting', { url: '/new-location', status: 301 });
    });
  });
});
