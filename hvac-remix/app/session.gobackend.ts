/**
 * Session management using GoBackend-Kratos instead of Prisma
 */

import { createCookieSessionStorage, redirect } from "@remix-run/node";
import type { User } from "~/lib/gobackend-auth";
import { getUserById, verifyLogin } from "~/lib/gobackend-auth";

invariant(process.env.SESSION_SECRET, "SESSION_SECRET must be set");

export const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: "__session",
    httpOnly: true,
    path: "/",
    sameSite: "lax",
    secrets: [process.env.SESSION_SECRET],
    secure: process.env.NODE_ENV === "production",
  },
});

const USER_SESSION_KEY = "userId";

export async function getSession(request: Request) {
  const cookie = request.headers.get("Cookie");
  return sessionStorage.getSession(cookie);
}

export async function getUserId(
  request: Request
): Promise<User["id"] | undefined> {
  const session = await getSession(request);
  const userId = session.get(USER_SESSION_KEY);
  return userId;
}

export async function getUser(request: Request): Promise<User | null> {
  const userId = await getUserId(request);
  if (userId === undefined) return null;

  const user = await getUserById(userId);
  if (user) return user;

  throw await logout(request);
}

export async function requireUserId(
  request: Request,
  redirectTo: string = new URL(request.url).pathname
): Promise<string> {
  const userId = await getUserId(request);
  if (!userId) {
    const searchParams = new URLSearchParams([["redirectTo", redirectTo]]);
    throw redirect(`/login?${searchParams}`);
  }
  return userId;
}

export async function requireUser(request: Request): Promise<User> {
  const userId = await requireUserId(request);

  const user = await getUserById(userId);
  if (user) return user;

  throw await logout(request);
}

export async function createUserSession({
  request,
  userId,
  remember,
  redirectTo,
}: {
  request: Request;
  userId: string;
  remember: boolean;
  redirectTo: string;
}) {
  const session = await getSession(request);
  session.set(USER_SESSION_KEY, userId);
  return redirect(redirectTo, {
    headers: {
      "Set-Cookie": await sessionStorage.commitSession(session, {
        maxAge: remember
          ? 60 * 60 * 24 * 7 // 7 days
          : undefined,
      }),
    },
  });
}

export async function logout(request: Request) {
  const session = await getSession(request);
  return redirect("/", {
    headers: {
      "Set-Cookie": await sessionStorage.destroySession(session),
    },
  });
}

export async function verifyUserLogin(email: string, password: string) {
  return await verifyLogin(email, password);
}

// Helper function to check if user is authenticated
export async function isAuthenticated(request: Request): Promise<boolean> {
  const userId = await getUserId(request);
  return userId !== undefined;
}

// Helper function to require specific role
export async function requireRole(
  request: Request,
  allowedRoles: User["role"][]
): Promise<User> {
  const user = await requireUser(request);
  
  if (!allowedRoles.includes(user.role)) {
    throw new Response("Forbidden", { status: 403 });
  }
  
  return user;
}

// Helper function to require admin role
export async function requireAdmin(request: Request): Promise<User> {
  return requireRole(request, ["ADMIN"]);
}

// Helper function to require manager or admin role
export async function requireManager(request: Request): Promise<User> {
  return requireRole(request, ["ADMIN", "MANAGER"]);
}

// Helper function to get user with error handling
export async function getOptionalUser(request: Request): Promise<User | null> {
  try {
    return await getUser(request);
  } catch {
    return null;
  }
}

// Helper function to check if user has permission
export async function requirePermission(
  request: Request,
  permission: string
): Promise<User> {
  const user = await requireUser(request);
  
  // Import hasPermission function
  const { hasPermission } = await import("~/lib/gobackend-auth");
  
  if (!hasPermission(user, permission)) {
    throw new Response("Forbidden", { status: 403 });
  }
  
  return user;
}

// Helper function to create session with user data
export async function createAuthenticatedSession({
  request,
  user,
  remember = false,
  redirectTo = "/dashboard",
}: {
  request: Request;
  user: User;
  remember?: boolean;
  redirectTo?: string;
}) {
  return createUserSession({
    request,
    userId: user.id,
    remember,
    redirectTo,
  });
}

// Helper function to validate session and refresh user data
export async function validateAndRefreshSession(request: Request): Promise<User | null> {
  const userId = await getUserId(request);
  if (!userId) return null;

  // Get fresh user data from GoBackend
  const user = await getUserById(userId);
  if (!user || !user.isActive) {
    // User no longer exists or is inactive, logout
    throw await logout(request);
  }

  return user;
}

// Helper function for API routes that need authentication
export async function requireApiAuth(request: Request): Promise<User> {
  const user = await getUser(request);
  if (!user) {
    throw new Response("Unauthorized", { 
      status: 401,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }
  return user;
}

// Helper function to get session data for client-side
export async function getSessionData(request: Request) {
  const user = await getOptionalUser(request);
  
  return {
    user: user ? {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isActive: user.isActive,
      emailVerified: user.emailVerified,
    } : null,
    isAuthenticated: !!user,
  };
}

// Add invariant function if not imported
function invariant(condition: any, message: string): asserts condition {
  if (!condition) {
    throw new Error(message);
  }
}
