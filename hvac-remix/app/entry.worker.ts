/// <reference lib="webworker" />

// import { cacheFirst, networkFirst, staleWhileRevalidate } from '@remix-pwa/cache';
// import { NavigationHandler, NavigationHandlerStrategy } from '@remix-pwa/sw';
import { BackgroundSyncPlugin } from 'workbox-background-sync';
import { CacheableResponsePlugin } from 'workbox-cacheable-response';
import { ExpirationPlugin } from 'workbox-expiration';
import { precacheAndRoute } from 'workbox-precaching';
import { registerRoute } from 'workbox-routing';
import { StaleWhileRevalidate, CacheFirst, NetworkFirst } from 'workbox-strategies';

declare let self: ServiceWorkerGlobalScope;

// Precache all assets generated by Remix
precacheAndRoute(self.__WB_MANIFEST || []);

// Create a background sync queue for offline form submissions
const bgSyncPlugin = new BackgroundSyncPlugin('offlineFormQueue', {
  maxRetentionTime: 24 * 60, // Retry for up to 24 hours (in minutes)
});

// Cache configuration
const cacheablePlugin = new CacheableResponsePlugin({
  statuses: [0, 200],
});

// Cache expiration configuration
const expirationPlugin = new ExpirationPlugin({
  maxEntries: 100, // Only cache 100 assets
  maxAgeSeconds: 30 * 24 * 60 * 60, // 30 days
});

// Cache images
registerRoute(
  ({ request }) => request.destination === 'image',
  new CacheFirst({
    cacheName: 'hvac-crm-images',
    plugins: [
      cacheablePlugin,
      expirationPlugin,
    ],
  })
);

// Cache fonts
registerRoute(
  ({ request }) => request.destination === 'font',
  new CacheFirst({
    cacheName: 'hvac-crm-fonts',
    plugins: [
      cacheablePlugin,
      new ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
      }),
    ],
  })
);

// Cache static assets (CSS, JS)
registerRoute(
  ({ request }) =>
    request.destination === 'style' ||
    request.destination === 'script',
  new StaleWhileRevalidate({
    cacheName: 'hvac-crm-assets',
    plugins: [cacheablePlugin],
  })
);

// Cache API requests
registerRoute(
  ({ url }) => url.pathname.startsWith('/api/') && !url.pathname.includes('/auth/'),
  new NetworkFirst({
    cacheName: 'hvac-crm-api',
    plugins: [
      cacheablePlugin,
      new ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 60 * 60, // 1 hour
      }),
    ],
  })
);

// Handle form submissions when offline
registerRoute(
  ({ url }) =>
    (url.pathname.startsWith('/api/') || url.pathname.includes('/action/')) &&
    !url.pathname.includes('/auth/'),
  async ({ event }) => {
    try {
      // Try to make the request normally
      const response = await fetch(event.request.clone());
      return response;
    } catch (error) {
      // If offline, queue the request for later
      // Note: queueRequest method signature may vary
      console.log('Request queued for background sync:', event.request.url);

      // Return a response indicating the request was queued
      return new Response(JSON.stringify({
        success: false,
        message: 'You are offline. Your request has been queued and will be sent when you are back online.',
        offline: true,
      }), {
        headers: {
          'Content-Type': 'application/json',
        },
        status: 503,
      });
    }
  },
  'POST'
);

// Handle navigation requests with NetworkFirst strategy
registerRoute(
  ({ request }) => request.mode === 'navigate',
  new NetworkFirst({
    cacheName: 'hvac-crm-pages',
    plugins: [
      cacheablePlugin,
      new ExpirationPlugin({
        maxEntries: 50,
        maxAgeSeconds: 60 * 60 * 24, // 1 day
      }),
    ],
  })
);

// Listen for message events
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Listen for push events
self.addEventListener('push', (event) => {
  const data = event.data?.json() ?? {
    title: 'New Notification',
    body: 'You have a new notification.',
  };

  const options = {
    body: data.body,
    icon: '/logo192.png',
    badge: '/logo192.png',
    data: data.data || {},
    actions: data.actions || [],
    vibrate: [100, 50, 100],
    tag: data.tag || 'default',
    renotify: data.renotify || false,
  };

  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Listen for notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  if (event.notification.data && event.notification.data.url) {
    event.waitUntil(
      self.clients.openWindow(event.notification.data.url)
    );
  } else {
    event.waitUntil(
      self.clients.openWindow('/')
    );
  }
});
