"use client"

import type { ServiceOrde<PERSON>, <PERSON><PERSON> } from "@prisma/client"
import { Link } from "@remix-run/react"
import { Badge } from "~/components/ui/badge"
import { But<PERSON> } from "~/components/ui/button"
import { Card } from "~/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "~/components/ui/tabs"

interface TechnicianMobileDashboardProps {
  serviceOrders: Array<ServiceOrder & {
    device?: Device & { name: string, model: string, serialNumber: string },
    customer?: { name: string, address: string, phone: string }
  }>
  upcomingServiceOrders: Array<ServiceOrder & {
    device?: Device & { name: string, model: string, serialNumber: string },
    customer?: { name: string, address: string, phone: string }
  }>
  deviceAlerts: Array<{
    id: string
    deviceId: string
    deviceName: string
    alertType: 'HIGH' | 'MEDIUM' | 'LOW'
    message: string
    createdAt: string
  }>
}

export function TechnicianMobileDashboard({
  serviceOrders,
  upcomingServiceOrders,
  deviceAlerts
}: TechnicianMobileDashboardProps) {

  // Status color mapping
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-500 text-white'
      case 'IN_PROGRESS':
        return 'bg-blue-500 text-white'
      case 'COMPLETED':
        return 'bg-green-500 text-white'
      case 'CANCELLED':
        return 'bg-red-500 text-white'
      default:
        return 'bg-gray-500 text-white'
    }
  }

  // Priority color mapping
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'bg-red-500 text-white'
      case 'MEDIUM':
        return 'bg-orange-500 text-white'
      case 'LOW':
        return 'bg-blue-500 text-white'
      default:
        return 'bg-gray-500 text-white'
    }
  }

  // Alert color mapping
  const getAlertColor = (alertType: string) => {
    switch (alertType) {
      case 'HIGH':
        return 'bg-red-500 text-white'
      case 'MEDIUM':
        return 'bg-orange-500 text-white'
      case 'LOW':
        return 'bg-blue-500 text-white'
      default:
        return 'bg-gray-500 text-white'
    }
  }

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-3 mb-6">
        <Button
          asChild
          className="h-16 flex flex-col items-center justify-center"
          variant="default"
        >
          <Link to="/service-orders/new">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span className="text-xs">Nowe zlecenie</span>
          </Link>
        </Button>

        <Button
          asChild
          className="h-16 flex flex-col items-center justify-center"
          variant="outline"
        >
          <Link to="/calendar">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span className="text-xs">Kalendarz</span>
          </Link>
        </Button>

        <Button
          asChild
          className="h-16 flex flex-col items-center justify-center"
          variant="outline"
        >
          <Link to="/search">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span className="text-xs">Wyszukaj</span>
          </Link>
        </Button>

        <Button
          asChild
          className="h-16 flex flex-col items-center justify-center"
          variant="outline"
        >
          <Link to="/devices?view=predictions">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span className="text-xs">Predykcje</span>
          </Link>
        </Button>
      </div>

      {/* Device Alerts */}
      {deviceAlerts.length > 0 && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-3">Alerty urządzeń</h2>
          <div className="space-y-2">
            {deviceAlerts.map((alert) => (
              <Card key={alert.id} className="p-3 border-l-4 border-red-500">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium">{alert.deviceName}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{alert.message}</p>
                  </div>
                  <Badge className={getAlertColor(alert.alertType)}>
                    {alert.alertType}
                  </Badge>
                </div>
                <div className="mt-2">
                  <Button
                    asChild
                    variant="outline"
                    size="sm"
                    className="text-xs"
                  >
                    <Link to={`/devices/${alert.deviceId}`}>
                      Szczegóły
                    </Link>
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Service Orders Tabs */}
      <div>
        <h2 className="text-lg font-semibold mb-3">Zlecenia serwisowe</h2>
        <Tabs defaultValue="today">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="today">Dzisiaj</TabsTrigger>
            <TabsTrigger value="upcoming">Nadchodzące</TabsTrigger>
          </TabsList>

          <TabsContent value="today" className="space-y-3">
            {serviceOrders.length > 0 ? (
              serviceOrders.map((order) => (
                <Card key={order.id} className="p-3">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium">{order.title}</h3>
                    <div className="flex space-x-1">
                      <Badge className={getStatusColor(order.status)}>
                        {order.status}
                      </Badge>
                      <Badge className={getPriorityColor(order.priority)}>
                        {order.priority}
                      </Badge>
                    </div>
                  </div>

                  <div className="text-sm mb-2">
                    <p className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium">Klient:</span> {order.customer?.name}
                    </p>
                    <p className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium">Adres:</span> {order.customer?.address}
                    </p>
                    <p className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium">Urządzenie:</span> {order.device?.name} {order.device?.model}
                    </p>
                    <p className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium">Czas:</span> {order.scheduledDate ? new Date(order.scheduledDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'Nie określono'}
                    </p>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      asChild
                      variant="default"
                      size="sm"
                      className="flex-1"
                    >
                      <Link to={`/service-orders/${order.id}`}>
                        Szczegóły
                      </Link>
                    </Button>
                    {order.status === 'PENDING' && (
                      <Button
                        asChild
                        variant="outline"
                        size="sm"
                        className="flex-1"
                      >
                        <Link to={`/service-orders/${order.id}/edit?status=IN_PROGRESS`}>
                          Rozpocznij
                        </Link>
                      </Button>
                    )}
                    {order.status === 'IN_PROGRESS' && (
                      <Button
                        asChild
                        variant="outline"
                        size="sm"
                        className="flex-1"
                      >
                        <Link to={`/service-orders/${order.id}/edit?status=COMPLETED`}>
                          Zakończ
                        </Link>
                      </Button>
                    )}
                  </div>
                </Card>
              ))
            ) : (
              <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                Brak zleceń na dzisiaj
              </div>
            )}
          </TabsContent>

          <TabsContent value="upcoming" className="space-y-3">
            {upcomingServiceOrders.length > 0 ? (
              upcomingServiceOrders.map((order) => (
                <Card key={order.id} className="p-3">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium">{order.title}</h3>
                    <div className="flex space-x-1">
                      <Badge className={getPriorityColor(order.priority)}>
                        {order.priority}
                      </Badge>
                    </div>
                  </div>

                  <div className="text-sm mb-2">
                    <p className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium">Klient:</span> {order.customer?.name}
                    </p>
                    <p className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium">Adres:</span> {order.customer?.address}
                    </p>
                    <p className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium">Urządzenie:</span> {order.device?.name} {order.device?.model}
                    </p>
                    <p className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium">Data:</span> {order.scheduledDate ? new Date(order.scheduledDate).toLocaleDateString() : 'Nie określono'}
                    </p>
                  </div>

                  <Button
                    asChild
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    <Link to={`/service-orders/${order.id}`}>
                      Szczegóły
                    </Link>
                  </Button>
                </Card>
              ))
            ) : (
              <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                Brak nadchodzących zleceń
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}