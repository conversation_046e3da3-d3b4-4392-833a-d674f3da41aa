import { Form } from "@remix-run/react";
import { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";

interface MaintenanceFormProps {
  predictionId: string;
}

export function MaintenanceForm({ predictionId }: MaintenanceFormProps) {
  const [open, setOpen] = useState(false);
  const [notes, setNotes] = useState("");
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>Mark Maintenance as Performed</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Record Maintenance</DialogTitle>
          <DialogDescription>
            Record details about the maintenance performed on this device.
          </DialogDescription>
        </DialogHeader>
        
        <Form action="/api/maintenance/perform" method="post" onSubmit={() => setOpen(false)}>
          <input type="hidden" name="predictionId" value={predictionId} />
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="notes">Maintenance Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                placeholder="Enter details about the maintenance performed..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={5}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit">Save Maintenance Record</Button>
          </DialogFooter>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
