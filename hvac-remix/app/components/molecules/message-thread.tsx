import { useState } from "react"
import { Badge } from "~/components/ui/badge"
import { But<PERSON> } from "~/components/ui/button"
import { Card } from "~/components/ui/card"

interface Message {
  id: string
  customerId: string
  userId?: string
  direction: 'INBOUND' | 'OUTBOUND'
  channel: 'EMAIL' | 'SMS' | 'PHONE' | 'IN_PERSON' | 'PORTAL'
  content: string
  subject?: string
  timestamp: string
  read: boolean
  attachments?: Array<{
    id: string
    name: string
    url: string
    type: string
  }>
}

interface MessageThreadProps {
  messages: Message[]
  onMarkAsRead: (messageId: string) => Promise<void>
}

export function MessageThread({ messages, onMarkAsRead }: MessageThreadProps) {
  const [expanded, setExpanded] = useState(false)
  const [isMarkingAsRead, setIsMarkingAsRead] = useState(false)
  
  // Sort messages by timestamp (oldest first)
  const sortedMessages = [...messages].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  )
  
  // Get the first and last message in the thread
  const firstMessage = sortedMessages[0]
  const lastMessage = sortedMessages[sortedMessages.length - 1]
  
  // Check if there are any unread messages in the thread
  const hasUnreadMessages = sortedMessages.some(message => !message.read && message.direction === 'INBOUND')
  
  // Get the thread title (subject for emails, or a generated title for other channels)
  const getThreadTitle = () => {
    if (firstMessage.channel === 'EMAIL' && firstMessage.subject) {
      return firstMessage.subject
    }
    
    // For other channels, use a generic title with the date
    const date = new Date(firstMessage.timestamp)
    const formattedDate = date.toLocaleDateString()
    
    switch (firstMessage.channel) {
      case 'SMS':
        return `SMS z ${formattedDate}`
      case 'PHONE':
        return `Rozmowa telefoniczna z ${formattedDate}`
      case 'IN_PERSON':
        return `Spotkanie z ${formattedDate}`
      case 'PORTAL':
        return `Wiadomość z portalu z ${formattedDate}`
      default:
        return `Wiadomość z ${formattedDate}`
    }
  }
  
  // Get channel badge
  const getChannelBadge = (channel: Message['channel']) => {
    switch (channel) {
      case 'EMAIL':
        return <Badge className="bg-blue-500 text-white">Email</Badge>
      case 'SMS':
        return <Badge className="bg-green-500 text-white">SMS</Badge>
      case 'PHONE':
        return <Badge className="bg-yellow-500 text-white">Telefon</Badge>
      case 'IN_PERSON':
        return <Badge className="bg-purple-500 text-white">Spotkanie</Badge>
      case 'PORTAL':
        return <Badge className="bg-indigo-500 text-white">Portal</Badge>
      default:
        return null
    }
  }
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }
  
  // Handle marking all unread messages as read
  const handleMarkAsRead = async () => {
    if (isMarkingAsRead) return
    
    setIsMarkingAsRead(true)
    
    try {
      // Get all unread inbound messages
      const unreadMessages = sortedMessages.filter(
        message => !message.read && message.direction === 'INBOUND'
      )
      
      // Mark each message as read
      for (const message of unreadMessages) {
        await onMarkAsRead(message.id)
      }
    } catch (error) {
      console.error("Error marking messages as read:", error)
      // Handle error (could show an error message to the user)
    } finally {
      setIsMarkingAsRead(false)
    }
  }
  
  return (
    <Card className={`p-4 ${hasUnreadMessages ? 'border-l-4 border-blue-500' : ''}`}>
      <div className="flex justify-between items-start mb-3">
        <div>
          <h3 className="font-medium">{getThreadTitle()}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {sortedMessages.length} wiadomości · Ostatnia: {formatDate(lastMessage.timestamp)}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {getChannelBadge(firstMessage.channel)}
          {hasUnreadMessages && (
            <Badge className="bg-red-500 text-white">Nieprzeczytane</Badge>
          )}
        </div>
      </div>
      
      {/* Preview of the last message */}
      <div className="mb-3">
        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
          {lastMessage.content}
        </p>
      </div>
      
      {/* Expanded thread view */}
      {expanded && (
        <div className="mt-4 space-y-4 border-t border-gray-200 dark:border-gray-700 pt-4">
          {sortedMessages.map(message => (
            <div 
              key={message.id} 
              className={`p-3 rounded-lg ${
                message.direction === 'OUTBOUND'
                  ? 'bg-blue-50 dark:bg-blue-900/20 ml-4'
                  : 'bg-gray-50 dark:bg-gray-800 mr-4'
              }`}
            >
              <div className="flex justify-between items-start mb-1">
                <span className="text-xs font-medium">
                  {message.direction === 'OUTBOUND' ? 'Wysłano' : 'Otrzymano'}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {formatDate(message.timestamp)}
                </span>
              </div>
              <p className="text-sm whitespace-pre-line">{message.content}</p>
              
              {/* Attachments if any */}
              {message.attachments && message.attachments.length > 0 && (
                <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                  <p className="text-xs font-medium mb-1">Załączniki:</p>
                  <div className="flex flex-wrap gap-2">
                    {message.attachments.map(attachment => (
                      <a
                        key={attachment.id}
                        href={attachment.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clipRule="evenodd" />
                        </svg>
                        {attachment.name}
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
      
      {/* Actions */}
      <div className="flex justify-between mt-3">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setExpanded(!expanded)}
        >
          {expanded ? "Zwiń" : "Rozwiń"}
        </Button>
        
        {hasUnreadMessages && (
          <Button
            variant="default"
            size="sm"
            onClick={handleMarkAsRead}
            disabled={isMarkingAsRead}
          >
            {isMarkingAsRead ? "Oznaczanie..." : "Oznacz jako przeczytane"}
          </Button>
        )}
      </div>
    </Card>
  )
}
