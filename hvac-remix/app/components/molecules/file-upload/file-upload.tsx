import { DocumentArrowUpIcon, XCircleIcon } from "@heroicons/react/24/outline";
import { useRef, useState } from "react";

import { cn } from "~/lib/utils";

interface FileUploadProps {
  name: string;
  label: string;
  accept?: string;
  maxSize?: number;
  required?: boolean;
  multiple?: boolean;
  className?: string;
  description?: string;
  error?: string;
}

export function FileUpload({
  name,
  label,
  accept = "application/pdf,image/jpeg,image/png,image/tiff",
  maxSize = 10 * 1024 * 1024, // 10MB
  required = false,
  multiple = false,
  className,
  description,
  error,
}: FileUploadProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const validateFiles = (files: File[]): boolean => {
    // Check file types
    const invalidTypeFiles = files.filter(file => !accept.split(",").includes(file.type));
    if (invalidTypeFiles.length > 0) {
      setValidationError(`Invalid file type: ${invalidTypeFiles.map(f => f.name).join(", ")}`);
      return false;
    }
    
    // Check file sizes
    const oversizedFiles = files.filter(file => file.size > maxSize);
    if (oversizedFiles.length > 0) {
      setValidationError(`File too large: ${oversizedFiles.map(f => f.name).join(", ")}`);
      return false;
    }
    
    setValidationError(null);
    return true;
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFiles = Array.from(e.dataTransfer.files);
      const filesToAdd = multiple ? droppedFiles : [droppedFiles[0]];
      
      if (validateFiles(filesToAdd)) {
        setSelectedFiles(prev => multiple ? [...prev, ...filesToAdd] : filesToAdd);
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    
    if (e.target.files && e.target.files.length > 0) {
      const selectedFiles = Array.from(e.target.files);
      const filesToAdd = multiple ? selectedFiles : [selectedFiles[0]];
      
      if (validateFiles(filesToAdd)) {
        setSelectedFiles(prev => multiple ? [...prev, ...filesToAdd] : filesToAdd);
      }
    }
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    setValidationError(null);
    
    // Reset the input value to allow selecting the same file again
    if (inputRef.current) {
      inputRef.current.value = "";
    }
  };

  const handleButtonClick = () => {
    inputRef.current?.click();
  };

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between">
        <label htmlFor={name} className="block text-sm font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        {description && (
          <span className="text-xs text-gray-500">{description}</span>
        )}
      </div>
      
      <div
        className={cn(
          "relative flex min-h-32 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors",
          dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:bg-gray-50",
          validationError || error ? "border-red-300 bg-red-50" : ""
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={handleButtonClick}
      >
        <input
          ref={inputRef}
          id={name}
          name={name}
          type="file"
          accept={accept}
          required={required && selectedFiles.length === 0}
          multiple={multiple}
          className="sr-only"
          onChange={handleChange}
        />
        
        <DocumentArrowUpIcon className="h-10 w-10 text-gray-400" />
        
        <div className="mt-2 text-center">
          <p className="text-sm font-medium">
            Drag and drop your {multiple ? "files" : "file"} here
          </p>
          <p className="mt-1 text-xs text-gray-500">
            or <span className="font-medium text-blue-600">browse</span> to upload
          </p>
          <p className="mt-1 text-xs text-gray-500">
            {accept.split(",").join(", ")} up to {(maxSize / (1024 * 1024)).toFixed(0)}MB
          </p>
        </div>
      </div>
      
      {(validationError || error) && (
        <p className="text-sm text-red-500">{validationError || error}</p>
      )}
      
      {selectedFiles.length > 0 && (
        <ul className="mt-2 space-y-2">
          {selectedFiles.map((file, index) => (
            <li key={index} className="flex items-center justify-between rounded-md bg-gray-50 px-3 py-2 text-sm">
              <span className="truncate">{file.name}</span>
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveFile(index);
                }}
                className="ml-2 text-gray-500 hover:text-red-500"
              >
                <XCircleIcon className="h-5 w-5" />
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}