import { RefreshCw } from 'lucide-react';
import React from 'react';
import { ServiceAvailabilityIndicator } from '~/components/atoms/service-availability-indicator';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { useServiceAvailability, ServiceType } from '~/contexts/service-availability';

interface ServiceAvailabilityPanelProps {
  className?: string;
}

/**
 * Component that displays the availability of all backend services
 */
export function ServiceAvailabilityPanel({ className = '' }: ServiceAvailabilityPanelProps) {
  const { checkAllServices } = useServiceAvailability();
  const [isRefreshing, setIsRefreshing] = React.useState(false);

  // Handle refresh all click
  const handleRefreshAll = async () => {
    setIsRefreshing(true);
    await checkAllServices();
    setTimeout(() => setIsRefreshing(false), 1000); // Show spinner for at least 1 second
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg">Service Status</CardTitle>
            <CardDescription>
              Current availability of backend services
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshAll}
            disabled={isRefreshing}
            className="h-8"
          >
            <RefreshCw className={`h-3 w-3 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {Object.values(ServiceType).map((serviceType) => (
            <div key={serviceType} className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {serviceType === ServiceType.BIELIK_LLM
                  ? 'Bielik LLM'
                  : serviceType === ServiceType.OCR
                  ? 'OCR Service'
                  : 'Predictive Maintenance'}
              </span>
              <ServiceAvailabilityIndicator serviceType={serviceType} />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
