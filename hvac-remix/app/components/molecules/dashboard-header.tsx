import { Setting<PERSON>, Edit, Check, LayoutDashboard, Moon, Sun } from "lucide-react";
import { DateRangePicker } from "~/components/molecules/date-range-picker";
import { Avatar } from "~/components/ui/avatar";
import { But<PERSON> } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

interface DashboardHeaderProps {
  userName?: string;
  avatarUrl?: string;
  isCustomizing: boolean;
  onCustomizeToggle: () => void;
}

export function DashboardHeader({
  userName,
  avatarUrl,
  isCustomizing,
  onCustomizeToggle
}: DashboardHeaderProps) {
  return (
    <div className="flex flex-col gap-4 mb-6 md:mb-8 animate-fade-in">
      {/* Mobile-First Header Layout */}
      <div className="flex items-center justify-between">
        {/* User Info - Optimized for Mobile */}
        <div className="flex items-center gap-3 min-w-0 flex-1">
          <div className="relative flex-shrink-0">
            <Avatar
              src={avatarUrl}
              alt={userName}
              size={48} // Slightly smaller for mobile
              className="ring-2 ring-primary/20 transition-all duration-300 hover:ring-primary/40"
            />
            <span className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-success rounded-full border-2 border-white dark:border-gray-800 animate-pulse"></span>
          </div>
          <div className="min-w-0 flex-1">
            <h1 className="text-lg sm:text-xl md:text-2xl font-bold mb-0.5 text-gradient-primary truncate">
              Cześć, {userName || "Użytkowniku"} 👋
            </h1>
            <p className="text-gray-500 dark:text-gray-400 text-xs sm:text-sm animate-slide-in-right truncate">
              Miłego dnia w HVAC CRM!
            </p>
          </div>
        </div>

        {/* Mobile Actions */}
        <div className="flex items-center gap-2 flex-shrink-0">
          {!isCustomizing && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 hover-glow touch-manipulation" // Touch-friendly size
                  aria-label="Ustawienia dashboardu"
                >
                  <Settings className="h-5 w-5 transition-transform duration-300 hover:rotate-90" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 animate-scale">
                <DropdownMenuLabel>Ustawienia dashboardu</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="h-10"> {/* Touch-friendly height */}
                  <LayoutDashboard className="mr-2 h-4 w-4" />
                  <span>Układ dashboardu</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="h-10">
                  <Sun className="mr-2 h-4 w-4" />
                  <span>Tryb jasny</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="h-10">
                  <Moon className="mr-2 h-4 w-4" />
                  <span>Tryb ciemny</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Secondary Actions Row - Mobile Optimized */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 animate-slide-in-left">
        {!isCustomizing && (
          <DateRangePicker
            className="w-full sm:w-auto glass order-2 sm:order-1"
            onChange={(range) => {
              console.log('Date range changed:', range);
            }}
          />
        )}

        <Button
          variant={isCustomizing ? "default" : "outline"}
          onClick={onCustomizeToggle}
          className={`
            flex items-center justify-center gap-2 h-11 px-4
            hover-lift touch-manipulation order-1 sm:order-2
            ${isCustomizing ? 'bg-primary hover:bg-primary-hover' : 'border-primary/30'}
          `}
          aria-label={isCustomizing ? "Zakończ edycję dashboardu" : "Dostosuj dashboard"}
        >
          {isCustomizing ? (
            <>
              <Check className="h-4 w-4 animate-pulse" />
              <span className="hidden sm:inline">Zakończ edycję</span>
              <span className="sm:hidden">Gotowe</span>
            </>
          ) : (
            <>
              <Edit className="h-4 w-4" />
              <span className="hidden sm:inline">Dostosuj dashboard</span>
              <span className="sm:hidden">Edytuj</span>
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
