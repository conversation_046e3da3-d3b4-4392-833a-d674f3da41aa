import { Link } from "@remix-run/react";
import { ChevronLeft } from "lucide-react";
import React from "react";
import { cn } from "~/lib/utils";

interface PageHeaderProps {
  title: string;
  description?: string;
  className?: string;
  actions?: React.ReactNode;
  backLink?: string;
}

export function PageHeader({
  title,
  description,
  className,
  actions,
  backLink,
}: PageHeaderProps) {
  return (
    <div
      className={cn(
        "flex flex-col space-y-2 pb-4 md:flex-row md:items-center md:justify-between md:space-y-0",
        className
      )}
    >
      <div>
        {backLink && (
          <Link 
            to={backLink} 
            className="flex items-center text-sm text-muted-foreground hover:text-foreground mb-2"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back
          </Link>
        )}
        <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
        {description && (
          <p className="text-muted-foreground">{description}</p>
        )}
      </div>
      {actions && <div className="flex items-center space-x-2">{actions}</div>}
    </div>
  );
}