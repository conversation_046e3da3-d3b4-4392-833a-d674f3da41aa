import { Badge } from "~/components/ui/badge"
import { But<PERSON> } from "~/components/ui/button"

interface CommunicationHistoryItem {
  channel: 'EMAIL' | 'SMS' | 'PHONE' | 'IN_PERSON' | 'PORTAL'
  month: string
  count: number
  lastDate: string
}

interface CommunicationHistoryProps {
  history: CommunicationHistoryItem[]
  onSelectMonth: (channel: CommunicationHistoryItem['channel'], month: string) => void
}

export function CommunicationHistory({ history, onSelectMonth }: CommunicationHistoryProps) {
  // Group history items by month
  const groupedByMonth = history.reduce<Record<string, CommunicationHistoryItem[]>>((acc, item) => {
    if (!acc[item.month]) {
      acc[item.month] = []
    }
    acc[item.month].push(item)
    return acc
  }, {})
  
  // Get channel icon
  const getChannelIcon = (channel: CommunicationHistoryItem['channel']) => {
    switch (channel) {
      case 'EMAIL':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
          </svg>
        )
      case 'SMS':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
          </svg>
        )
      case 'PHONE':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
          </svg>
        )
      case 'IN_PERSON':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
        )
      case 'PORTAL':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd" />
          </svg>
        )
      default:
        return null
    }
  }
  
  // Get channel color
  const getChannelColor = (channel: CommunicationHistoryItem['channel']) => {
    switch (channel) {
      case 'EMAIL':
        return 'text-blue-500'
      case 'SMS':
        return 'text-green-500'
      case 'PHONE':
        return 'text-yellow-500'
      case 'IN_PERSON':
        return 'text-purple-500'
      case 'PORTAL':
        return 'text-indigo-500'
      default:
        return 'text-gray-500'
    }
  }
  
  // Format month for display
  const formatMonth = (monthStr: string) => {
    const [year, month] = monthStr.split('-')
    const date = new Date(parseInt(year), parseInt(month) - 1, 1)
    
    return date.toLocaleDateString(undefined, { month: 'long', year: 'numeric' })
  }
  
  return (
    <div className="space-y-4">
      {Object.entries(groupedByMonth).length > 0 ? (
        Object.entries(groupedByMonth).map(([month, items]) => (
          <div key={month} className="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-0 last:pb-0">
            <h3 className="font-medium text-sm mb-2">{formatMonth(month)}</h3>
            <div className="space-y-2">
              {items.map((item) => (
                <Button
                  key={`${item.channel}_${item.month}`}
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => onSelectMonth(item.channel, item.month)}
                >
                  <div className="flex items-center w-full">
                    <span className={`mr-2 ${getChannelColor(item.channel)}`}>
                      {getChannelIcon(item.channel)}
                    </span>
                    <span className="flex-1 text-left">
                      {item.channel === 'EMAIL' ? 'Email' : 
                       item.channel === 'SMS' ? 'SMS' : 
                       item.channel === 'PHONE' ? 'Telefon' : 
                       item.channel === 'IN_PERSON' ? 'Spotkanie' : 'Portal'}
                    </span>
                    <Badge variant="outline" className="ml-2">
                      {item.count}
                    </Badge>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        ))
      ) : (
        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
          Brak historii komunikacji
        </div>
      )}
    </div>
  )
}
