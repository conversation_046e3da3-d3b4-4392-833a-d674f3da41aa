import { Link } from "@remix-run/react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from "~/components/ui/card";

interface DeviceTelemetry {
  id: string;
  timestamp: string;
  temperature: number | null;
  humidity: number | null;
  pressure: number | null;
  vibration: number | null;
  noise: number | null;
  powerUsage: number | null;
  runtime: number | null;
  cycles: number | null;
  errorCodes: string | null;
  customData: string | null;
  createdAt: string;
  updatedAt: string;
  deviceId: string;
}

interface MaintenancePrediction {
  id: string;
  predictionDate: string;
  failureProbability: number;
  predictedFailureDate: string | null;
  predictedComponent: string | null;
  recommendedAction: string | null;
  confidence: number;
  modelVersion: string;
  modelFeatures: string | null;
  maintenancePerformed: boolean;
  maintenanceDate: string | null;
  maintenanceNotes: string | null;
  createdAt: string;
  updatedAt: string;
  deviceId: string;
}

interface DeviceHealthSummaryProps {
  deviceId: string;
  telemetry: DeviceTelemetry[];
  predictions: MaintenancePrediction[];
}

export function DeviceHealthSummary({ deviceId, telemetry, predictions }: DeviceHealthSummaryProps) {
  // Get the latest telemetry and prediction
  const latestTelemetry = telemetry.length > 0 ? telemetry[0] : null;
  const latestPrediction = predictions.length > 0 ? predictions[0] : null;
  
  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };
  
  // Determine status color based on failure probability
  const getStatusColor = (probability: number) => {
    if (probability < 0.3) return "bg-green-500";
    if (probability < 0.7) return "bg-yellow-500";
    return "bg-red-500";
  };
  
  // Determine status text based on failure probability
  const getStatusText = (probability: number) => {
    if (probability < 0.3) return "Low Risk";
    if (probability < 0.7) return "Medium Risk";
    return "High Risk";
  };
  
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Device Health</CardTitle>
        <CardDescription>Telemetry and predictive maintenance summary</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Telemetry Summary */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Telemetry</h3>
            {latestTelemetry ? (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Last Updated:</span>
                  <span className="text-sm font-medium">{formatDate(latestTelemetry.timestamp)}</span>
                </div>
                
                {latestTelemetry.temperature !== null && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Temperature:</span>
                    <span className="text-sm font-medium">{latestTelemetry.temperature}°C</span>
                  </div>
                )}
                
                {latestTelemetry.humidity !== null && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Humidity:</span>
                    <span className="text-sm font-medium">{latestTelemetry.humidity}%</span>
                  </div>
                )}
                
                {latestTelemetry.powerUsage !== null && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Power Usage:</span>
                    <span className="text-sm font-medium">{latestTelemetry.powerUsage} kW</span>
                  </div>
                )}
                
                {latestTelemetry.runtime !== null && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Runtime:</span>
                    <span className="text-sm font-medium">{latestTelemetry.runtime} hours</span>
                  </div>
                )}
                
                {latestTelemetry.cycles !== null && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Cycles:</span>
                    <span className="text-sm font-medium">{latestTelemetry.cycles}</span>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500">No telemetry data available</p>
            )}
          </div>
          
          {/* Prediction Summary */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Maintenance Prediction</h3>
            {latestPrediction ? (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Status:</span>
                  <Badge className={getStatusColor(latestPrediction.failureProbability)}>
                    {getStatusText(latestPrediction.failureProbability)}
                  </Badge>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Failure Probability:</span>
                  <span className="text-sm font-medium">{Math.round(latestPrediction.failureProbability * 100)}%</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Prediction Date:</span>
                  <span className="text-sm font-medium">{formatDate(latestPrediction.predictionDate)}</span>
                </div>
                
                {latestPrediction.predictedFailureDate && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Predicted Failure:</span>
                    <span className="text-sm font-medium">{formatDate(latestPrediction.predictedFailureDate)}</span>
                  </div>
                )}
                
                {latestPrediction.predictedComponent && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Component:</span>
                    <span className="text-sm font-medium">{latestPrediction.predictedComponent}</span>
                  </div>
                )}
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Maintenance:</span>
                  <Badge variant={latestPrediction.maintenancePerformed ? "outline" : "default"}>
                    {latestPrediction.maintenancePerformed ? "Performed" : "Pending"}
                  </Badge>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">No prediction data available</p>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link to={`/devices/${deviceId}/telemetry`}>
          <Button variant="outline">View Telemetry</Button>
        </Link>
        <Link to={`/devices/${deviceId}/predictions`}>
          <Button variant="outline">View Predictions</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
