import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

interface MaintenancePrediction {
  id: string;
  predictionDate: string;
  failureProbability: number;
  predictedFailureDate: string | null;
  predictedComponent: string | null;
  recommendedAction: string | null;
  confidence: number;
  modelVersion: string;
  modelFeatures: string | null;
  maintenancePerformed: boolean;
  maintenanceDate: string | null;
  maintenanceNotes: string | null;
  createdAt: string;
  updatedAt: string;
  deviceId: string;
}

interface PredictionChartProps {
  prediction: MaintenancePrediction | null;
  title?: string;
  description?: string;
}

export function PredictionChart({ prediction, title = "Failure Risk", description = "Current risk assessment" }: PredictionChartProps) {
  if (!prediction) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <p className="text-gray-500">No prediction data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  // Prepare data for the pie chart
  const failureProbabilityData = [
    { name: "Failure Risk", value: prediction.failureProbability * 100 },
    { name: "Safe", value: 100 - (prediction.failureProbability * 100) },
  ];
  
  // Colors for the chart
  const COLORS = ["#ff0000", "#00c49f"];
  
  // Custom tooltip formatter
  const tooltipFormatter = (value: number) => `${value.toFixed(1)}%`;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={failureProbabilityData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {failureProbabilityData.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={tooltipFormatter} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Confidence</p>
              <p className="text-lg font-medium">{Math.round(prediction.confidence * 100)}%</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Model Version</p>
              <p className="text-lg font-medium">{prediction.modelVersion}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
