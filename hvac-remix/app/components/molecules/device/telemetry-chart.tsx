import { useState } from "react";
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "~/components/ui/tabs";

interface TelemetryData {
  id: string;
  timestamp: string;
  temperature: number | null;
  humidity: number | null;
  pressure: number | null;
  vibration: number | null;
  noise: number | null;
  powerUsage: number | null;
  runtime: number | null;
  cycles: number | null;
  errorCodes: string | null;
  customData: string | null;
  createdAt: string;
  updatedAt: string;
  deviceId: string;
}

interface TelemetryChartProps {
  telemetry: TelemetryData[];
  title?: string;
  description?: string;
}

export function TelemetryChart({ telemetry, title = "Device Telemetry", description = "Historical telemetry data" }: TelemetryChartProps) {
  const [activeMetric, setActiveMetric] = useState("temperature");
  
  // Prepare data for charts - reverse to show oldest first
  const chartData = [...telemetry]
    .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
    .map(t => ({
      timestamp: new Date(t.timestamp).toLocaleString(),
      temperature: t.temperature,
      humidity: t.humidity,
      pressure: t.pressure,
      vibration: t.vibration,
      noise: t.noise,
      powerUsage: t.powerUsage,
      runtime: t.runtime,
      cycles: t.cycles,
    }));
  
  // Define metrics and their display properties
  const metrics = [
    { id: "temperature", name: "Temperature", unit: "°C", color: "#8884d8" },
    { id: "humidity", name: "Humidity", unit: "%", color: "#82ca9d" },
    { id: "pressure", name: "Pressure", unit: "kPa", color: "#ffc658" },
    { id: "vibration", name: "Vibration", unit: "", color: "#ff8042" },
    { id: "noise", name: "Noise", unit: "dB", color: "#0088fe" },
    { id: "powerUsage", name: "Power Usage", unit: "kW", color: "#00C49F" },
    { id: "runtime", name: "Runtime", unit: "h", color: "#FFBB28" },
    { id: "cycles", name: "Cycles", unit: "", color: "#FF8042" },
  ];
  
  // Find the active metric details
  const activeMetricDetails = metrics.find(m => m.id === activeMetric) || metrics[0];
  
  // Check if we have data for the active metric
  const hasData = chartData.some(d => d[activeMetric as keyof typeof d] !== null);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeMetric} onValueChange={setActiveMetric} className="w-full">
          <TabsList className="grid grid-cols-4 md:grid-cols-8">
            {metrics.map(metric => (
              <TabsTrigger key={metric.id} value={metric.id}>
                {metric.name}
              </TabsTrigger>
            ))}
          </TabsList>
          
          <TabsContent value={activeMetric} className="mt-4">
            {hasData ? (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tick={{ fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis 
                      label={{ 
                        value: `${activeMetricDetails.name} (${activeMetricDetails.unit})`, 
                        angle: -90, 
                        position: 'insideLeft',
                        style: { textAnchor: 'middle' }
                      }} 
                    />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey={activeMetric} 
                      stroke={activeMetricDetails.color} 
                      activeDot={{ r: 8 }} 
                      name={activeMetricDetails.name}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-[300px] flex items-center justify-center">
                <p className="text-gray-500">No data available for {activeMetricDetails.name}</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
