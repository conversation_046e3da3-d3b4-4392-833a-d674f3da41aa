import { Form } from "@remix-run/react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Checkbox } from "~/components/ui/checkbox";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";

interface SemanticSearchFormProps {
  defaultQuery?: string;
  defaultCollection?: string;
  defaultLimit?: number;
  onSearch?: (query: string, collection: string, limit: number, useSemanticSearch: boolean) => void;
}

export function SemanticSearchForm({
  defaultQuery = "",
  defaultCollection = "all",
  defaultLimit = 10,
  onSearch,
}: SemanticSearchFormProps) {
  const [query, setQuery] = useState(defaultQuery);
  const [collection, setCollection] = useState(defaultCollection);
  const [limit, setLimit] = useState(defaultLimit);
  const [useSemanticSearch, setUseSemanticSearch] = useState(true);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(query, collection, limit, useSemanticSearch);
    }
  };
  
  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <Form method="get" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="query">Wyszukiwanie</Label>
              <div className="flex gap-2">
                <Input
                  id="query"
                  name="q"
                  placeholder="Wpisz zapytanie..."
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  className="flex-1"
                />
                <Button type="submit">Szukaj</Button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex flex-col gap-2">
                <Label htmlFor="collection">Kolekcja</Label>
                <Select
                  name="collection"
                  value={collection}
                  onValueChange={(value) => setCollection(value)}
                >
                  <SelectTrigger id="collection">
                    <SelectValue placeholder="Wybierz kolekcję" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Wszystkie</SelectItem>
                    <SelectItem value="customers">Klienci</SelectItem>
                    <SelectItem value="devices">Urządzenia</SelectItem>
                    <SelectItem value="service_orders">Zlecenia serwisowe</SelectItem>
                    <SelectItem value="notes">Notatki</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex flex-col gap-2">
                <Label htmlFor="limit">Limit wyników</Label>
                <Select
                  name="limit"
                  value={limit.toString()}
                  onValueChange={(value) => setLimit(parseInt(value))}
                >
                  <SelectTrigger id="limit">
                    <SelectValue placeholder="Limit wyników" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-end gap-2 h-full pb-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="semantic"
                    name="semantic"
                    checked={useSemanticSearch}
                    onCheckedChange={(checked) => setUseSemanticSearch(checked === true)}
                  />
                  <Label htmlFor="semantic" className="cursor-pointer">
                    Wyszukiwanie semantyczne
                  </Label>
                </div>
              </div>
            </div>
          </div>
        </Form>
      </CardContent>
    </Card>
  );
}