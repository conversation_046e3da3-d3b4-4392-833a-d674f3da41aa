import { Link } from "@remix-run/react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from "~/components/ui/card";

interface SearchResultsProps {
  results: Array<{
    id: string;
    score: number;
    collection: string;
    payload: any;
  }>;
  isLoading?: boolean;
}

export function SearchResults({ results, isLoading = false }: SearchResultsProps) {
  if (isLoading) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Wyszukiwanie...</p>
      </div>
    );
  }
  
  if (results.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Brak wyników wyszukiwania.</p>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      {results.map((result) => (
        <SearchResultCard key={`${result.collection}-${result.id}`} result={result} />
      ))}
    </div>
  );
}

function SearchResultCard({ result }: { result: { id: string; score: number; collection: string; payload: any } }) {
  const { id, score, collection, payload } = result;
  
  // Format the score as a percentage
  const formattedScore = Math.round(score * 100);
  
  // Get the appropriate link based on collection type
  const getLink = () => {
    switch (collection) {
      case 'customers':
        return `/customers/${id}`;
      case 'devices':
        return `/devices/${id}`;
      case 'service_orders':
        return `/service-orders/${id}`;
      case 'notes':
        return `/notes/${id}`;
      default:
        return '#';
    }
  };
  
  // Get the appropriate title based on collection type
  const getTitle = () => {
    switch (collection) {
      case 'customers':
        return payload.name || 'Klient';
      case 'devices':
        return payload.name || 'Urządzenie';
      case 'service_orders':
        return payload.title || 'Zlecenie serwisowe';
      case 'notes':
        return payload.title || 'Notatka';
      default:
        return 'Wynik';
    }
  };
  
  // Get the appropriate description based on collection type
  const getDescription = () => {
    switch (collection) {
      case 'customers':
        return payload.email || '';
      case 'devices':
        return payload.model || '';
      case 'service_orders':
        return payload.status || '';
      case 'notes':
        return payload.userEmail || '';
      default:
        return '';
    }
  };
  
  // Get the appropriate content based on collection type
  const getContent = () => {
    switch (collection) {
      case 'customers':
        return (
          <div className="space-y-2">
            {payload.phone && (
              <div>
                <span className="text-sm font-medium">Telefon:</span> {payload.phone}
              </div>
            )}
            {payload.address && (
              <div>
                <span className="text-sm font-medium">Adres:</span> {payload.address}
              </div>
            )}
          </div>
        );
      case 'devices':
        return (
          <div className="space-y-2">
            {payload.serialNumber && (
              <div>
                <span className="text-sm font-medium">Numer seryjny:</span> {payload.serialNumber}
              </div>
            )}
            {payload.manufacturer && (
              <div>
                <span className="text-sm font-medium">Producent:</span> {payload.manufacturer}
              </div>
            )}
            {payload.customerName && (
              <div>
                <span className="text-sm font-medium">Klient:</span>{" "}
                <Link to={`/customers/${payload.customerId}`} className="text-blue-500 hover:underline">
                  {payload.customerName}
                </Link>
              </div>
            )}
          </div>
        );
      case 'service_orders':
        return (
          <div className="space-y-2">
            {payload.description && (
              <div>
                <span className="text-sm font-medium">Opis:</span> {payload.description}
              </div>
            )}
            {payload.customerName && (
              <div>
                <span className="text-sm font-medium">Klient:</span>{" "}
                <Link to={`/customers/${payload.customerId}`} className="text-blue-500 hover:underline">
                  {payload.customerName}
                </Link>
              </div>
            )}
            {payload.deviceName && (
              <div>
                <span className="text-sm font-medium">Urządzenie:</span>{" "}
                <Link to={`/devices/${payload.deviceId}`} className="text-blue-500 hover:underline">
                  {payload.deviceName}
                </Link>
              </div>
            )}
          </div>
        );
      case 'notes':
        return (
          <div className="space-y-2">
            {payload.body && (
              <div>
                <span className="text-sm font-medium">Treść:</span> {payload.body}
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };
  
  // Get the appropriate collection label
  const getCollectionLabel = () => {
    switch (collection) {
      case 'customers':
        return 'Klient';
      case 'devices':
        return 'Urządzenie';
      case 'service_orders':
        return 'Zlecenie';
      case 'notes':
        return 'Notatka';
      default:
        return collection;
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>
              <Link to={getLink()} className="hover:underline">
                {getTitle()}
              </Link>
            </CardTitle>
            <CardDescription>
              {getDescription()}
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Badge variant="outline">{getCollectionLabel()}</Badge>
            <Badge variant="secondary">{formattedScore}% dopasowania</Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {getContent()}
      </CardContent>
      <CardFooter>
        <Link to={getLink()}>
          <Button variant="outline">Szczegóły</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}