import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';
import { pl } from 'date-fns/locale';
import { Calendar as CalendarIcon } from "lucide-react";
import { useState } from 'react';
import { Button } from "~/components/ui/button";
import { Calendar } from "~/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { cn } from "~/lib/utils";

interface DateRangePickerProps {
  className?: string;
  onChange?: (range: { from: Date; to: Date }) => void;
}

export function DateRangePicker({ className, onChange }: DateRangePickerProps) {
  const [date, setDate] = useState<{ from: Date; to: Date }>({
    from: subDays(new Date(), 30),
    to: new Date(),
  });

  const [isOpen, setIsOpen] = useState(false);

  const presets = [
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      getValue: () => {
        const today = new Date();
        return { from: today, to: today };
      },
    },
    {
      name: 'Wczoraj',
      getValue: () => {
        const yesterday = subDays(new Date(), 1);
        return { from: yesterday, to: yesterday };
      },
    },
    {
      name: 'Ostatnie 7 dni',
      getValue: () => ({ from: subDays(new Date(), 6), to: new Date() }),
    },
    {
      name: 'Ostatnie 30 dni',
      getValue: () => ({ from: subDays(new Date(), 29), to: new Date() }),
    },
    {
      name: 'Ten tydzień',
      getValue: () => {
        const now = new Date();
        return {
          from: startOfWeek(now, { weekStartsOn: 1 }),
          to: endOfWeek(now, { weekStartsOn: 1 }),
        };
      },
    },
    {
      name: 'Ten miesiąc',
      getValue: () => {
        const now = new Date();
        return { from: startOfMonth(now), to: endOfMonth(now) };
      },
    },
  ];

  const handleSelect = (preset: { from: Date; to: Date }) => {
    setDate(preset);
    if (onChange) {
      onChange(preset);
    }
    setIsOpen(false);
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal hover-lift border-primary/20",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4 text-primary animate-pulse" />
            {date?.from ? (
              date.from.toDateString() === date.to?.toDateString() ? (
                <span className="font-medium">{format(date.from, "PPP", { locale: pl })}</span>
              ) : (
                <span className="font-medium">
                  {format(date.from, "dd MMM", { locale: pl })} -{" "}
                  {format(date.to, "dd MMM yyyy", { locale: pl })}
                </span>
              )
            ) : (
              <span>Wybierz zakres dat</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex flex-col sm:flex-row gap-2 p-3 border-b">
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {presets.map((preset) => (
                <Button
                  key={preset.name}
                  variant="outline"
                  size="sm"
                  onClick={() => handleSelect(preset.getValue())}
                  className="text-xs hover:bg-primary/10 hover:text-primary hover:border-primary/30 transition-colors duration-300"
                >
                  {preset.name}
                </Button>
              ))}
            </div>
          </div>
          <Calendar
            mode="range"
            selected={date}
            onSelect={(range) => {
              if (range?.from && range?.to) {
                const validRange = { from: range.from, to: range.to };
                setDate(validRange);
                if (onChange) {
                  onChange(validRange);
                }
              }
            }}
            numberOfMonths={2}
            locale={pl}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
