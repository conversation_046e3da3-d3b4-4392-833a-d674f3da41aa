import type { Customer } from "@prisma/client";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";

interface CustomerSelectProps {
  id: string;
  name: string;
  customers: Pick<Customer, "id" | "name">[];
  defaultValue?: string;
  onChange?: (value: string) => void;
  required?: boolean;
}

export function CustomerSelect({
  id,
  name,
  customers,
  defaultValue = "",
  onChange,
  required = false,
}: CustomerSelectProps) {
  return (
    <Select
      name={name}
      defaultValue={defaultValue}
      onValueChange={onChange}
      required={required}
    >
      <SelectTrigger id={id}>
        <SelectValue placeholder="Select a customer" />
      </SelectTrigger>
      <SelectContent>
        {customers.map((customer) => (
          <SelectItem key={customer.id} value={customer.id}>
            {customer.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}