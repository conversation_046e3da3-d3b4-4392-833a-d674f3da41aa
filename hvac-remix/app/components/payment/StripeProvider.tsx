import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { useEffect, useState } from 'react';

interface StripeProviderProps {
  children: React.ReactNode;
}

// Load Stripe outside of a component's render to avoid recreating the Stripe object on every render
let stripePromise: ReturnType<typeof loadStripe>;

export default function StripeProvider({ children }: StripeProviderProps) {
  const [stripeKey, setStripeKey] = useState<string | null>(null);

  useEffect(() => {
    // Get the Stripe publishable key from the window object
    // This should be set in root.tsx or a similar file that loads on every page
    const key = window.ENV?.STRIPE_PUBLISHABLE_KEY;
    setStripeKey(key || null);

    if (key && !stripePromise) {
      stripePromise = loadStripe(key);
    }
  }, []);

  if (!stripeKey || !stripePromise) {
    return <div>Loading payment system...</div>;
  }

  return (
    <Elements stripe={stripePromise}>
      {children}
    </Elements>
  );
}
