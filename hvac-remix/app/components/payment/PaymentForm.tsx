import { Form } from '@remix-run/react';
import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import type { StripeCardElementChangeEvent } from '@stripe/stripe-js';
import { useState } from 'react';

interface PaymentFormProps {
  clientSecret: string;
  invoiceId: string;
  amount: number;
  currency?: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export default function PaymentForm({
  clientSecret,
  invoiceId,
  amount,
  currency = 'PLN',
  onSuccess,
  onError,
}: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [error, setError] = useState<string | null>(null);
  const [cardComplete, setCardComplete] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [billingDetails, setBillingDetails] = useState({
    name: '',
    email: '',
  });

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet. Make sure to disable form submission until Stripe.js has loaded.
      return;
    }

    if (!cardComplete) {
      setError('Please complete your card details');
      return;
    }

    setProcessing(true);

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      setError('Card element not found');
      setProcessing(false);
      return;
    }

    const { error: paymentError, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: cardElement,
        billing_details: billingDetails,
      },
    });

    setProcessing(false);

    if (paymentError) {
      setError(paymentError.message || 'An error occurred during payment');
      if (onError) onError(paymentError.message || 'An error occurred during payment');
    } else if (paymentIntent && paymentIntent.status === 'succeeded') {
      // Payment succeeded
      if (onSuccess) onSuccess();
    } else {
      setError('Payment failed. Please try again.');
      if (onError) onError('Payment failed. Please try again.');
    }
  };

  const handleCardChange = (event: StripeCardElementChangeEvent) => {
    setError(event.error ? event.error.message : '');
    setCardComplete(event.complete);
  };

  return (
    <Form onSubmit={handleSubmit} className="space-y-6">
      <input type="hidden" name="invoiceId" value={invoiceId} />
      
      <div className="bg-gray-50 p-4 rounded-md mb-4">
        <div className="text-lg font-semibold mb-2">Payment Amount</div>
        <div className="text-2xl font-bold">
          {new Intl.NumberFormat('pl-PL', {
            style: 'currency',
            currency: currency,
          }).format(amount)}
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Name on Card
          </label>
          <input
            id="name"
            type="text"
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={billingDetails.name}
            onChange={(e) => setBillingDetails({ ...billingDetails, name: e.target.value })}
          />
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email
          </label>
          <input
            id="email"
            type="email"
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={billingDetails.email}
            onChange={(e) => setBillingDetails({ ...billingDetails, email: e.target.value })}
          />
        </div>

        <div>
          <label htmlFor="card" className="block text-sm font-medium text-gray-700">
            Card Details
          </label>
          <div className="mt-1 block w-full rounded-md border border-gray-300 p-3 shadow-sm focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500">
            <CardElement
              id="card"
              options={{
                style: {
                  base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                      color: '#aab7c4',
                    },
                  },
                  invalid: {
                    color: '#9e2146',
                  },
                },
              }}
              onChange={handleCardChange}
            />
          </div>
        </div>
      </div>

      {error && (
        <div className="text-red-500 text-sm mt-2">
          {error}
        </div>
      )}

      <button
        type="submit"
        disabled={!stripe || processing || !cardComplete}
        className="w-full rounded-md bg-blue-600 px-4 py-2 text-white font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
      >
        {processing ? 'Processing...' : 'Pay Now'}
      </button>
    </Form>
  );
}
