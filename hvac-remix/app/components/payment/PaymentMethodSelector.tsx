import type { PaymentMethod } from '@prisma/client';
import { useState } from 'react';

interface PaymentMethodSelectorProps {
  paymentMethods: PaymentMethod[];
  onSelect: (paymentMethodId: string | null) => void;
  selectedPaymentMethodId?: string | null;
}

export default function PaymentMethodSelector({
  paymentMethods,
  onSelect,
  selectedPaymentMethodId,
}: PaymentMethodSelectorProps) {
  const [selected, setSelected] = useState<string | null>(selectedPaymentMethodId || null);
  const [showNewCard, setShowNewCard] = useState(!paymentMethods.length);

  const handleSelect = (paymentMethodId: string | null) => {
    setSelected(paymentMethodId);
    onSelect(paymentMethodId);
    if (paymentMethodId === null) {
      setShowNewCard(true);
    } else {
      setShowNewCard(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-lg font-semibold">Payment Method</div>

      {paymentMethods.length > 0 && (
        <div className="space-y-2">
          <div className="text-sm font-medium text-gray-700">Saved Payment Methods</div>
          <div className="space-y-2">
            {paymentMethods.map((method) => (
              <div
                key={method.id}
                className={`flex items-center p-3 border rounded-md cursor-pointer ${
                  selected === method.paymentMethodId
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-300 hover:border-blue-300'
                }`}
                onClick={() => handleSelect(method.paymentMethodId || null)}
              >
                <div className="flex-1">
                  <div className="font-medium">{method.name}</div>
                  <div className="text-sm text-gray-500">
                    {method.brand && method.lastFourDigits
                      ? `${method.brand.charAt(0).toUpperCase() + method.brand.slice(1)} ending in ${method.lastFourDigits}`
                      : 'Card'}
                    {method.expiryMonth && method.expiryYear
                      ? ` - Expires ${method.expiryMonth}/${method.expiryYear}`
                      : ''}
                  </div>
                </div>
                <div>
                  <input
                    type="radio"
                    name="paymentMethod"
                    value={method.paymentMethodId || ''}
                    checked={selected === method.paymentMethodId}
                    onChange={() => handleSelect(method.paymentMethodId || null)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="space-y-2">
        <div className="flex items-center">
          <input
            type="radio"
            id="newCard"
            name="paymentMethod"
            value="new"
            checked={showNewCard}
            onChange={() => handleSelect(null)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          />
          <label htmlFor="newCard" className="ml-2 block text-sm font-medium text-gray-700">
            Use a new card
          </label>
        </div>
      </div>
    </div>
  );
}
