/**
 * Unified HVAC Dashboard - Integration of CopilotKit + GoBackend-Kratos
 *
 * Revolutionary dashboard combining the best of both worlds:
 * - CopilotKit AI assistant for natural language interaction
 * - GoBackend-Kratos high-performance backend services
 * - Real-time updates and monitoring
 */

"use client";

import { CopilotKit , useCopilotReadable, useCopilotAction } from '@copilotkit/react-core';
import { Copilot<PERSON>idebar, CopilotPopup } from '@copilotkit/react-ui';
import React, { useState, useEffect, useCallback } from 'react';
import '@copilotkit/react-ui/styles.css';

import { Alert, AlertDescription } from '~/components/ui/alert';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';

import { goBackendBridge } from '~/services/gobackend-bridge.server';
import type {
  RealTimeMetrics,
  Job,
  Customer,
  SystemHealth,
  AIAnalysisResponse
} from '~/types/gobackend-api';

interface UnifiedDashboardProps {
  initialMetrics?: RealTimeMetrics;
  initialJobs?: Job[];
  initialCustomers?: Customer[];
  userRole?: 'admin' | 'technician' | 'manager' | 'customer_service';
}

interface DashboardState {
  metrics: RealTimeMetrics | null;
  jobs: Job[];
  customers: Customer[];
  systemHealth: SystemHealth | null;
  isLoading: boolean;
  error: string | null;
  lastUpdate: Date;
}

export const UnifiedHVACDashboard: React.FC<UnifiedDashboardProps> = ({
  initialMetrics,
  initialJobs = [],
  initialCustomers = [],
  userRole = 'manager',
}) => {
  const [state, setState] = useState<DashboardState>({
    metrics: initialMetrics || null,
    jobs: initialJobs,
    customers: initialCustomers,
    systemHealth: null,
    isLoading: false,
    error: null,
    lastUpdate: new Date(),
  });

  const [aiMode, setAiMode] = useState<'sidebar' | 'popup' | 'hidden'>('sidebar');
  const [_selectedJob, setSelectedJob] = useState<Job | null>(null);
  // Note: selectedCustomer removed as it's not used in current implementation

  /**
   * Real-time data fetching
   */
  const fetchRealTimeData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const [metricsResponse, jobsResponse, healthResponse] = await Promise.all([
        goBackendBridge.getRealTimeMetrics(),
        goBackendBridge.getJobs({ limit: 20 }),
        goBackendBridge.checkHealth(),
      ]);

      setState(prev => ({
        ...prev,
        metrics: metricsResponse.success ? metricsResponse.data! : prev.metrics,
        jobs: jobsResponse.success ? jobsResponse.data!.data : prev.jobs,
        systemHealth: healthResponse,
        isLoading: false,
        lastUpdate: new Date(),
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to fetch data',
        isLoading: false,
      }));
    }
  }, []);

  /**
   * WebSocket for real-time updates
   */
  useEffect(() => {
    // Initial data fetch
    fetchRealTimeData();

    // Set up real-time updates every 30 seconds
    const interval = setInterval(fetchRealTimeData, 30000);

    // WebSocket connection for instant updates
    const ws = new WebSocket(`${process.env.GOBACKEND_WS_URL || 'ws://localhost:8080'}/ws`);

    ws.onmessage = (event) => {
      try {
        const update = JSON.parse(event.data);
        handleRealTimeUpdate(update);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    return () => {
      clearInterval(interval);
      ws.close();
    };
  }, [fetchRealTimeData, handleRealTimeUpdate]);

  /**
   * Handle real-time updates from WebSocket
   */
  const handleRealTimeUpdate = useCallback((update: any) => {
    switch (update.type) {
      case 'job_update':
        setState(prev => ({
          ...prev,
          jobs: prev.jobs.map(job =>
            job.id === update.data.jobId
              ? { ...job, status: update.data.status }
              : job
          ),
          lastUpdate: new Date(),
        }));
        break;

      case 'metrics_update':
        setState(prev => ({
          ...prev,
          metrics: update.data,
          lastUpdate: new Date(),
        }));
        break;

      case 'system_alert':
        // Handle system alerts
        console.warn('System alert:', update.data);
        break;
    }
  }, []);

  /**
   * CopilotKit context - make dashboard data available to AI
   */
  useCopilotReadable({
    description: "Current HVAC dashboard state with real-time metrics, jobs, and system health",
    value: {
      metrics: state.metrics,
      jobs: state.jobs.slice(0, 10), // Limit for context size
      customers: state.customers.slice(0, 10),
      systemHealth: state.systemHealth,
      userRole,
      lastUpdate: state.lastUpdate,
    },
  });

  /**
   * CopilotKit Actions - AI-powered dashboard operations
   */
  useCopilotAction({
    name: "analyzeJobIssue",
    description: "Analyze a specific job issue using GoBackend AI",
    parameters: [
      { name: "jobId", type: "string", description: "ID of the job to analyze" },
      { name: "issueDescription", type: "string", description: "Description of the issue" },
    ],
    handler: async ({ jobId, issueDescription }) => {
      const job = state.jobs.find(j => j.id === jobId);
      if (!job) return "Job not found";

      const analysisResponse = await goBackendBridge.analyzeCustomerIssue({
        description: issueDescription,
        customerHistory: { customerId: job.customerId },
        urgencyLevel: job.priority === 'urgent' ? 'high' : 'medium',
      });

      if (analysisResponse.success) {
        const analysis = analysisResponse.data!;
        return `AI Analysis for Job ${jobId}:

**Issue Category**: ${analysis.category}
**Urgency Level**: ${analysis.urgency}
**Confidence**: ${(analysis.confidence * 100).toFixed(1)}%

**Analysis**: ${analysis.analysis}

**Suggested Actions**:
${analysis.suggestedActions.map(action => `- ${action.description}`).join('\n')}

**Estimated Cost**: $${analysis.estimatedCost?.min || 0} - $${analysis.estimatedCost?.max || 0}
**Estimated Duration**: ${analysis.estimatedDuration || 0} minutes`;
      }

      return "Failed to analyze the issue. Please try again.";
    },
  });

  useCopilotAction({
    name: "getSystemStatus",
    description: "Get current system health and performance status",
    parameters: [],
    handler: async () => {
      const health = state.systemHealth;
      if (!health) return "System health data not available";

      return `System Status Report:

**Overall Status**: ${health.status.toUpperCase()}
**Database**: ${health.database.status} (${health.database.latency}ms)
**AI Service**: ${health.ai.status} (Queue: ${health.ai.queueSize})
**Memory Usage**: ${health.memory.percentage.toFixed(1)}% (${health.memory.used}MB / ${health.memory.total}MB)
**CPU Usage**: ${health.cpu.usage.toFixed(1)}%

**Services**:
${Object.entries(health.services).map(([name, service]) =>
  `- ${name}: ${service.status} ${service.latency ? `(${service.latency}ms)` : ''}`
).join('\n')}`;
    },
  });

  useCopilotAction({
    name: "createUrgentJob",
    description: "Create an urgent job for immediate attention",
    parameters: [
      { name: "customerId", type: "string", description: "Customer ID" },
      { name: "title", type: "string", description: "Job title" },
      { name: "description", type: "string", description: "Detailed description" },
    ],
    handler: async ({ customerId, title, description }) => {
      const response = await goBackendBridge.createJob({
        customerId,
        title,
        description,
        priority: 'urgent',
        status: 'pending',
        type: 'emergency',
      });

      if (response.success) {
        // Update local state
        setState(prev => ({
          ...prev,
          jobs: [response.data!, ...prev.jobs],
        }));

        return `✅ Urgent job created successfully!

**Job ID**: ${response.data!.id}
**Title**: ${title}
**Priority**: URGENT
**Status**: Pending Assignment

The job has been added to the queue and will be assigned to the next available technician.`;
      }

      return "❌ Failed to create the job. Please try again or contact support.";
    },
  });

  /**
   * Render metrics cards
   */
  const renderMetricsCards = () => {
    if (!state.metrics) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <Badge variant={state.metrics.activeJobs > 50 ? "destructive" : "default"}>
              {state.metrics.activeJobs}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{state.metrics.activeJobs}</div>
            <p className="text-xs text-muted-foreground">
              {state.metrics.jobsTrend > 0 ? '+' : ''}{state.metrics.jobsTrend.toFixed(1)}% from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customer Satisfaction</CardTitle>
            <Badge variant={state.metrics.customerSatisfaction > 80 ? "default" : "secondary"}>
              {state.metrics.customerSatisfaction.toFixed(1)}%
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{state.metrics.customerSatisfaction.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {state.metrics.satisfactionTrend > 0 ? '+' : ''}{state.metrics.satisfactionTrend.toFixed(1)}% this week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue Today</CardTitle>
            <Badge variant="default">${state.metrics.revenueToday.toLocaleString()}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${state.metrics.revenueToday.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {state.metrics.revenueTrend > 0 ? '+' : ''}{state.metrics.revenueTrend.toFixed(1)}% vs yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <Badge variant={
              state.systemHealth?.status === 'healthy' ? 'default' :
              state.systemHealth?.status === 'warning' ? 'secondary' : 'destructive'
            }>
              {state.systemHealth?.status || 'Unknown'}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {state.systemHealth?.status === 'healthy' ? '✅' :
               state.systemHealth?.status === 'warning' ? '⚠️' : '❌'}
            </div>
            <p className="text-xs text-muted-foreground">
              Last check: {state.lastUpdate.toLocaleTimeString()}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  };

  /**
   * Render jobs list
   */
  const renderJobsList = () => (
    <Card>
      <CardHeader>
        <CardTitle>Recent Jobs</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {state.jobs.slice(0, 5).map((job) => (
            <button
              key={job.id}
              className="flex items-center justify-between p-2 border rounded cursor-pointer hover:bg-gray-50 w-full text-left"
              onClick={() => setSelectedJob(job)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setSelectedJob(job);
                }
              }}
              aria-label={`Select job: ${job.title} for ${job.customer?.name || job.customerId}`}
            >
              <div>
                <div className="font-medium">{job.title}</div>
                <div className="text-sm text-gray-500">{job.customer?.name || job.customerId}</div>
              </div>
              <div className="text-right">
                <Badge variant={
                  job.status === 'completed' ? 'default' :
                  job.status === 'in_progress' ? 'secondary' :
                  job.priority === 'urgent' ? 'destructive' : 'outline'
                }>
                  {job.status}
                </Badge>
                <div className="text-xs text-gray-500 mt-1">{job.priority}</div>
              </div>
            </button>
          ))}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <CopilotKit runtimeUrl="/api/copilotkit">
      <div className="min-h-screen bg-gray-50 p-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">HVAC Command Center</h1>
            <p className="text-gray-600">Unified dashboard powered by AI</p>
          </div>

          <div className="flex gap-2">
            <Button
              variant={aiMode === 'sidebar' ? 'default' : 'outline'}
              onClick={() => setAiMode(aiMode === 'sidebar' ? 'hidden' : 'sidebar')}
            >
              🤖 AI Assistant
            </Button>
            <Button
              variant="outline"
              onClick={fetchRealTimeData}
              disabled={state.isLoading}
            >
              {state.isLoading ? '🔄' : '↻'} Refresh
            </Button>
          </div>
        </div>

        {/* Error Alert */}
        {state.error && (
          <Alert className="mb-4">
            <AlertDescription>{state.error}</AlertDescription>
          </Alert>
        )}

        {/* Metrics Cards */}
        {renderMetricsCards()}

        {/* Main Content */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="jobs">Jobs</TabsTrigger>
            <TabsTrigger value="customers">Customers</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {renderJobsList()}

              <Card>
                <CardHeader>
                  <CardTitle>AI Insights</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <div className="text-4xl mb-2">🤖</div>
                    <p className="text-gray-600">
                      Ask the AI assistant anything about your HVAC operations!
                    </p>
                    <Button
                      className="mt-4"
                      onClick={() => setAiMode('sidebar')}
                    >
                      Start AI Chat
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="jobs">
            <Card>
              <CardHeader>
                <CardTitle>All Jobs</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Jobs table/list component would go here */}
                <p>Jobs management interface coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="customers">
            <Card>
              <CardHeader>
                <CardTitle>Customer Management</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Customers interface would go here */}
                <p>Customer management interface coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Analytics charts would go here */}
                <p>Advanced analytics coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* AI Assistant */}
        {aiMode === 'sidebar' && (
          <CopilotSidebar
            instructions={`You are an expert HVAC assistant with access to real-time system data.
            Help users manage jobs, analyze issues, and optimize operations.
            Current user role: ${userRole}.
            You can analyze jobs, check system status, and create urgent jobs.`}
            labels={{
              title: "HVAC AI Assistant",
              initial: "Hello! I'm your HVAC AI assistant. I can help you analyze jobs, check system status, create urgent jobs, and answer questions about your HVAC operations. What would you like to know?",
            }}
          />
        )}

        {aiMode === 'popup' && (
          <CopilotPopup
            instructions={`You are an expert HVAC assistant. Help with quick questions and tasks.`}
            labels={{
              title: "Quick HVAC Help",
              initial: "Quick question? I'm here to help!",
            }}
          />
        )}
      </div>
    </CopilotKit>
  );
};

export default UnifiedHVACDashboard;
