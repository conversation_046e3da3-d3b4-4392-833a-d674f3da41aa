import { Pencil } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import type { CustomFieldDefinition } from "~/models/metadata.server";

interface DynamicViewProps {
  entityType?: string;
  entityId?: string;
  fields: CustomFieldDefinition[];
  values: Record<string, any>;
  title?: string;
  description?: string;
  onEdit?: () => void;
  showEditButton?: boolean;
  className?: string;
}

export function DynamicView({
  fields,
  values,
  title,
  description,
  onEdit,
  showEditButton = true,
  className = ""
}: DynamicViewProps) {
  // Sort fields by order
  const sortedFields = [...fields].sort((a, b) => a.order - b.order);

  // Format field value based on type
  const formatFieldValue = (field: CustomFieldDefinition, value: any) => {
    if (value === null || value === undefined) {
      return "-";
    }

    switch (field.fieldType) {
      case 'BOOLEAN':
        return value ? "Yes" : "No";

      case 'DATE':
        try {
          return new Date(value).toLocaleDateString();
        } catch (e) {
          return value;
        }

      case 'SELECT':
      case 'MULTI_SELECT':
        if (Array.isArray(value)) {
          return value.join(", ");
        }
        return value;

      default:
        return value;
    }
  };

  return (
    <Card className={className}>
      {title && (
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <p className="text-sm text-muted-foreground">{description}</p>}
          </div>

          {showEditButton && onEdit && (
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Pencil className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </CardHeader>
      )}

      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {sortedFields.map(field => (
            <div key={field.id} className="space-y-1">
              <h4 className="text-sm font-medium">{field.label}</h4>
              <p className="text-sm">
                {formatFieldValue(field, values[field.name])}
              </p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}