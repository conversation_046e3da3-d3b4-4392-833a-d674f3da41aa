import { X } from "lucide-react";
import { useState } from "react";
import { DynamicForm } from "~/components/dynamic/DynamicForm";
import { DynamicView } from "~/components/dynamic/DynamicView";
import { Button } from "~/components/ui/button";
import type { CustomFieldDefinition, EntityType } from "~/models/metadata.server";

interface CustomFieldsSectionProps {
  entityType: EntityType;
  entityId: string;
  fields: CustomFieldDefinition[];
  values: Record<string, any>;
  title?: string;
  onSave?: (values: Record<string, any>) => void;
  className?: string;
}

export function CustomFieldsSection({
  entityType,
  entityId,
  fields,
  values,
  title = "Custom Fields",
  onSave,
  className = ""
}: CustomFieldsSectionProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Only show the section if there are fields
  if (fields.length === 0) {
    return null;
  }

  const handleSave = async (formData: FormData) => {
    if (!onSave) return;

    setIsSubmitting(true);

    try {
      // Get custom fields data from form
      const customFieldsData = formData.get("customFieldsData");
      if (customFieldsData) {
        const parsedData = JSON.parse(customFieldsData as string);
        await onSave(parsedData);
      }

      setIsEditing(false);
    } catch (error) {
      console.error("Error saving custom fields:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={className}>
      {isEditing ? (
        <DynamicForm
          entityType={entityType}
          entityId={entityId}
          fields={fields}
          defaultValues={values}
          onSubmit={handleSave}
          submitButtonText="Save Custom Fields"
          title={title}
          isSubmitting={isSubmitting}
          className="relative"
        >
          <div className="absolute top-4 right-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(false)}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DynamicForm>
      ) : (
        <DynamicView
          entityType={entityType}
          entityId={entityId}
          fields={fields}
          values={values}
          title={title}
          onEdit={() => setIsEditing(true)}
          showEditButton={!!onSave}
        />
      )}
    </div>
  );
}