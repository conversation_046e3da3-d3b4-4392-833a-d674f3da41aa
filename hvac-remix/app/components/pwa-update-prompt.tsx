import { RefreshCw } from "lucide-react";
import { useState, useEffect } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "~/components/ui/alert-dialog";

/**
 * PWA Update Prompt Component
 *
 * This component shows a dialog when a new service worker is available,
 * prompting the user to refresh the page to update the application.
 */
export function PWAUpdatePrompt() {
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [offlineStatus, setOfflineStatus] = useState(false);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);

  // Check for service worker updates
  useEffect(() => {
    // Only run in the browser
    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
      return;
    }

    // Check if service worker is registered
    navigator.serviceWorker.getRegistration().then((reg) => {
      if (reg) {
        setRegistration(reg);

        // Listen for new service worker
        reg.addEventListener('updatefound', () => {
          const newWorker = reg.installing;

          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              // If the new service worker is installed but waiting
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                setShowUpdatePrompt(true);
              }
            });
          }
        });
      }
    });

    // Listen for service worker update events
    window.addEventListener('sw-update-available', () => {
      setShowUpdatePrompt(true);
    });

    // Listen for online/offline events
    const handleOnline = () => setOfflineStatus(false);
    const handleOffline = () => setOfflineStatus(true);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial offline status
    setOfflineStatus(!navigator.onLine);

    // Cleanup
    return () => {
      window.removeEventListener('sw-update-available', () => {
        setShowUpdatePrompt(true);
      });
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Handle update click
  const handleUpdate = () => {
    if (registration && registration.waiting) {
      // Send message to service worker to skip waiting
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }

    // Reload the page to get the new version
    window.location.reload();
  };

  return (
    <>
      {/* Update Prompt Dialog */}
      <AlertDialog open={showUpdatePrompt} onOpenChange={setShowUpdatePrompt}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Dostępna aktualizacja</AlertDialogTitle>
            <AlertDialogDescription>
              Nowa wersja aplikacji jest dostępna. Odśwież stronę, aby zaktualizować.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Później</AlertDialogCancel>
            <AlertDialogAction onClick={handleUpdate}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Aktualizuj teraz
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Offline Status Indicator */}
      {offlineStatus && (
        <div className="fixed bottom-4 left-4 z-50 flex items-center gap-2 rounded-full bg-red-100 px-3 py-1.5 text-sm font-medium text-red-800 shadow-md dark:bg-red-900/30 dark:text-red-400">
          <span className="h-2 w-2 rounded-full bg-red-500 dark:bg-red-400"></span>
          Offline
        </div>
      )}
    </>
  );
}
