import { Link } from '@remix-run/react';
import { ServerOff } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import { useServiceAvailability, ServiceStatus } from '~/contexts/service-availability';

interface ServiceStatusIndicatorProps {
  className?: string;
}

/**
 * Component that indicates the overall status of backend services
 */
export function ServiceStatusIndicator({ className = '' }: ServiceStatusIndicatorProps) {
  const { services } = useServiceAvailability();

  // Check if any service is unavailable
  const hasUnavailableServices = Object.values(services).some(
    (service) => service.status === ServiceStatus.UNAVAILABLE
  );

  // Count unavailable services
  const unavailableCount = Object.values(services).filter(
    (service) => service.status === ServiceStatus.UNAVAILABLE
  ).length;

  // If all services are available or unknown, don't show the indicator
  if (!hasUnavailableServices) {
    return null;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link
            to="/settings/service-status"
            className={`flex items-center px-3 py-1 rounded-full cursor-pointer bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-200 ${className}`}
          >
            <ServerOff className="w-4 h-4 mr-2" />
            <span className="text-sm font-medium">
              {unavailableCount} service{unavailableCount > 1 ? 's' : ''} unavailable
            </span>
          </Link>
        </TooltipTrigger>
        <TooltipContent>
          <p>Some backend services are unavailable.</p>
          <p className="text-xs mt-1">Click to view service status details.</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
