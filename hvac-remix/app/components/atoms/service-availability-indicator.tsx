import { AlertCircle, CheckCircle, HelpCircle, RefreshCw } from 'lucide-react';
import React from 'react';
import { Button } from '~/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import { useServiceAvailability, ServiceType, ServiceStatus } from '~/contexts/service-availability';
import { cn } from '~/lib/utils';

interface ServiceAvailabilityIndicatorProps {
  serviceType: ServiceType;
  showLabel?: boolean;
  className?: string;
}

/**
 * Component that indicates the availability of a backend service
 */
export function ServiceAvailabilityIndicator({
  serviceType,
  showLabel = false,
  className = '',
}: ServiceAvailabilityIndicatorProps) {
  const { services, checkService } = useServiceAvailability();
  const serviceInfo = services[serviceType];
  const [isChecking, setIsChecking] = React.useState(false);

  // Get service display name
  const getServiceDisplayName = (type: ServiceType): string => {
    switch (type) {
      case ServiceType.BIELIK_LLM:
        return 'Bielik LLM';
      case ServiceType.OCR:
        return 'OCR Service';
      case ServiceType.PREDICTIVE_MAINTENANCE:
        return 'Predictive Maintenance';
      default:
        return 'Unknown Service';
    }
  };

  // Handle refresh click
  const handleRefresh = async () => {
    setIsChecking(true);
    await checkService(serviceType);
    setTimeout(() => setIsChecking(false), 1000); // Show spinner for at least 1 second
  };

  // Get status icon
  const getStatusIcon = () => {
    if (isChecking) {
      return <RefreshCw className="h-4 w-4 animate-spin" />;
    }

    switch (serviceInfo.status) {
      case ServiceStatus.AVAILABLE:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case ServiceStatus.UNAVAILABLE:
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case ServiceStatus.UNKNOWN:
      default:
        return <HelpCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  // Get status text
  const getStatusText = (): string => {
    if (isChecking) {
      return 'Checking...';
    }

    switch (serviceInfo.status) {
      case ServiceStatus.AVAILABLE:
        return 'Available';
      case ServiceStatus.UNAVAILABLE:
        return 'Unavailable';
      case ServiceStatus.UNKNOWN:
      default:
        return 'Unknown';
    }
  };

  // Get tooltip text
  const getTooltipText = (): string => {
    const serviceName = getServiceDisplayName(serviceType);
    
    if (isChecking) {
      return `Checking ${serviceName} availability...`;
    }

    switch (serviceInfo.status) {
      case ServiceStatus.AVAILABLE:
        return `${serviceName} is available`;
      case ServiceStatus.UNAVAILABLE:
        return `${serviceName} is unavailable: ${serviceInfo.errorMessage || 'Unknown error'}`;
      case ServiceStatus.UNKNOWN:
      default:
        return `${serviceName} status is unknown`;
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'flex items-center gap-2 rounded-md px-2 py-1',
              serviceInfo.status === ServiceStatus.AVAILABLE
                ? 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300'
                : serviceInfo.status === ServiceStatus.UNAVAILABLE
                ? 'bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-300'
                : 'bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-300',
              className
            )}
          >
            {getStatusIcon()}
            {showLabel && (
              <span className="text-xs font-medium">
                {getServiceDisplayName(serviceType)}: {getStatusText()}
              </span>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 ml-1"
              onClick={handleRefresh}
              disabled={isChecking}
            >
              <RefreshCw className="h-3 w-3" />
              <span className="sr-only">Refresh</span>
            </Button>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{getTooltipText()}</p>
          {serviceInfo.lastChecked && (
            <p className="text-xs text-gray-500">
              Last checked: {new Date(serviceInfo.lastChecked).toLocaleTimeString()}
            </p>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
