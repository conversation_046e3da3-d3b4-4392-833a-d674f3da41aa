/**
 * Enhanced HVAC Dashboard with CopilotKit Integration
 * Replaces agent-protocol components with CopilotKit AI assistant
 */

"use client";

import { CopilotKit , useCopilotReadable, useCopilotAdditionalInstructions } from '@copilotkit/react-core';
import { Co<PERSON><PERSON>Sidebar, CopilotPopup } from '@copilotkit/react-ui';
import React, { useState, useEffect } from 'react';
import '@copilotkit/react-ui/styles.css';

import type { CustomerContext } from '~/types/copilotkit-bridge';
import { HVACCopilotActions } from './HVACCopilotActions';

interface EnhancedHVACDashboardProps {
  children: React.ReactNode;
  customerContext?: CustomerContext;
  currentPage?: string;
  userRole?: string;
}

export function EnhancedHVACDashboard({ 
  children, 
  customerContext,
  currentPage = 'dashboard',
  userRole = 'technician'
}: EnhancedHVACDashboardProps) {
  const [isAIAssistantOpen, setIsAIAssistantOpen] = useState(false);
  const [systemStatus, setSystemStatus] = useState<any>(null);

  // Make current context readable to CopilotKit
  useCopilotReadable({
    description: "Current customer context and service history",
    value: customerContext || null
  });

  useCopilotReadable({
    description: "Current page and user role context",
    value: {
      current_page: currentPage,
      user_role: userRole,
      timestamp: new Date().toISOString()
    }
  });

  // Dynamic instructions based on current context
  useCopilotAdditionalInstructions({
    instructions: `
Current Context:
- Page: ${currentPage}
- User Role: ${userRole}
- Customer: ${customerContext?.customer_id || 'none selected'}

Page-specific guidance:
${getPageSpecificInstructions(currentPage)}

Role-specific capabilities:
${getRoleSpecificInstructions(userRole)}
    `,
    available: "enabled"
  });

  // Check system status periodically
  useEffect(() => {
    const checkStatus = async () => {
      try {
        const response = await fetch('/api/copilotkit/status');
        const status = await response.json();
        setSystemStatus(status);
      } catch (error) {
        console.error('Failed to check system status:', error);
      }
    };

    checkStatus();
    const interval = setInterval(checkStatus, 60000); // Check every minute
    return () => clearInterval(interval);
  }, []);

  return (
    <CopilotKit runtimeUrl="/api/copilotkit">
      <div className="enhanced-hvac-dashboard relative">
        {/* Main Dashboard Content */}
        <div className="dashboard-content">
          {children}
        </div>

        {/* AI Assistant Status Indicator */}
        <div className="fixed top-4 right-4 z-50">
          <div className="flex items-center space-x-2 bg-white rounded-lg shadow-lg p-2">
            <div className={`w-3 h-3 rounded-full ${
              systemStatus?.trubackend_integration === 'enabled' 
                ? 'bg-green-500' 
                : 'bg-red-500'
            }`}></div>
            <span className="text-sm font-medium text-gray-700">
              AI Assistant
            </span>
            <button
              onClick={() => setIsAIAssistantOpen(!isAIAssistantOpen)}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              {isAIAssistantOpen ? 'Hide' : 'Show'}
            </button>
          </div>
        </div>

        {/* CopilotKit Sidebar - Main AI Interface */}
        <CopilotSidebar
          instructions={`
You are the HVAC CRM AI Assistant with full access to TruBackend services.

CURRENT CONTEXT:
- User Role: ${userRole}
- Current Page: ${currentPage}
- Customer: ${customerContext?.customer_id || 'None selected'}

AVAILABLE CAPABILITIES:
🔍 Customer Issue Analysis - Analyze HVAC problems with Bielik V3
📚 Customer History Search - Search service records and interactions
🤖 Expert Consultation - Ask Bielik V3 for technical insights
💬 Response Generation - Create professional customer responses
🗺️ Route Optimization - Optimize technician schedules and routes
🔮 Predictive Maintenance - Predict equipment maintenance needs
📄 Document Analysis - Analyze manuals, invoices, and photos
📊 System Monitoring - Check TruBackend services health

QUICK COMMANDS:
- "Analyze this issue: [customer description]"
- "Search for customer [name or ID]"
- "What does Bielik think about [technical question]?"
- "Generate response for [situation]"
- "Optimize route for today's service calls"
- "Predict maintenance for equipment [ID]"
- "Check system status"

SAFETY PRIORITIES:
🚨 Gas leaks → Immediate emergency response
⚡ Electrical issues → Safety first protocols
🔥 Heating failures in winter → Priority scheduling
❄️ AC failures in summer → Urgent response
          `}
          labels={{
            title: "🔧 HVAC AI Assistant",
            initial: `Hello! I'm your HVAC AI assistant with access to advanced AI services. 

Current context: ${currentPage} page, ${userRole} role
${customerContext ? `Customer: ${customerContext.customer_id}` : 'No customer selected'}

How can I help you today? Try:
• "Analyze this HVAC issue: [description]"
• "Search customer history for [query]"
• "Check system status"
• "Generate response for urgent AC repair"`
          }}
          defaultOpen={isAIAssistantOpen}
          onSetOpen={setIsAIAssistantOpen}
        />

        {/* Mobile CopilotKit Popup for smaller screens */}
        <div className="md:hidden">
          <CopilotPopup
            instructions="Mobile HVAC AI Assistant - optimized for touch interactions"
            labels={{
              title: "🔧 HVAC AI",
              initial: "Mobile AI Assistant ready! How can I help with HVAC services?"
            }}
          />
        </div>

        {/* Register all HVAC-specific CopilotKit actions */}
        <HVACCopilotActions />

        {/* Quick Action Buttons */}
        <div className="fixed bottom-4 right-4 z-40 space-y-2">
          <QuickActionButton
            icon="🔍"
            label="Quick Analysis"
            onClick={() => setIsAIAssistantOpen(true)}
          />
          <QuickActionButton
            icon="📊"
            label="System Status"
            onClick={() => {
              // Trigger system status check through AI
              setIsAIAssistantOpen(true);
            }}
          />
        </div>
      </div>
    </CopilotKit>
  );
}

// Helper component for quick action buttons
function QuickActionButton({ 
  icon, 
  label, 
  onClick 
}: { 
  icon: string; 
  label: string; 
  onClick: () => void; 
}) {
  return (
    <button
      onClick={onClick}
      className="flex items-center space-x-2 bg-blue-600 text-white px-3 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
      title={label}
    >
      <span className="text-lg">{icon}</span>
      <span className="hidden sm:inline text-sm font-medium">{label}</span>
    </button>
  );
}

// Helper functions for context-specific instructions
function getPageSpecificInstructions(page: string): string {
  const instructions: Record<string, string> = {
    dashboard: "Focus on overview metrics, urgent issues, and daily priorities",
    customers: "Emphasize customer history, service records, and relationship insights",
    'service-orders': "Prioritize scheduling, route optimization, and technician assignments",
    devices: "Focus on equipment status, maintenance predictions, and technical analysis",
    calendar: "Emphasize scheduling optimization and availability management",
    reports: "Focus on data analysis, trends, and business insights",
    inventory: "Emphasize parts availability, ordering, and cost optimization"
  };
  
  return instructions[page] || "Provide general HVAC CRM assistance";
}

function getRoleSpecificInstructions(role: string): string {
  const instructions: Record<string, string> = {
    admin: "Full system access - focus on business metrics, user management, and system optimization",
    manager: "Team oversight - emphasize scheduling, performance metrics, and resource allocation",
    technician: "Field operations - focus on service execution, technical guidance, and mobile workflows",
    dispatcher: "Coordination focus - emphasize scheduling, routing, and communication",
    customer_service: "Customer interaction - focus on issue resolution and communication"
  };
  
  return instructions[role] || "Standard HVAC CRM capabilities";
}

export default EnhancedHVACDashboard;