import type { User } from "@prisma/client";
import { Footer } from "~/components/organisms/footer";
import { Header } from "~/components/organisms/header";
import { OfflineIndicator, OfflineBanner } from "~/components/ui/offline-indicator";
import type { UserRole, Notification } from "~/types/shared";

interface MainLayoutProps {
  user: User | null;
  children: React.ReactNode;
  userRole?: UserRole;
  notifications?: Notification[];
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
}

export function MainLayout({
  user,
  children,
  userRole = 'USER',
  notifications = [],
  onMarkAsRead,
  onMarkAllAsRead
}: MainLayoutProps) {
  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-background to-background/95">
      {/* Offline Banner */}
      <OfflineBanner />

      {/* Header */}
      <Header
        user={user}
        userRole={userRole}
        notifications={notifications}
        onMarkAsRead={onMarkAsRead}
        onMarkAllAsRead={onMarkAllAsRead}
      />

      {/* Offline Indicator */}
      <div className="fixed bottom-4 right-4 z-50">
        <OfflineIndicator />
      </div>

      {/* Main Content */}
      <main className="flex-1 bg-gradient-to-b from-background/50 to-background/80 backdrop-blur-[2px] pt-6">
        <div className="mx-auto w-full max-w-[1920px]">
          {children}
        </div>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
}