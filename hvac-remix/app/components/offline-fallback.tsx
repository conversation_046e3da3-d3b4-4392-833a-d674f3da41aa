import { WifiOffIcon, RefreshCwIcon, HomeIcon } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";

/**
 * Offline Fallback Component
 * 
 * This component is shown when the user is offline and tries to access a page
 * that is not cached. It provides options to retry or go to the home page.
 */
export function OfflineFallback() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <WifiOffIcon className="h-10 w-10 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-2xl font-bold">Brak połączenia z internetem</CardTitle>
          <CardDescription>
            Nie możemy połączyć się z serwerem. Ta strona nie jest dostępna offline.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-md bg-muted p-4">
            <div className="flex items-center space-x-2">
              <p className="text-sm text-muted-foreground">
                Niektóre funkcje aplikacji są dostępne offline, ale ta strona wymaga połączenia z internetem.
                Sprawdź swoje połączenie internetowe i spróbuj ponownie.
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button
            className="w-full"
            onClick={() => {
              window.location.reload();
            }}
          >
            <RefreshCwIcon className="mr-2 h-4 w-4" />
            Odśwież stronę
          </Button>
          <Button variant="outline" className="w-full" onClick={() => window.location.href = '/'}>
            <HomeIcon className="mr-2 h-4 w-4" />
            Wróć do strony głównej
          </Button>
        </CardFooter>
      </Card>
      <p className="mt-8 text-center text-sm text-muted-foreground">
        Jeśli problem będzie się powtarzał, skontaktuj się z administratorem systemu.
      </p>
    </div>
  );
}
