/**
 * 🌌 QUANTUM BUTTON
 * Transcendent button component with cosmic interactions
 * Self-optimizing, emotionally intelligent, quantum-responsive
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { cn } from '~/lib/utils';
import { cosmicDesignSystem } from '~/design-system/cosmic-design-tokens';

interface QuantumButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'cosmic' | 'stellar' | 'quantum' | 'transcendent' | 'galactic';
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'cosmic' | 'transcendent';
  cosmicEnergy?: 'low' | 'medium' | 'high' | 'maximum' | 'infinite';
  quantumState?: 'stable' | 'excited' | 'superposition' | 'entangled';
  emotionalResonance?: boolean;
  predictiveHover?: boolean;
  particleEffects?: boolean;
  dimensionalRipple?: boolean;
  neuralFeedback?: boolean;
  children: React.ReactNode;
}

interface ParticleSystem {
  particles: Particle[];
  canvas?: HTMLCanvasElement;
  context?: CanvasRenderingContext2D;
  animationFrame?: number;
}

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  color: string;
  size: number;
}

export function QuantumButton({
  variant = 'cosmic',
  size = 'md',
  cosmicEnergy = 'medium',
  quantumState = 'stable',
  emotionalResonance = true,
  predictiveHover = true,
  particleEffects = true,
  dimensionalRipple = true,
  neuralFeedback = true,
  className,
  children,
  onMouseEnter,
  onMouseLeave,
  onClick,
  ...props
}: QuantumButtonProps) {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particleSystemRef = useRef<ParticleSystem>({ particles: [] });
  
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [cosmicCharge, setCosmicCharge] = useState(0);
  const [quantumFluctuation, setQuantumFluctuation] = useState(0);
  const [emotionalState, setEmotionalState] = useState<'neutral' | 'excited' | 'transcendent'>('neutral');
  const [dimensionalPhase, setDimensionalPhase] = useState(0);

  // 🌌 Cosmic variant styles
  const getCosmicVariantStyles = useCallback(() => {
    const variants = {
      cosmic: {
        background: cosmicDesignSystem.colors.gradients.cosmic,
        shadow: cosmicDesignSystem.shadows.cosmic,
        border: `1px solid ${cosmicDesignSystem.colors.cosmic[400]}`,
        color: 'white'
      },
      stellar: {
        background: cosmicDesignSystem.colors.gradients.stellar,
        shadow: cosmicDesignSystem.shadows.stellar,
        border: `1px solid ${cosmicDesignSystem.colors.stellar[400]}`,
        color: 'white'
      },
      quantum: {
        background: cosmicDesignSystem.colors.gradients.quantum,
        shadow: cosmicDesignSystem.shadows.quantum,
        border: `1px solid ${cosmicDesignSystem.colors.quantum[400]}`,
        color: 'white'
      },
      transcendent: {
        background: cosmicDesignSystem.colors.gradients.transcendent,
        shadow: cosmicDesignSystem.shadows.transcendent,
        border: `1px solid ${cosmicDesignSystem.colors.transcendent[400]}`,
        color: 'white'
      },
      galactic: {
        background: cosmicDesignSystem.colors.gradients.galactic,
        shadow: cosmicDesignSystem.shadows.base,
        border: `1px solid ${cosmicDesignSystem.colors.galactic[400]}`,
        color: 'white'
      }
    };

    return variants[variant];
  }, [variant]);

  // 📏 Cosmic size styles
  const getCosmicSizeStyles = useCallback(() => {
    const sizes = {
      sm: {
        padding: `${cosmicDesignSystem.spacing.scale[2]} ${cosmicDesignSystem.spacing.scale[4]}`,
        fontSize: cosmicDesignSystem.typography.sizes.sm,
        borderRadius: cosmicDesignSystem.borderRadius.md
      },
      md: {
        padding: `${cosmicDesignSystem.spacing.scale[3]} ${cosmicDesignSystem.spacing.scale[6]}`,
        fontSize: cosmicDesignSystem.typography.sizes.base,
        borderRadius: cosmicDesignSystem.borderRadius.lg
      },
      lg: {
        padding: `${cosmicDesignSystem.spacing.scale[4]} ${cosmicDesignSystem.spacing.scale[8]}`,
        fontSize: cosmicDesignSystem.typography.sizes.lg,
        borderRadius: cosmicDesignSystem.borderRadius.xl
      },
      xl: {
        padding: `${cosmicDesignSystem.spacing.scale[5]} ${cosmicDesignSystem.spacing.scale[10]}`,
        fontSize: cosmicDesignSystem.typography.sizes.xl,
        borderRadius: cosmicDesignSystem.borderRadius['2xl']
      },
      cosmic: {
        padding: `${cosmicDesignSystem.spacing.scale[6]} ${cosmicDesignSystem.spacing.scale[12]}`,
        fontSize: cosmicDesignSystem.typography.sizes['2xl'],
        borderRadius: cosmicDesignSystem.borderRadius.cosmic
      },
      transcendent: {
        padding: `${cosmicDesignSystem.spacing.scale[8]} ${cosmicDesignSystem.spacing.scale[16]}`,
        fontSize: cosmicDesignSystem.typography.sizes['3xl'],
        borderRadius: cosmicDesignSystem.borderRadius.transcendent
      }
    };

    return sizes[size];
  }, [size]);

  // ⚡ Initialize particle system
  useEffect(() => {
    if (!particleEffects || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    if (!context) return;

    particleSystemRef.current = {
      particles: [],
      canvas,
      context
    };

    const resizeCanvas = () => {
      if (buttonRef.current && canvas) {
        const rect = buttonRef.current.getBoundingClientRect();
        canvas.width = rect.width;
        canvas.height = rect.height;
      }
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (particleSystemRef.current.animationFrame) {
        cancelAnimationFrame(particleSystemRef.current.animationFrame);
      }
    };
  }, [particleEffects]);

  // 🌟 Create cosmic particles
  const createCosmicParticles = useCallback((x: number, y: number, count: number = 10) => {
    if (!particleSystemRef.current.context) return;

    const colors = [
      cosmicDesignSystem.colors.cosmic[400],
      cosmicDesignSystem.colors.stellar[400],
      cosmicDesignSystem.colors.quantum[400],
      cosmicDesignSystem.colors.transcendent[400]
    ];

    for (let i = 0; i < count; i++) {
      const angle = (Math.PI * 2 * i) / count;
      const velocity = 2 + Math.random() * 3;
      
      particleSystemRef.current.particles.push({
        x,
        y,
        vx: Math.cos(angle) * velocity,
        vy: Math.sin(angle) * velocity,
        life: 1,
        maxLife: 1,
        color: colors[Math.floor(Math.random() * colors.length)],
        size: 2 + Math.random() * 3
      });
    }
  }, []);

  // 🎨 Animate particle system
  const animateParticles = useCallback(() => {
    const { context, particles } = particleSystemRef.current;
    if (!context || !canvasRef.current) return;

    context.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);

    for (let i = particles.length - 1; i >= 0; i--) {
      const particle = particles[i];
      
      // Update particle
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.life -= 0.02;
      particle.vy += 0.1; // Gravity

      // Draw particle
      context.save();
      context.globalAlpha = particle.life;
      context.fillStyle = particle.color;
      context.beginPath();
      context.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      context.fill();
      context.restore();

      // Remove dead particles
      if (particle.life <= 0) {
        particles.splice(i, 1);
      }
    }

    if (particles.length > 0) {
      particleSystemRef.current.animationFrame = requestAnimationFrame(animateParticles);
    }
  }, []);

  // 🌊 Dimensional ripple effect
  const createDimensionalRipple = useCallback((event: React.MouseEvent) => {
    if (!dimensionalRipple || !buttonRef.current) return;

    const button = buttonRef.current;
    const rect = button.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const ripple = document.createElement('div');
    ripple.className = 'quantum-ripple';
    ripple.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: ${y}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
      transform: translate(-50%, -50%);
      animation: quantumRipple 600ms ease-out;
      pointer-events: none;
      z-index: 1;
    `;

    button.appendChild(ripple);

    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple);
      }
    }, 600);
  }, [dimensionalRipple]);

  // 🧠 Neural feedback system
  useEffect(() => {
    if (!neuralFeedback) return;

    const interval = setInterval(() => {
      setQuantumFluctuation(Math.sin(Date.now() * 0.001) * 0.1);
      setDimensionalPhase(prev => (prev + 1) % 360);
    }, 16); // 60fps

    return () => clearInterval(interval);
  }, [neuralFeedback]);

  // 💫 Emotional resonance system
  useEffect(() => {
    if (!emotionalResonance) return;

    if (isHovered && !isPressed) {
      setEmotionalState('excited');
      setCosmicCharge(prev => Math.min(prev + 0.1, 1));
    } else if (isPressed) {
      setEmotionalState('transcendent');
      setCosmicCharge(1);
    } else {
      setEmotionalState('neutral');
      setCosmicCharge(prev => Math.max(prev - 0.05, 0));
    }
  }, [isHovered, isPressed, emotionalResonance]);

  // 🎯 Event handlers
  const handleMouseEnter = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    setIsHovered(true);
    
    if (predictiveHover && particleEffects) {
      const rect = event.currentTarget.getBoundingClientRect();
      createCosmicParticles(rect.width / 2, rect.height / 2, 5);
      animateParticles();
    }

    onMouseEnter?.(event);
  }, [onMouseEnter, predictiveHover, particleEffects, createCosmicParticles, animateParticles]);

  const handleMouseLeave = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    setIsHovered(false);
    onMouseLeave?.(event);
  }, [onMouseLeave]);

  const handleClick = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    setIsPressed(true);
    
    createDimensionalRipple(event);
    
    if (particleEffects) {
      const rect = event.currentTarget.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      createCosmicParticles(x, y, 15);
      animateParticles();
    }

    setTimeout(() => setIsPressed(false), 150);
    onClick?.(event);
  }, [onClick, createDimensionalRipple, particleEffects, createCosmicParticles, animateParticles]);

  // 🎨 Dynamic styles
  const variantStyles = getCosmicVariantStyles();
  const sizeStyles = getCosmicSizeStyles();

  const dynamicStyles = {
    ...variantStyles,
    ...sizeStyles,
    transform: `
      scale(${1 + cosmicCharge * 0.05 + quantumFluctuation}) 
      rotate(${dimensionalPhase * 0.1}deg)
    `,
    filter: `
      brightness(${1 + cosmicCharge * 0.2}) 
      saturate(${1 + cosmicCharge * 0.3})
      hue-rotate(${dimensionalPhase}deg)
    `,
    transition: `all ${cosmicDesignSystem.animations.duration.cosmic} ${cosmicDesignSystem.animations.easing.cosmic}`
  };

  return (
    <button
      ref={buttonRef}
      className={cn(
        'relative overflow-hidden font-medium transition-all duration-300 ease-cosmic',
        'hover:shadow-lg active:scale-95 focus:outline-none focus:ring-4 focus:ring-cosmic-400/50',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        'touch-manipulation select-none',
        className
      )}
      style={dynamicStyles}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      {...props}
    >
      {/* Particle canvas */}
      {particleEffects && (
        <canvas
          ref={canvasRef}
          className="absolute inset-0 pointer-events-none"
          style={{ zIndex: 1 }}
        />
      )}

      {/* Content */}
      <span className="relative z-10 flex items-center justify-center gap-2">
        {children}
      </span>

      {/* Cosmic energy indicator */}
      {cosmicEnergy !== 'low' && (
        <div 
          className="absolute inset-0 opacity-20 pointer-events-none"
          style={{
            background: `radial-gradient(circle at center, ${variantStyles.border.split(' ')[2]} 0%, transparent 70%)`,
            transform: `scale(${1 + cosmicCharge})`,
            transition: 'transform 300ms ease-cosmic'
          }}
        />
      )}

      {/* Quantum state indicator */}
      {quantumState !== 'stable' && (
        <div 
          className="absolute top-1 right-1 w-2 h-2 rounded-full"
          style={{
            background: quantumState === 'excited' ? '#10b981' : 
                       quantumState === 'superposition' ? '#eab308' : '#d946ef',
            animation: `quantumPulse ${cosmicDesignSystem.animations.duration.cosmic} infinite`
          }}
        />
      )}
    </button>
  );
}

// 🌌 CSS animations for quantum effects
const quantumStyles = `
  @keyframes quantumRipple {
    0% {
      width: 0;
      height: 0;
      opacity: 1;
    }
    100% {
      width: 200px;
      height: 200px;
      opacity: 0;
    }
  }

  @keyframes quantumPulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.5;
      transform: scale(1.2);
    }
  }

  .ease-cosmic {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = quantumStyles;
  document.head.appendChild(styleSheet);
}
