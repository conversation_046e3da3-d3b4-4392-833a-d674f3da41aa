import { CalendarIcon, CheckIcon } from "@chakra-ui/icons";
import { <PERSON><PERSON>, Box, Text, Flex, Spinner } from "@chakra-ui/react";
import { useSubmit } from "@remix-run/react";
import { useState } from "react";

interface CalendarEntryServiceOrderButtonProps {
  calendarEntryId: string;
  hasServiceOrder: boolean;
  serviceOrderId?: string;
}

export default function CalendarEntryServiceOrderButton({
  calendarEntryId,
  hasServiceOrder,
  serviceOrderId
}: CalendarEntryServiceOrderButtonProps) {
  const [isCreating, setIsCreating] = useState(false);
  const submit = useSubmit();

  const handleCreateServiceOrder = () => {
    setIsCreating(true);
    
    const formData = new FormData();
    formData.append("action", "process-single");
    formData.append("calendarEntryId", calendarEntryId);
    
    submit(formData, {
      method: "post",
      action: "/api/calendar-to-service-order",
      encType: "application/x-www-form-urlencoded",
      replace: true,
      preventScrollReset: true,
    });
    
    // Set a timeout to reset the state in case the response is not received
    setTimeout(() => {
      setIsCreating(false);
    }, 5000);
  };

  // Note: Success and error handling would be implemented via useEffect 
  // listening to navigation state or fetcher state in a real implementation

  if (hasServiceOrder && serviceOrderId) {
    return (
      <Box mb={4} p={3} borderWidth="1px" borderRadius="md" bg="green.50">
        <Flex align="center">
          <CheckIcon color="green.500" mr={2} />
          <Text fontWeight="medium">
            This calendar entry is linked to a service order.
          </Text>
          <Button
            as="a"
            href={`/service-orders/${serviceOrderId}`}
            size="sm"
            colorScheme="green"
            ml={2}
          >
            View Service Order
          </Button>
        </Flex>
      </Box>
    );
  }

  return (
    <Box mb={4} p={3} borderWidth="1px" borderRadius="md" bg="blue.50">
      <Flex align="center" justify="space-between">
        <Flex align="center">
          <CalendarIcon color="blue.500" mr={2} />
          <Text fontWeight="medium">
            Create a service order from this calendar entry
          </Text>
        </Flex>
        <Button
          colorScheme="blue"
          size="sm"
          onClick={handleCreateServiceOrder}
          isLoading={isCreating}
          loadingText="Creating..."
          disabled={isCreating}
          leftIcon={isCreating ? <Spinner size="xs" /> : undefined}
        >
          Create Service Order
        </Button>
      </Flex>
    </Box>
  );
}