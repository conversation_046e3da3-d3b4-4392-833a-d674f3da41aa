/**
 * 🚀 CUSTOMER LIFECYCLE DASHBOARD
 * 
 * Advanced dashboard for customer lifecycle management with real-time metrics,
 * health scoring, and actionable insights.
 * 
 * Philosophy: "Data becomes wisdom when it guides compassionate action"
 */

import { 
  Users, 
  TrendingUp, 
  Heart, 
  AlertTriangle, 
  Star,
  Phone,
  Mail,
  MessageSquare,
  Calendar,
  DollarSign,
  Target
} from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Progress } from '~/components/ui/progress';
import { 
  CustomerLifecycleStage, 
  CustomerSegment, 
  CustomerAnalyticsProfile,
  CustomerHealthMetrics 
} from '~/models/customer-lifecycle.server';

interface CustomerLifecycleDashboardProps {
  customerId?: string;
  showOverview?: boolean;
}

interface LifecycleMetrics {
  totalCustomers: number;
  stageDistribution: Record<CustomerLifecycleStage, number>;
  segmentDistribution: Record<CustomerSegment, number>;
  averageHealthScore: number;
  atRiskCustomers: number;
  loyalCustomers: number;
  totalLifetimeValue: number;
  churnRate: number;
}

export function CustomerLifecycleDashboard({ 
  customerId, 
  showOverview = true 
}: CustomerLifecycleDashboardProps) {
  const [metrics, setMetrics] = useState<LifecycleMetrics | null>(null);
  const [customerProfile, setCustomerProfile] = useState<CustomerAnalyticsProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, [customerId]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      if (customerId) {
        // Load specific customer profile
        const response = await fetch(`/api/customer-lifecycle/${customerId}`);
        const profile = await response.json();
        setCustomerProfile(profile);
      } else {
        // Load overview metrics
        const response = await fetch('/api/customer-lifecycle/metrics');
        const metricsData = await response.json();
        setMetrics(metricsData);
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStageColor = (stage: CustomerLifecycleStage): string => {
    const colors = {
      [CustomerLifecycleStage.LEAD]: 'bg-blue-100 text-blue-800',
      [CustomerLifecycleStage.PROSPECT]: 'bg-purple-100 text-purple-800',
      [CustomerLifecycleStage.NEW_CUSTOMER]: 'bg-green-100 text-green-800',
      [CustomerLifecycleStage.ACTIVE_CUSTOMER]: 'bg-emerald-100 text-emerald-800',
      [CustomerLifecycleStage.LOYAL_CUSTOMER]: 'bg-yellow-100 text-yellow-800',
      [CustomerLifecycleStage.AT_RISK]: 'bg-orange-100 text-orange-800',
      [CustomerLifecycleStage.CHURNED]: 'bg-red-100 text-red-800',
      [CustomerLifecycleStage.REACTIVATED]: 'bg-indigo-100 text-indigo-800'
    };
    return colors[stage] || 'bg-gray-100 text-gray-800';
  };

  const getHealthScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (customerId && customerProfile) {
    return (
      <div className="space-y-6">
        {/* Customer Profile Header */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Customer Lifecycle Profile
              </CardTitle>
              <Badge className={getStageColor(customerProfile.lifecycleStage)}>
                {customerProfile.lifecycleStage.replace('_', ' ')}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Health Score */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Health Score</span>
                  <span className={`text-lg font-bold ${getHealthScoreColor(customerProfile.healthScore.overallScore)}`}>
                    {customerProfile.healthScore.overallScore}%
                  </span>
                </div>
                <Progress value={customerProfile.healthScore.overallScore} className="h-2" />
                <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                  <div>Service: {customerProfile.healthScore.serviceFrequency}%</div>
                  <div>Payment: {customerProfile.healthScore.paymentHistory}%</div>
                  <div>Communication: {customerProfile.healthScore.communicationResponsiveness}%</div>
                  <div>Compliance: {customerProfile.healthScore.contractCompliance}%</div>
                </div>
              </div>

              {/* Customer Value */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">Customer Value</span>
                </div>
                <div className="space-y-1">
                  <div className="text-2xl font-bold text-green-600">
                    ${customerProfile.customerLifetimeValue.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">
                    Lifetime Value
                  </div>
                  <div className="text-sm">
                    Total Revenue: ${customerProfile.totalRevenue.toLocaleString()}
                  </div>
                  <div className="text-sm">
                    Avg Job: ${customerProfile.averageJobValue.toLocaleString()}
                  </div>
                </div>
              </div>

              {/* Loyalty & Risk */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium">Loyalty & Risk</span>
                </div>
                <div className="space-y-2">
                  <Badge variant="outline" className="w-full justify-center">
                    {customerProfile.loyaltyTier} Tier
                  </Badge>
                  <div className="flex items-center justify-between text-sm">
                    <span>Churn Risk:</span>
                    <span className={customerProfile.churnRisk > 70 ? 'text-red-600' : 
                                   customerProfile.churnRisk > 40 ? 'text-yellow-600' : 'text-green-600'}>
                      {customerProfile.churnRisk}%
                    </span>
                  </div>
                  {customerProfile.satisfactionScore && (
                    <div className="flex items-center justify-between text-sm">
                      <span>Satisfaction:</span>
                      <span className="text-green-600">
                        {customerProfile.satisfactionScore}/5 ⭐
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Service & Equipment Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Service Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {customerProfile.lastServiceDate && (
                  <div className="flex justify-between text-sm">
                    <span>Last Service:</span>
                    <span>{new Date(customerProfile.lastServiceDate).toLocaleDateString()}</span>
                  </div>
                )}
                {customerProfile.nextScheduledService && (
                  <div className="flex justify-between text-sm">
                    <span>Next Service:</span>
                    <span>{new Date(customerProfile.nextScheduledService).toLocaleDateString()}</span>
                  </div>
                )}
                <div className="flex justify-between text-sm">
                  <span>Equipment Count:</span>
                  <span>{customerProfile.equipmentCount}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Maintenance Compliance:</span>
                  <span className={customerProfile.maintenanceCompliance >= 80 ? 'text-green-600' : 'text-yellow-600'}>
                    {customerProfile.maintenanceCompliance}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Communication Preferences
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  {customerProfile.communicationPreference === 'EMAIL' && <Mail className="h-4 w-4" />}
                  {customerProfile.communicationPreference === 'PHONE' && <Phone className="h-4 w-4" />}
                  {customerProfile.communicationPreference === 'SMS' && <MessageSquare className="h-4 w-4" />}
                  <span className="text-sm">
                    Preferred: {customerProfile.communicationPreference}
                  </span>
                </div>
                {customerProfile.preferredServiceTime && (
                  <div className="text-sm">
                    <span className="font-medium">Preferred Time:</span> {customerProfile.preferredServiceTime}
                  </div>
                )}
                {customerProfile.preferredTechnician && (
                  <div className="text-sm">
                    <span className="font-medium">Preferred Technician:</span> {customerProfile.preferredTechnician}
                  </div>
                )}
                {customerProfile.lastInteractionDate && (
                  <div className="text-sm">
                    <span className="font-medium">Last Contact:</span> {new Date(customerProfile.lastInteractionDate).toLocaleDateString()}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Recommended Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {customerProfile.churnRisk > 70 && (
                <Button variant="outline" className="justify-start">
                  <AlertTriangle className="h-4 w-4 mr-2 text-red-600" />
                  Address Churn Risk
                </Button>
              )}
              {customerProfile.healthScore.overallScore < 60 && (
                <Button variant="outline" className="justify-start">
                  <Heart className="h-4 w-4 mr-2 text-yellow-600" />
                  Improve Health Score
                </Button>
              )}
              {customerProfile.loyaltyTier === 'PLATINUM' && (
                <Button variant="outline" className="justify-start">
                  <Star className="h-4 w-4 mr-2 text-yellow-600" />
                  VIP Treatment
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Overview Dashboard
  return (
    <div className="space-y-6">
      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Customers</p>
                <p className="text-2xl font-bold">{metrics?.totalCustomers || 0}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Health Score</p>
                <p className="text-2xl font-bold">{metrics?.averageHealthScore || 0}%</p>
              </div>
              <Heart className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">At Risk</p>
                <p className="text-2xl font-bold text-red-600">{metrics?.atRiskCustomers || 0}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Loyal Customers</p>
                <p className="text-2xl font-bold text-yellow-600">{metrics?.loyalCustomers || 0}</p>
              </div>
              <Star className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lifecycle Stage Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Lifecycle Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(metrics?.stageDistribution || {}).map(([stage, count]) => (
              <div key={stage} className="text-center">
                <Badge className={getStageColor(stage as CustomerLifecycleStage)}>
                  {stage.replace('_', ' ')}
                </Badge>
                <p className="text-2xl font-bold mt-2">{count}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
