import { Link, useFetcher } from "@remix-run/react";
import { useState, useEffect } from "react";
import { NotificationBadge } from "~/components/molecules/notifications/notification-badge";
import { NotificationItem } from "~/components/molecules/notifications/notification-item";
import type { Notification } from "~/types/shared";

interface NotificationCenterProps {
  notifications: Notification[];
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
  userRole?: string;
}

/**
 * Komponent centrum powiadomień
 * Wyświetla listę powiadomień z możliwością oznaczania jako przeczytane
 * Obsługuje powiadomienia w czasie rzeczywistym
 */
export function NotificationCenter({
  notifications: initialNotifications,
  onMarkAsRead,
  onMarkAllAsRead,
  userRole
}: NotificationCenterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>(initialNotifications);
  const [lastChecked, setLastChecked] = useState<Date>(new Date());
  const fetcher = useFetcher();

  // Filtruj powiadomienia na podstawie roli użytkownika
  useEffect(() => {
    if (userRole && initialNotifications.length > 0) {
      let filteredNotifications = [...initialNotifications];

      // Filtrowanie powiadomień na podstawie roli
      if (userRole === 'TECHNICIAN') {
        // Technicy widzą powiadomienia o zleceniach i urządzeniach
        filteredNotifications = filteredNotifications.filter(n =>
          n.type === 'SERVICE_ORDER_CREATED' ||
          n.type === 'SERVICE_ORDER_UPDATED' ||
          n.type === 'DEVICE_ALERT' ||
          n.type === 'CALENDAR_REMINDER'
        );
      } else if (userRole === 'CUSTOMER') {
        // Klienci widzą tylko powiadomienia dotyczące ich urządzeń i zleceń
        filteredNotifications = filteredNotifications.filter(n =>
          n.type === 'SERVICE_ORDER_UPDATED' ||
          n.type === 'SERVICE_ORDER_COMPLETED' ||
          n.type === 'DEVICE_ALERT'
        );
      }

      setNotifications(filteredNotifications);
    } else {
      setNotifications(initialNotifications);
    }
  }, [initialNotifications, userRole]);

  // Sprawdzaj nowe powiadomienia co 30 sekund
  useEffect(() => {
    const checkNewNotifications = () => {
      fetcher.load(`/api/notifications?since=${lastChecked.toISOString()}`);
      setLastChecked(new Date());
    };

    const intervalId = setInterval(checkNewNotifications, 30000);

    return () => clearInterval(intervalId);
  }, [fetcher, lastChecked]);

  // Obsługa nowych powiadomień
  useEffect(() => {
    if (fetcher.data && typeof fetcher.data === 'object' && 'notifications' in fetcher.data && (fetcher.data as any).notifications) {
      const newNotifications = (fetcher.data as any).notifications;
      if (newNotifications.length > 0) {
        // Dodaj nowe powiadomienia do listy
        setNotifications(prev => [...newNotifications, ...prev]);

        // Pokaż powiadomienie systemowe dla użytkownika
        if (Notification.permission === "granted" && !document.hasFocus()) {
          newNotifications.forEach((notification: Notification) => {
            new window.Notification(notification.title, {
              body: notification.message,
              icon: "/favicon.ico"
            });
          });
        }
      }
    }
  }, [fetcher.data]);

  // Poproś o pozwolenie na powiadomienia push
  useEffect(() => {
    if ("Notification" in window && Notification.permission !== "denied") {
      Notification.requestPermission();
    }
  }, []);

  const unreadCount = notifications.filter(n => n.status === 'unread').length;

  const toggleOpen = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative">
      {/* Przycisk powiadomień */}
      <button
        type="button"
        className="relative text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"
        onClick={toggleOpen}
        aria-label="Powiadomienia"
      >
        <span className="sr-only">Powiadomienia</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
          />
        </svg>

        {unreadCount > 0 && (
          <NotificationBadge count={unreadCount} />
        )}
      </button>

      {/* Panel powiadomień */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 overflow-hidden">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h3 className="font-medium">Powiadomienia</h3>
            {unreadCount > 0 && (
              <button
                type="button"
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                onClick={onMarkAllAsRead}
              >
                Oznacz wszystkie jako przeczytane
              </button>
            )}
          </div>

          <div className="max-h-96 overflow-y-auto p-2">
            {notifications.length > 0 ? (
              notifications.map(notification => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={onMarkAsRead}
                />
              ))
            ) : (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                Brak powiadomień
              </div>
            )}
          </div>

          <div className="p-2 border-t border-gray-200 dark:border-gray-700 text-center">
            <Link
              to="/notifications"
              className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
              onClick={() => setIsOpen(false)}
            >
              Zobacz wszystkie powiadomienia
            </Link>
          </div>

          <div className="p-2 border-t border-gray-200 dark:border-gray-700 text-center">
            <Link
              to="/settings/notifications"
              className="text-sm text-gray-600 dark:text-gray-400 hover:underline"
              onClick={() => setIsOpen(false)}
            >
              Ustawienia powiadomień
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}