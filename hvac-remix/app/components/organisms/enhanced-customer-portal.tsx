/**
 * Enhanced Customer Portal
 * 
 * Comprehensive customer self-service portal with service history,
 * document access, feedback systems, and rating capabilities
 */

import {
  Star,
  Calendar,
  FileText,
  Download,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Clock,
  CheckCircle,
  AlertCircle,
  DollarSign,
  User,
  Phone,
  Mail,
  MapPin,
  Settings,
  Bell,
  CreditCard,
  History,
  Award
} from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Label } from '~/components/ui/label';
import { Progress } from '~/components/ui/progress';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { Textarea } from '~/components/ui/textarea';

interface ServiceHistory {
  id: string;
  title: string;
  description: string;
  status: 'completed' | 'in_progress' | 'scheduled' | 'cancelled';
  date: Date;
  technician: {
    name: string;
    avatar?: string;
    rating: number;
  };
  amount: number;
  duration: number;
  serviceType: string;
  rating?: number;
  feedback?: string;
  documents: Array<{
    id: string;
    name: string;
    type: 'invoice' | 'report' | 'warranty' | 'photo';
    url: string;
    size: string;
  }>;
}

interface CustomerProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  avatar?: string;
  memberSince: Date;
  totalJobs: number;
  totalSpent: number;
  loyaltyPoints: number;
  preferredTechnician?: string;
  serviceReminders: boolean;
  emailNotifications: boolean;
}

interface FeedbackForm {
  serviceId: string;
  rating: number;
  feedback: string;
  wouldRecommend: boolean;
  categories: {
    punctuality: number;
    professionalism: number;
    quality: number;
    communication: number;
  };
}

export function EnhancedCustomerPortal({ customerId }: { customerId: string }) {
  const [profile, setProfile] = useState<CustomerProfile | null>(null);
  const [serviceHistory, setServiceHistory] = useState<ServiceHistory[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [feedbackForm, setFeedbackForm] = useState<Partial<FeedbackForm>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCustomerData();
  }, [customerId]);

  const fetchCustomerData = async () => {
    try {
      const [profileResponse, historyResponse] = await Promise.all([
        fetch(`/api/customers/${customerId}/profile`),
        fetch(`/api/customers/${customerId}/service-history`),
      ]);

      if (profileResponse.ok && historyResponse.ok) {
        const profileData = await profileResponse.json();
        const historyData = await historyResponse.json();
        
        setProfile(profileData);
        setServiceHistory(historyData);
      }
    } catch (error) {
      console.error('Failed to fetch customer data:', error);
    } finally {
      setLoading(false);
    }
  };

  const submitFeedback = async (serviceId: string) => {
    try {
      const response = await fetch(`/api/services/${serviceId}/feedback`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(feedbackForm),
      });

      if (response.ok) {
        // Update service history with new feedback
        setServiceHistory(prev => 
          prev.map(service => 
            service.id === serviceId 
              ? { ...service, rating: feedbackForm.rating, feedback: feedbackForm.feedback }
              : service
          )
        );
        setFeedbackForm({});
      }
    } catch (error) {
      console.error('Failed to submit feedback:', error);
    }
  };

  const downloadDocument = async (documentId: string, filename: string) => {
    try {
      const response = await fetch(`/api/documents/${documentId}/download`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to download document:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-blue-600" />;
      case 'scheduled':
        return <Calendar className="h-5 w-5 text-orange-600" />;
      case 'cancelled':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'scheduled':
        return 'bg-orange-100 text-orange-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStarRating = (rating: number, onRatingChange?: (rating: number) => void) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-5 w-5 cursor-pointer ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
            onClick={() => onRatingChange?.(star)}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading your portal...</span>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold">Unable to load customer data</h3>
        <p className="text-gray-600">Please try again later or contact support.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-lg">
        <div className="flex items-center space-x-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={profile.avatar} />
            <AvatarFallback>{profile.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <h1 className="text-2xl font-bold">Welcome back, {profile.name}!</h1>
            <p className="opacity-90">Customer since {profile.memberSince.toLocaleDateString()}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
          <div className="text-center">
            <div className="text-2xl font-bold">{profile.totalJobs}</div>
            <div className="text-sm opacity-90">Total Services</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">${profile.totalSpent.toLocaleString()}</div>
            <div className="text-sm opacity-90">Total Spent</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{profile.loyaltyPoints}</div>
            <div className="text-sm opacity-90">Loyalty Points</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              <Award className="h-6 w-6 inline" />
            </div>
            <div className="text-sm opacity-90">VIP Member</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="history">Service History</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="feedback">Feedback</TabsTrigger>
          <TabsTrigger value="profile">Profile</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Next Service</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Dec 15</div>
                <p className="text-xs text-muted-foreground">
                  Annual HVAC Maintenance
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Last Service</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Nov 8</div>
                <p className="text-xs text-muted-foreground">
                  Heating System Repair
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Service Score</CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.8</div>
                <p className="text-xs text-muted-foreground">
                  Average rating
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Services */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Services</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {serviceHistory.slice(0, 3).map((service) => (
                  <div key={service.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      {getStatusIcon(service.status)}
                      <div>
                        <div className="font-medium">{service.title}</div>
                        <div className="text-sm text-gray-600">
                          {service.date.toLocaleDateString()} • {service.technician.name}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">${service.amount}</div>
                      <Badge className={getStatusColor(service.status)}>
                        {service.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          {serviceHistory.map((service) => (
            <Card key={service.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center space-x-2">
                      {getStatusIcon(service.status)}
                      <span>{service.title}</span>
                    </CardTitle>
                    <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                  </div>
                  <Badge className={getStatusColor(service.status)}>
                    {service.status.replace('_', ' ')}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <Label className="text-sm font-medium">Date & Time</Label>
                    <p className="text-sm">{service.date.toLocaleString()}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Technician</Label>
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={service.technician.avatar} />
                        <AvatarFallback>{service.technician.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{service.technician.name}</span>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Amount</Label>
                    <p className="text-sm font-bold">${service.amount}</p>
                  </div>
                </div>

                {service.documents.length > 0 && (
                  <div className="mb-4">
                    <Label className="text-sm font-medium mb-2 block">Documents</Label>
                    <div className="flex flex-wrap gap-2">
                      {service.documents.map((doc) => (
                        <Button
                          key={doc.id}
                          variant="outline"
                          size="sm"
                          onClick={() => downloadDocument(doc.id, doc.name)}
                        >
                          <FileText className="h-4 w-4 mr-2" />
                          {doc.name}
                          <Download className="h-4 w-4 ml-2" />
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                {service.status === 'completed' && (
                  <div className="border-t pt-4">
                    <Label className="text-sm font-medium mb-2 block">Service Rating</Label>
                    {service.rating ? (
                      <div className="flex items-center space-x-2">
                        {renderStarRating(service.rating)}
                        <span className="text-sm text-gray-600">({service.rating}/5)</span>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {renderStarRating(
                          feedbackForm.rating || 0,
                          (rating) => setFeedbackForm(prev => ({ ...prev, rating, serviceId: service.id }))
                        )}
                        <Textarea
                          placeholder="Share your feedback about this service..."
                          value={feedbackForm.feedback || ''}
                          onChange={(e) => setFeedbackForm(prev => ({ ...prev, feedback: e.target.value }))}
                          className="mt-2"
                        />
                        <Button
                          size="sm"
                          onClick={() => submitFeedback(service.id)}
                          disabled={!feedbackForm.rating}
                        >
                          Submit Feedback
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <CardTitle>Your Documents</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {serviceHistory.flatMap(service => service.documents).map((doc) => (
                  <div key={doc.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between mb-2">
                      <FileText className="h-8 w-8 text-blue-600" />
                      <Badge variant="outline">{doc.type}</Badge>
                    </div>
                    <h4 className="font-medium mb-1">{doc.name}</h4>
                    <p className="text-sm text-gray-600 mb-3">{doc.size}</p>
                    <Button
                      size="sm"
                      className="w-full"
                      onClick={() => downloadDocument(doc.id, doc.name)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="feedback">
          <Card>
            <CardHeader>
              <CardTitle>Service Feedback</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {serviceHistory
                  .filter(service => service.status === 'completed' && service.rating)
                  .map((service) => (
                    <div key={service.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium">{service.title}</h4>
                        <span className="text-sm text-gray-600">
                          {service.date.toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 mb-2">
                        {renderStarRating(service.rating || 0)}
                        <span className="text-sm text-gray-600">({service.rating}/5)</span>
                      </div>
                      {service.feedback && (
                        <p className="text-sm text-gray-700">{service.feedback}</p>
                      )}
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profile">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-gray-600" />
                  <span>{profile.name}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-gray-600" />
                  <span>{profile.email}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-gray-600" />
                  <span>{profile.phone}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-gray-600" />
                  <span>{profile.address}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Bell className="h-5 w-5 text-gray-600" />
                    <span>Service Reminders</span>
                  </div>
                  <Badge variant={profile.serviceReminders ? "default" : "secondary"}>
                    {profile.serviceReminders ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-gray-600" />
                    <span>Email Notifications</span>
                  </div>
                  <Badge variant={profile.emailNotifications ? "default" : "secondary"}>
                    {profile.emailNotifications ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
                {profile.preferredTechnician && (
                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5 text-gray-600" />
                    <span>Preferred Technician: {profile.preferredTechnician}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default EnhancedCustomerPortal;
