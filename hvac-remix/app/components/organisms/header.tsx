import type { User } from "@prisma/client";
import { Link } from "@remix-run/react";
import { useState } from "react";
import { ModeToggle } from "~/components/mode-toggle";
import { Navigation } from "~/components/organisms/navigation";
import { NotificationCenter } from "~/components/organisms/notification-center";
import { Button } from "~/components/ui/button";
import type { UserRole, Notification } from "~/types/shared";

interface HeaderProps {
  user: User | null;
  userRole: UserRole;
  notifications?: Notification[];
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
}

export function Header({
  user,
  userRole,
  notifications = [],
  onMarkAsRead,
  onMarkAllAsRead
}: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="sticky top-0 z-40 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 py-3">
        <div className="flex h-14 items-center justify-between">
          {/* Logo and mobile menu button */}
          <div className="flex items-center gap-2">
            <button
              type="button"
              className="md:hidden mr-2 text-muted-foreground hover:text-foreground transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-expanded={isMenuOpen}
            >
              <span className="sr-only">Open menu</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
            <Link to="/" className="flex items-center gap-2">
              <span className="text-xl font-heading font-bold text-gradient">HVAC CRM</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex md:items-center md:gap-6">
            <Navigation userRole={userRole} className="flex gap-6" />
          </div>

          {/* User menu and theme toggle */}
          <div className="flex items-center gap-4">
            {/* Notifications center */}
            {user && (
              <NotificationCenter
                notifications={notifications}
                onMarkAsRead={onMarkAsRead}
                onMarkAllAsRead={onMarkAllAsRead}
              />
            )}

            {/* Theme toggle */}
            <ModeToggle />

            {/* User menu */}
            {user ? (
              <div className="relative group">
                <button
                  type="button"
                  className="flex items-center gap-2 text-sm font-medium transition-colors hover:text-accent"
                  aria-expanded="false"
                >
                  <span className="sr-only">Open user menu</span>
                  <div className="h-9 w-9 rounded-full bg-primary/10 ring-1 ring-primary/20 flex items-center justify-center text-primary">
                    {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                  </div>
                </button>
                <div className="hidden group-hover:block absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-xl bg-card py-2 shadow-lg ring-1 ring-border focus:outline-none transition-all duration-200 animate-in fade-in-50 slide-in-from-top-5">
                  <div className="px-4 py-3 text-sm border-b border-border">
                    <div className="font-medium">{user.name || user.email}</div>
                    <div className="text-xs text-muted-foreground">{userRole}</div>
                  </div>
                  <Link
                    to="/settings"
                    className="block px-4 py-2 text-sm hover:bg-accent/10 hover:text-accent transition-colors"
                  >
                    Ustawienia
                  </Link>
                  <Link
                    to="/logout"
                    className="block px-4 py-2 text-sm hover:bg-accent/10 hover:text-accent transition-colors"
                  >
                    Wyloguj
                  </Link>
                </div>
              </div>
            ) : (
              <Button asChild variant="default" size="sm" className="shadow-md">
                <Link to="/login">Zaloguj</Link>
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden border-t border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="px-4 py-3 space-y-3">
            <Navigation userRole={userRole} className="flex flex-col space-y-3" />
          </div>
        </div>
      )}
    </header>
  );
}