/**
 * Custom Report Builder Interface
 *
 * Flexible drag-and-drop report generation system for HVAC CRM
 * Features field selection, filtering, grouping, and real-time preview
 */

import {
  Plus,
  Trash2,
  Play,
  Save,
  Download,
  Filter,
  Group,
  SortAsc,
  SortDesc,
  Eye,
  Settings,
  Database,
  Calendar,
  User,
  DollarSign,
  FileText,
  BarChart3
} from 'lucide-react';
import React, { useState, useCallback } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Checkbox } from '~/components/ui/checkbox';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Separator } from '~/components/ui/separator';

interface ReportField {
  id: string;
  name: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'currency';
  table: string;
  icon: React.ReactNode;
  aggregatable?: boolean;
}

interface ReportFilter {
  id: string;
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'between' | 'in';
  value: any;
  label: string;
}

interface ReportGrouping {
  field: string;
  order: 'asc' | 'desc';
}

interface ReportSort {
  field: string;
  direction: 'asc' | 'desc';
}

interface ReportConfig {
  name: string;
  description: string;
  selectedFields: ReportField[];
  filters: ReportFilter[];
  grouping: ReportGrouping[];
  sorting: ReportSort[];
  aggregations: Record<string, 'sum' | 'avg' | 'count' | 'min' | 'max'>;
}

const AVAILABLE_FIELDS: ReportField[] = [
  // Customer fields
  { id: 'customer_name', name: 'customer.name', label: 'Customer Name', type: 'string', table: 'customers', icon: <User className="h-4 w-4" /> },
  { id: 'customer_email', name: 'customer.email', label: 'Customer Email', type: 'string', table: 'customers', icon: <User className="h-4 w-4" /> },
  { id: 'customer_phone', name: 'customer.phone', label: 'Customer Phone', type: 'string', table: 'customers', icon: <User className="h-4 w-4" /> },
  { id: 'customer_address', name: 'customer.address', label: 'Customer Address', type: 'string', table: 'customers', icon: <User className="h-4 w-4" /> },
  { id: 'customer_created', name: 'customer.created_at', label: 'Customer Since', type: 'date', table: 'customers', icon: <Calendar className="h-4 w-4" /> },

  // Job fields
  { id: 'job_title', name: 'job.title', label: 'Job Title', type: 'string', table: 'jobs', icon: <FileText className="h-4 w-4" /> },
  { id: 'job_status', name: 'job.status', label: 'Job Status', type: 'string', table: 'jobs', icon: <FileText className="h-4 w-4" /> },
  { id: 'job_priority', name: 'job.priority', label: 'Job Priority', type: 'string', table: 'jobs', icon: <FileText className="h-4 w-4" /> },
  { id: 'job_created', name: 'job.created_at', label: 'Job Created', type: 'date', table: 'jobs', icon: <Calendar className="h-4 w-4" /> },
  { id: 'job_completed', name: 'job.completed_at', label: 'Job Completed', type: 'date', table: 'jobs', icon: <Calendar className="h-4 w-4" /> },
  { id: 'job_duration', name: 'job.duration_hours', label: 'Duration (Hours)', type: 'number', table: 'jobs', icon: <BarChart3 className="h-4 w-4" />, aggregatable: true },

  // Financial fields
  { id: 'job_amount', name: 'job.total_amount', label: 'Job Amount', type: 'currency', table: 'jobs', icon: <DollarSign className="h-4 w-4" />, aggregatable: true },
  { id: 'job_cost', name: 'job.cost', label: 'Job Cost', type: 'currency', table: 'jobs', icon: <DollarSign className="h-4 w-4" />, aggregatable: true },
  { id: 'job_profit', name: 'job.profit', label: 'Job Profit', type: 'currency', table: 'jobs', icon: <DollarSign className="h-4 w-4" />, aggregatable: true },

  // Service fields
  { id: 'service_type', name: 'service.type', label: 'Service Type', type: 'string', table: 'services', icon: <Settings className="h-4 w-4" /> },
  { id: 'technician_name', name: 'technician.name', label: 'Technician', type: 'string', table: 'technicians', icon: <User className="h-4 w-4" /> },
];

const FILTER_OPERATORS = [
  { value: 'equals', label: 'Equals' },
  { value: 'contains', label: 'Contains' },
  { value: 'greater_than', label: 'Greater Than' },
  { value: 'less_than', label: 'Less Than' },
  { value: 'between', label: 'Between' },
  { value: 'in', label: 'In List' },
];

const AGGREGATION_TYPES = [
  { value: 'sum', label: 'Sum' },
  { value: 'avg', label: 'Average' },
  { value: 'count', label: 'Count' },
  { value: 'min', label: 'Minimum' },
  { value: 'max', label: 'Maximum' },
];

// Draggable Field Component
function DraggableField({ field }: { field: ReportField }) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'field',
    item: field,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={drag}
      className={`flex items-center space-x-2 p-2 border rounded cursor-move hover:bg-gray-50 ${
        isDragging ? 'opacity-50' : ''
      }`}
    >
      {field.icon}
      <span className="text-sm">{field.label}</span>
      <Badge variant="outline" className="text-xs">
        {field.table}
      </Badge>
    </div>
  );
}

// Drop Zone for Selected Fields
function SelectedFieldsDropZone({
  selectedFields,
  onFieldAdd,
  onFieldRemove
}: {
  selectedFields: ReportField[];
  onFieldAdd: (field: ReportField) => void;
  onFieldRemove: (fieldId: string) => void;
}) {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'field',
    drop: (item: ReportField) => {
      if (!selectedFields.find(f => f.id === item.id)) {
        onFieldAdd(item);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }));

  return (
    <div
      ref={drop}
      className={`min-h-32 p-4 border-2 border-dashed rounded-lg ${
        isOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
      }`}
    >
      {selectedFields.length === 0 ? (
        <div className="text-center text-gray-500">
          <Database className="h-8 w-8 mx-auto mb-2" />
          <p>Drag fields here to build your report</p>
        </div>
      ) : (
        <div className="space-y-2">
          {selectedFields.map((field) => (
            <div key={field.id} className="flex items-center justify-between p-2 bg-white border rounded">
              <div className="flex items-center space-x-2">
                {field.icon}
                <span className="text-sm font-medium">{field.label}</span>
                <Badge variant="outline" className="text-xs">
                  {field.type}
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onFieldRemove(field.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export function CustomReportBuilder() {
  const [reportConfig, setReportConfig] = useState<ReportConfig>({
    name: '',
    description: '',
    selectedFields: [],
    filters: [],
    grouping: [],
    sorting: [],
    aggregations: {},
  });

  const [activeTab, setActiveTab] = useState<'fields' | 'filters' | 'grouping' | 'preview' | 'schedule'>('fields');
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleFieldAdd = useCallback((field: ReportField) => {
    setReportConfig(prev => ({
      ...prev,
      selectedFields: [...prev.selectedFields, field],
    }));
  }, []);

  const handleFieldRemove = useCallback((fieldId: string) => {
    setReportConfig(prev => ({
      ...prev,
      selectedFields: prev.selectedFields.filter(f => f.id !== fieldId),
    }));
  }, []);

  const handleFilterAdd = () => {
    const newFilter: ReportFilter = {
      id: `filter_${Date.now()}`,
      field: '',
      operator: 'equals',
      value: '',
      label: '',
    };
    setReportConfig(prev => ({
      ...prev,
      filters: [...prev.filters, newFilter],
    }));
  };

  const handleFilterUpdate = (filterId: string, updates: Partial<ReportFilter>) => {
    setReportConfig(prev => ({
      ...prev,
      filters: prev.filters.map(f => f.id === filterId ? { ...f, ...updates } : f),
    }));
  };

  const handleFilterRemove = (filterId: string) => {
    setReportConfig(prev => ({
      ...prev,
      filters: prev.filters.filter(f => f.id !== filterId),
    }));
  };

  const generatePreview = async () => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/reports/preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(reportConfig),
      });

      if (response.ok) {
        const data = await response.json();
        setPreviewData(data.rows || []);
      }
    } catch (error) {
      console.error('Failed to generate preview:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const saveReport = async () => {
    try {
      const response = await fetch('/api/reports/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(reportConfig),
      });

      if (response.ok) {
        // Show success message
        console.log('Report saved successfully');
      }
    } catch (error) {
      console.error('Failed to save report:', error);
    }
  };

  const exportReport = async (format: 'pdf' | 'excel' | 'csv') => {
    try {
      const response = await fetch('/api/reports/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...reportConfig, format }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${reportConfig.name || 'report'}.${format === 'excel' ? 'xlsx' : format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to export report:', error);
    }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold">Custom Report Builder</h2>
            <p className="text-muted-foreground">
              Create custom reports with drag-and-drop functionality
            </p>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={generatePreview} disabled={isGenerating}>
              <Eye className="h-4 w-4 mr-2" />
              {isGenerating ? 'Generating...' : 'Preview'}
            </Button>
            <Button variant="outline" onClick={saveReport}>
              <Save className="h-4 w-4 mr-2" />
              Save Report
            </Button>
            <Button variant="outline" onClick={() => setActiveTab('schedule')}>
              <Calendar className="h-4 w-4 mr-2" />
              Schedule
            </Button>
            <Select onValueChange={(format) => exportReport(format as any)}>
              <SelectTrigger className="w-32">
                <Download className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Export" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pdf">PDF</SelectItem>
                <SelectItem value="excel">Excel</SelectItem>
                <SelectItem value="csv">CSV</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Report Configuration */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Report Details */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Report Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="report-name">Report Name</Label>
                <Input
                  id="report-name"
                  value={reportConfig.name}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter report name"
                />
              </div>
              <div>
                <Label htmlFor="report-description">Description</Label>
                <Input
                  id="report-description"
                  value={reportConfig.description}
                  onChange={(e) => setReportConfig(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter description"
                />
              </div>

              <Separator />

              {/* Tab Navigation */}
              <div className="space-y-2">
                <Button
                  variant={activeTab === 'fields' ? 'default' : 'ghost'}
                  className="w-full justify-start"
                  onClick={() => setActiveTab('fields')}
                >
                  <Database className="h-4 w-4 mr-2" />
                  Fields ({reportConfig.selectedFields.length})
                </Button>
                <Button
                  variant={activeTab === 'filters' ? 'default' : 'ghost'}
                  className="w-full justify-start"
                  onClick={() => setActiveTab('filters')}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Filters ({reportConfig.filters.length})
                </Button>
                <Button
                  variant={activeTab === 'grouping' ? 'default' : 'ghost'}
                  className="w-full justify-start"
                  onClick={() => setActiveTab('grouping')}
                >
                  <Group className="h-4 w-4 mr-2" />
                  Grouping
                </Button>
                <Button
                  variant={activeTab === 'preview' ? 'default' : 'ghost'}
                  className="w-full justify-start"
                  onClick={() => setActiveTab('preview')}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                <Button
                  variant={activeTab === 'schedule' ? 'default' : 'ghost'}
                  className="w-full justify-start"
                  onClick={() => setActiveTab('schedule')}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            {activeTab === 'fields' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Available Fields */}
                <Card>
                  <CardHeader>
                    <CardTitle>Available Fields</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-96">
                      <div className="space-y-2">
                        {AVAILABLE_FIELDS.map((field) => (
                          <DraggableField key={field.id} field={field} />
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>

                {/* Selected Fields */}
                <Card>
                  <CardHeader>
                    <CardTitle>Selected Fields</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <SelectedFieldsDropZone
                      selectedFields={reportConfig.selectedFields}
                      onFieldAdd={handleFieldAdd}
                      onFieldRemove={handleFieldRemove}
                    />
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'filters' && (
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Report Filters</CardTitle>
                  <Button onClick={handleFilterAdd}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Filter
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {reportConfig.filters.map((filter) => (
                      <div key={filter.id} className="flex items-center space-x-2 p-4 border rounded">
                        <Select
                          value={filter.field}
                          onValueChange={(value) => handleFilterUpdate(filter.id, { field: value })}
                        >
                          <SelectTrigger className="w-48">
                            <SelectValue placeholder="Select field" />
                          </SelectTrigger>
                          <SelectContent>
                            {reportConfig.selectedFields.map((field) => (
                              <SelectItem key={field.id} value={field.id}>
                                {field.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <Select
                          value={filter.operator}
                          onValueChange={(value) => handleFilterUpdate(filter.id, { operator: value as any })}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {FILTER_OPERATORS.map((op) => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <Input
                          value={filter.value}
                          onChange={(e) => handleFilterUpdate(filter.id, { value: e.target.value })}
                          placeholder="Filter value"
                          className="flex-1"
                        />

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleFilterRemove(filter.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}

                    {reportConfig.filters.length === 0 && (
                      <div className="text-center text-gray-500 py-8">
                        <Filter className="h-8 w-8 mx-auto mb-2" />
                        <p>No filters added yet</p>
                        <p className="text-sm">Click "Add Filter" to create your first filter</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {activeTab === 'preview' && (
              <Card>
                <CardHeader>
                  <CardTitle>Report Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  {previewData.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse border">
                        <thead>
                          <tr>
                            {reportConfig.selectedFields.map((field) => (
                              <th key={field.id} className="border p-2 text-left bg-gray-50">
                                {field.label}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {previewData.slice(0, 10).map((row, index) => (
                            <tr key={index}>
                              {reportConfig.selectedFields.map((field) => (
                                <td key={field.id} className="border p-2">
                                  {row[field.name] || '-'}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {previewData.length > 10 && (
                        <p className="text-sm text-gray-500 mt-2">
                          Showing first 10 rows of {previewData.length} total rows
                        </p>
                      )}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 py-8">
                      <BarChart3 className="h-8 w-8 mx-auto mb-2" />
                      <p>No preview data available</p>
                      <p className="text-sm">Add fields and click "Preview" to see your report</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {activeTab === 'schedule' && (
              <Card>
                <CardHeader>
                  <CardTitle>Schedule Report</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Schedule Configuration */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="frequency">Frequency</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select frequency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                            <SelectItem value="quarterly">Quarterly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="time">Time</Label>
                        <Input
                          type="time"
                          placeholder="09:00"
                        />
                      </div>
                    </div>

                    {/* Recipients */}
                    <div>
                      <Label className="text-base font-medium">Email Recipients</Label>
                      <div className="space-y-2 mt-2">
                        <div className="flex space-x-2">
                          <Input placeholder="Email address" className="flex-1" />
                          <Input placeholder="Name" className="flex-1" />
                          <Select>
                            <SelectTrigger className="w-32">
                              <SelectValue placeholder="Role" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="admin">Admin</SelectItem>
                              <SelectItem value="manager">Manager</SelectItem>
                              <SelectItem value="customer">Customer</SelectItem>
                            </SelectContent>
                          </Select>
                          <Button size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Format Selection */}
                    <div>
                      <Label className="text-base font-medium">Report Format</Label>
                      <div className="flex space-x-4 mt-2">
                        <label className="flex items-center space-x-2">
                          <input type="radio" name="format" value="pdf" defaultChecked />
                          <span>PDF</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="radio" name="format" value="excel" />
                          <span>Excel</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input type="radio" name="format" value="csv" />
                          <span>CSV</span>
                        </label>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2 pt-4 border-t">
                      <Button>
                        <Calendar className="h-4 w-4 mr-2" />
                        Create Schedule
                      </Button>
                      <Button variant="outline">
                        <Play className="h-4 w-4 mr-2" />
                        Test Run
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </DndProvider>
  );
}

export default CustomReportBuilder;
