/**
 * Advanced Data Visualization Components
 * 
 * Interactive chart library with multiple visualization types
 * Complements the analytics dashboard with specialized charts
 */

import {
  BarChart3,
  <PERSON>Chart,
  PieChart,
  TrendingUp,
  Target,
  Zap,
  Activity,
  Download,
  Settings,
  RefreshCw
} from 'lucide-react';
import React, { useState, useMemo } from 'react';
import {
  ComposedChart,
  Line,
  Bar,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Treemap,
  Cell,
  FunnelChart,
  Funnel,
  LabelList,
  ScatterChart,
  Scatter,
  ZAxis,
  Sankey,
  Rectangle
} from 'recharts';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';

interface ChartData {
  name: string;
  value: number;
  category?: string;
  date?: string;
  [key: string]: any;
}

interface VisualizationProps {
  data: ChartData[];
  title: string;
  type: 'composed' | 'radar' | 'treemap' | 'funnel' | 'scatter' | 'sankey';
  config?: {
    xAxis?: string;
    yAxis?: string[];
    colors?: string[];
    showLegend?: boolean;
    showGrid?: boolean;
  };
}

const CHART_COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00', 
  '#0088fe', '#00c49f', '#ffbb28', '#ff8042', '#8dd1e1'
];

// Sample data for different chart types
const SAMPLE_DATA = {
  performance: [
    { name: 'Jan', revenue: 4000, jobs: 24, satisfaction: 85, efficiency: 78 },
    { name: 'Feb', revenue: 3000, jobs: 18, satisfaction: 88, efficiency: 82 },
    { name: 'Mar', revenue: 5000, jobs: 32, satisfaction: 92, efficiency: 85 },
    { name: 'Apr', revenue: 4500, jobs: 28, satisfaction: 89, efficiency: 80 },
    { name: 'May', revenue: 6000, jobs: 38, satisfaction: 94, efficiency: 88 },
    { name: 'Jun', revenue: 5500, jobs: 35, satisfaction: 91, efficiency: 86 },
  ],
  technician: [
    { subject: 'Punctuality', A: 120, B: 110, fullMark: 150 },
    { subject: 'Quality', A: 98, B: 130, fullMark: 150 },
    { subject: 'Communication', A: 86, B: 130, fullMark: 150 },
    { subject: 'Efficiency', A: 99, B: 100, fullMark: 150 },
    { subject: 'Customer Service', A: 85, B: 90, fullMark: 150 },
    { subject: 'Technical Skills', A: 65, B: 85, fullMark: 150 },
  ],
  serviceTypes: [
    { name: 'HVAC Repair', value: 3500, children: [
      { name: 'Heating', value: 2000 },
      { name: 'Cooling', value: 1500 }
    ]},
    { name: 'Maintenance', value: 2500, children: [
      { name: 'Preventive', value: 1800 },
      { name: 'Emergency', value: 700 }
    ]},
    { name: 'Installation', value: 4000, children: [
      { name: 'New Systems', value: 3000 },
      { name: 'Upgrades', value: 1000 }
    ]},
  ],
  salesFunnel: [
    { name: 'Leads', value: 1000, fill: '#8884d8' },
    { name: 'Qualified', value: 800, fill: '#82ca9d' },
    { name: 'Proposals', value: 600, fill: '#ffc658' },
    { name: 'Negotiations', value: 400, fill: '#ff7300' },
    { name: 'Closed', value: 200, fill: '#00ff00' },
  ],
  efficiency: [
    { x: 100, y: 200, z: 200, name: 'Team A' },
    { x: 120, y: 100, z: 260, name: 'Team B' },
    { x: 170, y: 300, z: 400, name: 'Team C' },
    { x: 140, y: 250, z: 280, name: 'Team D' },
    { x: 150, y: 400, z: 500, name: 'Team E' },
    { x: 110, y: 280, z: 200, name: 'Team F' },
  ],
};

function ComposedVisualization({ data, title }: { data: ChartData[], title: string }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <BarChart3 className="h-5 w-5 mr-2" />
          {title}
        </CardTitle>
        <Badge variant="outline">Composed Chart</Badge>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <ComposedChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip />
            <Legend />
            <Bar yAxisId="left" dataKey="revenue" fill="#8884d8" name="Revenue ($)" />
            <Bar yAxisId="left" dataKey="jobs" fill="#82ca9d" name="Jobs Completed" />
            <Line yAxisId="right" type="monotone" dataKey="satisfaction" stroke="#ff7300" name="Satisfaction %" />
            <Line yAxisId="right" type="monotone" dataKey="efficiency" stroke="#ffc658" name="Efficiency %" />
          </ComposedChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

function RadarVisualization({ data, title }: { data: any[], title: string }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <Target className="h-5 w-5 mr-2" />
          {title}
        </CardTitle>
        <Badge variant="outline">Radar Chart</Badge>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <RadarChart data={data}>
            <PolarGrid />
            <PolarAngleAxis dataKey="subject" />
            <PolarRadiusAxis angle={90} domain={[0, 150]} />
            <Radar name="Technician A" dataKey="A" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
            <Radar name="Technician B" dataKey="B" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
            <Legend />
          </RadarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

function TreemapVisualization({ data, title }: { data: any[], title: string }) {
  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <Activity className="h-5 w-5 mr-2" />
          {title}
        </CardTitle>
        <Badge variant="outline">Treemap</Badge>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <Treemap
            data={data}
            dataKey="value"
            ratio={4/3}
            stroke="#fff"
            fill="#8884d8"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Treemap>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

function FunnelVisualization({ data, title }: { data: any[], title: string }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <TrendingUp className="h-5 w-5 mr-2" />
          {title}
        </CardTitle>
        <Badge variant="outline">Funnel Chart</Badge>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <FunnelChart>
            <Tooltip />
            <Funnel
              dataKey="value"
              data={data}
              isAnimationActive
            >
              <LabelList position="center" fill="#fff" stroke="none" />
            </Funnel>
          </FunnelChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

function ScatterVisualization({ data, title }: { data: any[], title: string }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <Zap className="h-5 w-5 mr-2" />
          {title}
        </CardTitle>
        <Badge variant="outline">Scatter Plot</Badge>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <ScatterChart data={data}>
            <CartesianGrid />
            <XAxis type="number" dataKey="x" name="Hours Worked" />
            <YAxis type="number" dataKey="y" name="Jobs Completed" />
            <ZAxis type="number" dataKey="z" range={[60, 400]} name="Revenue" />
            <Tooltip cursor={{ strokeDasharray: '3 3' }} />
            <Scatter name="Teams" data={data} fill="#8884d8" />
          </ScatterChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

export function AdvancedDataVisualizations() {
  const [selectedChart, setSelectedChart] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate data refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const handleExport = (chartType: string) => {
    // Export chart as image or PDF
    console.log(`Exporting ${chartType} chart`);
  };

  const visibleCharts = useMemo(() => {
    if (selectedChart === 'all') {
      return ['composed', 'radar', 'treemap', 'funnel', 'scatter'];
    }
    return [selectedChart];
  }, [selectedChart]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Advanced Data Visualizations</h2>
          <p className="text-muted-foreground">
            Interactive charts and specialized visualizations for deeper insights
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={selectedChart} onValueChange={setSelectedChart}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Charts</SelectItem>
              <SelectItem value="composed">Performance Overview</SelectItem>
              <SelectItem value="radar">Technician Comparison</SelectItem>
              <SelectItem value="treemap">Service Distribution</SelectItem>
              <SelectItem value="funnel">Sales Funnel</SelectItem>
              <SelectItem value="scatter">Team Efficiency</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button variant="outline" onClick={() => handleExport(selectedChart)}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Chart Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {visibleCharts.includes('composed') && (
          <ComposedVisualization 
            data={SAMPLE_DATA.performance} 
            title="Performance Overview"
          />
        )}
        
        {visibleCharts.includes('radar') && (
          <RadarVisualization 
            data={SAMPLE_DATA.technician} 
            title="Technician Performance Comparison"
          />
        )}
        
        {visibleCharts.includes('treemap') && (
          <TreemapVisualization 
            data={SAMPLE_DATA.serviceTypes} 
            title="Service Type Distribution"
          />
        )}
        
        {visibleCharts.includes('funnel') && (
          <FunnelVisualization 
            data={SAMPLE_DATA.salesFunnel} 
            title="Sales Conversion Funnel"
          />
        )}
        
        {visibleCharts.includes('scatter') && (
          <ScatterVisualization 
            data={SAMPLE_DATA.efficiency} 
            title="Team Efficiency Analysis"
          />
        )}
      </div>

      {/* Chart Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Chart Insights & Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <h4 className="font-semibold text-green-600 mb-2">Performance Trends</h4>
              <p className="text-sm text-gray-600">
                Revenue and job completion show strong correlation. May revenue peak suggests 
                optimal capacity utilization.
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h4 className="font-semibold text-blue-600 mb-2">Technician Analysis</h4>
              <p className="text-sm text-gray-600">
                Technician B shows superior performance across most metrics. Consider 
                knowledge sharing sessions.
              </p>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h4 className="font-semibold text-orange-600 mb-2">Service Distribution</h4>
              <p className="text-sm text-gray-600">
                Installation services generate highest revenue. Consider expanding 
                installation team capacity.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default AdvancedDataVisualizations;
