import { useNavigation, useRevalidator } from "@remix-run/react";
import { Refresh<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, Check<PERSON>ircle, Clock } from "lucide-react";
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";


interface TelemetryData {
  timestamp: string;
  temperature?: number;
  pressure?: number;
  vibration?: number;
  powerConsumption?: number;
  efficiency?: number;
}

interface DeviceTelemetryDashboardProps {
  deviceId: string;
  initialData: {
    telemetry: TelemetryData[];
    healthScore: number;
    status: 'optimal' | 'warning' | 'critical';
    nextMaintenance: string | null;
    lastUpdated: string;
  };
}

export function DeviceTelemetryDashboard({ initialData }: DeviceTelemetryDashboardProps) {
  const { telemetry, healthScore, status, nextMaintenance, lastUpdated } = initialData;
  const { state } = useNavigation();
  const { revalidate } = useRevalidator();

  const isLoading = state === 'loading' || state === 'submitting';

  const getStatusBadge = () => {
    switch (status) {
      case 'optimal':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" /> Optimal
          </span>
        );
      case 'warning':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <AlertTriangle className="h-3 w-3 mr-1" /> Warning
          </span>
        );
      case 'critical':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <AlertTriangle className="h-3 w-3 mr-1" /> Critical
          </span>
        );
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Device Telemetry</h2>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            Last updated: {formatDate(lastUpdated)}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => revalidate()}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Health Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-baseline">
              <div className="text-3xl font-bold">{healthScore}</div>
              <span className="ml-2 text-sm text-gray-500">/ 100</span>
            </div>
            <div className="mt-2">
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className={`h-2.5 rounded-full ${
                    healthScore >= 70 ? 'bg-green-500' :
                    healthScore >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${healthScore}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              {getStatusBadge()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Next Maintenance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-gray-400 mr-2" />
              <span>{nextMaintenance ? formatDate(nextMaintenance) : 'Not scheduled'}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Telemetry Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            {isLoading ? (
              <div className="h-full flex items-center justify-center">
                <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={telemetry}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                  />
                  <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                  <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                  <Tooltip
                    labelFormatter={(value) => `Time: ${new Date(value).toLocaleString()}`}
                  />
                  <Legend />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="temperature"
                    name="Temperature (°C)"
                    stroke="#8884d8"
                    activeDot={{ r: 8 }}
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="pressure"
                    name="Pressure (kPa)"
                    stroke="#82ca9d"
                  />
                  <Line
                    yAxisId="left"
                    type="monotone"
                    dataKey="vibration"
                    name="Vibration (mm/s²)"
                    stroke="#ffc658"
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
