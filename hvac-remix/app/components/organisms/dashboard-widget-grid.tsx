import { X, GripV<PERSON><PERSON>, Maximize, Minimize } from "lucide-react";
import { useState, useEffect, useRef } from 'react';
import { DashboardWidget } from "~/components/organisms/dashboard-widget";
import { But<PERSON> } from "~/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";

interface Widget {
  id: string;
  position: number;
  size: 'small' | 'medium' | 'large';
}

interface DashboardWidgetGridProps {
  widgets: Widget[];
  isCustomizing: boolean;
  onLayoutChange: (widgets: Widget[]) => void;
}

export function DashboardWidgetGrid({
  widgets,
  isCustomizing,
  onLayoutChange
}: DashboardWidgetGridProps) {
  const [orderedWidgets, setOrderedWidgets] = useState<Widget[]>([]);
  const [draggedWidget, setDraggedWidget] = useState<Widget | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const gridRef = useRef<HTMLDivElement>(null);

  // Sort widgets by position on initial load and when widgets change
  useEffect(() => {
    const sorted = [...widgets].sort((a, b) => a.position - b.position);
    setOrderedWidgets(sorted);
  }, [widgets]);

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, widget: Widget) => {
    if (!isCustomizing) return;

    setDraggedWidget(widget);
    e.dataTransfer.setData('text/plain', widget.id);

    // Set a custom drag image (optional)
    const dragImage = document.createElement('div');
    dragImage.textContent = widget.id.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    dragImage.className = 'bg-primary text-primary-foreground p-2 rounded shadow-lg';
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 0, 0);

    // Clean up the drag image element after a short delay
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    if (!isCustomizing || !draggedWidget) return;

    setDragOverIndex(index);
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (!isCustomizing || !draggedWidget) return;

    const draggedId = e.dataTransfer.getData('text/plain');
    const draggedIndex = orderedWidgets.findIndex(w => w.id === draggedId);

    if (draggedIndex === dropIndex) return;

    // Reorder the widgets
    const reordered = Array.from(orderedWidgets);
    const [removed] = reordered.splice(draggedIndex, 1);
    reordered.splice(dropIndex, 0, removed);

    // Update positions
    const updated = reordered.map((widget, index) => ({
      ...widget,
      position: index
    }));

    setOrderedWidgets(updated);
    onLayoutChange(updated);
    setDraggedWidget(null);
    setDragOverIndex(null);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggedWidget(null);
    setDragOverIndex(null);
  };

  // Handle widget removal
  const handleRemoveWidget = (widgetId: string) => {
    const filtered = orderedWidgets.filter(w => w.id !== widgetId);
    const updated = filtered.map((widget, index) => ({
      ...widget,
      position: index
    }));

    setOrderedWidgets(updated);
    onLayoutChange(updated);
  };

  // Handle widget size change
  const handleSizeChange = (widgetId: string) => {
    const updated = orderedWidgets.map(widget => {
      if (widget.id === widgetId) {
        const sizes: Record<string, 'small' | 'medium' | 'large'> = {
          'small': 'medium',
          'medium': 'large',
          'large': 'small'
        };
        return { ...widget, size: sizes[widget.size] };
      }
      return widget;
    });

    setOrderedWidgets(updated);
    onLayoutChange(updated);
  };

  // Get column span based on widget size
  const getColSpan = (size: string) => {
    switch (size) {
      case 'small': return 'col-span-1';
      case 'medium': return 'col-span-1 md:col-span-2';
      case 'large': return 'col-span-1 md:col-span-3';
      default: return 'col-span-1';
    }
  };

  return (
    <div
      ref={gridRef}
      className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4"
    >
      {orderedWidgets.map((widget, index) => (
        <div
          key={widget.id}
          draggable={isCustomizing}
          onDragStart={(e) => handleDragStart(e, widget)}
          onDragOver={(e) => handleDragOver(e, index)}
          onDrop={(e) => handleDrop(e, index)}
          onDragEnd={handleDragEnd}
          className={`${getColSpan(widget.size)} ${dragOverIndex === index ? 'opacity-50' : ''} ${draggedWidget?.id === widget.id ? 'opacity-50' : ''}`}
        >
          <Card
            className={`h-full widget card-hover ${isCustomizing ? 'border-2 border-dashed' : ''}
              ${dragOverIndex === index ? 'border-primary shadow-md' : ''}
              ${draggedWidget?.id === widget.id ? 'opacity-50 scale-[0.98]' : 'animate-scale'}
              transition-all duration-300`}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <span className="inline-block w-2 h-2 rounded-full bg-primary animate-pulse"></span>
                <span className="text-gradient-primary">
                  {widget.id.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                </span>
              </CardTitle>
              {isCustomizing && (
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 btn-icon-pulse"
                    onClick={() => handleSizeChange(widget.id)}
                  >
                    {widget.size === 'large' ? (
                      <Minimize className="h-4 w-4" />
                    ) : (
                      <Maximize className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 text-destructive hover:bg-destructive/10"
                    onClick={() => handleRemoveWidget(widget.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                  <div className="cursor-grab p-1 rounded-md hover:bg-muted">
                    <GripVertical className="h-4 w-4" />
                  </div>
                </div>
              )}
            </CardHeader>
            <CardContent className="relative">
              <div className={`${isCustomizing ? 'opacity-50' : 'opacity-100'} transition-opacity duration-300`}>
                <DashboardWidget
                  widgetId={widget.id}
                  size={widget.size}
                />
              </div>
              {isCustomizing && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-sm text-muted-foreground animate-pulse">
                    Drag to reposition
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  );
}
