import React, { useState } from 'react';
import { cn } from '~/lib/utils';
import { DashboardHeader } from '~/components/molecules/dashboard-header';
import { QuickActions } from '~/components/molecules/quick-actions';
import { MobileGestures, useIsMobile, useIsTouchDevice } from '~/components/molecules/mobile-gestures';
import { MobileLoading } from '~/components/molecules/mobile-loading';
import { ToastProvider } from '~/components/molecules/mobile-toast';

interface MobileDashboardLayoutProps {
  children: React.ReactNode;
  userName?: string;
  avatarUrl?: string;
  isLoading?: boolean;
  className?: string;
}

export function MobileDashboardLayout({
  children,
  userName,
  avatarUrl,
  isLoading = false,
  className
}: MobileDashboardLayoutProps) {
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const isMobile = useIsMobile();
  const isTouchDevice = useIsTouchDevice();

  const handlePullToRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    await new Promise(resolve => setTimeout(resolve, 1500));
    setRefreshing(false);
    
    // In real app, this would trigger data refresh
    console.log('Dashboard refreshed');
  };

  const handleSwipeLeft = () => {
    if (isMobile) {
      // Navigate to next section or open sidebar
      console.log('Swipe left detected');
    }
  };

  const handleSwipeRight = () => {
    if (isMobile) {
      // Navigate to previous section or close sidebar
      console.log('Swipe right detected');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <MobileLoading 
          variant="skeleton" 
          message="Ładowanie dashboardu..." 
          fullScreen 
        />
      </div>
    );
  }

  return (
    <ToastProvider>
      <MobileGestures
        onPullToRefresh={handlePullToRefresh}
        onSwipeLeft={handleSwipeLeft}
        onSwipeRight={handleSwipeRight}
        enablePullToRefresh={isMobile}
        className={cn('min-h-screen bg-background', className)}
      >
        {/* Mobile-optimized container */}
        <div className="flex flex-col min-h-screen">
          {/* Header with mobile optimizations */}
          <header className="sticky top-0 z-40 bg-background/95 backdrop-blur-sm border-b border-border/40">
            <div className="container mx-auto px-4 py-3">
              <DashboardHeader
                userName={userName}
                avatarUrl={avatarUrl}
                isCustomizing={isCustomizing}
                onCustomizeToggle={() => setIsCustomizing(!isCustomizing)}
              />
            </div>
          </header>

          {/* Main content area */}
          <main className="flex-1 container mx-auto px-4 py-4 pb-20 md:pb-4">
            {/* Quick Actions - Mobile optimized */}
            <div className="mb-6">
              <QuickActions 
                size={isMobile ? 'lg' : 'md'}
                layout="grid"
                className="animate-fade-in"
              />
            </div>

            {/* Dashboard content */}
            <div className="space-y-6">
              {refreshing && (
                <div className="text-center py-4">
                  <MobileLoading 
                    variant="dots" 
                    message="Odświeżanie danych..." 
                    size="sm"
                  />
                </div>
              )}
              
              {children}
            </div>
          </main>

          {/* Mobile-specific floating action button */}
          {isMobile && (
            <div className="fixed bottom-20 right-4 z-30">
              <button
                className="w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 active:scale-95 touch-manipulation"
                aria-label="Szybka akcja"
              >
                <svg className="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
            </div>
          )}

          {/* Mobile bottom padding for navigation */}
          <div className="h-16 md:hidden" />
        </div>

        {/* Mobile-specific overlays */}
        {isMobile && isCustomizing && (
          <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="bg-background rounded-lg p-6 m-4 max-w-sm w-full shadow-xl">
              <h3 className="text-lg font-semibold mb-4">Dostosuj dashboard</h3>
              <p className="text-muted-foreground mb-6">
                Przeciągnij i upuść komponenty, aby zmienić ich układ.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setIsCustomizing(false)}
                  className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg touch-manipulation"
                >
                  Gotowe
                </button>
              </div>
            </div>
          </div>
        )}
      </MobileGestures>
    </ToastProvider>
  );
}

// Hook for dashboard state management
export function useMobileDashboard() {
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const isMobile = useIsMobile();
  const isTouchDevice = useIsTouchDevice();

  const refresh = async () => {
    setRefreshing(true);
    try {
      // Implement actual refresh logic here
      await new Promise(resolve => setTimeout(resolve, 1000));
    } finally {
      setRefreshing(false);
    }
  };

  return {
    isCustomizing,
    setIsCustomizing,
    refreshing,
    refresh,
    isMobile,
    isTouchDevice
  };
}

// Mobile-specific dashboard metrics component
export function MobileDashboardMetrics({ metrics }: { metrics: any[] }) {
  const isMobile = useIsMobile();

  return (
    <div className={cn(
      'grid gap-4',
      isMobile ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-4'
    )}>
      {metrics.map((metric, index) => (
        <div
          key={metric.id}
          className="bg-card rounded-lg p-4 border shadow-sm hover:shadow-md transition-all duration-200 animate-fade-in"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-2xl">{metric.icon}</span>
            <span className={cn(
              'text-xs px-2 py-1 rounded-full',
              metric.trend === 'up' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
            )}>
              {metric.change}
            </span>
          </div>
          <div className="space-y-1">
            <p className="text-2xl font-bold">{metric.value}</p>
            <p className="text-sm text-muted-foreground">{metric.label}</p>
          </div>
        </div>
      ))}
    </div>
  );
}
