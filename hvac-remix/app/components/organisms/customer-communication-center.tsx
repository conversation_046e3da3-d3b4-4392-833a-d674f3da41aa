import { useState } from "react"
import { CommunicationHistory } from "~/components/molecules/communication-history"
import { MessageThread } from "~/components/molecules/message-thread"
import { Button } from "~/components/ui/button"
import { Card } from "~/components/ui/card"
import { Input } from "~/components/ui/input"
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "~/components/ui/tabs"
import { Textarea } from "~/components/ui/textarea"

interface Message {
  id: string
  customerId: string
  userId?: string
  direction: 'INBOUND' | 'OUTBOUND'
  channel: 'EMAIL' | 'SMS' | 'PHONE' | 'IN_PERSON' | 'PORTAL'
  content: string
  subject?: string
  timestamp: string
  read: boolean
  attachments?: Array<{
    id: string
    name: string
    url: string
    type: string
  }>
}

interface CustomerCommunicationCenterProps {
  customerId: string
  customerName: string
  messages: Message[]
  onSendMessage: (message: {
    customerId: string
    channel: Message['channel']
    subject?: string
    content: string
  }) => Promise<void>
  onMarkAsRead: (messageId: string) => Promise<void>
}

export function CustomerCommunicationCenter({
  customerId,
  customerName,
  messages,
  onSendMessage,
  onMarkAsRead
}: CustomerCommunicationCenterProps) {
  const [activeTab, setActiveTab] = useState("messages")
  const [messageChannel, setMessageChannel] = useState<Message['channel']>("EMAIL")
  const [messageSubject, setMessageSubject] = useState("")
  const [messageContent, setMessageContent] = useState("")
  const [isSending, setIsSending] = useState(false)

  // Group messages by thread (based on subject for emails, or by date for other channels)
  const getMessageThreads = () => {
    const threads: Record<string, Message[]> = {}

    messages.forEach(message => {
      let threadKey = ""

      if (message.channel === "EMAIL" && message.subject) {
        // For emails, use the subject as the thread key
        threadKey = message.subject.toLowerCase().trim()
      } else {
        // For other channels, group by date (YYYY-MM-DD)
        const date = new Date(message.timestamp)
        threadKey = `${message.channel}_${date.toISOString().split('T')[0]}`
      }

      if (!threads[threadKey]) {
        threads[threadKey] = []
      }

      threads[threadKey].push(message)
    })

    // Sort messages within each thread by timestamp
    Object.keys(threads).forEach(key => {
      threads[key].sort((a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      )
    })

    return threads
  }

  const messageThreads = getMessageThreads()

  // Get communication history grouped by channel and month
  const getCommunicationHistory = () => {
    const history: Record<string, {
      count: number
      lastDate: string
    }> = {}

    messages.forEach(message => {
      const date = new Date(message.timestamp)
      const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`
      const channelMonthKey = `${message.channel}_${monthKey}`

      if (!history[channelMonthKey]) {
        history[channelMonthKey] = {
          count: 0,
          lastDate: message.timestamp
        }
      }

      history[channelMonthKey].count += 1

      // Update last date if this message is newer
      if (new Date(message.timestamp) > new Date(history[channelMonthKey].lastDate)) {
        history[channelMonthKey].lastDate = message.timestamp
      }
    })

    return Object.entries(history).map(([key, data]) => {
      const [channel, month] = key.split('_')
      return {
        channel: channel as Message['channel'],
        month,
        count: data.count,
        lastDate: data.lastDate
      }
    }).sort((a, b) => b.month.localeCompare(a.month))
  }

  const communicationHistory = getCommunicationHistory()

  // Handle sending a new message
  const handleSendMessage = async () => {
    if (!messageContent.trim()) return

    setIsSending(true)

    try {
      await onSendMessage({
        customerId,
        channel: messageChannel,
        subject: messageSubject,
        content: messageContent
      })

      // Reset form after successful send
      setMessageSubject("")
      setMessageContent("")
    } catch (error) {
      console.error("Error sending message:", error)
      // Handle error (could show an error message to the user)
    } finally {
      setIsSending(false)
    }
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Left sidebar - Communication history */}
      <div className="md:col-span-1">
        <Card className="p-4">
          <h2 className="text-lg font-semibold mb-3">Komunikacja z klientem</h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            {customerName}
          </p>

          <CommunicationHistory
            history={communicationHistory}
            onSelectMonth={() => {
              setActiveTab("messages")
              // Additional filtering logic could be added here
            }}
          />
        </Card>
      </div>

      {/* Main content area */}
      <div className="md:col-span-2">
        <Card className="p-4">
          <Tabs defaultValue="messages" onValueChange={setActiveTab} value={activeTab}>
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="messages">Wiadomości</TabsTrigger>
              <TabsTrigger value="new">Nowa wiadomość</TabsTrigger>
            </TabsList>

            {/* Messages tab */}
            <TabsContent value="messages">
              <div className="space-y-6">
                {Object.entries(messageThreads).length > 0 ? (
                  Object.entries(messageThreads).map(([threadKey, messages]) => (
                    <MessageThread
                      key={threadKey}
                      messages={messages}
                      onMarkAsRead={onMarkAsRead}
                    />
                  ))
                ) : (
                  <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                    Brak wiadomości dla tego klienta
                  </div>
                )}
              </div>
            </TabsContent>

            {/* New message tab */}
            <TabsContent value="new">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="communication-channel">
                    Kanał komunikacji
                  </label>
                  <div className="flex space-x-2" role="radiogroup" aria-labelledby="communication-channel">
                    <Button
                      type="button"
                      variant={messageChannel === "EMAIL" ? "default" : "outline"}
                      onClick={() => setMessageChannel("EMAIL")}
                    >
                      Email
                    </Button>
                    <Button
                      type="button"
                      variant={messageChannel === "SMS" ? "default" : "outline"}
                      onClick={() => setMessageChannel("SMS")}
                    >
                      SMS
                    </Button>
                    <Button
                      type="button"
                      variant={messageChannel === "PORTAL" ? "default" : "outline"}
                      onClick={() => setMessageChannel("PORTAL")}
                    >
                      Portal
                    </Button>
                  </div>
                </div>

                {messageChannel === "EMAIL" && (
                  <div>
                    <label className="block text-sm font-medium mb-1" htmlFor="message-subject">
                      Temat
                    </label>
                    <Input
                      id="message-subject"
                      value={messageSubject}
                      onChange={(e) => setMessageSubject(e.target.value)}
                      placeholder="Wprowadź temat wiadomości"
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="message-content">
                    Treść wiadomości
                  </label>
                  <Textarea
                    id="message-content"
                    value={messageContent}
                    onChange={(e) => setMessageContent(e.target.value)}
                    placeholder="Wprowadź treść wiadomości"
                    rows={6}
                  />
                </div>

                <div className="flex justify-end">
                  <Button
                    type="button"
                    onClick={handleSendMessage}
                    disabled={!messageContent.trim() || isSending}
                  >
                    {isSending ? "Wysyłanie..." : "Wyślij wiadomość"}
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </Card>
      </div>
    </div>
  )
}
