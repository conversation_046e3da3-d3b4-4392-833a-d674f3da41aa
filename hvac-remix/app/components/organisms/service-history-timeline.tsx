import { Link } from "@remix-run/react"
import { TimelineEvent } from "~/components/molecules/timeline-event"
import { Badge } from "~/components/ui/badge"
import { But<PERSON> } from "~/components/ui/button"
import { <PERSON>, CardHeader, CardTitle, CardContent } from "~/components/ui/card"


interface ServiceEvent {
  id: string
  date: string
  type: 'INSTALLATION' | 'MAINTENANCE' | 'REPAIR' | 'INSPECTION' | 'OTHER'
  title: string
  description: string
  technician?: string
  components?: string[]
  cost?: number
  status?: 'COMPLETED' | 'SCHEDULED' | 'CANCELLED'
  serviceOrderId?: string
}

interface ServiceHistoryTimelineProps {
  events: ServiceEvent[]
  deviceId?: string
  customerId?: string
  maxItems?: number
  showViewAllLink?: boolean
}

export function ServiceHistoryTimeline({
  events,
  deviceId,
  customerId,
  maxItems,
  showViewAllLink = true
}: ServiceHistoryTimelineProps) {
  // Sort events by date (newest first)
  const sortedEvents = [...events].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  )

  // Limit the number of events if maxItems is provided
  const displayedEvents = maxItems ? sortedEvents.slice(0, maxItems) : sortedEvents

  // Get icon for event type
  const getEventIcon = (type: ServiceEvent['type']) => {
    switch (type) {
      case 'INSTALLATION':
        return (
          <div className="h-9 w-9 rounded-full bg-success/10 border border-success/20 flex items-center justify-center text-success shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M2 20a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8l-7 5V8l-7 5V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z"></path>
              <path d="M17 18h1"></path>
              <path d="M12 18h1"></path>
              <path d="M7 18h1"></path>
            </svg>
          </div>
        )
      case 'MAINTENANCE':
        return (
          <div className="h-9 w-9 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center text-primary shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
            </svg>
          </div>
        )
      case 'REPAIR':
        return (
          <div className="h-9 w-9 rounded-full bg-destructive/10 border border-destructive/20 flex items-center justify-center text-destructive shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M10.29 3.86 1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
              <line x1="12" y1="9" x2="12" y2="13"></line>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
          </div>
        )
      case 'INSPECTION':
        return (
          <div className="h-9 w-9 rounded-full bg-warning/10 border border-warning/20 flex items-center justify-center text-warning shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
          </div>
        )
      default:
        return (
          <div className="h-9 w-9 rounded-full bg-secondary/10 border border-secondary/20 flex items-center justify-center text-secondary shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
          </div>
        )
    }
  }

  // Get badge for event status
  const getStatusBadge = (status?: ServiceEvent['status']) => {
    if (!status) return null

    switch (status) {
      case 'COMPLETED':
        return <Badge variant="outline" className="bg-success/10 text-success border-success/20 hover:bg-success/20">Zakończone</Badge>
      case 'SCHEDULED':
        return <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 hover:bg-primary/20">Zaplanowane</Badge>
      case 'CANCELLED':
        return <Badge variant="outline" className="bg-destructive/10 text-destructive border-destructive/20 hover:bg-destructive/20">Anulowane</Badge>
      default:
        return null
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString()
  }

  // Determine view all link
  const getViewAllLink = () => {
    if (deviceId) {
      return `/devices/${deviceId}/service-history`
    } else if (customerId) {
      return `/customers/${customerId}/service-history`
    } else {
      return '/service-orders'
    }
  }

  return (
    <Card className="transition-all duration-300 hover:shadow-md">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl font-heading">Historia serwisowa</CardTitle>
        {showViewAllLink && events.length > (maxItems || 0) && (
          <Button
            asChild
            variant="ghost"
            size="sm"
            className="ml-auto"
          >
            <Link to={getViewAllLink()}>
              <span className="mr-1">Zobacz wszystkie</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </Link>
          </Button>
        )}
      </CardHeader>

      <CardContent>
        <div className="space-y-6">
          {displayedEvents.length > 0 ? (
            displayedEvents.map((event, index) => (
              <TimelineEvent
                key={event.id}
                icon={getEventIcon(event.type)}
                title={event.title}
                description={event.description}
                date={formatDate(event.date)}
                badge={getStatusBadge(event.status)}
                isLast={index === displayedEvents.length - 1}
                metadata={{
                  ...(event.technician && { "Technik": event.technician }),
                  ...(event.components?.length && { "Komponenty": event.components.join(", ") }),
                  ...(event.cost !== undefined && { "Koszt": `${event.cost} zł` })
                }}
                link={event.serviceOrderId ? `/service-orders/${event.serviceOrderId}` : undefined}
                linkText={event.serviceOrderId ? "Zobacz zlecenie" : undefined}
              />
            ))
          ) : (
            <div className="text-center py-6 text-muted-foreground flex flex-col items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-2 text-muted-foreground/50" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
              Brak historii serwisowej
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
