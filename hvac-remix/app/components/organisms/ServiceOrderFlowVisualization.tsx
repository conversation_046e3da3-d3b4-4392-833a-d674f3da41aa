import gsap from "gsap";
import { useEffect, useRef } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "~/components/ui/card";

interface ServiceOrder {
  id: string;
  title: string;
  status: string;
  priority: string;
  createdAt: string;
  updatedAt: string;
  customer: {
    id: string;
    name: string;
  };
}

interface StatusCount {
  status: string;
  _count: {
    id: number;
  };
}

interface PriorityCount {
  priority: string;
  _count: {
    id: number;
  };
}

interface ServiceOrderFlowVisualizationProps {
  serviceOrders: ServiceOrder[];
  statusCounts: StatusCount[];
  priorityCounts: PriorityCount[];
  averageCompletionTime: number;
  completedOrdersCount: number;
}

export function ServiceOrderFlowVisualization({
  serviceOrders,
  statusCounts,
  priorityCounts,
  averageCompletionTime,
  completedOrdersCount,
}: ServiceOrderFlowVisualizationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const statusChartRef = useRef<HTMLDivElement>(null);
  const priorityChartRef = useRef<HTMLDivElement>(null);
  const flowRef = useRef<HTMLDivElement>(null);

  // Format average completion time
  const formatTime = (ms: number) => {
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));

    return `${days}d ${hours}h ${minutes}m`;
  };

  // Status colors
  const statusColors: Record<string, string> = {
    PENDING: "#f59e0b",
    IN_PROGRESS: "#3b82f6",
    COMPLETED: "#10b981",
    CANCELLED: "#ef4444",
    ON_HOLD: "#6b7280",
  };

  // Priority colors
  const priorityColors: Record<string, string> = {
    LOW: "#10b981",
    MEDIUM: "#f59e0b",
    HIGH: "#ef4444",
    URGENT: "#7f1d1d",
  };

  useEffect(() => {
    if (!containerRef.current) return;

    // Create status chart
    if (statusChartRef.current) {
      // Clear previous chart
      statusChartRef.current.innerHTML = "";

      const maxCount = Math.max(...statusCounts.map(s => s._count.id));

      // Create a tooltip element
      const tooltip = document.createElement("div");
      tooltip.className = "absolute bg-popover text-popover-foreground p-2 rounded shadow-lg text-sm z-10 pointer-events-none opacity-0 transform -translate-y-full transition-opacity duration-200";
      tooltip.style.left = "-1000px"; // Position off-screen initially
      statusChartRef.current.appendChild(tooltip);

      statusCounts.forEach((statusCount, index) => {
        const barContainer = document.createElement("div");
        barContainer.className = "flex items-center mb-4 relative";

        const label = document.createElement("div");
        label.className = "w-24 text-sm font-medium";
        label.textContent = statusCount.status;

        const barWrapper = document.createElement("div");
        barWrapper.className = "flex-1 bg-muted rounded-full h-8 overflow-hidden";

        const bar = document.createElement("div");
        bar.className = "h-8 rounded-full flex items-center justify-end px-3 text-white text-sm transition-all duration-300";
        bar.style.width = "0%";
        bar.style.backgroundColor = statusColors[statusCount.status] || "#6b7280";
        bar.textContent = statusCount._count.id.toString();

        // Add hover effects
        const handleMouseEnter = (e: MouseEvent) => {
          tooltip.textContent = `${statusCount.status}: ${statusCount._count.id} orders`;
          tooltip.style.opacity = "1";
          tooltip.style.left = `${e.pageX}px`;
          tooltip.style.top = `${e.pageY - 10}px`;

          gsap.to(bar, {
            scale: 1.02,
            brightness: 1.1,
            duration: 0.3,
            ease: "power2.out",
          });
        };

        const handleMouseMove = (e: MouseEvent) => {
          tooltip.style.left = `${e.pageX}px`;
          tooltip.style.top = `${e.pageY - 10}px`;
        };

        const handleMouseLeave = () => {
          tooltip.style.opacity = "0";

          gsap.to(bar, {
            scale: 1,
            brightness: 1,
            duration: 0.3,
            ease: "power2.out",
          });
        };

        barContainer.addEventListener("mouseenter", handleMouseEnter as EventListener);
        barContainer.addEventListener("mousemove", handleMouseMove as EventListener);
        barContainer.addEventListener("mouseleave", handleMouseLeave as EventListener);

        barWrapper.appendChild(bar);
        barContainer.appendChild(label);
        barContainer.appendChild(barWrapper);
        statusChartRef.current?.appendChild(barContainer);

        // Animate the bar with improved animation
        gsap.to(bar, {
          width: `${(statusCount._count.id / maxCount) * 100}%`,
          duration: 1.2,
          delay: index * 0.15,
          ease: "elastic.out(1, 0.75)",
        });
      });
    }

    // Create priority chart
    if (priorityChartRef.current) {
      // Clear previous chart
      priorityChartRef.current.innerHTML = "";

      const total = priorityCounts.reduce((sum, p) => sum + p._count.id, 0);
      const chartSize = Math.min(priorityChartRef.current.clientWidth, 200);
      const centerX = chartSize / 2;
      const centerY = chartSize / 2;
      const radius = chartSize / 2 - 10;

      // Create tooltip for pie chart
      const tooltip = document.createElement("div");
      tooltip.className = "absolute bg-popover text-popover-foreground p-2 rounded shadow-lg text-sm z-10 pointer-events-none opacity-0 transition-opacity duration-200";
      tooltip.style.left = "-1000px"; // Position off-screen initially
      priorityChartRef.current.appendChild(tooltip);

      const container = document.createElement("div");
      container.className = "relative flex flex-col items-center";

      const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svg.setAttribute("width", chartSize.toString());
      svg.setAttribute("height", chartSize.toString());
      svg.setAttribute("viewBox", `0 0 ${chartSize} ${chartSize}`);
      svg.style.cursor = "pointer";

      let startAngle = 0;
      const legend = document.createElement("div");
      legend.className = "mt-6 grid grid-cols-2 gap-3 w-full";

      // Create center circle for enhanced look
      const centerCircle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
      centerCircle.setAttribute("cx", centerX.toString());
      centerCircle.setAttribute("cy", centerY.toString());
      centerCircle.setAttribute("r", (radius * 0.5).toString());
      centerCircle.setAttribute("fill", "#f8f9fa");
      centerCircle.setAttribute("opacity", "0");
      svg.appendChild(centerCircle);

      // Animate center circle
      gsap.to(centerCircle, {
        opacity: 0.7,
        duration: 0.8,
        delay: 0.5,
      });

      const paths: SVGPathElement[] = [];

      priorityCounts.forEach((priorityCount, index) => {
        const percentage = priorityCount._count.id / total;
        const endAngle = startAngle + percentage * 360;

        // Calculate the SVG arc path
        const startRad = (startAngle - 90) * Math.PI / 180;
        const endRad = (endAngle - 90) * Math.PI / 180;

        const x1 = centerX + radius * Math.cos(startRad);
        const y1 = centerY + radius * Math.sin(startRad);
        const x2 = centerX + radius * Math.cos(endRad);
        const y2 = centerY + radius * Math.sin(endRad);

        const largeArcFlag = percentage > 0.5 ? 1 : 0;

        const pathData = [
          `M ${centerX} ${centerY}`,
          `L ${x1} ${y1}`,
          `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
          "Z",
        ].join(" ");

        const path = document.createElementNS("http://www.w3.org/2000/svg", "path");
        path.setAttribute("d", pathData);
        path.setAttribute("fill", priorityColors[priorityCount.priority] || "#6b7280");
        path.setAttribute("stroke", "white");
        path.setAttribute("stroke-width", "1");
        path.setAttribute("data-priority", priorityCount.priority);
        path.setAttribute("data-count", priorityCount._count.id.toString());
        path.setAttribute("data-percentage", `${Math.round(percentage * 100)}%`);

        // Segment hover effects
        path.addEventListener("mouseenter", (e: MouseEvent) => {
          // Show tooltip
          tooltip.textContent = `${priorityCount.priority}: ${priorityCount._count.id} (${Math.round(percentage * 100)}%)`;
          tooltip.style.opacity = "1";
          tooltip.style.left = `${e.pageX}px`;
          tooltip.style.top = `${e.pageY - 10}px`;

          // Segment pop out effect
          gsap.to(path, {
            transform: `translate(${Math.cos((startAngle + endAngle) / 2 * Math.PI / 180) * 5}px, ${Math.sin((startAngle + endAngle) / 2 * Math.PI / 180) * 5}px)`,
            duration: 0.3,
            ease: "power2.out",
          });
        });

        path.addEventListener("mousemove", (e: MouseEvent) => {
          tooltip.style.left = `${e.pageX}px`;
          tooltip.style.top = `${e.pageY - 10}px`;
        });

        path.addEventListener("mouseleave", () => {
          tooltip.style.opacity = "0";

          gsap.to(path, {
            transform: "translate(0, 0)",
            duration: 0.3,
            ease: "power2.out",
          });
        });

        // Start with opacity 0
        path.style.opacity = "0";
        svg.appendChild(path);
        paths.push(path);

        // Enhanced animation with staggered reveal
        gsap.to(path, {
          opacity: 1,
          duration: 0.7,
          delay: index * 0.15,
          ease: "power2.out",
        });

        // Add legend item with improved styling
        const legendItem = document.createElement("div");
        legendItem.className = "flex items-center text-sm p-2 rounded hover:bg-muted/50 transition-colors duration-200 cursor-pointer";

        const colorBox = document.createElement("div");
        colorBox.className = "w-4 h-4 mr-2 rounded-sm";
        colorBox.style.backgroundColor = priorityColors[priorityCount.priority] || "#6b7280";

        const label = document.createElement("span");
        label.textContent = `${priorityCount.priority}: ${priorityCount._count.id} (${Math.round(percentage * 100)}%)`;

        // Legend item interaction
        legendItem.addEventListener("mouseenter", () => {
          // Highlight corresponding pie segment
          gsap.to(path, {
            opacity: 1,
            transform: `translate(${Math.cos((startAngle + endAngle) / 2 * Math.PI / 180) * 5}px, ${Math.sin((startAngle + endAngle) / 2 * Math.PI / 180) * 5}px)`,
            duration: 0.3,
          });

          // Dim other segments
          paths.forEach(p => {
            if (p !== path) {
              gsap.to(p, { opacity: 0.5, duration: 0.3 });
            }
          });
        });

        legendItem.addEventListener("mouseleave", () => {
          // Reset all segments
          paths.forEach(p => {
            gsap.to(p, {
              opacity: 1,
              transform: "translate(0, 0)",
              duration: 0.3
            });
          });
        });

        legendItem.appendChild(colorBox);
        legendItem.appendChild(label);
        legend.appendChild(legendItem);

        startAngle = endAngle;
      });

      container.appendChild(svg);
      container.appendChild(legend);
      priorityChartRef.current.appendChild(container);
      priorityChartRef.current.appendChild(tooltip);
    }

    // Create service order flow visualization
    if (flowRef.current && serviceOrders.length > 0) {
      // Clear previous visualization
      flowRef.current.innerHTML = "";

      // Create tooltip for order cards
      const tooltip = document.createElement("div");
      tooltip.className = "absolute bg-popover text-popover-foreground p-3 rounded shadow-lg text-sm z-10 pointer-events-none opacity-0 transition-opacity duration-200 max-w-xs";
      tooltip.style.left = "-1000px"; // Position off-screen initially

      const flowContainer = document.createElement("div");
      flowContainer.className = "relative mt-4 p-4";

      // Create status lanes
      const statuses = ["PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED", "ON_HOLD"];
      const lanes = document.createElement("div");
      lanes.className = "grid grid-cols-1 md:grid-cols-5 gap-4 mb-6";

      statuses.forEach((status, idx) => {
        const lane = document.createElement("div");
        lane.className = "bg-muted/50 p-3 rounded-md text-center font-medium relative overflow-hidden";

        // Add colored indicator
        const indicator = document.createElement("div");
        indicator.className = "absolute left-0 top-0 h-full w-1";
        indicator.style.backgroundColor = statusColors[status] || "#6b7280";
        lane.appendChild(indicator);

        // Add status text with count
        const statusText = document.createElement("span");
        statusText.textContent = status;

        const statusCount = statusCounts.find(s => s.status === status)?._count.id || 0;
        const countBadge = document.createElement("span");
        countBadge.className = "ml-2 px-2 py-0.5 bg-background rounded-full text-xs font-normal";
        countBadge.textContent = statusCount.toString();

        lane.appendChild(statusText);
        lane.appendChild(countBadge);
        lanes.appendChild(lane);

        // Animate lane appearance
        gsap.fromTo(
          lane,
          { opacity: 0, x: -20 },
          { opacity: 1, x: 0, duration: 0.5, delay: idx * 0.1, ease: "power2.out" }
        );
      });

      flowContainer.appendChild(lanes);

      // Create order cards
      const orderContainer = document.createElement("div");
      orderContainer.className = "grid grid-cols-1 md:grid-cols-5 gap-4";

      const statusColumns: Record<string, HTMLDivElement> = {};

      statuses.forEach(status => {
        const column = document.createElement("div");
        column.className = "space-y-3 min-h-[200px]";

        // Add drop area styling for drag-and-drop future enhancement
        column.style.transition = "background-color 0.2s";
        column.setAttribute("data-status", status);

        statusColumns[status] = column;
        orderContainer.appendChild(column);
      });

      // Sort service orders by priority for better visual hierarchy
      const sortedOrders = [...serviceOrders].sort((a, b) => {
        const priorityValues = { URGENT: 0, HIGH: 1, MEDIUM: 2, LOW: 3 };
        return (priorityValues[a.priority as keyof typeof priorityValues] || 4) -
               (priorityValues[b.priority as keyof typeof priorityValues] || 4);
      });

      sortedOrders.forEach((order, index) => {
        const card = document.createElement("div");
        card.className = "bg-card p-3 rounded-md shadow-sm border text-sm transition-all duration-300 cursor-pointer hover:shadow-md";
        card.setAttribute("data-order-id", order.id);

        // Add subtle left border with priority color
        card.style.borderLeft = `3px solid ${priorityColors[order.priority] || '#6b7280'}`;

        card.innerHTML = `
          <div class="font-medium truncate">${order.title}</div>
          <div class="text-xs text-muted-foreground mt-1">${order.customer.name}</div>
          <div class="flex justify-between items-center mt-2">
            <span class="text-xs px-1.5 py-0.5 rounded-full" style="background-color: ${priorityColors[order.priority] || '#6b7280'}20; color: ${priorityColors[order.priority] || '#6b7280'}">
              ${order.priority}
            </span>
            <span class="text-xs">${new Date(order.createdAt).toLocaleDateString()}</span>
          </div>
        `;

        // Add detailed tooltip on hover
        card.addEventListener("mouseenter", (e: MouseEvent) => {
          const daysAgo = Math.round((Date.now() - new Date(order.createdAt).getTime()) / (1000 * 60 * 60 * 24));

          tooltip.innerHTML = `
            <div class="font-medium mb-1">${order.title}</div>
            <div class="mb-2">${order.customer.name}</div>
            <div class="grid grid-cols-2 gap-x-3 gap-y-1 text-xs">
              <div class="text-muted-foreground">Status:</div>
              <div>${order.status}</div>
              <div class="text-muted-foreground">Priority:</div>
              <div style="color: ${priorityColors[order.priority] || '#6b7280'}">${order.priority}</div>
              <div class="text-muted-foreground">Created:</div>
              <div>${new Date(order.createdAt).toLocaleDateString()} (${daysAgo} days ago)</div>
              <div class="text-muted-foreground">Updated:</div>
              <div>${new Date(order.updatedAt).toLocaleDateString()}</div>
            </div>
          `;
          tooltip.style.opacity = "1";
          tooltip.style.left = `${e.pageX + 10}px`;
          tooltip.style.top = `${e.pageY - 20}px`;

          // Highlight effect
          gsap.to(card, {
            scale: 1.03,
            boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
            duration: 0.2,
          });
        });

        card.addEventListener("mousemove", (e: MouseEvent) => {
          tooltip.style.left = `${e.pageX + 10}px`;
          tooltip.style.top = `${e.pageY - 20}px`;
        });

        card.addEventListener("mouseleave", () => {
          tooltip.style.opacity = "0";

          gsap.to(card, {
            scale: 1,
            boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
            duration: 0.2,
          });
        });

        // Add to appropriate column
        const column = statusColumns[order.status] || statusColumns.PENDING;
        column.appendChild(card);

        // Animate the card with staggered entrance
        gsap.fromTo(
          card,
          { opacity: 0, y: 20, scale: 0.95 },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.6,
            delay: 0.4 + (index * 0.05),
            ease: "back.out(1.5)"
          }
        );
      });

      flowContainer.appendChild(orderContainer);
      flowRef.current.appendChild(flowContainer);
      flowRef.current.appendChild(tooltip);

      // Add empty state for columns with no orders
      Object.entries(statusColumns).forEach(([status, column]) => {
        if (column.children.length === 0) {
          const emptyState = document.createElement("div");
          emptyState.className = "flex flex-col items-center justify-center h-24 text-sm text-muted-foreground bg-muted/20 rounded-md border border-dashed p-4";
          emptyState.innerHTML = `<div>No service orders in ${status}</div>`;
          column.appendChild(emptyState);

          gsap.fromTo(
            emptyState,
            { opacity: 0 },
            { opacity: 0.7, duration: 0.5, delay: 0.8 }
          );
        }
      });
    }
  }, [serviceOrders, statusCounts, priorityCounts]);

  return (
    <div ref={containerRef} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Service Orders by Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div ref={statusChartRef} className="h-64"></div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Service Orders by Priority</CardTitle>
          </CardHeader>
          <CardContent>
            <div ref={priorityChartRef} className="h-64 flex flex-col items-center justify-center"></div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Service Order Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-muted/30 p-5 rounded-lg border border-border/50 shadow-sm transition-all duration-300 hover:shadow-md hover:border-border">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Average Completion Time</div>
                  <div className="text-2xl font-bold mt-0.5 flex items-end">
                    {formatTime(averageCompletionTime)}
                    <span className="text-xs text-muted-foreground ml-2 mb-1">per order</span>
                  </div>
                </div>
              </div>
              <div className="mt-3 text-xs text-muted-foreground">
                Based on {completedOrdersCount} completed orders
              </div>
            </div>

            <div className="bg-muted/30 p-5 rounded-lg border border-border/50 shadow-sm transition-all duration-300 hover:shadow-md hover:border-border">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Completed Orders</div>
                  <div className="text-2xl font-bold mt-0.5 flex items-end">
                    {completedOrdersCount}
                    <span className="text-xs text-muted-foreground ml-2 mb-1">
                      {serviceOrders.length > 0 ?
                        `(${Math.round((completedOrdersCount / serviceOrders.length) * 100)}%)` :
                        '(0%)'}
                    </span>
                  </div>
                </div>
              </div>
              <div className="mt-3 text-xs text-muted-foreground">
                Out of {serviceOrders.length} total orders
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Service Order Flow</CardTitle>
        </CardHeader>
        <CardContent>
          <div ref={flowRef} className="min-h-[400px]"></div>
        </CardContent>
      </Card>
    </div>
  );
}