/**
 * 🔧 EQUIPMENT REGISTRY DASHBOARD
 * 
 * Comprehensive equipment management with lifecycle tracking,
 * preventive maintenance, and performance analytics.
 * 
 * Philosophy: "Every machine tells a story - listen, learn, optimize"
 */

import { 
  Settings, 
  Wrench, 
  Calendar, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Thermometer,
  Zap,
  Shield,
  BarChart3,
  Camera,
  FileText,
  QrCode,
  MapPin,
  Star,
  Activity
} from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Progress } from '~/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { 
  ComprehensiveEquipmentRecord,
  EquipmentStatus,
  EquipmentCategory,
  MaintenanceType 
} from '~/models/equipment-management.server';

interface EquipmentRegistryDashboardProps {
  customerId?: string;
  equipmentId?: string;
  onActionClick?: (action: string, data?: any) => void;
}

export function EquipmentRegistryDashboard({ 
  customerId, 
  equipmentId,
  onActionClick 
}: EquipmentRegistryDashboardProps) {
  const [equipmentList, setEquipmentList] = useState<ComprehensiveEquipmentRecord[]>([]);
  const [selectedEquipment, setSelectedEquipment] = useState<ComprehensiveEquipmentRecord | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadEquipmentData();
  }, [customerId, equipmentId]);

  const loadEquipmentData = async () => {
    try {
      setLoading(true);
      
      if (equipmentId) {
        // Load specific equipment
        const response = await fetch(`/api/equipment-management/${equipmentId}`);
        const equipment = await response.json();
        setSelectedEquipment(equipment);
      } else if (customerId) {
        // Load all equipment for customer
        const response = await fetch(`/api/equipment-management/customer/${customerId}`);
        const equipment = await response.json();
        setEquipmentList(equipment);
        if (equipment.length > 0) {
          setSelectedEquipment(equipment[0]);
        }
      }
    } catch (error) {
      console.error('Failed to load equipment data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: EquipmentStatus): string => {
    switch (status) {
      case EquipmentStatus.OPERATIONAL: return 'bg-green-100 text-green-800';
      case EquipmentStatus.MAINTENANCE_REQUIRED: return 'bg-yellow-100 text-yellow-800';
      case EquipmentStatus.REPAIR_NEEDED: return 'bg-red-100 text-red-800';
      case EquipmentStatus.OUT_OF_SERVICE: return 'bg-gray-100 text-gray-800';
      case EquipmentStatus.UNDER_WARRANTY: return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: EquipmentCategory) => {
    switch (category) {
      case EquipmentCategory.HVAC_UNIT: return <Settings className="h-5 w-5" />;
      case EquipmentCategory.THERMOSTAT: return <Thermometer className="h-5 w-5" />;
      case EquipmentCategory.FURNACE: return <Zap className="h-5 w-5" />;
      default: return <Wrench className="h-5 w-5" />;
    }
  };

  const getHealthScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Equipment List Overview */}
      {!equipmentId && equipmentList.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Equipment Registry
              </CardTitle>
              <Button onClick={() => onActionClick?.('add_equipment')}>
                <Settings className="h-4 w-4 mr-2" />
                Add Equipment
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {equipmentList.map((equipment) => (
                <Card 
                  key={equipment.id} 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedEquipment?.id === equipment.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedEquipment(equipment)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getCategoryIcon(equipment.category)}
                        <h4 className="font-medium">{equipment.name}</h4>
                      </div>
                      <Badge className={getStatusColor(equipment.status)}>
                        {equipment.status.replace('_', ' ')}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      {equipment.specifications.manufacturer} {equipment.specifications.model}
                    </p>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Health Score:</span>
                        <span className={`font-medium ${getHealthScoreColor(equipment.performance.currentMetrics.healthScore)}`}>
                          {equipment.performance.currentMetrics.healthScore}%
                        </span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Location:</span>
                        <span>{equipment.location}</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Age:</span>
                        <span>
                          {Math.floor((Date.now() - equipment.installation.installationDate.getTime()) / (365 * 24 * 60 * 60 * 1000))} years
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Equipment View */}
      {selectedEquipment && (
        <div className="space-y-6">
          {/* Equipment Header */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(selectedEquipment.category)}
                    <div>
                      <h1 className="text-2xl font-bold">{selectedEquipment.name}</h1>
                      <p className="text-gray-600">
                        {selectedEquipment.specifications.manufacturer} {selectedEquipment.specifications.model}
                      </p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(selectedEquipment.status)}>
                    {selectedEquipment.status.replace('_', ' ')}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" onClick={() => onActionClick?.('edit_equipment', selectedEquipment.id)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button onClick={() => onActionClick?.('schedule_maintenance', selectedEquipment.id)}>
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule Maintenance
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {/* Health Score */}
                <div className="space-y-2">
                  <h3 className="font-medium text-sm text-gray-700">Health Score</h3>
                  <div className="text-center">
                    <div className={`text-3xl font-bold ${getHealthScoreColor(selectedEquipment.performance.currentMetrics.healthScore)}`}>
                      {selectedEquipment.performance.currentMetrics.healthScore}%
                    </div>
                    <Progress value={selectedEquipment.performance.currentMetrics.healthScore} className="h-2 mt-2" />
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="space-y-2">
                  <h3 className="font-medium text-sm text-gray-700">Performance</h3>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Efficiency:</span>
                      <span className="font-medium">{selectedEquipment.performance.currentMetrics.metrics.efficiency}%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Runtime:</span>
                      <span className="font-medium">{selectedEquipment.performance.currentMetrics.metrics.runtime}h</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Cycles:</span>
                      <span className="font-medium">{selectedEquipment.performance.currentMetrics.metrics.cycleCount}</span>
                    </div>
                  </div>
                </div>

                {/* Maintenance Info */}
                <div className="space-y-2">
                  <h3 className="font-medium text-sm text-gray-700">Maintenance</h3>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Last Service:</span>
                      <span className="font-medium">
                        {selectedEquipment.maintenance.lastMaintenance ? 
                          Math.floor((Date.now() - selectedEquipment.maintenance.lastMaintenance.getTime()) / (24 * 60 * 60 * 1000))
                          : 'N/A'
                        } days ago
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Next Service:</span>
                      <span className="font-medium">
                        {selectedEquipment.maintenance.nextMaintenance ? 
                          Math.floor((selectedEquipment.maintenance.nextMaintenance.getTime() - Date.now()) / (24 * 60 * 60 * 1000))
                          : 'TBD'
                        } days
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Total Hours:</span>
                      <span className="font-medium">{selectedEquipment.maintenance.totalMaintenanceHours}h</span>
                    </div>
                  </div>
                </div>

                {/* Warranty Info */}
                <div className="space-y-2">
                  <h3 className="font-medium text-sm text-gray-700">Warranty</h3>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Status:</span>
                      <Badge variant={selectedEquipment.warranty.isActive ? "default" : "secondary"}>
                        {selectedEquipment.warranty.isActive ? 'Active' : 'Expired'}
                      </Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Type:</span>
                      <span className="font-medium">{selectedEquipment.warranty.warrantyType}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Expires:</span>
                      <span className="font-medium">
                        {selectedEquipment.warranty.endDate.toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
              <TabsTrigger value="parts">Parts</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Specifications */}
              <Card>
                <CardHeader>
                  <CardTitle>Equipment Specifications</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Serial Number:</span>
                        <span className="font-medium">{selectedEquipment.specifications.serialNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Capacity:</span>
                        <span className="font-medium">{selectedEquipment.specifications.capacity}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Efficiency:</span>
                        <span className="font-medium">{selectedEquipment.specifications.efficiency}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Voltage:</span>
                        <span className="font-medium">{selectedEquipment.specifications.voltage}</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Installation Date:</span>
                        <span className="font-medium">
                          {selectedEquipment.installation.installationDate.toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Location:</span>
                        <span className="font-medium">{selectedEquipment.location}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Age:</span>
                        <span className="font-medium">
                          {Math.floor((Date.now() - selectedEquipment.installation.installationDate.getTime()) / (365 * 24 * 60 * 60 * 1000))} years
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Estimated Life:</span>
                        <span className="font-medium">
                          {selectedEquipment.performance.currentMetrics.estimatedRemainingLife} months
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Recent Alerts */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Alerts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {selectedEquipment.performance.alerts.length > 0 ? (
                      selectedEquipment.performance.alerts.map((alert, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <AlertTriangle className={`h-5 w-5 ${
                            alert.type === 'CRITICAL' ? 'text-red-600' : 
                            alert.type === 'WARNING' ? 'text-yellow-600' : 'text-blue-600'
                          }`} />
                          <div className="flex-1">
                            <p className="text-sm font-medium">{alert.message}</p>
                            <p className="text-xs text-gray-600">
                              {alert.timestamp.toLocaleDateString()} • {alert.type}
                            </p>
                          </div>
                          <Badge variant={alert.isResolved ? "secondary" : "destructive"}>
                            {alert.isResolved ? 'Resolved' : 'Active'}
                          </Badge>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-3" />
                        <p className="text-gray-600">No active alerts</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Performance Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {selectedEquipment.performance.currentMetrics.metrics.efficiency}%
                      </div>
                      <p className="text-sm text-gray-600">Efficiency</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {selectedEquipment.performance.currentMetrics.metrics.energyConsumption}
                      </div>
                      <p className="text-sm text-gray-600">kWh Consumed</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {selectedEquipment.performance.currentMetrics.metrics.runtime}
                      </div>
                      <p className="text-sm text-gray-600">Runtime Hours</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {selectedEquipment.performance.currentMetrics.metrics.cycleCount}
                      </div>
                      <p className="text-sm text-gray-600">Cycle Count</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="maintenance" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Wrench className="h-5 w-5" />
                      Maintenance Schedule
                    </CardTitle>
                    <Button onClick={() => onActionClick?.('create_maintenance_schedule', selectedEquipment.id)}>
                      <Calendar className="h-4 w-4 mr-2" />
                      Add Schedule
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedEquipment.maintenance.schedules.map((schedule) => (
                      <div key={schedule.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{schedule.description}</h4>
                          <Badge variant={schedule.isActive ? "default" : "secondary"}>
                            {schedule.frequency}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Type:</span>
                            <span className="ml-2 font-medium">{schedule.type}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Duration:</span>
                            <span className="ml-2 font-medium">{schedule.estimatedDuration} min</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Next Due:</span>
                            <span className="ml-2 font-medium">{schedule.nextDue.toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="parts" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Parts & Components</CardTitle>
                    <Button onClick={() => onActionClick?.('add_part', selectedEquipment.id)}>
                      Add Part
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Parts management interface will be displayed here.</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="documents" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Equipment Documents</CardTitle>
                    <Button onClick={() => onActionClick?.('upload_document', selectedEquipment.id)}>
                      <FileText className="h-4 w-4 mr-2" />
                      Upload Document
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <FileText className="h-5 w-5 text-blue-600" />
                        <span className="font-medium">Installation Manual</span>
                      </div>
                      <p className="text-sm text-gray-600">Manufacturer installation guide</p>
                      <Button variant="outline" size="sm" className="mt-2">View</Button>
                    </div>
                    <div className="border rounded-lg p-4">
                      <div className="flex items-center gap-3 mb-2">
                        <Camera className="h-5 w-5 text-green-600" />
                        <span className="font-medium">Installation Photos</span>
                      </div>
                      <p className="text-sm text-gray-600">Photos from installation</p>
                      <Button variant="outline" size="sm" className="mt-2">View</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}
