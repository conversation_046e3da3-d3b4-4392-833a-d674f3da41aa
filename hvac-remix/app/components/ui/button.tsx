import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "~/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-[0.98] touch-manipulation",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow-lg hover:shadow-primary/30 hover:bg-primary/90 rounded-xl hover:translate-y-[-1px]",
        destructive: "bg-destructive text-destructive-foreground shadow-lg hover:shadow-destructive/30 hover:bg-destructive/90 rounded-xl hover:translate-y-[-1px]",
        outline: "border-2 border-input bg-background hover:bg-accent/10 hover:text-accent hover:border-accent rounded-xl hover:shadow-sm",
        secondary: "bg-secondary text-secondary-foreground shadow-lg hover:shadow-secondary/30 hover:bg-secondary/90 rounded-xl hover:translate-y-[-1px]",
        ghost: "hover:bg-accent/10 hover:text-accent rounded-xl",
        link: "text-primary underline-offset-4 hover:underline",
        success: "bg-success text-success-foreground shadow-lg hover:shadow-success/30 hover:bg-success/90 rounded-xl hover:translate-y-[-1px]",
        warning: "bg-warning text-warning-foreground shadow-lg hover:shadow-warning/30 hover:bg-warning/90 rounded-xl hover:translate-y-[-1px]",
        info: "bg-info text-info-foreground shadow-lg hover:shadow-info/30 hover:bg-info/90 rounded-xl hover:translate-y-[-1px]",
      },
      size: {
        default: "h-11 px-6 py-2 text-sm",
        sm: "h-9 rounded-lg px-4 text-xs",
        lg: "h-12 rounded-2xl px-10 text-base font-semibold",
        xl: "h-14 rounded-2xl px-12 text-lg font-semibold", // Extra large for mobile
        icon: "h-11 w-11",
        "icon-sm": "h-9 w-9",
        "icon-lg": "h-12 w-12",
        "icon-xl": "h-14 w-14", // Extra large icon for mobile
        mobile: "h-12 px-6 py-3 text-base min-w-[44px]", // Mobile-optimized
        touch: "h-14 px-8 py-4 text-lg min-w-[56px]", // Touch-optimized
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };