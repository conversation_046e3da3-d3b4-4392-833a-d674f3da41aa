import * as React from "react";

import { cn } from "~/lib/utils";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: 'default' | 'mobile' | 'touch';
  icon?: React.ReactNode;
  error?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, variant = 'default', icon, error, ...props }, ref) => {
    const baseClasses = "flex w-full rounded-xl border bg-background text-sm ring-offset-background transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground/70 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50";

    const variantClasses = {
      default: "h-10 px-4 py-2 border-input focus-visible:border-primary hover:border-primary/50",
      mobile: "h-12 px-4 py-3 text-base border-input focus-visible:border-primary hover:border-primary/50 touch-manipulation", // Larger for mobile
      touch: "h-14 px-5 py-4 text-lg border-2 border-input focus-visible:border-primary hover:border-primary/50 touch-manipulation" // Extra large for touch
    };

    const errorClasses = error
      ? "border-destructive focus-visible:ring-destructive/20 focus-visible:border-destructive"
      : "";

    const iconPadding = icon ? "pl-12" : "";

    return (
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground pointer-events-none">
            {icon}
          </div>
        )}
        <input
          type={type}
          className={cn(
            baseClasses,
            variantClasses[variant],
            errorClasses,
            iconPadding,
            className
          )}
          ref={ref}
          {...props}
        />
      </div>
    );
  }
);
Input.displayName = "Input";

export { Input };