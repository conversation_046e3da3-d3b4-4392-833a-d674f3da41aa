import { WifiOffIcon, WifiIcon } from "lucide-react";
import { useState, useEffect } from "react";
import { cn } from "~/utils";

interface OfflineIndicatorProps {
  className?: string;
  showOnlineStatus?: boolean;
}

/**
 * OfflineIndicator component
 * 
 * Displays an indicator when the user is offline.
 * Can optionally show online status as well.
 */
export function OfflineIndicator({ 
  className, 
  showOnlineStatus = false 
}: OfflineIndicatorProps) {
  const [isOnline, setIsOnline] = useState(true);
  
  useEffect(() => {
    // Set initial state
    setIsOnline(navigator.onLine);
    
    // Add event listeners
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // Clean up
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  // If online and not showing online status, don't render anything
  if (isOnline && !showOnlineStatus) {
    return null;
  }
  
  return (
    <div 
      className={cn(
        "flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium rounded-full",
        isOnline 
          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400" 
          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
        className
      )}
    >
      {isOnline ? (
        <>
          <WifiIcon className="h-3 w-3" />
          <span>Online</span>
        </>
      ) : (
        <>
          <WifiOffIcon className="h-3 w-3" />
          <span>Offline</span>
        </>
      )}
    </div>
  );
}

/**
 * OfflineBanner component
 * 
 * Displays a banner at the top of the page when the user is offline.
 */
export function OfflineBanner() {
  const [isOnline, setIsOnline] = useState(true);
  const [showBanner, setShowBanner] = useState(false);
  
  useEffect(() => {
    // Set initial state
    const online = navigator.onLine;
    setIsOnline(online);
    setShowBanner(!online);
    
    // Add event listeners
    const handleOnline = () => {
      setIsOnline(true);
      // Keep banner visible for a moment when coming back online
      setTimeout(() => setShowBanner(false), 3000);
    };
    
    const handleOffline = () => {
      setIsOnline(false);
      setShowBanner(true);
    };
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // Clean up
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  if (!showBanner) {
    return null;
  }
  
  return (
    <div 
      className={cn(
        "fixed top-0 left-0 right-0 z-50 p-2 text-center text-sm font-medium transition-all",
        isOnline 
          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400" 
          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
      )}
    >
      {isOnline ? (
        <div className="flex items-center justify-center gap-1.5">
          <WifiIcon className="h-4 w-4" />
          <span>Połączenie z internetem zostało przywrócone</span>
        </div>
      ) : (
        <div className="flex items-center justify-center gap-1.5">
          <WifiOffIcon className="h-4 w-4" />
          <span>Brak połączenia z internetem. Niektóre funkcje mogą być niedostępne.</span>
        </div>
      )}
    </div>
  );
}
