/**
 * 🚀 ENHANCED CUSTOMER LIFECYCLE MANAGEMENT
 * 
 * Advanced customer relationship management with lifecycle tracking,
 * segmentation, health scoring, and communication history.
 * 
 * Philosophy: "Every customer interaction is a sacred moment of connection"
 */

import { prisma } from "~/db.server";

// 📊 Customer Lifecycle Stages
export enum CustomerLifecycleStage {
  LEAD = 'LEAD',
  PROSPECT = 'PROSPECT', 
  NEW_CUSTOMER = 'NEW_CUSTOMER',
  ACTIVE_CUSTOMER = 'ACTIVE_CUSTOMER',
  LOYAL_CUSTOMER = 'LOYAL_CUSTOMER',
  AT_RISK = 'AT_RISK',
  CHURNED = 'CHURNED',
  REACTIVATED = 'REACTIVATED'
}

// 🎯 Customer Segments
export enum CustomerSegment {
  RESIDENTIAL_BASIC = 'RESIDENTIAL_BASIC',
  RESIDENTIAL_PREMIUM = 'RESIDENTIAL_PREMIUM',
  COMMERCIAL_SMALL = 'COMMERCIAL_SMALL',
  COMMERCIAL_ENTERPRISE = 'COMMERCIAL_ENTERPRISE',
  INDUSTRIAL = 'INDUSTRIAL',
  GOVERNMENT = 'GOVERNMENT'
}

// 💬 Communication Channels
export enum CommunicationChannel {
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  SMS = 'SMS',
  IN_PERSON = 'IN_PERSON',
  PORTAL = 'PORTAL',
  CHAT = 'CHAT'
}

// 📈 Customer Health Score Components
export interface CustomerHealthMetrics {
  serviceFrequency: number;
  paymentHistory: number;
  communicationResponsiveness: number;
  contractCompliance: number;
  referralActivity: number;
  overallScore: number;
}

// 🔄 Customer Lifecycle Event
export interface CustomerLifecycleEvent {
  id: string;
  customerId: string;
  eventType: string;
  eventData: Record<string, any>;
  timestamp: Date;
  triggeredBy: string;
  impact: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
}

// 📞 Communication History Entry
export interface CommunicationHistoryEntry {
  id: string;
  customerId: string;
  channel: CommunicationChannel;
  direction: 'INBOUND' | 'OUTBOUND';
  subject: string;
  content: string;
  timestamp: Date;
  userId: string;
  sentiment: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  tags: string[];
  followUpRequired: boolean;
  followUpDate?: Date;
}

// 🎯 Customer Segmentation Rules
export interface SegmentationRule {
  id: string;
  name: string;
  segment: CustomerSegment;
  criteria: {
    annualRevenue?: { min?: number; max?: number };
    serviceFrequency?: { min?: number; max?: number };
    customerType?: string[];
    equipmentCount?: { min?: number; max?: number };
    contractValue?: { min?: number; max?: number };
  };
  isActive: boolean;
}

// 📊 Customer Analytics Profile
export interface CustomerAnalyticsProfile {
  customerId: string;
  lifecycleStage: CustomerLifecycleStage;
  segment: CustomerSegment;
  healthScore: CustomerHealthMetrics;
  totalRevenue: number;
  averageJobValue: number;
  lastServiceDate?: Date;
  nextScheduledService?: Date;
  customerLifetimeValue: number;
  acquisitionDate: Date;
  churnRisk: number;
  satisfactionScore?: number;
  loyaltyTier: 'BRONZE' | 'SILVER' | 'GOLD' | 'PLATINUM';
  preferredTechnician?: string;
  preferredServiceTime?: string;
  communicationPreference: CommunicationChannel;
  equipmentCount: number;
  maintenanceCompliance: number;
  referralCount: number;
  lastInteractionDate?: Date;
  updatedAt: Date;
}

/**
 * 🚀 Customer Lifecycle Management Service
 */
export class CustomerLifecycleService {
  
  /**
   * Calculate customer health score based on multiple factors
   */
  static async calculateHealthScore(customerId: string): Promise<CustomerHealthMetrics> {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        serviceOrders: {
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        invoices: {
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        devices: true
      }
    });

    if (!customer) {
      throw new Error('Customer not found');
    }

    const now = new Date();
    const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);

    // Service Frequency Score (0-100)
    const recentServices = customer.serviceOrders.filter(
      so => so.createdAt > sixMonthsAgo
    ).length;
    const serviceFrequency = Math.min(100, (recentServices / 6) * 100);

    // Payment History Score (0-100)
    const paidInvoices = customer.invoices.filter(
      inv => inv.paymentStatus === 'PAID'
    ).length;
    const totalInvoices = customer.invoices.length;
    const paymentHistory = totalInvoices > 0 ? (paidInvoices / totalInvoices) * 100 : 100;

    // Communication Responsiveness (placeholder - would integrate with actual communication data)
    const communicationResponsiveness = 85; // Default good score

    // Contract Compliance (based on scheduled vs completed services)
    const scheduledServices = customer.serviceOrders.filter(
      so => so.status === 'COMPLETED' && so.scheduledDate
    ).length;
    const totalScheduled = customer.serviceOrders.filter(
      so => so.scheduledDate
    ).length;
    const contractCompliance = totalScheduled > 0 ? (scheduledServices / totalScheduled) * 100 : 100;

    // Referral Activity (placeholder)
    const referralActivity = 70; // Default score

    // Overall Score (weighted average)
    const overallScore = (
      serviceFrequency * 0.25 +
      paymentHistory * 0.30 +
      communicationResponsiveness * 0.15 +
      contractCompliance * 0.20 +
      referralActivity * 0.10
    );

    return {
      serviceFrequency,
      paymentHistory,
      communicationResponsiveness,
      contractCompliance,
      referralActivity,
      overallScore: Math.round(overallScore)
    };
  }

  /**
   * Determine customer lifecycle stage based on behavior and metrics
   */
  static async determineLifecycleStage(customerId: string): Promise<CustomerLifecycleStage> {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        serviceOrders: {
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!customer) {
      throw new Error('Customer not found');
    }

    const now = new Date();
    const threeMonthsAgo = new Date(now.getTime() - 3 * 30 * 24 * 60 * 60 * 1000);
    const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    const recentServices = customer.serviceOrders.filter(
      so => so.createdAt > threeMonthsAgo
    ).length;

    const servicesLastSixMonths = customer.serviceOrders.filter(
      so => so.createdAt > sixMonthsAgo
    ).length;

    const servicesLastYear = customer.serviceOrders.filter(
      so => so.createdAt > oneYearAgo
    ).length;

    const totalServices = customer.serviceOrders.length;
    const healthScore = await this.calculateHealthScore(customerId);

    // Determine stage based on service history and health score
    if (totalServices === 0) {
      return CustomerLifecycleStage.LEAD;
    } else if (totalServices === 1 && customer.createdAt > threeMonthsAgo) {
      return CustomerLifecycleStage.NEW_CUSTOMER;
    } else if (recentServices >= 2 && healthScore.overallScore >= 80) {
      return CustomerLifecycleStage.LOYAL_CUSTOMER;
    } else if (recentServices >= 1 && healthScore.overallScore >= 60) {
      return CustomerLifecycleStage.ACTIVE_CUSTOMER;
    } else if (servicesLastSixMonths === 0 && servicesLastYear > 0) {
      return CustomerLifecycleStage.AT_RISK;
    } else if (servicesLastYear === 0 && totalServices > 0) {
      return CustomerLifecycleStage.CHURNED;
    } else {
      return CustomerLifecycleStage.PROSPECT;
    }
  }

  /**
   * Segment customer based on business rules
   */
  static async segmentCustomer(customerId: string): Promise<CustomerSegment> {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        serviceOrders: true,
        invoices: true,
        devices: true
      }
    });

    if (!customer) {
      throw new Error('Customer not found');
    }

    const totalRevenue = customer.invoices
      .filter(inv => inv.paymentStatus === 'PAID')
      .reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);

    const equipmentCount = customer.devices.length;
    const serviceCount = customer.serviceOrders.length;

    // Segmentation logic
    if (totalRevenue >= 50000 || equipmentCount >= 10) {
      return CustomerSegment.COMMERCIAL_ENTERPRISE;
    } else if (totalRevenue >= 20000 || equipmentCount >= 5) {
      return CustomerSegment.COMMERCIAL_SMALL;
    } else if (totalRevenue >= 10000 && serviceCount >= 5) {
      return CustomerSegment.RESIDENTIAL_PREMIUM;
    } else {
      return CustomerSegment.RESIDENTIAL_BASIC;
    }
  }
}
