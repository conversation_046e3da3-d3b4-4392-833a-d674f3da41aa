/**
 * 👤 COMPREHENSIVE CUSTOMER PROFILE MANAGEMENT
 * 
 * Enhanced customer profile system with detailed contact history,
 * preferences, service notes, and relationship mapping.
 * 
 * Philosophy: "Know thy customer as thyself - every detail matters"
 */

import { prisma } from "~/db.server";

// 📞 Communication Channel Types
export enum CommunicationChannelType {
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  SMS = 'SMS',
  IN_PERSON = 'IN_PERSON',
  VIDEO_CALL = 'VIDEO_CALL',
  PORTAL = 'PORTAL',
  CHAT = 'CHAT',
  SOCIAL_MEDIA = 'SOCIAL_MEDIA'
}

// 💭 Communication Sentiment
export enum CommunicationSentiment {
  VERY_POSITIVE = 'VERY_POSITIVE',
  POSITIVE = 'POSITIVE',
  NEUTRAL = 'NEUTRAL',
  NEGATIVE = 'NEGATIVE',
  VERY_NEGATIVE = 'VERY_NEGATIVE'
}

// 📋 Customer Preference Categories
export enum CustomerPreferenceCategory {
  COMMUNICATION = 'COMMUNICATION',
  SCHEDULING = 'SCHEDULING',
  SERVICE = 'SERVICE',
  BILLING = 'BILLING',
  TECHNICAL = 'TECHNICAL'
}

// 🏷️ Document Types
export enum DocumentType {
  CONTRACT = 'CONTRACT',
  WARRANTY = 'WARRANTY',
  SERVICE_AGREEMENT = 'SERVICE_AGREEMENT',
  INVOICE = 'INVOICE',
  PHOTO = 'PHOTO',
  TECHNICAL_DRAWING = 'TECHNICAL_DRAWING',
  PERMIT = 'PERMIT',
  CERTIFICATE = 'CERTIFICATE',
  MANUAL = 'MANUAL',
  OTHER = 'OTHER'
}

// 👥 Relationship Types
export enum RelationshipType {
  PRIMARY_CONTACT = 'PRIMARY_CONTACT',
  BILLING_CONTACT = 'BILLING_CONTACT',
  TECHNICAL_CONTACT = 'TECHNICAL_CONTACT',
  EMERGENCY_CONTACT = 'EMERGENCY_CONTACT',
  DECISION_MAKER = 'DECISION_MAKER',
  INFLUENCER = 'INFLUENCER',
  USER = 'USER'
}

// 📞 Communication Record
export interface CommunicationRecord {
  id: string;
  customerId: string;
  contactPersonId?: string;
  channel: CommunicationChannelType;
  direction: 'INBOUND' | 'OUTBOUND';
  subject: string;
  content: string;
  summary?: string;
  sentiment: CommunicationSentiment;
  sentimentScore: number; // -100 to 100
  duration?: number; // in minutes for calls
  attachments?: string[];
  tags: string[];
  followUpRequired: boolean;
  followUpDate?: Date;
  followUpNotes?: string;
  userId: string; // who handled the communication
  timestamp: Date;
  isResolved: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  relatedServiceOrderId?: string;
  relatedEquipmentId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 🎯 Customer Preference
export interface CustomerPreference {
  id: string;
  customerId: string;
  category: CustomerPreferenceCategory;
  key: string;
  value: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 📄 Customer Document
export interface CustomerDocument {
  id: string;
  customerId: string;
  equipmentId?: string;
  serviceOrderId?: string;
  type: DocumentType;
  title: string;
  description?: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadedBy: string;
  isPublic: boolean; // visible in customer portal
  expirationDate?: Date;
  tags: string[];
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// 👥 Contact Person (for business customers)
export interface ContactPerson {
  id: string;
  customerId: string;
  firstName: string;
  lastName: string;
  title?: string;
  department?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  relationshipType: RelationshipType;
  isPrimary: boolean;
  isActive: boolean;
  notes?: string;
  preferredContactMethod: CommunicationChannelType;
  preferredContactTime?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 📝 Service Note
export interface ServiceNote {
  id: string;
  customerId: string;
  equipmentId?: string;
  serviceOrderId?: string;
  userId: string;
  noteType: 'GENERAL' | 'TECHNICAL' | 'BILLING' | 'SAFETY' | 'FOLLOW_UP';
  title: string;
  content: string;
  isPrivate: boolean; // not visible to customer
  isPinned: boolean;
  tags: string[];
  attachments?: string[];
  createdAt: Date;
  updatedAt: Date;
}

// 📊 Enhanced Customer Profile
export interface EnhancedCustomerProfile {
  customerId: string;
  basicInfo: {
    name: string;
    type: 'RESIDENTIAL' | 'COMMERCIAL' | 'INDUSTRIAL';
    status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
    accountNumber: string;
    taxId?: string;
    website?: string;
    industry?: string;
    companySize?: string;
  };
  contactInfo: {
    primaryAddress: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
      coordinates?: { lat: number; lng: number };
    };
    billingAddress?: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
    primaryPhone: string;
    secondaryPhone?: string;
    email: string;
    alternateEmail?: string;
  };
  businessHours?: {
    monday?: { open: string; close: string };
    tuesday?: { open: string; close: string };
    wednesday?: { open: string; close: string };
    thursday?: { open: string; close: string };
    friday?: { open: string; close: string };
    saturday?: { open: string; close: string };
    sunday?: { open: string; close: string };
  };
  preferences: CustomerPreference[];
  contactPersons: ContactPerson[];
  serviceNotes: ServiceNote[];
  documents: CustomerDocument[];
  communicationHistory: CommunicationRecord[];
  statistics: {
    totalServiceOrders: number;
    totalRevenue: number;
    averageJobValue: number;
    lastServiceDate?: Date;
    nextScheduledService?: Date;
    communicationFrequency: number;
    responseRate: number;
    satisfactionScore?: number;
  };
  relationships: {
    referredBy?: string;
    referrals: string[];
    relatedAccounts: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 🏢 Customer Profile Management Service
 */
export class CustomerProfileManagementService {

  /**
   * Get comprehensive customer profile
   */
  static async getEnhancedCustomerProfile(customerId: string): Promise<EnhancedCustomerProfile | null> {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        serviceOrders: {
          orderBy: { createdAt: 'desc' },
          include: { invoices: true }
        },
        invoices: {
          orderBy: { createdAt: 'desc' }
        },
        devices: true
      }
    });

    if (!customer) return null;

    // Calculate statistics
    const totalServiceOrders = customer.serviceOrders.length;
    const totalRevenue = customer.invoices
      .filter(inv => inv.paymentStatus === 'PAID')
      .reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);
    const averageJobValue = totalServiceOrders > 0 ? totalRevenue / totalServiceOrders : 0;
    
    const completedServices = customer.serviceOrders.filter(so => so.status === 'COMPLETED');
    const lastServiceDate = completedServices.length > 0 ? 
      completedServices[0].completedDate : undefined;

    // Build enhanced profile
    const enhancedProfile: EnhancedCustomerProfile = {
      customerId: customer.id,
      basicInfo: {
        name: customer.name,
        type: customer.type as 'RESIDENTIAL' | 'COMMERCIAL' | 'INDUSTRIAL',
        status: 'ACTIVE', // Would come from customer status field
        accountNumber: customer.id, // Would be a separate field
        taxId: customer.taxId,
        website: customer.website,
        industry: customer.industry,
        companySize: customer.companySize
      },
      contactInfo: {
        primaryAddress: {
          street: customer.address || '',
          city: customer.city || '',
          state: customer.state || '',
          zipCode: customer.zipCode || '',
          country: 'USA'
        },
        primaryPhone: customer.phone || '',
        email: customer.email || ''
      },
      preferences: [], // Would load from preferences table
      contactPersons: [], // Would load from contact persons table
      serviceNotes: [], // Would load from service notes table
      documents: [], // Would load from documents table
      communicationHistory: [], // Would load from communication records table
      statistics: {
        totalServiceOrders,
        totalRevenue,
        averageJobValue,
        lastServiceDate,
        communicationFrequency: 0, // Would calculate from communication records
        responseRate: 0, // Would calculate from communication records
      },
      relationships: {
        referrals: [],
        relatedAccounts: []
      },
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt
    };

    return enhancedProfile;
  }

  /**
   * Add communication record
   */
  static async addCommunicationRecord(record: Omit<CommunicationRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<CommunicationRecord> {
    // In a real implementation, this would save to database
    const communicationRecord: CommunicationRecord = {
      ...record,
      id: `comm_${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return communicationRecord;
  }

  /**
   * Analyze communication sentiment
   */
  static async analyzeCommunicationSentiment(content: string): Promise<{
    sentiment: CommunicationSentiment;
    score: number;
    confidence: number;
  }> {
    // Simplified sentiment analysis - in production would use AI service
    const positiveWords = ['great', 'excellent', 'satisfied', 'happy', 'pleased', 'good', 'wonderful'];
    const negativeWords = ['bad', 'terrible', 'awful', 'disappointed', 'angry', 'frustrated', 'poor'];
    
    const words = content.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.some(pw => word.includes(pw))) positiveCount++;
      if (negativeWords.some(nw => word.includes(nw))) negativeCount++;
    });

    const score = (positiveCount - negativeCount) * 20; // Scale to -100 to 100
    let sentiment: CommunicationSentiment;

    if (score >= 40) sentiment = CommunicationSentiment.VERY_POSITIVE;
    else if (score >= 10) sentiment = CommunicationSentiment.POSITIVE;
    else if (score >= -10) sentiment = CommunicationSentiment.NEUTRAL;
    else if (score >= -40) sentiment = CommunicationSentiment.NEGATIVE;
    else sentiment = CommunicationSentiment.VERY_NEGATIVE;

    return {
      sentiment,
      score: Math.max(-100, Math.min(100, score)),
      confidence: Math.min(100, Math.abs(score) + 50)
    };
  }

  /**
   * Get customer communication summary
   */
  static async getCommunicationSummary(customerId: string, days: number = 30): Promise<{
    totalCommunications: number;
    byChannel: Record<CommunicationChannelType, number>;
    bySentiment: Record<CommunicationSentiment, number>;
    averageSentimentScore: number;
    responseRate: number;
    averageResponseTime: number;
  }> {
    // In production, would query actual communication records
    return {
      totalCommunications: 15,
      byChannel: {
        [CommunicationChannelType.EMAIL]: 8,
        [CommunicationChannelType.PHONE]: 5,
        [CommunicationChannelType.SMS]: 2,
        [CommunicationChannelType.IN_PERSON]: 0,
        [CommunicationChannelType.VIDEO_CALL]: 0,
        [CommunicationChannelType.PORTAL]: 0,
        [CommunicationChannelType.CHAT]: 0,
        [CommunicationChannelType.SOCIAL_MEDIA]: 0
      },
      bySentiment: {
        [CommunicationSentiment.VERY_POSITIVE]: 3,
        [CommunicationSentiment.POSITIVE]: 8,
        [CommunicationSentiment.NEUTRAL]: 3,
        [CommunicationSentiment.NEGATIVE]: 1,
        [CommunicationSentiment.VERY_NEGATIVE]: 0
      },
      averageSentimentScore: 25,
      responseRate: 95,
      averageResponseTime: 2.5 // hours
    };
  }
}
