/**
 * 🔧 COMPREHENSIVE EQUIPMENT MANAGEMENT SYSTEM
 * 
 * Advanced equipment registry with lifecycle tracking, preventive maintenance,
 * performance analytics, and parts management.
 * 
 * Philosophy: "Every machine has a soul - understand it, nurture it, optimize it"
 */

import { prisma } from "~/db.server";

// 🏭 Equipment Categories
export enum EquipmentCategory {
  HVAC_UNIT = 'HVAC_UNIT',
  AIR_HANDLER = 'AIR_HANDLER',
  CONDENSER = 'CONDENSER',
  EVAPORATOR = 'EVAPORATOR',
  FURNACE = 'FURNACE',
  HEAT_PUMP = 'HEAT_PUMP',
  BOILER = 'BOILER',
  CHILLER = 'CHILLER',
  THERMOSTAT = 'THERMOSTAT',
  DUCTWORK = 'DUCTWORK',
  VENTILATION = 'VENTILATION',
  FILTER_SYSTEM = 'FILTER_SYSTEM',
  CONTROL_SYSTEM = 'CONTROL_SYSTEM',
  OTHER = 'OTHER'
}

// 📊 Equipment Status
export enum EquipmentStatus {
  OPERATIONAL = 'OPERATIONAL',
  MAINTENANCE_REQUIRED = 'MAINTENANCE_REQUIRED',
  REPAIR_NEEDED = 'REPAIR_NEEDED',
  OUT_OF_SERVICE = 'OUT_OF_SERVICE',
  DECOMMISSIONED = 'DECOMMISSIONED',
  UNDER_WARRANTY = 'UNDER_WARRANTY',
  SCHEDULED_REPLACEMENT = 'SCHEDULED_REPLACEMENT'
}

// 🔄 Maintenance Types
export enum MaintenanceType {
  PREVENTIVE = 'PREVENTIVE',
  CORRECTIVE = 'CORRECTIVE',
  EMERGENCY = 'EMERGENCY',
  INSPECTION = 'INSPECTION',
  CLEANING = 'CLEANING',
  CALIBRATION = 'CALIBRATION',
  UPGRADE = 'UPGRADE',
  REPLACEMENT = 'REPLACEMENT'
}

// ⚡ Performance Metrics
export interface EquipmentPerformanceMetrics {
  equipmentId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  metrics: {
    efficiency: number; // percentage
    energyConsumption: number; // kWh
    runtime: number; // hours
    cycleCount: number;
    temperatureVariance: number;
    pressureReadings: number[];
    vibrationLevels: number[];
    errorCount: number;
    maintenanceEvents: number;
  };
  benchmarks: {
    industryAverage: number;
    optimalRange: { min: number; max: number };
    alertThresholds: { warning: number; critical: number };
  };
  recommendations: string[];
  healthScore: number; // 0-100
  predictedFailureDate?: Date;
  estimatedRemainingLife: number; // months
}

// 🔧 Equipment Specification
export interface EquipmentSpecification {
  manufacturer: string;
  model: string;
  serialNumber: string;
  capacity: string; // e.g., "3 Ton", "80,000 BTU"
  efficiency: string; // e.g., "SEER 16", "AFUE 95%"
  refrigerant?: string;
  voltage: string;
  amperage: string;
  dimensions: {
    length: number;
    width: number;
    height: number;
    weight: number;
  };
  operatingConditions: {
    minTemperature: number;
    maxTemperature: number;
    humidity: string;
    altitude: string;
  };
  features: string[];
  certifications: string[];
}

// 📅 Maintenance Schedule
export interface MaintenanceSchedule {
  id: string;
  equipmentId: string;
  type: MaintenanceType;
  frequency: 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'SEMI_ANNUAL' | 'ANNUAL' | 'CUSTOM';
  customFrequencyDays?: number;
  description: string;
  estimatedDuration: number; // minutes
  requiredParts: string[];
  requiredTools: string[];
  skillLevel: 'BASIC' | 'INTERMEDIATE' | 'ADVANCED' | 'SPECIALIST';
  safetyRequirements: string[];
  instructions: string;
  lastPerformed?: Date;
  nextDue: Date;
  isActive: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  createdAt: Date;
  updatedAt: Date;
}

// 🛠️ Parts Information
export interface EquipmentPart {
  id: string;
  equipmentId: string;
  partNumber: string;
  partName: string;
  description: string;
  manufacturer: string;
  category: string;
  isConsumable: boolean;
  averageLifespan: number; // months
  currentStock: number;
  minimumStock: number;
  unitCost: number;
  supplier: string;
  lastReplaced?: Date;
  nextReplacementDue?: Date;
  warrantyMonths: number;
  isUnderWarranty: boolean;
  warrantyExpiration?: Date;
  installationNotes?: string;
  compatibleModels: string[];
  createdAt: Date;
  updatedAt: Date;
}

// 📋 Comprehensive Equipment Record
export interface ComprehensiveEquipmentRecord {
  id: string;
  customerId: string;
  category: EquipmentCategory;
  status: EquipmentStatus;
  name: string;
  description?: string;
  location: string; // within customer premises
  specifications: EquipmentSpecification;
  installation: {
    installationDate: Date;
    installedBy: string;
    installationNotes?: string;
    commissioningDate?: Date;
    initialSettings: Record<string, any>;
  };
  warranty: {
    manufacturer: string;
    warrantyType: 'PARTS' | 'LABOR' | 'COMPREHENSIVE';
    startDate: Date;
    endDate: Date;
    isActive: boolean;
    terms: string;
    registrationNumber?: string;
  };
  maintenance: {
    schedules: MaintenanceSchedule[];
    lastMaintenance?: Date;
    nextMaintenance?: Date;
    maintenanceHistory: string[]; // service order IDs
    totalMaintenanceHours: number;
    maintenanceCost: number;
  };
  performance: {
    currentMetrics: EquipmentPerformanceMetrics;
    historicalData: EquipmentPerformanceMetrics[];
    alerts: Array<{
      type: 'WARNING' | 'CRITICAL' | 'INFO';
      message: string;
      timestamp: Date;
      isResolved: boolean;
    }>;
  };
  parts: EquipmentPart[];
  documents: string[]; // document IDs
  photos: string[]; // photo URLs
  qrCode?: string;
  tags: string[];
  notes: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 🏭 Equipment Management Service
 */
export class EquipmentManagementService {

  /**
   * Get comprehensive equipment record
   */
  static async getComprehensiveEquipmentRecord(equipmentId: string): Promise<ComprehensiveEquipmentRecord | null> {
    const equipment = await prisma.device.findUnique({
      where: { id: equipmentId },
      include: {
        customer: true,
        serviceOrders: {
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!equipment) return null;

    // Build comprehensive record from existing data
    const comprehensiveRecord: ComprehensiveEquipmentRecord = {
      id: equipment.id,
      customerId: equipment.customerId,
      category: this.mapDeviceTypeToCategory(equipment.type),
      status: this.determineEquipmentStatus(equipment),
      name: equipment.name,
      description: equipment.description,
      location: equipment.location || 'Main Building',
      specifications: {
        manufacturer: equipment.manufacturer || 'Unknown',
        model: equipment.model || 'Unknown',
        serialNumber: equipment.serialNumber || 'N/A',
        capacity: equipment.capacity || 'Unknown',
        efficiency: equipment.efficiency || 'Unknown',
        refrigerant: equipment.refrigerant,
        voltage: equipment.voltage || 'Unknown',
        amperage: equipment.amperage || 'Unknown',
        dimensions: {
          length: 0,
          width: 0,
          height: 0,
          weight: 0
        },
        operatingConditions: {
          minTemperature: -20,
          maxTemperature: 120,
          humidity: '0-95%',
          altitude: 'Sea level to 6000 ft'
        },
        features: [],
        certifications: []
      },
      installation: {
        installationDate: equipment.installationDate || equipment.createdAt,
        installedBy: 'Unknown',
        commissioningDate: equipment.installationDate,
        initialSettings: {}
      },
      warranty: {
        manufacturer: equipment.manufacturer || 'Unknown',
        warrantyType: 'COMPREHENSIVE',
        startDate: equipment.warrantyStartDate || equipment.createdAt,
        endDate: equipment.warrantyEndDate || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        isActive: equipment.warrantyEndDate ? equipment.warrantyEndDate > new Date() : false,
        terms: 'Standard manufacturer warranty'
      },
      maintenance: {
        schedules: [],
        lastMaintenance: equipment.lastMaintenanceDate,
        nextMaintenance: equipment.nextMaintenanceDate,
        maintenanceHistory: equipment.serviceOrders.map(so => so.id),
        totalMaintenanceHours: 0,
        maintenanceCost: 0
      },
      performance: {
        currentMetrics: await this.calculateCurrentPerformanceMetrics(equipmentId),
        historicalData: [],
        alerts: []
      },
      parts: [],
      documents: [],
      photos: [],
      tags: [],
      notes: equipment.notes || '',
      createdAt: equipment.createdAt,
      updatedAt: equipment.updatedAt
    };

    return comprehensiveRecord;
  }

  /**
   * Calculate current performance metrics
   */
  static async calculateCurrentPerformanceMetrics(equipmentId: string): Promise<EquipmentPerformanceMetrics> {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // In production, this would analyze real sensor data
    return {
      equipmentId,
      period: {
        startDate: thirtyDaysAgo,
        endDate: now
      },
      metrics: {
        efficiency: 85,
        energyConsumption: 1250,
        runtime: 720,
        cycleCount: 450,
        temperatureVariance: 2.5,
        pressureReadings: [35, 36, 34, 37, 35],
        vibrationLevels: [0.2, 0.3, 0.2, 0.4, 0.2],
        errorCount: 2,
        maintenanceEvents: 1
      },
      benchmarks: {
        industryAverage: 80,
        optimalRange: { min: 85, max: 95 },
        alertThresholds: { warning: 75, critical: 65 }
      },
      recommendations: [
        'Schedule filter replacement',
        'Check refrigerant levels',
        'Calibrate temperature sensors'
      ],
      healthScore: 85,
      estimatedRemainingLife: 84 // months
    };
  }

  /**
   * Generate preventive maintenance schedule
   */
  static async generatePreventiveMaintenanceSchedule(equipmentId: string): Promise<MaintenanceSchedule[]> {
    const equipment = await prisma.device.findUnique({
      where: { id: equipmentId }
    });

    if (!equipment) return [];

    const baseSchedules: Omit<MaintenanceSchedule, 'id' | 'equipmentId' | 'createdAt' | 'updatedAt'>[] = [
      {
        type: MaintenanceType.INSPECTION,
        frequency: 'MONTHLY',
        description: 'Monthly visual inspection and basic checks',
        estimatedDuration: 30,
        requiredParts: [],
        requiredTools: ['Flashlight', 'Multimeter'],
        skillLevel: 'BASIC',
        safetyRequirements: ['Safety glasses', 'Work gloves'],
        instructions: 'Perform visual inspection of all components, check for leaks, unusual noises, or vibrations.',
        nextDue: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        isActive: true,
        priority: 'MEDIUM'
      },
      {
        type: MaintenanceType.CLEANING,
        frequency: 'QUARTERLY',
        description: 'Quarterly cleaning and filter replacement',
        estimatedDuration: 60,
        requiredParts: ['Air Filter', 'Cleaning Solution'],
        requiredTools: ['Vacuum', 'Cleaning cloths', 'Screwdriver set'],
        skillLevel: 'BASIC',
        safetyRequirements: ['Safety glasses', 'Dust mask'],
        instructions: 'Replace air filters, clean coils, check and clean drain pans.',
        nextDue: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
        isActive: true,
        priority: 'HIGH'
      },
      {
        type: MaintenanceType.PREVENTIVE,
        frequency: 'SEMI_ANNUAL',
        description: 'Semi-annual comprehensive maintenance',
        estimatedDuration: 120,
        requiredParts: ['Refrigerant', 'Belts', 'Lubricants'],
        requiredTools: ['Manifold gauges', 'Torque wrench', 'Leak detector'],
        skillLevel: 'INTERMEDIATE',
        safetyRequirements: ['Safety glasses', 'Work gloves', 'Refrigerant handling certification'],
        instructions: 'Check refrigerant levels, inspect electrical connections, lubricate moving parts, test safety controls.',
        nextDue: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
        isActive: true,
        priority: 'HIGH'
      },
      {
        type: MaintenanceType.CALIBRATION,
        frequency: 'ANNUAL',
        description: 'Annual calibration and performance testing',
        estimatedDuration: 180,
        requiredParts: [],
        requiredTools: ['Calibration equipment', 'Performance testing tools'],
        skillLevel: 'ADVANCED',
        safetyRequirements: ['Full PPE', 'Electrical safety training'],
        instructions: 'Calibrate all sensors and controls, perform comprehensive performance testing, update firmware if needed.',
        nextDue: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        isActive: true,
        priority: 'MEDIUM'
      }
    ];

    return baseSchedules.map(schedule => ({
      ...schedule,
      id: `maint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      equipmentId,
      createdAt: new Date(),
      updatedAt: new Date()
    }));
  }

  /**
   * Analyze equipment health and predict failures
   */
  static async analyzeEquipmentHealth(equipmentId: string): Promise<{
    healthScore: number;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    predictedFailures: Array<{
      component: string;
      probability: number;
      timeframe: string;
      impact: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
      recommendedAction: string;
    }>;
    maintenanceRecommendations: string[];
    replacementRecommendation?: {
      recommended: boolean;
      timeframe: string;
      reason: string;
      estimatedCost: number;
    };
  }> {
    const equipment = await this.getComprehensiveEquipmentRecord(equipmentId);
    if (!equipment) throw new Error('Equipment not found');

    // Calculate health score based on multiple factors
    const age = Math.floor((Date.now() - equipment.installation.installationDate.getTime()) / (365 * 24 * 60 * 60 * 1000));
    const maintenanceCompliance = equipment.maintenance.schedules.length > 0 ? 85 : 60;
    const performanceScore = equipment.performance.currentMetrics.healthScore;
    
    const healthScore = Math.round(
      (performanceScore * 0.4) +
      (maintenanceCompliance * 0.3) +
      (Math.max(0, 100 - age * 5) * 0.3)
    );

    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    if (healthScore >= 80) riskLevel = 'LOW';
    else if (healthScore >= 60) riskLevel = 'MEDIUM';
    else if (healthScore >= 40) riskLevel = 'HIGH';
    else riskLevel = 'CRITICAL';

    return {
      healthScore,
      riskLevel,
      predictedFailures: [
        {
          component: 'Compressor',
          probability: 15,
          timeframe: '6-12 months',
          impact: 'HIGH',
          recommendedAction: 'Monitor refrigerant levels and electrical connections'
        },
        {
          component: 'Fan Motor',
          probability: 25,
          timeframe: '3-6 months',
          impact: 'MEDIUM',
          recommendedAction: 'Lubricate bearings and check electrical connections'
        }
      ],
      maintenanceRecommendations: [
        'Schedule quarterly filter replacement',
        'Check refrigerant levels monthly',
        'Inspect electrical connections semi-annually'
      ],
      replacementRecommendation: age > 15 ? {
        recommended: true,
        timeframe: '1-2 years',
        reason: 'Equipment approaching end of useful life',
        estimatedCost: 8500
      } : undefined
    };
  }

  /**
   * Map device type to equipment category
   */
  private static mapDeviceTypeToCategory(deviceType: string): EquipmentCategory {
    const typeMap: Record<string, EquipmentCategory> = {
      'HVAC': EquipmentCategory.HVAC_UNIT,
      'AIR_CONDITIONER': EquipmentCategory.HVAC_UNIT,
      'FURNACE': EquipmentCategory.FURNACE,
      'HEAT_PUMP': EquipmentCategory.HEAT_PUMP,
      'THERMOSTAT': EquipmentCategory.THERMOSTAT
    };

    return typeMap[deviceType] || EquipmentCategory.OTHER;
  }

  /**
   * Determine equipment status
   */
  private static determineEquipmentStatus(equipment: any): EquipmentStatus {
    const now = new Date();
    
    if (equipment.warrantyEndDate && equipment.warrantyEndDate > now) {
      return EquipmentStatus.UNDER_WARRANTY;
    }
    
    if (equipment.nextMaintenanceDate && equipment.nextMaintenanceDate < now) {
      return EquipmentStatus.MAINTENANCE_REQUIRED;
    }
    
    return EquipmentStatus.OPERATIONAL;
  }
}
