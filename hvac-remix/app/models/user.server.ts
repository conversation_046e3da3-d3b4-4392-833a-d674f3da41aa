/**
 * User Model - Divine Quality Estate User Management
 * Comprehensive user management with security and role-based access
 */

import type { User, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { prisma } from '~/db.server';

export type { User, UserRole } from '@prisma/client';

export interface CreateUserData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: UserRole;
  companyId?: string;
}

export interface UpdateUserData {
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  isActive?: boolean;
  lastLoginAt?: Date;
}

export interface UserWithProfile extends User {
  company?: {
    id: string;
    name: string;
  };
  _count?: {
    sessions: number;
    auditLogs: number;
  };
}

/**
 * Get user by ID with optional profile data
 */
export async function getUserById(id: string, includeProfile = false): Promise<UserWithProfile | null> {
  try {
    return await prisma.user.findUnique({
      where: { id },
      include: includeProfile ? {
        company: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            sessions: true,
            auditLogs: true,
          },
        },
      } : undefined,
    });
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
}

/**
 * Get user by email
 */
export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    return await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
}

/**
 * Create new user with hashed password
 */
export async function createUser(userData: CreateUserData): Promise<User> {
  try {
    const hashedPassword = await bcrypt.hash(userData.password, 12);
    
    return await prisma.user.create({
      data: {
        email: userData.email.toLowerCase(),
        passwordHash: hashedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role || 'TECHNICIAN',
        companyId: userData.companyId,
        isActive: true,
        emailVerified: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('Error creating user:', error);
    throw new Error('Failed to create user');
  }
}

/**
 * Update user data
 */
export async function updateUser(id: string, userData: UpdateUserData): Promise<User | null> {
  try {
    return await prisma.user.update({
      where: { id },
      data: {
        ...userData,
        updatedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return null;
  }
}

/**
 * Delete user (soft delete by deactivating)
 */
export async function deleteUser(id: string): Promise<boolean> {
  try {
    await prisma.user.update({
      where: { id },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    });
    return true;
  } catch (error) {
    console.error('Error deleting user:', error);
    return false;
  }
}

/**
 * Verify user password
 */
export async function verifyLogin(email: string, password: string): Promise<User | null> {
  try {
    const user = await getUserByEmail(email);
    
    if (!user || !user.isActive) {
      return null;
    }
    
    const isValid = await bcrypt.compare(password, user.passwordHash);
    
    if (!isValid) {
      return null;
    }
    
    // Update last login
    await updateUser(user.id, { lastLoginAt: new Date() });
    
    return user;
  } catch (error) {
    console.error('Error verifying login:', error);
    return null;
  }
}

/**
 * Change user password
 */
export async function changePassword(id: string, newPassword: string): Promise<boolean> {
  try {
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    await prisma.user.update({
      where: { id },
      data: {
        passwordHash: hashedPassword,
        updatedAt: new Date(),
      },
    });
    
    return true;
  } catch (error) {
    console.error('Error changing password:', error);
    return false;
  }
}

/**
 * Get users by role
 */
export async function getUsersByRole(role: UserRole): Promise<User[]> {
  try {
    return await prisma.user.findMany({
      where: {
        role,
        isActive: true,
      },
      orderBy: {
        firstName: 'asc',
      },
    });
  } catch (error) {
    console.error('Error getting users by role:', error);
    return [];
  }
}

/**
 * Get users by company
 */
export async function getUsersByCompany(companyId: string): Promise<UserWithProfile[]> {
  try {
    return await prisma.user.findMany({
      where: {
        companyId,
        isActive: true,
      },
      include: {
        company: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        firstName: 'asc',
      },
    });
  } catch (error) {
    console.error('Error getting users by company:', error);
    return [];
  }
}

/**
 * Search users
 */
export async function searchUsers(query: string, limit = 10): Promise<UserWithProfile[]> {
  try {
    return await prisma.user.findMany({
      where: {
        isActive: true,
        OR: [
          {
            firstName: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            lastName: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            email: {
              contains: query,
              mode: 'insensitive',
            },
          },
        ],
      },
      include: {
        company: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      take: limit,
      orderBy: {
        firstName: 'asc',
      },
    });
  } catch (error) {
    console.error('Error searching users:', error);
    return [];
  }
}

/**
 * Get user statistics
 */
export async function getUserStats() {
  try {
    const [
      totalUsers,
      activeUsers,
      adminUsers,
      technicianUsers,
      managerUsers,
      recentLogins
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.user.count({ where: { role: 'ADMIN', isActive: true } }),
      prisma.user.count({ where: { role: 'TECHNICIAN', isActive: true } }),
      prisma.user.count({ where: { role: 'MANAGER', isActive: true } }),
      prisma.user.count({
        where: {
          lastLoginAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
          },
        },
      }),
    ]);

    return {
      total: totalUsers,
      active: activeUsers,
      inactive: totalUsers - activeUsers,
      byRole: {
        admin: adminUsers,
        technician: technicianUsers,
        manager: managerUsers,
      },
      recentLogins,
    };
  } catch (error) {
    console.error('Error getting user stats:', error);
    throw new Error('Failed to get user statistics');
  }
}

/**
 * Check if user has permission
 */
export function hasPermission(user: User, permission: string): boolean {
  const rolePermissions: Record<UserRole, string[]> = {
    ADMIN: ['*'], // Admin has all permissions
    MANAGER: [
      'users.read',
      'users.create',
      'users.update',
      'customers.read',
      'customers.create',
      'customers.update',
      'jobs.read',
      'jobs.create',
      'jobs.update',
      'reports.read',
      'analytics.read',
    ],
    TECHNICIAN: [
      'customers.read',
      'jobs.read',
      'jobs.update',
      'devices.read',
      'devices.update',
    ],
    CUSTOMER_SERVICE: [
      'customers.read',
      'customers.create',
      'customers.update',
      'jobs.read',
      'jobs.create',
    ],
  };

  const userPermissions = rolePermissions[user.role] || [];
  
  return userPermissions.includes('*') || userPermissions.includes(permission);
}

/**
 * Verify email address
 */
export async function verifyEmail(id: string): Promise<boolean> {
  try {
    await prisma.user.update({
      where: { id },
      data: {
        emailVerified: true,
        updatedAt: new Date(),
      },
    });
    
    return true;
  } catch (error) {
    console.error('Error verifying email:', error);
    return false;
  }
}
