/**
 * Mock User Model - Temporary for demo without database
 */

export type UserRole = 'ADMIN' | 'MANAGER' | 'TECHNICIAN' | 'CUSTOMER_SERVICE';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
  emailVerified: boolean;
  passwordHash: string;
  companyId?: string;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: UserRole;
  companyId?: string;
}

export interface UpdateUserData {
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  isActive?: boolean;
  lastLoginAt?: Date;
}

export interface UserWithProfile extends User {
  company?: {
    id: string;
    name: string;
  };
  _count?: {
    sessions: number;
    auditLogs: number;
  };
}

// Mock data
const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Jan',
    lastName: '<PERSON><PERSON><PERSON>',
    role: 'ADMIN',
    isActive: true,
    emailVerified: true,
    passwordHash: 'hashed_password',
    companyId: 'fulmark-1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    email: '<EMAIL>',
    firstName: 'Piotr',
    lastName: 'Nowak',
    role: 'TECHNICIAN',
    isActive: true,
    emailVerified: true,
    passwordHash: 'hashed_password',
    companyId: 'fulmark-1',
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

/**
 * Get user by ID with optional profile data
 */
export async function getUserById(id: string, includeProfile = false): Promise<UserWithProfile | null> {
  const user = mockUsers.find(u => u.id === id);
  if (!user) return null;

  if (includeProfile) {
    return {
      ...user,
      company: {
        id: 'fulmark-1',
        name: 'Fulmark Klimatyzacja'
      },
      _count: {
        sessions: 5,
        auditLogs: 25
      }
    };
  }

  return user;
}

/**
 * Get user by email
 */
export async function getUserByEmail(email: string): Promise<User | null> {
  return mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase()) || null;
}

/**
 * Create new user with hashed password
 */
export async function createUser(userData: CreateUserData): Promise<User> {
  const newUser: User = {
    id: String(mockUsers.length + 1),
    email: userData.email.toLowerCase(),
    passwordHash: 'hashed_' + userData.password,
    firstName: userData.firstName,
    lastName: userData.lastName,
    role: userData.role || 'TECHNICIAN',
    companyId: userData.companyId,
    isActive: true,
    emailVerified: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  mockUsers.push(newUser);
  return newUser;
}

/**
 * Update user data
 */
export async function updateUser(id: string, userData: UpdateUserData): Promise<User | null> {
  const userIndex = mockUsers.findIndex(u => u.id === id);
  if (userIndex === -1) return null;

  mockUsers[userIndex] = {
    ...mockUsers[userIndex],
    ...userData,
    updatedAt: new Date(),
  };

  return mockUsers[userIndex];
}

/**
 * Delete user (soft delete by deactivating)
 */
export async function deleteUser(id: string): Promise<boolean> {
  const userIndex = mockUsers.findIndex(u => u.id === id);
  if (userIndex === -1) return false;

  mockUsers[userIndex].isActive = false;
  mockUsers[userIndex].updatedAt = new Date();
  return true;
}

/**
 * Verify user password
 */
export async function verifyLogin(email: string, password: string): Promise<User | null> {
  const user = await getUserByEmail(email);
  
  if (!user || !user.isActive) {
    return null;
  }
  
  // Mock password verification
  if (password === 'demo123') {
    await updateUser(user.id, { lastLoginAt: new Date() });
    return user;
  }
  
  return null;
}

/**
 * Change user password
 */
export async function changePassword(id: string, newPassword: string): Promise<boolean> {
  const userIndex = mockUsers.findIndex(u => u.id === id);
  if (userIndex === -1) return false;

  mockUsers[userIndex].passwordHash = 'hashed_' + newPassword;
  mockUsers[userIndex].updatedAt = new Date();
  return true;
}

/**
 * Get users by role
 */
export async function getUsersByRole(role: UserRole): Promise<User[]> {
  return mockUsers.filter(u => u.role === role && u.isActive);
}

/**
 * Get users by company
 */
export async function getUsersByCompany(companyId: string): Promise<UserWithProfile[]> {
  return mockUsers
    .filter(u => u.companyId === companyId && u.isActive)
    .map(u => ({
      ...u,
      company: {
        id: 'fulmark-1',
        name: 'Fulmark Klimatyzacja'
      }
    }));
}

/**
 * Search users
 */
export async function searchUsers(query: string, limit = 10): Promise<UserWithProfile[]> {
  const filtered = mockUsers
    .filter(u => 
      u.isActive && (
        u.firstName.toLowerCase().includes(query.toLowerCase()) ||
        u.lastName.toLowerCase().includes(query.toLowerCase()) ||
        u.email.toLowerCase().includes(query.toLowerCase())
      )
    )
    .slice(0, limit);

  return filtered.map(u => ({
    ...u,
    company: {
      id: 'fulmark-1',
      name: 'Fulmark Klimatyzacja'
    }
  }));
}

/**
 * Get user statistics
 */
export async function getUserStats() {
  const activeUsers = mockUsers.filter(u => u.isActive);
  
  return {
    total: mockUsers.length,
    active: activeUsers.length,
    inactive: mockUsers.length - activeUsers.length,
    byRole: {
      admin: activeUsers.filter(u => u.role === 'ADMIN').length,
      technician: activeUsers.filter(u => u.role === 'TECHNICIAN').length,
      manager: activeUsers.filter(u => u.role === 'MANAGER').length,
    },
    recentLogins: activeUsers.filter(u => 
      u.lastLoginAt && u.lastLoginAt > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    ).length,
  };
}

/**
 * Check if user has permission
 */
export function hasPermission(user: User, permission: string): boolean {
  const rolePermissions: Record<UserRole, string[]> = {
    ADMIN: ['*'], // Admin has all permissions
    MANAGER: [
      'users.read',
      'users.create',
      'users.update',
      'customers.read',
      'customers.create',
      'customers.update',
      'jobs.read',
      'jobs.create',
      'jobs.update',
      'reports.read',
      'analytics.read',
    ],
    TECHNICIAN: [
      'customers.read',
      'jobs.read',
      'jobs.update',
      'devices.read',
      'devices.update',
    ],
    CUSTOMER_SERVICE: [
      'customers.read',
      'customers.create',
      'customers.update',
      'jobs.read',
      'jobs.create',
    ],
  };

  const userPermissions = rolePermissions[user.role] || [];
  
  return userPermissions.includes('*') || userPermissions.includes(permission);
}

/**
 * Verify email address
 */
export async function verifyEmail(id: string): Promise<boolean> {
  const userIndex = mockUsers.findIndex(u => u.id === id);
  if (userIndex === -1) return false;

  mockUsers[userIndex].emailVerified = true;
  mockUsers[userIndex].updatedAt = new Date();
  return true;
}
