/**
 * 🏆 SERVICE EXCELLENCE FEATURES
 * 
 * Advanced service management with SLA tracking, technician performance,
 * maintenance contracts, and inventory optimization.
 * 
 * Philosophy: "Excellence is not a skill, it's an attitude"
 */

import { prisma } from "~/db.server";

// 📋 Service Level Agreement (SLA) Types
export enum SLAType {
  EMERGENCY_RESPONSE = 'EMERGENCY_RESPONSE',
  STANDARD_SERVICE = 'STANDARD_SERVICE',
  MAINTENANCE_VISIT = 'MAINTENANCE_VISIT',
  INSTALLATION = 'INSTALLATION',
  REPAIR = 'REPAIR'
}

// 🎯 SLA Priority Levels
export enum SLAPriority {
  CRITICAL = 'CRITICAL',    // 2 hours
  HIGH = 'HIGH',           // 4 hours
  MEDIUM = 'MEDIUM',       // 24 hours
  LOW = 'LOW'              // 72 hours
}

// 📊 Technician Performance Metrics
export interface TechnicianPerformanceMetrics {
  technicianId: string;
  technicianName: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  metrics: {
    totalJobs: number;
    completedJobs: number;
    completionRate: number;
    averageJobDuration: number;
    customerSatisfactionScore: number;
    slaComplianceRate: number;
    firstTimeFixRate: number;
    revenueGenerated: number;
    costEfficiency: number;
    safetyScore: number;
  };
  rankings: {
    overallRank: number;
    satisfactionRank: number;
    efficiencyRank: number;
    revenueRank: number;
  };
}

// 📝 Service Level Agreement Definition
export interface ServiceLevelAgreement {
  id: string;
  customerId: string;
  contractNumber: string;
  slaType: SLAType;
  priority: SLAPriority;
  responseTimeHours: number;
  resolutionTimeHours: number;
  availabilityHours: string; // e.g., "24/7", "8-17 Mon-Fri"
  coverageDetails: {
    equipmentTypes: string[];
    serviceTypes: string[];
    excludedServices: string[];
  };
  penaltyClause: {
    enabled: boolean;
    penaltyPercentage: number;
    maxPenalty: number;
  };
  isActive: boolean;
  startDate: Date;
  endDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 🔧 Maintenance Contract
export interface MaintenanceContract {
  id: string;
  customerId: string;
  contractNumber: string;
  contractType: 'PREVENTIVE' | 'COMPREHENSIVE' | 'EMERGENCY_ONLY';
  equipmentCovered: string[];
  serviceFrequency: 'MONTHLY' | 'QUARTERLY' | 'SEMI_ANNUAL' | 'ANNUAL';
  nextServiceDate: Date;
  contractValue: number;
  paymentSchedule: 'MONTHLY' | 'QUARTERLY' | 'ANNUAL' | 'UPFRONT';
  autoRenewal: boolean;
  terms: {
    responseTime: number;
    partsIncluded: boolean;
    laborIncluded: boolean;
    emergencyService: boolean;
    discountRate: number;
  };
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'SUSPENDED';
  startDate: Date;
  endDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 📦 Inventory Item with Smart Optimization
export interface InventoryItem {
  id: string;
  partNumber: string;
  name: string;
  description: string;
  category: string;
  manufacturer: string;
  currentStock: number;
  minimumStock: number;
  maximumStock: number;
  reorderPoint: number;
  reorderQuantity: number;
  unitCost: number;
  sellingPrice: number;
  location: string;
  supplier: string;
  leadTimeDays: number;
  usageRate: number; // parts per month
  lastOrderDate?: Date;
  nextOrderDate?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 📈 Inventory Optimization Recommendation
export interface InventoryOptimizationRecommendation {
  itemId: string;
  partNumber: string;
  name: string;
  currentStock: number;
  recommendedAction: 'ORDER_NOW' | 'ORDER_SOON' | 'REDUCE_STOCK' | 'DISCONTINUE';
  recommendedQuantity?: number;
  reasoning: string;
  urgency: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  potentialSavings?: number;
  riskLevel: 'HIGH' | 'MEDIUM' | 'LOW';
}

/**
 * 🏆 Service Excellence Management Service
 */
export class ServiceExcellenceService {

  /**
   * Calculate technician performance metrics for a given period
   */
  static async calculateTechnicianPerformance(
    technicianId: string,
    startDate: Date,
    endDate: Date
  ): Promise<TechnicianPerformanceMetrics> {
    
    // Get technician info
    const technician = await prisma.user.findUnique({
      where: { id: technicianId }
    });

    if (!technician) {
      throw new Error('Technician not found');
    }

    // Get service orders for the period
    const serviceOrders = await prisma.serviceOrder.findMany({
      where: {
        assignedTechnicianId: technicianId,
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        customer: true,
        invoices: true
      }
    });

    const totalJobs = serviceOrders.length;
    const completedJobs = serviceOrders.filter(so => so.status === 'COMPLETED').length;
    const completionRate = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0;

    // Calculate average job duration
    const jobsWithDuration = serviceOrders.filter(so => 
      so.scheduledDate && so.completedDate
    );
    const totalDuration = jobsWithDuration.reduce((sum, so) => {
      const duration = so.completedDate!.getTime() - so.scheduledDate!.getTime();
      return sum + (duration / (1000 * 60 * 60)); // Convert to hours
    }, 0);
    const averageJobDuration = jobsWithDuration.length > 0 ? 
      totalDuration / jobsWithDuration.length : 0;

    // Calculate customer satisfaction
    const ratedJobs = serviceOrders.filter(so => so.customerRating);
    const totalRating = ratedJobs.reduce((sum, so) => sum + (so.customerRating || 0), 0);
    const customerSatisfactionScore = ratedJobs.length > 0 ? 
      (totalRating / ratedJobs.length) * 20 : 0; // Convert 1-5 scale to 0-100

    // Calculate revenue generated
    const revenueGenerated = serviceOrders.reduce((sum, so) => {
      return sum + so.invoices.reduce((invSum, inv) => 
        invSum + (inv.totalAmount || 0), 0
      );
    }, 0);

    // Placeholder calculations for other metrics
    const slaComplianceRate = 95; // Would calculate based on actual SLA data
    const firstTimeFixRate = 85; // Would calculate based on repeat visits
    const costEfficiency = 90; // Would calculate based on cost vs revenue
    const safetyScore = 98; // Would integrate with safety incident tracking

    return {
      technicianId,
      technicianName: technician.name || 'Unknown',
      period: { startDate, endDate },
      metrics: {
        totalJobs,
        completedJobs,
        completionRate: Math.round(completionRate),
        averageJobDuration: Math.round(averageJobDuration * 100) / 100,
        customerSatisfactionScore: Math.round(customerSatisfactionScore),
        slaComplianceRate,
        firstTimeFixRate,
        revenueGenerated,
        costEfficiency,
        safetyScore
      },
      rankings: {
        overallRank: 1, // Would calculate based on comparison with other technicians
        satisfactionRank: 1,
        efficiencyRank: 1,
        revenueRank: 1
      }
    };
  }

  /**
   * Check SLA compliance for a service order
   */
  static async checkSLACompliance(serviceOrderId: string): Promise<{
    isCompliant: boolean;
    responseTimeCompliant: boolean;
    resolutionTimeCompliant: boolean;
    details: {
      responseTimeActual: number;
      responseTimeRequired: number;
      resolutionTimeActual?: number;
      resolutionTimeRequired: number;
    };
  }> {
    const serviceOrder = await prisma.serviceOrder.findUnique({
      where: { id: serviceOrderId },
      include: { customer: true }
    });

    if (!serviceOrder) {
      throw new Error('Service order not found');
    }

    // Get SLA for this customer/service type (placeholder logic)
    const slaResponseTime = this.getSLAResponseTime(serviceOrder.priority);
    const slaResolutionTime = this.getSLAResolutionTime(serviceOrder.priority);

    const createdAt = serviceOrder.createdAt.getTime();
    const scheduledAt = serviceOrder.scheduledDate?.getTime();
    const completedAt = serviceOrder.completedDate?.getTime();

    // Calculate actual response time (time to schedule)
    const responseTimeActual = scheduledAt ? 
      (scheduledAt - createdAt) / (1000 * 60 * 60) : 0; // hours

    // Calculate actual resolution time (time to complete)
    const resolutionTimeActual = completedAt ? 
      (completedAt - createdAt) / (1000 * 60 * 60) : undefined; // hours

    const responseTimeCompliant = responseTimeActual <= slaResponseTime;
    const resolutionTimeCompliant = resolutionTimeActual ? 
      resolutionTimeActual <= slaResolutionTime : true;

    return {
      isCompliant: responseTimeCompliant && resolutionTimeCompliant,
      responseTimeCompliant,
      resolutionTimeCompliant,
      details: {
        responseTimeActual,
        responseTimeRequired: slaResponseTime,
        resolutionTimeActual,
        resolutionTimeRequired: slaResolutionTime
      }
    };
  }

  /**
   * Generate inventory optimization recommendations
   */
  static async generateInventoryOptimizationRecommendations(): Promise<InventoryOptimizationRecommendation[]> {
    // This would integrate with actual inventory data
    // For now, returning placeholder recommendations
    return [
      {
        itemId: '1',
        partNumber: 'FILTER-001',
        name: 'HVAC Air Filter 20x25x1',
        currentStock: 5,
        recommendedAction: 'ORDER_NOW',
        recommendedQuantity: 50,
        reasoning: 'Stock below reorder point, high usage rate',
        urgency: 'CRITICAL',
        potentialSavings: 0,
        riskLevel: 'HIGH'
      },
      {
        itemId: '2',
        partNumber: 'COIL-002',
        name: 'Evaporator Coil Assembly',
        currentStock: 25,
        recommendedAction: 'REDUCE_STOCK',
        reasoning: 'Overstocked, low usage rate',
        urgency: 'LOW',
        potentialSavings: 1500,
        riskLevel: 'LOW'
      }
    ];
  }

  /**
   * Get SLA response time based on priority
   */
  private static getSLAResponseTime(priority: string): number {
    switch (priority) {
      case 'URGENT': return 2; // 2 hours
      case 'HIGH': return 4;   // 4 hours
      case 'MEDIUM': return 24; // 24 hours
      case 'LOW': return 72;   // 72 hours
      default: return 24;
    }
  }

  /**
   * Get SLA resolution time based on priority
   */
  private static getSLAResolutionTime(priority: string): number {
    switch (priority) {
      case 'URGENT': return 8;   // 8 hours
      case 'HIGH': return 24;    // 24 hours
      case 'MEDIUM': return 72;  // 72 hours
      case 'LOW': return 168;    // 1 week
      default: return 72;
    }
  }
}
