/**
 * 📄 DOCUMENT MANAGEMENT SYSTEM
 * 
 * Comprehensive document storage and organization for contracts,
 * warranties, service agreements, photos, and technical documentation.
 * 
 * Philosophy: "Documents are the memory of our business relationships"
 */

import { prisma } from "~/db.server";
import { DocumentType, CustomerDocument } from "./customer-profile-management.server";

// 📁 Document Category
export enum DocumentCategory {
  LEGAL = 'LEGAL',
  FINANCIAL = 'FINANCIAL',
  TECHNICAL = 'TECHNICAL',
  OPERATIONAL = 'OPERATIONAL',
  COMPLIANCE = 'COMPLIANCE',
  MARKETING = 'MARKETING'
}

// 🔒 Access Level
export enum DocumentAccessLevel {
  PUBLIC = 'PUBLIC',           // Visible to customer
  INTERNAL = 'INTERNAL',       // Internal use only
  CONFIDENTIAL = 'CONFIDENTIAL', // Restricted access
  RESTRICTED = 'RESTRICTED'    // Admin only
}

// 📋 Document Template
export interface DocumentTemplate {
  id: string;
  name: string;
  description: string;
  type: DocumentType;
  category: DocumentCategory;
  template: string; // HTML template with placeholders
  requiredFields: Array<{
    name: string;
    type: 'text' | 'number' | 'date' | 'boolean' | 'select';
    required: boolean;
    options?: string[];
  }>;
  isActive: boolean;
  version: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// 📊 Document Analytics
export interface DocumentAnalytics {
  totalDocuments: number;
  byType: Record<DocumentType, number>;
  byCategory: Record<DocumentCategory, number>;
  storageUsed: number; // in MB
  recentActivity: Array<{
    action: 'UPLOADED' | 'VIEWED' | 'DOWNLOADED' | 'SHARED' | 'DELETED';
    documentId: string;
    documentName: string;
    userId: string;
    timestamp: Date;
  }>;
  expiringDocuments: Array<{
    documentId: string;
    documentName: string;
    expirationDate: Date;
    daysUntilExpiration: number;
  }>;
  accessStatistics: {
    mostViewed: Array<{
      documentId: string;
      documentName: string;
      viewCount: number;
    }>;
    recentlyAccessed: Array<{
      documentId: string;
      documentName: string;
      lastAccessed: Date;
    }>;
  };
}

// 🔍 Document Search Result
export interface DocumentSearchResult {
  document: CustomerDocument;
  relevanceScore: number;
  matchedFields: string[];
  highlights: Record<string, string>;
}

// 📤 Document Share
export interface DocumentShare {
  id: string;
  documentId: string;
  sharedBy: string;
  sharedWith: string; // email or user ID
  accessLevel: 'VIEW' | 'DOWNLOAD' | 'EDIT';
  expirationDate?: Date;
  password?: string;
  downloadCount: number;
  maxDownloads?: number;
  isActive: boolean;
  createdAt: Date;
  lastAccessed?: Date;
}

// 📝 Document Version
export interface DocumentVersion {
  id: string;
  documentId: string;
  version: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  uploadedBy: string;
  changeLog: string;
  isActive: boolean;
  createdAt: Date;
}

/**
 * 📄 Document Management Service
 */
export class DocumentManagementService {

  /**
   * Upload document
   */
  static async uploadDocument(
    documentData: Omit<CustomerDocument, 'id' | 'createdAt' | 'updatedAt'>,
    file: File
  ): Promise<CustomerDocument> {
    
    // In production, upload file to cloud storage
    const filePath = await this.uploadFileToStorage(file);
    
    const document: CustomerDocument = {
      ...documentData,
      id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      filePath,
      fileSize: file.size,
      mimeType: file.type,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // In production, save to database
    await this.indexDocumentForSearch(document);
    
    return document;
  }

  /**
   * Get documents for customer
   */
  static async getCustomerDocuments(
    customerId: string,
    filters?: {
      type?: DocumentType;
      category?: DocumentCategory;
      isPublic?: boolean;
      tags?: string[];
    }
  ): Promise<CustomerDocument[]> {
    
    // In production, query database with filters
    const sampleDocuments: CustomerDocument[] = [
      {
        id: 'doc_1',
        customerId,
        type: DocumentType.CONTRACT,
        title: 'Annual Service Contract 2024',
        description: 'Comprehensive HVAC maintenance contract',
        fileName: 'service_contract_2024.pdf',
        filePath: '/documents/contracts/service_contract_2024.pdf',
        fileSize: 245760,
        mimeType: 'application/pdf',
        uploadedBy: 'user_1',
        isPublic: true,
        expirationDate: new Date('2024-12-31'),
        tags: ['contract', 'maintenance', '2024'],
        metadata: {
          contractValue: 2400,
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        },
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: 'doc_2',
        customerId,
        type: DocumentType.WARRANTY,
        title: 'HVAC Unit Warranty Certificate',
        description: 'Manufacturer warranty for main HVAC unit',
        fileName: 'hvac_warranty.pdf',
        filePath: '/documents/warranties/hvac_warranty.pdf',
        fileSize: 156432,
        mimeType: 'application/pdf',
        uploadedBy: 'user_1',
        isPublic: true,
        expirationDate: new Date('2029-06-15'),
        tags: ['warranty', 'hvac', 'manufacturer'],
        metadata: {
          manufacturer: 'Carrier',
          model: 'XYZ-3000',
          serialNumber: 'ABC123456'
        },
        createdAt: new Date('2024-06-15'),
        updatedAt: new Date('2024-06-15')
      },
      {
        id: 'doc_3',
        customerId,
        equipmentId: 'equip_1',
        type: DocumentType.PHOTO,
        title: 'Installation Photos - Main Unit',
        description: 'Photos taken during HVAC installation',
        fileName: 'installation_photos.zip',
        filePath: '/documents/photos/installation_photos.zip',
        fileSize: 5242880,
        mimeType: 'application/zip',
        uploadedBy: 'tech_1',
        isPublic: false,
        tags: ['photos', 'installation', 'documentation'],
        metadata: {
          photoCount: 12,
          installationDate: '2024-06-10'
        },
        createdAt: new Date('2024-06-10'),
        updatedAt: new Date('2024-06-10')
      }
    ];

    let filteredDocuments = sampleDocuments;

    if (filters?.type) {
      filteredDocuments = filteredDocuments.filter(doc => doc.type === filters.type);
    }

    if (filters?.isPublic !== undefined) {
      filteredDocuments = filteredDocuments.filter(doc => doc.isPublic === filters.isPublic);
    }

    if (filters?.tags && filters.tags.length > 0) {
      filteredDocuments = filteredDocuments.filter(doc => 
        filters.tags!.some(tag => doc.tags.includes(tag))
      );
    }

    return filteredDocuments;
  }

  /**
   * Search documents
   */
  static async searchDocuments(
    query: string,
    filters?: {
      customerId?: string;
      type?: DocumentType;
      category?: DocumentCategory;
      dateRange?: { start: Date; end: Date };
    }
  ): Promise<DocumentSearchResult[]> {
    
    // In production, use full-text search engine
    const allDocuments = await this.getCustomerDocuments(filters?.customerId || '');
    
    const results: DocumentSearchResult[] = allDocuments
      .filter(doc => {
        const searchText = `${doc.title} ${doc.description} ${doc.tags.join(' ')}`.toLowerCase();
        return searchText.includes(query.toLowerCase());
      })
      .map(doc => ({
        document: doc,
        relevanceScore: this.calculateRelevanceScore(doc, query),
        matchedFields: this.getMatchedFields(doc, query),
        highlights: this.generateHighlights(doc, query)
      }))
      .sort((a, b) => b.relevanceScore - a.relevanceScore);

    return results;
  }

  /**
   * Generate document from template
   */
  static async generateDocumentFromTemplate(
    templateId: string,
    data: Record<string, any>,
    customerId: string
  ): Promise<CustomerDocument> {
    
    const template = await this.getDocumentTemplate(templateId);
    if (!template) throw new Error('Template not found');

    // Generate document content from template
    const content = this.processTemplate(template.template, data);
    
    // Convert to PDF (in production, use PDF generation service)
    const fileName = `${template.name}_${Date.now()}.pdf`;
    const filePath = `/documents/generated/${fileName}`;

    const document: CustomerDocument = {
      id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      customerId,
      type: template.type,
      title: `${template.name} - ${new Date().toLocaleDateString()}`,
      description: `Generated from template: ${template.name}`,
      fileName,
      filePath,
      fileSize: content.length,
      mimeType: 'application/pdf',
      uploadedBy: 'system',
      isPublic: false,
      tags: ['generated', 'template', template.name.toLowerCase()],
      metadata: {
        templateId,
        templateVersion: template.version,
        generatedData: data
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return document;
  }

  /**
   * Get document analytics
   */
  static async getDocumentAnalytics(customerId?: string): Promise<DocumentAnalytics> {
    
    // In production, query actual data
    return {
      totalDocuments: 156,
      byType: {
        [DocumentType.CONTRACT]: 23,
        [DocumentType.WARRANTY]: 45,
        [DocumentType.SERVICE_AGREEMENT]: 18,
        [DocumentType.INVOICE]: 34,
        [DocumentType.PHOTO]: 28,
        [DocumentType.TECHNICAL_DRAWING]: 5,
        [DocumentType.PERMIT]: 2,
        [DocumentType.CERTIFICATE]: 1,
        [DocumentType.MANUAL]: 0,
        [DocumentType.OTHER]: 0
      },
      byCategory: {
        [DocumentCategory.LEGAL]: 41,
        [DocumentCategory.FINANCIAL]: 34,
        [DocumentCategory.TECHNICAL]: 33,
        [DocumentCategory.OPERATIONAL]: 28,
        [DocumentCategory.COMPLIANCE]: 3,
        [DocumentCategory.MARKETING]: 17
      },
      storageUsed: 2847, // MB
      recentActivity: [
        {
          action: 'UPLOADED',
          documentId: 'doc_123',
          documentName: 'Service Report - Unit A',
          userId: 'tech_1',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
          action: 'VIEWED',
          documentId: 'doc_456',
          documentName: 'Warranty Certificate',
          userId: 'customer_1',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
        }
      ],
      expiringDocuments: [
        {
          documentId: 'doc_789',
          documentName: 'Service Contract 2024',
          expirationDate: new Date('2024-12-31'),
          daysUntilExpiration: 45
        }
      ],
      accessStatistics: {
        mostViewed: [
          {
            documentId: 'doc_contract',
            documentName: 'Annual Service Contract',
            viewCount: 23
          }
        ],
        recentlyAccessed: [
          {
            documentId: 'doc_warranty',
            documentName: 'HVAC Warranty',
            lastAccessed: new Date()
          }
        ]
      }
    };
  }

  /**
   * Share document
   */
  static async shareDocument(
    documentId: string,
    shareData: Omit<DocumentShare, 'id' | 'downloadCount' | 'createdAt'>
  ): Promise<DocumentShare> {
    
    const share: DocumentShare = {
      ...shareData,
      id: `share_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      downloadCount: 0,
      createdAt: new Date()
    };

    // In production, save to database and send notification
    return share;
  }

  /**
   * Get document templates
   */
  static async getDocumentTemplates(type?: DocumentType): Promise<DocumentTemplate[]> {
    
    const templates: DocumentTemplate[] = [
      {
        id: 'template_service_contract',
        name: 'Annual Service Contract',
        description: 'Standard annual HVAC service contract template',
        type: DocumentType.CONTRACT,
        category: DocumentCategory.LEGAL,
        template: '<html><body><h1>Service Contract</h1><p>Customer: {{customerName}}</p><p>Service Period: {{startDate}} to {{endDate}}</p></body></html>',
        requiredFields: [
          { name: 'customerName', type: 'text', required: true },
          { name: 'startDate', type: 'date', required: true },
          { name: 'endDate', type: 'date', required: true },
          { name: 'contractValue', type: 'number', required: true }
        ],
        isActive: true,
        version: '1.0',
        createdBy: 'admin',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    return type ? templates.filter(t => t.type === type) : templates;
  }

  /**
   * Upload file to storage (placeholder)
   */
  private static async uploadFileToStorage(file: File): Promise<string> {
    // In production, upload to cloud storage (AWS S3, Google Cloud, etc.)
    return `/documents/${Date.now()}_${file.name}`;
  }

  /**
   * Index document for search
   */
  private static async indexDocumentForSearch(document: CustomerDocument): Promise<void> {
    // In production, index in search engine (Elasticsearch, etc.)
    console.log(`Indexing document ${document.id} for search`);
  }

  /**
   * Calculate relevance score
   */
  private static calculateRelevanceScore(document: CustomerDocument, query: string): number {
    let score = 0;
    const queryLower = query.toLowerCase();
    
    if (document.title.toLowerCase().includes(queryLower)) score += 10;
    if (document.description?.toLowerCase().includes(queryLower)) score += 5;
    document.tags.forEach(tag => {
      if (tag.toLowerCase().includes(queryLower)) score += 3;
    });
    
    return score;
  }

  /**
   * Get matched fields
   */
  private static getMatchedFields(document: CustomerDocument, query: string): string[] {
    const fields = [];
    const queryLower = query.toLowerCase();
    
    if (document.title.toLowerCase().includes(queryLower)) fields.push('title');
    if (document.description?.toLowerCase().includes(queryLower)) fields.push('description');
    if (document.tags.some(tag => tag.toLowerCase().includes(queryLower))) fields.push('tags');
    
    return fields;
  }

  /**
   * Generate highlights
   */
  private static generateHighlights(document: CustomerDocument, query: string): Record<string, string> {
    const highlights: Record<string, string> = {};
    const queryLower = query.toLowerCase();
    
    if (document.title.toLowerCase().includes(queryLower)) {
      highlights.title = document.title.replace(
        new RegExp(query, 'gi'),
        `<mark>$&</mark>`
      );
    }
    
    return highlights;
  }

  /**
   * Get document template
   */
  private static async getDocumentTemplate(templateId: string): Promise<DocumentTemplate | null> {
    const templates = await this.getDocumentTemplates();
    return templates.find(t => t.id === templateId) || null;
  }

  /**
   * Process template with data
   */
  private static processTemplate(template: string, data: Record<string, any>): string {
    let processed = template;
    
    Object.entries(data).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      processed = processed.replace(new RegExp(placeholder, 'g'), String(value));
    });
    
    return processed;
  }
}
