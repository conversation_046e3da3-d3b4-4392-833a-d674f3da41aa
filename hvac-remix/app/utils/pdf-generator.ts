/**
 * Utility for generating PDF documents
 * Uses jsPDF and html2canvas for PDF generation
 */
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

// PDF page sizes
export const PAGE_SIZES = {
  A4: { width: 210, height: 297 }, // mm
  LETTER: { width: 215.9, height: 279.4 }, // mm
  LEGAL: { width: 215.9, height: 355.6 }, // mm
};

// PDF orientation
export type PDFOrientation = 'portrait' | 'landscape';

// PDF options
export interface PDFOptions {
  filename?: string;
  pageSize?: keyof typeof PAGE_SIZES;
  orientation?: PDFOrientation;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  compress?: boolean;
}

// Default PDF options
const DEFAULT_OPTIONS: PDFOptions = {
  filename: 'document.pdf',
  pageSize: 'A4',
  orientation: 'portrait',
  margin: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10,
  },
  compress: true,
};

/**
 * Generate a PDF from an HTML element
 */
export async function generatePDFFromElement(
  element: HTMLElement,
  options: PDFOptions = {}
): Promise<jsPDF> {
  // Merge options with defaults
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const { pageSize, orientation, margin, compress } = mergedOptions;
  
  // Create PDF document
  const pdf = new jsPDF({
    orientation: orientation,
    unit: 'mm',
    format: pageSize,
    compress: compress,
  });
  
  // Get page dimensions
  const pageDimensions = PAGE_SIZES[pageSize || 'A4'];
  const pageWidth = orientation === 'portrait' ? pageDimensions.width : pageDimensions.height;
  const pageHeight = orientation === 'portrait' ? pageDimensions.height : pageDimensions.width;
  
  // Calculate content area
  const contentWidth = pageWidth - (margin?.left || 0) - (margin?.right || 0);
  
  // Render HTML to canvas
  const canvas = await html2canvas(element, {
    scale: 2, // Higher scale for better quality
    useCORS: true, // Allow loading cross-origin images
    logging: false,
  });
  
  // Calculate scaling to fit content width
  const imgWidth = contentWidth;
  const imgHeight = (canvas.height * imgWidth) / canvas.width;
  
  // Convert canvas to image
  const imgData = canvas.toDataURL('image/png');
  
  // Add image to PDF
  pdf.addImage(
    imgData,
    'PNG',
    margin?.left || 0,
    margin?.top || 0,
    imgWidth,
    imgHeight
  );
  
  // If content is taller than page, add more pages
  let heightLeft = imgHeight;
  let position = margin?.top || 0;
  
  heightLeft -= pageHeight - (margin?.top || 0) - (margin?.bottom || 0);
  
  while (heightLeft > 0) {
    position = 0 - (pageHeight - (margin?.bottom || 0)) + position;
    
    // Add new page
    pdf.addPage();
    
    // Add image to new page
    pdf.addImage(
      imgData,
      'PNG',
      margin?.left || 0,
      position,
      imgWidth,
      imgHeight
    );
    
    heightLeft -= pageHeight - (margin?.bottom || 0);
  }
  
  return pdf;
}

/**
 * Generate a PDF from HTML content
 */
export async function generatePDFFromHTML(
  htmlContent: string,
  options: PDFOptions = {}
): Promise<jsPDF> {
  // Create temporary container
  const container = document.createElement('div');
  container.innerHTML = htmlContent;
  container.style.position = 'absolute';
  container.style.left = '-9999px';
  container.style.top = '-9999px';
  
  // Add to document
  document.body.appendChild(container);
  
  try {
    // Generate PDF
    const pdf = await generatePDFFromElement(container, options);
    return pdf;
  } finally {
    // Clean up
    document.body.removeChild(container);
  }
}

/**
 * Save a PDF to file
 */
export function savePDF(pdf: jsPDF, filename: string = 'document.pdf'): void {
  pdf.save(filename);
}

/**
 * Open a PDF in a new window
 */
export function openPDF(pdf: jsPDF): void {
  const blob = pdf.output('blob');
  const url = URL.createObjectURL(blob);
  window.open(url, '_blank');
}

/**
 * Get a PDF as a data URL
 */
export function getPDFDataURL(pdf: jsPDF): string {
  return pdf.output('datauristring');
}

/**
 * Get a PDF as a blob
 */
export function getPDFBlob(pdf: jsPDF): Blob {
  return pdf.output('blob');
}
