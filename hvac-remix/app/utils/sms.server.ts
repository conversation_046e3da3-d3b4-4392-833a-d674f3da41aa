/**
 * SMS Service - Divine Quality Estate Communications
 * Comprehensive SMS functionality with templates and delivery tracking
 */

import { prisma } from '~/db.server';
import { recordMetric, log } from '~/utils/monitoring.server';

export interface SMSOptions {
  to: string;
  message: string;
  from?: string;
}

export interface SMSTemplate {
  name: string;
  message: string;
}

export interface SMSResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * SMS service class
 */
class SMSService {
  private apiKey: string;
  private apiUrl: string;
  private fromNumber: string;
  private templates: Map<string, SMSTemplate> = new Map();

  constructor() {
    this.apiKey = process.env.SMS_API_KEY || '';
    this.apiUrl = process.env.SMS_API_URL || 'https://api.twilio.com/2010-04-01';
    this.fromNumber = process.env.SMS_FROM_NUMBER || '';
    
    if (!this.apiKey) {
      console.warn('SMS_API_KEY not configured - SMS features will be limited');
    }
    
    this.loadTemplates();
  }

  /**
   * Load SMS templates
   */
  private loadTemplates() {
    // Service reminder template
    this.templates.set('service-reminder', {
      name: 'service-reminder',
      message: 'Hi {{customerName}}, your {{equipmentType}} is due for maintenance. Please call us to schedule: {{phoneNumber}}',
    });

    // Appointment confirmation template
    this.templates.set('appointment-confirmation', {
      name: 'appointment-confirmation',
      message: 'Hi {{customerName}}, your HVAC service is confirmed for {{date}} at {{time}}. Technician: {{technicianName}}',
    });

    // Job completion template
    this.templates.set('job-completed', {
      name: 'job-completed',
      message: 'Hi {{customerName}}, your HVAC service has been completed. Thank you for choosing {{companyName}}!',
    });

    // Emergency alert template
    this.templates.set('emergency-alert', {
      name: 'emergency-alert',
      message: 'URGENT: {{equipmentType}} alert at {{location}}. Issue: {{issue}}. Contact us immediately: {{phoneNumber}}',
    });

    // Payment reminder template
    this.templates.set('payment-reminder', {
      name: 'payment-reminder',
      message: 'Hi {{customerName}}, payment for invoice #{{invoiceNumber}} ({{amount}}) is due. Pay online: {{paymentLink}}',
    });
  }

  /**
   * Send SMS message
   */
  async sendSMS(options: SMSOptions): Promise<SMSResult> {
    try {
      if (!this.apiKey) {
        // Log SMS for development
        log({
          level: 'info',
          message: 'SMS would be sent',
          metadata: {
            to: options.to,
            message: options.message.substring(0, 50) + '...',
          },
        });
        
        return {
          success: true,
          messageId: 'dev-sms-' + Date.now(),
        };
      }

      // Format phone number
      const formattedTo = this.formatPhoneNumber(options.to);
      
      if (!formattedTo) {
        throw new Error('Invalid phone number format');
      }

      // Send SMS via API (Twilio example)
      const response = await fetch(`${this.apiUrl}/Accounts/${process.env.TWILIO_ACCOUNT_SID}/Messages.json`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${Buffer.from(`${process.env.TWILIO_ACCOUNT_SID}:${this.apiKey}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          To: formattedTo,
          From: options.from || this.fromNumber,
          Body: options.message,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`SMS API error: ${errorData.message || response.statusText}`);
      }

      const result = await response.json();

      // Record success metric
      recordMetric('sms_sent', 1, 'counter', { status: 'success' });

      // Log SMS sent
      await this.logSMSSent(options, result.sid);

      return {
        success: true,
        messageId: result.sid,
      };
    } catch (error) {
      console.error('Failed to send SMS:', error);

      // Record failure metric
      recordMetric('sms_sent', 1, 'counter', { status: 'failure' });

      // Log SMS failure
      await this.logSMSFailed(options, error instanceof Error ? error.message : 'Unknown error');

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Send SMS using template
   */
  async sendTemplateSMS(
    templateName: string,
    to: string,
    variables: Record<string, string>,
    options?: Partial<SMSOptions>
  ): Promise<SMSResult> {
    try {
      const template = this.templates.get(templateName);
      
      if (!template) {
        throw new Error(`SMS template '${templateName}' not found`);
      }

      // Replace variables in template
      const message = this.replaceVariables(template.message, variables);

      return await this.sendSMS({
        to,
        message,
        ...options,
      });
    } catch (error) {
      console.error('Failed to send template SMS:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Replace variables in template
   */
  private replaceVariables(template: string, variables: Record<string, string>): string {
    let result = template;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
    
    return result;
  }

  /**
   * Format phone number for SMS
   */
  private formatPhoneNumber(phoneNumber: string): string | null {
    // Remove all non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');
    
    // Check if it's a valid US/international number
    if (digits.length === 10) {
      return `+1${digits}`;
    } else if (digits.length === 11 && digits.startsWith('1')) {
      return `+${digits}`;
    } else if (digits.length > 11) {
      return `+${digits}`;
    }
    
    return null;
  }

  /**
   * Log SMS sent to database
   */
  private async logSMSSent(options: SMSOptions, messageId: string) {
    try {
      await prisma.smsLog.create({
        data: {
          to: options.to,
          message: options.message,
          messageId,
          status: 'sent',
          sentAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Failed to log SMS:', error);
    }
  }

  /**
   * Log SMS failure to database
   */
  private async logSMSFailed(options: SMSOptions, errorMessage: string) {
    try {
      await prisma.smsLog.create({
        data: {
          to: options.to,
          message: options.message,
          status: 'failed',
          error: errorMessage,
          sentAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Failed to log SMS failure:', error);
    }
  }

  /**
   * Get SMS statistics
   */
  async getSMSStats(days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const [totalSent, totalFailed] = await Promise.all([
        prisma.smsLog.count({
          where: {
            status: 'sent',
            sentAt: { gte: startDate },
          },
        }),
        prisma.smsLog.count({
          where: {
            status: 'failed',
            sentAt: { gte: startDate },
          },
        }),
      ]);

      return {
        totalSent,
        totalFailed,
        successRate: totalSent + totalFailed > 0 ? (totalSent / (totalSent + totalFailed)) * 100 : 0,
        period: `${days} days`,
      };
    } catch (error) {
      console.error('Failed to get SMS stats:', error);
      throw new Error('Failed to get SMS statistics');
    }
  }

  /**
   * Validate phone number
   */
  validatePhoneNumber(phoneNumber: string): boolean {
    const formatted = this.formatPhoneNumber(phoneNumber);
    return formatted !== null;
  }
}

// Export singleton instance
export const smsService = new SMSService();

// Export convenience functions
export const sendSMS = (options: SMSOptions) => smsService.sendSMS(options);
export const sendTemplateSMS = (
  templateName: string,
  to: string,
  variables: Record<string, string>,
  options?: Partial<SMSOptions>
) => smsService.sendTemplateSMS(templateName, to, variables, options);

/**
 * Send service reminder SMS
 */
export async function sendServiceReminderSMS(
  customerPhone: string,
  customerName: string,
  equipmentType: string,
  phoneNumber: string
): Promise<SMSResult> {
  return sendTemplateSMS('service-reminder', customerPhone, {
    customerName,
    equipmentType,
    phoneNumber,
  });
}

/**
 * Send appointment confirmation SMS
 */
export async function sendAppointmentConfirmationSMS(
  customerPhone: string,
  customerName: string,
  date: string,
  time: string,
  technicianName: string
): Promise<SMSResult> {
  return sendTemplateSMS('appointment-confirmation', customerPhone, {
    customerName,
    date,
    time,
    technicianName,
  });
}

/**
 * Send job completion SMS
 */
export async function sendJobCompletionSMS(
  customerPhone: string,
  customerName: string,
  companyName: string
): Promise<SMSResult> {
  return sendTemplateSMS('job-completed', customerPhone, {
    customerName,
    companyName,
  });
}

/**
 * Send emergency alert SMS
 */
export async function sendEmergencyAlertSMS(
  customerPhone: string,
  equipmentType: string,
  location: string,
  issue: string,
  phoneNumber: string
): Promise<SMSResult> {
  return sendTemplateSMS('emergency-alert', customerPhone, {
    equipmentType,
    location,
    issue,
    phoneNumber,
  });
}
