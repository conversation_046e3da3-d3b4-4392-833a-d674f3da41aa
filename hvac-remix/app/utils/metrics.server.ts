/**
 * Custom metrics for monitoring the HVAC CRM application
 * This file contains utilities for collecting and reporting metrics
 */

import { captureMessage , startTransaction } from './monitoring.server';

// Define metric types
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
}

// Define metric names
export enum MetricName {
  // Python backend metrics
  PYTHON_API_REQUEST = 'python_api_request',
  PYTHON_API_RESPONSE_TIME = 'python_api_response_time',
  PYTHON_API_ERROR = 'python_api_error',
  BIELIK_LLM_REQUEST = 'bielik_llm_request',
  BIELIK_LLM_RESPONSE_TIME = 'bielik_llm_response_time',
  BIELIK_LLM_ERROR = 'bielik_llm_error',
  BIELIK_LLM_TOKEN_COUNT = 'bielik_llm_token_count',
  
  // GraphQL metrics
  GRAPHQL_QUERY = 'graphql_query',
  GRAPHQL_MUTATION = 'graphql_mutation',
  GRAPHQL_RESPONSE_TIME = 'graphql_response_time',
  GRAPHQL_ERROR = 'graphql_error',
  
  // Database metrics
  DB_QUERY = 'db_query',
  DB_QUERY_TIME = 'db_query_time',
  DB_ERROR = 'db_error',
  
  // Cache metrics
  CACHE_HIT = 'cache_hit',
  CACHE_MISS = 'cache_miss',
  CACHE_SIZE = 'cache_size',
  
  // User metrics
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_SIGNUP = 'user_signup',
  USER_ERROR = 'user_error',
  
  // Feature usage metrics
  FEATURE_USAGE = 'feature_usage',
  
  // Performance metrics
  PAGE_LOAD_TIME = 'page_load_time',
  API_RESPONSE_TIME = 'api_response_time',
  
  // Error metrics
  ERROR_COUNT = 'error_count',
}

// In-memory storage for metrics (for development)
const metricsStore: Record<string, number> = {};

/**
 * Increment a counter metric
 * @param name Metric name
 * @param value Value to increment by (default: 1)
 * @param tags Additional tags for the metric
 */
export function incrementCounter(
  name: MetricName,
  value: number = 1,
  tags: Record<string, string> = {}
): void {
  const metricKey = getMetricKey(name, tags);
  
  if (!metricsStore[metricKey]) {
    metricsStore[metricKey] = 0;
  }
  
  metricsStore[metricKey] += value;
  
  // In production, send to monitoring service
  if (process.env.NODE_ENV === 'production') {
    captureMessage(`METRIC:${MetricType.COUNTER}:${metricKey}:${value}`);
  } else if (process.env.NODE_ENV === 'development') {
    console.log(`METRIC [${MetricType.COUNTER}] ${metricKey}: ${metricsStore[metricKey]}`);
  }
}

/**
 * Set a gauge metric
 * @param name Metric name
 * @param value Value to set
 * @param tags Additional tags for the metric
 */
export function setGauge(
  name: MetricName,
  value: number,
  tags: Record<string, string> = {}
): void {
  const metricKey = getMetricKey(name, tags);
  
  metricsStore[metricKey] = value;
  
  // In production, send to monitoring service
  if (process.env.NODE_ENV === 'production') {
    captureMessage(`METRIC:${MetricType.GAUGE}:${metricKey}:${value}`);
  } else if (process.env.NODE_ENV === 'development') {
    console.log(`METRIC [${MetricType.GAUGE}] ${metricKey}: ${value}`);
  }
}

/**
 * Record a histogram metric
 * @param name Metric name
 * @param value Value to record
 * @param tags Additional tags for the metric
 */
export function recordHistogram(
  name: MetricName,
  value: number,
  tags: Record<string, string> = {}
): void {
  const metricKey = getMetricKey(name, tags);
  
  // In production, send to monitoring service
  if (process.env.NODE_ENV === 'production') {
    captureMessage(`METRIC:${MetricType.HISTOGRAM}:${metricKey}:${value}`);
  } else if (process.env.NODE_ENV === 'development') {
    console.log(`METRIC [${MetricType.HISTOGRAM}] ${metricKey}: ${value}`);
  }
}

/**
 * Time a function and record its execution time as a histogram
 * @param name Metric name
 * @param fn Function to time
 * @param tags Additional tags for the metric
 * @returns The result of the function
 */
export async function timeFunction<T>(
  name: MetricName,
  fn: () => Promise<T>,
  tags: Record<string, string> = {}
): Promise<T> {
  const transaction = startTransaction(name, 'function');
  const startTime = performance.now();
  
  try {
    const result = await fn();
    return result;
  } finally {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    recordHistogram(name, duration, tags);
    
    if (transaction) {
      transaction.setMeasurement('duration_ms', duration);
      transaction.finish();
    }
  }
}

/**
 * Get a unique key for a metric based on its name and tags
 * @param name Metric name
 * @param tags Additional tags for the metric
 * @returns A unique key for the metric
 */
function getMetricKey(name: MetricName, tags: Record<string, string> = {}): string {
  const tagString = Object.entries(tags)
    .map(([key, value]) => `${key}:${value}`)
    .join(',');
  
  return tagString ? `${name}[${tagString}]` : name;
}

/**
 * Get all metrics (for development)
 * @returns All metrics
 */
export function getAllMetrics(): Record<string, number> {
  return { ...metricsStore };
}
