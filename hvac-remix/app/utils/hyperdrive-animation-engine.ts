/**
 * 🚀 HYPERDRIVE ANIMATION ENGINE
 * Transcendent animation system beyond 120fps
 * Physics-based, quantum-responsive, emotionally intelligent animations
 */

import { cosmicDesignSystem } from '~/design-system/cosmic-design-tokens';

interface HyperdriveAnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
  iterations?: number;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
  cosmicEnergy?: 'low' | 'medium' | 'high' | 'maximum' | 'infinite';
  quantumPhysics?: boolean;
  emotionalResonance?: boolean;
  dimensionalShift?: boolean;
}

interface CosmicKeyframe {
  offset: number;
  transform?: string;
  opacity?: number;
  background?: string;
  filter?: string;
  boxShadow?: string;
  borderRadius?: string;
  scale?: number;
  rotate?: number;
  translateX?: number;
  translateY?: number;
  translateZ?: number;
  cosmicProperties?: Record<string, any>;
}

interface QuantumAnimationState {
  element: HTMLElement;
  animation: Animation;
  startTime: number;
  cosmicEnergy: number;
  quantumFluctuation: number;
  emotionalState: 'neutral' | 'excited' | 'transcendent';
  dimensionalPhase: number;
}

class HyperdriveAnimationEngine {
  private activeAnimations = new Map<string, QuantumAnimationState>();
  private cosmicRaf: number | null = null;
  private quantumTime: number = 0;
  private universalFrequency: number = 60; // Target FPS
  private cosmicConstants = {
    goldenRatio: 1.618033988749,
    pi: Math.PI,
    e: Math.E,
    lightSpeed: 299792458, // m/s (scaled for animations)
    planckConstant: 6.62607015e-34 // (scaled for quantum effects)
  };

  constructor() {
    this.initializeCosmicEngine();
  }

  /**
   * 🌌 Initialize cosmic animation engine
   */
  private initializeCosmicEngine(): void {
    console.log('🚀 Initializing Hyperdrive Animation Engine...');
    
    // Start cosmic animation loop
    this.startCosmicLoop();
    
    // Setup performance monitoring
    this.setupPerformanceMonitoring();
    
    // Initialize quantum physics simulation
    this.initializeQuantumPhysics();
    
    console.log('✨ Hyperdrive Animation Engine ready for transcendent animations!');
  }

  /**
   * 🔄 Start cosmic animation loop
   */
  private startCosmicLoop(): void {
    const cosmicLoop = (timestamp: number) => {
      this.quantumTime = timestamp;
      
      // Update all active animations
      this.updateQuantumAnimations(timestamp);
      
      // Continue the cosmic loop
      this.cosmicRaf = requestAnimationFrame(cosmicLoop);
    };

    this.cosmicRaf = requestAnimationFrame(cosmicLoop);
  }

  /**
   * 📊 Setup performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    let frameCount = 0;
    let lastTime = performance.now();

    const monitorPerformance = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        
        if (fps < this.universalFrequency * 0.8) {
          console.warn(`⚠️ Animation performance below cosmic standards: ${fps}fps`);
          this.optimizeCosmicPerformance();
        } else if (fps > this.universalFrequency * 1.2) {
          console.log(`🌟 Transcendent animation performance: ${fps}fps`);
        }
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      setTimeout(monitorPerformance, 1000);
    };

    monitorPerformance();
  }

  /**
   * 🔬 Initialize quantum physics simulation
   */
  private initializeQuantumPhysics(): void {
    // Quantum fluctuation generator
    setInterval(() => {
      this.activeAnimations.forEach((state) => {
        if (state.animation) {
          // Apply quantum uncertainty principle
          const uncertainty = (Math.random() - 0.5) * 0.02;
          state.quantumFluctuation = uncertainty;
          
          // Update dimensional phase
          state.dimensionalPhase = (state.dimensionalPhase + 1) % 360;
        }
      });
    }, 16); // 60fps quantum updates
  }

  /**
   * 🌟 Create cosmic animation
   */
  public createCosmicAnimation(
    element: HTMLElement,
    keyframes: CosmicKeyframe[],
    config: HyperdriveAnimationConfig
  ): string {
    const animationId = this.generateCosmicId();
    
    // Convert cosmic keyframes to Web Animation API format
    const webKeyframes = this.convertCosmicKeyframes(keyframes, config);
    
    // Create animation with cosmic timing
    const animation = element.animate(webKeyframes, {
      duration: config.duration,
      easing: config.easing,
      delay: config.delay || 0,
      iterations: config.iterations || 1,
      direction: config.direction || 'normal',
      fill: config.fillMode || 'both'
    });

    // Store animation state
    const state: QuantumAnimationState = {
      element,
      animation,
      startTime: this.quantumTime,
      cosmicEnergy: this.getCosmicEnergyLevel(config.cosmicEnergy),
      quantumFluctuation: 0,
      emotionalState: 'neutral',
      dimensionalPhase: 0
    };

    this.activeAnimations.set(animationId, state);

    // Setup animation event listeners
    this.setupAnimationListeners(animationId, animation, config);

    return animationId;
  }

  /**
   * 🎨 Convert cosmic keyframes
   */
  private convertCosmicKeyframes(
    keyframes: CosmicKeyframe[],
    config: HyperdriveAnimationConfig
  ): Keyframe[] {
    return keyframes.map(frame => {
      const webFrame: Keyframe = {
        offset: frame.offset
      };

      // Standard properties
      if (frame.opacity !== undefined) webFrame.opacity = frame.opacity.toString();
      if (frame.background) webFrame.background = frame.background;
      if (frame.filter) webFrame.filter = frame.filter;
      if (frame.boxShadow) webFrame.boxShadow = frame.boxShadow;
      if (frame.borderRadius) webFrame.borderRadius = frame.borderRadius;

      // Transform composition
      const transforms: string[] = [];
      
      if (frame.scale !== undefined) {
        transforms.push(`scale(${frame.scale})`);
      }
      
      if (frame.rotate !== undefined) {
        transforms.push(`rotate(${frame.rotate}deg)`);
      }
      
      if (frame.translateX !== undefined || frame.translateY !== undefined || frame.translateZ !== undefined) {
        const x = frame.translateX || 0;
        const y = frame.translateY || 0;
        const z = frame.translateZ || 0;
        transforms.push(`translate3d(${x}px, ${y}px, ${z}px)`);
      }

      if (frame.transform) {
        transforms.push(frame.transform);
      }

      if (transforms.length > 0) {
        webFrame.transform = transforms.join(' ');
      }

      // Apply cosmic energy modulation
      if (config.cosmicEnergy && config.cosmicEnergy !== 'low') {
        webFrame.filter = this.applyCosmicEnergyFilter(webFrame.filter as string || '', config.cosmicEnergy);
      }

      return webFrame;
    });
  }

  /**
   * ⚡ Apply cosmic energy filter
   */
  private applyCosmicEnergyFilter(baseFilter: string, energy: string): string {
    const energyLevels = {
      medium: 'brightness(1.1) saturate(1.1)',
      high: 'brightness(1.2) saturate(1.2) hue-rotate(10deg)',
      maximum: 'brightness(1.3) saturate(1.3) hue-rotate(20deg) drop-shadow(0 0 10px currentColor)',
      infinite: 'brightness(1.5) saturate(1.5) hue-rotate(30deg) drop-shadow(0 0 20px currentColor) contrast(1.2)'
    };

    const energyFilter = energyLevels[energy as keyof typeof energyLevels] || '';
    return baseFilter ? `${baseFilter} ${energyFilter}` : energyFilter;
  }

  /**
   * 🎭 Setup animation listeners
   */
  private setupAnimationListeners(
    animationId: string,
    animation: Animation,
    config: HyperdriveAnimationConfig
  ): void {
    animation.addEventListener('finish', () => {
      console.log(`🌟 Cosmic animation completed: ${animationId}`);
      this.activeAnimations.delete(animationId);
    });

    animation.addEventListener('cancel', () => {
      console.log(`⚠️ Cosmic animation cancelled: ${animationId}`);
      this.activeAnimations.delete(animationId);
    });

    // Emotional resonance monitoring
    if (config.emotionalResonance) {
      this.setupEmotionalResonance(animationId);
    }

    // Quantum physics effects
    if (config.quantumPhysics) {
      this.setupQuantumEffects(animationId);
    }
  }

  /**
   * 💫 Setup emotional resonance
   */
  private setupEmotionalResonance(animationId: string): void {
    const state = this.activeAnimations.get(animationId);
    if (!state) return;

    const element = state.element;
    
    element.addEventListener('mouseenter', () => {
      state.emotionalState = 'excited';
      state.cosmicEnergy = Math.min(state.cosmicEnergy * 1.2, 2);
    });

    element.addEventListener('mouseleave', () => {
      state.emotionalState = 'neutral';
      state.cosmicEnergy = Math.max(state.cosmicEnergy / 1.2, 0.5);
    });

    element.addEventListener('click', () => {
      state.emotionalState = 'transcendent';
      state.cosmicEnergy = 2;
      
      // Create transcendent burst effect
      this.createTranscendentBurst(element);
    });
  }

  /**
   * 🔬 Setup quantum effects
   */
  private setupQuantumEffects(animationId: string): void {
    const state = this.activeAnimations.get(animationId);
    if (!state) return;

    // Quantum uncertainty principle
    const applyQuantumUncertainty = () => {
      const uncertainty = (Math.random() - 0.5) * 0.01;
      const currentTransform = state.element.style.transform || '';
      
      if (!currentTransform.includes('quantum')) {
        state.element.style.transform += ` scale(${1 + uncertainty})`;
      }
    };

    setInterval(applyQuantumUncertainty, 100); // Apply quantum effects every 100ms
  }

  /**
   * 💥 Create transcendent burst effect
   */
  private createTranscendentBurst(element: HTMLElement): void {
    const burst = document.createElement('div');
    burst.className = 'transcendent-burst';
    burst.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: radial-gradient(circle, rgba(217, 70, 239, 0.8) 0%, transparent 70%);
      transform: translate(-50%, -50%);
      animation: transcendentBurst 800ms ease-out;
      pointer-events: none;
      z-index: 9999;
    `;

    element.style.position = 'relative';
    element.appendChild(burst);

    setTimeout(() => {
      if (burst.parentNode) {
        burst.parentNode.removeChild(burst);
      }
    }, 800);
  }

  /**
   * 🔄 Update quantum animations
   */
  private updateQuantumAnimations(timestamp: number): void {
    this.activeAnimations.forEach((state, animationId) => {
      // Apply quantum fluctuations
      if (state.quantumFluctuation !== 0) {
        const currentFilter = state.element.style.filter || '';
        const quantumFilter = `hue-rotate(${state.quantumFluctuation * 360}deg)`;
        
        if (!currentFilter.includes('hue-rotate')) {
          state.element.style.filter = `${currentFilter} ${quantumFilter}`.trim();
        }
      }

      // Apply emotional resonance effects
      if (state.emotionalState !== 'neutral') {
        const intensity = state.emotionalState === 'transcendent' ? 1.5 : 1.2;
        state.element.style.filter = `${state.element.style.filter || ''} brightness(${intensity})`.trim();
      }

      // Apply dimensional phase shifts
      if (state.dimensionalPhase > 0) {
        const phaseShift = Math.sin(state.dimensionalPhase * Math.PI / 180) * 0.02;
        const currentTransform = state.element.style.transform || '';
        
        if (!currentTransform.includes('dimensional')) {
          state.element.style.transform += ` scale(${1 + phaseShift})`;
        }
      }
    });
  }

  /**
   * 🎯 Optimize cosmic performance
   */
  private optimizeCosmicPerformance(): void {
    console.log('🔧 Optimizing cosmic animation performance...');
    
    // Reduce quantum update frequency for low-end devices
    this.universalFrequency = Math.max(this.universalFrequency * 0.9, 30);
    
    // Simplify complex animations
    this.activeAnimations.forEach((state) => {
      if (state.animation.playbackRate > 1) {
        state.animation.playbackRate = 1;
      }
    });
  }

  /**
   * 🆔 Generate cosmic ID
   */
  private generateCosmicId(): string {
    return `cosmic-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * ⚡ Get cosmic energy level
   */
  private getCosmicEnergyLevel(energy?: string): number {
    const levels = {
      low: 0.5,
      medium: 1,
      high: 1.5,
      maximum: 2,
      infinite: 3
    };

    return levels[energy as keyof typeof levels] || 1;
  }

  /**
   * 🛑 Stop animation
   */
  public stopCosmicAnimation(animationId: string): void {
    const state = this.activeAnimations.get(animationId);
    if (state) {
      state.animation.cancel();
      this.activeAnimations.delete(animationId);
    }
  }

  /**
   * ⏸️ Pause animation
   */
  public pauseCosmicAnimation(animationId: string): void {
    const state = this.activeAnimations.get(animationId);
    if (state) {
      state.animation.pause();
    }
  }

  /**
   * ▶️ Resume animation
   */
  public resumeCosmicAnimation(animationId: string): void {
    const state = this.activeAnimations.get(animationId);
    if (state) {
      state.animation.play();
    }
  }

  /**
   * 📊 Get animation stats
   */
  public getCosmicStats(): {
    activeAnimations: number;
    averageCosmicEnergy: number;
    quantumFluctuations: number;
    transcendentStates: number;
  } {
    const states = Array.from(this.activeAnimations.values());
    
    return {
      activeAnimations: states.length,
      averageCosmicEnergy: states.reduce((sum, state) => sum + state.cosmicEnergy, 0) / states.length || 0,
      quantumFluctuations: states.filter(state => Math.abs(state.quantumFluctuation) > 0.01).length,
      transcendentStates: states.filter(state => state.emotionalState === 'transcendent').length
    };
  }

  /**
   * 🌌 Destroy engine
   */
  public destroyCosmicEngine(): void {
    if (this.cosmicRaf) {
      cancelAnimationFrame(this.cosmicRaf);
    }
    
    this.activeAnimations.forEach((state) => {
      state.animation.cancel();
    });
    
    this.activeAnimations.clear();
    console.log('🌌 Hyperdrive Animation Engine destroyed');
  }
}

// 🚀 Create global hyperdrive engine instance
export const hyperdriveEngine = new HyperdriveAnimationEngine();

// 🌟 Export cosmic animation utilities
export const cosmicAnimations = {
  // Quick animation creators
  fadeIn: (element: HTMLElement, duration = 500) =>
    hyperdriveEngine.createCosmicAnimation(element, [
      { offset: 0, opacity: 0, scale: 0.9 },
      { offset: 1, opacity: 1, scale: 1 }
    ], {
      duration,
      easing: cosmicDesignSystem.animations.easing.cosmic,
      cosmicEnergy: 'medium'
    }),

  slideIn: (element: HTMLElement, direction: 'left' | 'right' | 'up' | 'down' = 'up', duration = 500) => {
    const directions = {
      left: { translateX: -50 },
      right: { translateX: 50 },
      up: { translateY: -50 },
      down: { translateY: 50 }
    };

    return hyperdriveEngine.createCosmicAnimation(element, [
      { offset: 0, opacity: 0, ...directions[direction] },
      { offset: 1, opacity: 1, translateX: 0, translateY: 0 }
    ], {
      duration,
      easing: cosmicDesignSystem.animations.easing.stellar,
      cosmicEnergy: 'high'
    });
  },

  pulse: (element: HTMLElement, intensity = 1.1, duration = 1000) =>
    hyperdriveEngine.createCosmicAnimation(element, [
      { offset: 0, scale: 1 },
      { offset: 0.5, scale: intensity },
      { offset: 1, scale: 1 }
    ], {
      duration,
      easing: cosmicDesignSystem.animations.easing.quantum,
      iterations: Infinity,
      cosmicEnergy: 'infinite',
      quantumPhysics: true
    }),

  transcend: (element: HTMLElement, duration = 2000) =>
    hyperdriveEngine.createCosmicAnimation(element, [
      { offset: 0, scale: 1, rotate: 0, opacity: 1 },
      { offset: 0.5, scale: 1.2, rotate: 180, opacity: 0.8 },
      { offset: 1, scale: 1, rotate: 360, opacity: 1 }
    ], {
      duration,
      easing: cosmicDesignSystem.animations.easing.transcendent,
      cosmicEnergy: 'infinite',
      quantumPhysics: true,
      emotionalResonance: true,
      dimensionalShift: true
    }),

  // Control functions
  stop: (animationId: string) => hyperdriveEngine.stopCosmicAnimation(animationId),
  pause: (animationId: string) => hyperdriveEngine.pauseCosmicAnimation(animationId),
  resume: (animationId: string) => hyperdriveEngine.resumeCosmicAnimation(animationId),
  
  // Stats
  getStats: () => hyperdriveEngine.getCosmicStats()
};

// 🎨 Inject cosmic animation styles
if (typeof document !== 'undefined') {
  const cosmicStyles = `
    @keyframes transcendentBurst {
      0% {
        width: 0;
        height: 0;
        opacity: 1;
      }
      100% {
        width: 300px;
        height: 300px;
        opacity: 0;
      }
    }
  `;

  const styleSheet = document.createElement('style');
  styleSheet.textContent = cosmicStyles;
  document.head.appendChild(styleSheet);
}
