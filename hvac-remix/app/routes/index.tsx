import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { ArrowRightIcon, BarChart3Icon, CalendarIcon, ClipboardListIcon, UsersIcon, WrenchIcon } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { getUser } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await getUser(request);
  return json({ user });
}

export default function Index() {
  const { user } = useLoaderData<typeof loader>();

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              HVAC CRM Servicetool
            </h1>
            <p className="text-xl mb-8">
              Kompleksowy system zarządzania dla firm HVAC. Zarządzaj klientami, urządzeniami, zleceniami serwisowymi i więcej.
            </p>
            {user ? (
              <Link to="/dashboard">
                <Button size="lg" className="bg-white text-blue-700 hover:bg-blue-50">
                  Przejdź do panelu <ArrowRightIcon className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            ) : (
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/login">
                  <Button size="lg" className="bg-white text-blue-700 hover:bg-blue-50 w-full sm:w-auto">
                    Zaloguj się
                  </Button>
                </Link>
                <Link to="/join">
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-blue-700 w-full sm:w-auto">
                    Zarejestruj się
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Kluczowe funkcjonalności</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <UsersIcon className="h-8 w-8 text-blue-600 mb-2" />
                <CardTitle>Zarządzanie klientami</CardTitle>
                <CardDescription>
                  Przechowuj i zarządzaj danymi klientów w jednym miejscu
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Kompletna baza danych klientów z historią serwisową, urządzeniami i kontaktami.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <WrenchIcon className="h-8 w-8 text-blue-600 mb-2" />
                <CardTitle>Zarządzanie urządzeniami</CardTitle>
                <CardDescription>
                  Śledź wszystkie urządzenia klientów
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Pełna historia serwisowa, dane techniczne i harmonogramy konserwacji dla każdego urządzenia.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <ClipboardListIcon className="h-8 w-8 text-blue-600 mb-2" />
                <CardTitle>Zlecenia serwisowe</CardTitle>
                <CardDescription>
                  Zarządzaj zleceniami serwisowymi od początku do końca
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Tworzenie, przydzielanie i śledzenie zleceń serwisowych z powiadomieniami i raportami.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CalendarIcon className="h-8 w-8 text-blue-600 mb-2" />
                <CardTitle>Kalendarz i harmonogramowanie</CardTitle>
                <CardDescription>
                  Planuj wizyty serwisowe i zarządzaj harmonogramem
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Integracja z Microsoft Outlook Calendar, widoki dzień/tydzień/miesiąc i optymalizacja tras.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <BarChart3Icon className="h-8 w-8 text-blue-600 mb-2" />
                <CardTitle>Raporty i analityka</CardTitle>
                <CardDescription>
                  Analizuj dane biznesowe i generuj raporty
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Raporty finansowe, operacyjne i klienckie z interaktywnymi wizualizacjami.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <svg className="h-8 w-8 text-blue-600 mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" />
                  <path d="M12 7V12L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                </svg>
                <CardTitle>Praca offline</CardTitle>
                <CardDescription>
                  Pracuj nawet bez dostępu do internetu
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Synchronizacja danych, rozwiązywanie konfliktów i pełna funkcjonalność offline dla techników w terenie.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Gotowy, aby usprawnić swoją firmę HVAC?</h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Dołącz do setek firm HVAC, które już korzystają z naszego systemu do zwiększenia wydajności i zadowolenia klientów.
          </p>
          {user ? (
            <Link to="/dashboard">
              <Button size="lg" className="bg-blue-600 text-white hover:bg-blue-700">
                Przejdź do panelu <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          ) : (
            <Link to="/join">
              <Button size="lg" className="bg-blue-600 text-white hover:bg-blue-700">
                Rozpocznij teraz <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          )}
        </div>
      </section>
    </div>
  );
}
