import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation, Link, useSearchParams } from "@remix-run/react";
import * as React from "react";
import { useEffect, useRef, useState } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Checkbox } from "~/components/ui/checkbox";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "~/components/ui/select";
import { Textarea } from "~/components/ui/textarea";
import { createCalendarEntry } from "~/services/calendar.service";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);

  // Get date from URL or use current date
  const dateParam = url.searchParams.get("date");
  const date = dateParam || new Date().toISOString().split('T')[0];

  return json({ date });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const formData = await request.formData();

  const title = formData.get("title") as string;
  const description = formData.get("description") as string;
  const location = formData.get("location") as string;
  const startDate = formData.get("startDate") as string;
  const startTime = formData.get("startTime") as string;
  const endDate = formData.get("endDate") as string;
  const endTime = formData.get("endTime") as string;
  const isAllDay = formData.get("isAllDay") === "on";
  const color = formData.get("color") as string;

  // Validate required fields
  const errors: Record<string, string | null> = {
    title: title ? null : "Title is required",
    startDate: startDate ? null : "Start date is required",
    endDate: endDate ? null : "End date is required",
  };

  if (!isAllDay) {
    errors.startTime = startTime ? null : "Start time is required";
    errors.endTime = endTime ? null : "End time is required";
  }

  const hasErrors = Object.values(errors).some(
    (errorMessage) => errorMessage !== null
  );

  if (hasErrors) {
    const values = {
      title: title || "",
      description: description || "",
      location: location || "",
      startDate: startDate || "",
      endDate: endDate || "",
      startTime: startTime || "",
      endTime: endTime || "",
      color: color || "",
      isAllDay: isAllDay ? "on" : "",
    };
    return json({ errors, values });
  }

  // Create start and end times
  let startDateTime, endDateTime;

  if (isAllDay) {
    startDateTime = new Date(`${startDate}T00:00:00`);
    endDateTime = new Date(`${endDate}T23:59:59`);
  } else {
    startDateTime = new Date(`${startDate}T${startTime}`);
    endDateTime = new Date(`${endDate}T${endTime}`);
  }

  // Validate that end time is after start time
  if (endDateTime <= startDateTime) {
    return json({
      errors: {
        ...errors,
        endDate: "End time must be after start time",
      },
      values: {
        title: title || "",
        description: description || "",
        location: location || "",
        startDate: startDate || "",
        endDate: endDate || "",
        startTime: startTime || "",
        endTime: endTime || "",
        color: color || "",
        isAllDay: isAllDay ? "on" : "",
      },
    });
  }

  // Create calendar entry
  const calendarResponse = await createCalendarEntry(
    {
      title,
      description: description || null,
      location: location || null,
      startTime: startDateTime,
      endTime: endDateTime,
      isAllDay,
      color: color || "#3B82F6", // Default to blue
    },
    userId
  );

  if (!calendarResponse.success) {
    return json({
      errors: {
        ...errors,
        form: calendarResponse.error || "Failed to create calendar entry",
      },
      values: {
        title: title || "",
        description: description || "",
        location: location || "",
        startDate: startDate || "",
        endDate: endDate || "",
        startTime: startTime || "",
        endTime: endTime || "",
        color: color || "",
        isAllDay: isAllDay ? "on" : "",
      },
    });
  }

  return redirect("/calendar");
}

export default function NewCalendarEntryPage() {
  const { date } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  // Helper functions for safe access to actionData
  const getError = (field: string) => actionData?.errors && field in actionData.errors ? actionData.errors[field] : null;
  const getValue = (field: string) => actionData?.values && field in actionData.values ? actionData.values[field] : "";

  const titleRef = useRef<HTMLInputElement>(null);
  const [isAllDay, setIsAllDay] = useState(
    getValue('isAllDay') === "on" || false
  );

  // Focus on the title field when there's an error
  useEffect(() => {
    if (getError('title')) {
      titleRef.current?.focus();
    }
  }, [actionData]);

  // Set default times if not provided
  const defaultStartTime = "09:00";
  const defaultEndTime = "10:00";

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to="/calendar" className="text-blue-500 hover:underline">
          ← Back to Calendar
        </Link>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Create New Event</CardTitle>
          <CardDescription>
            Add a new event to your calendar
          </CardDescription>
        </CardHeader>

        <Form method="post">
          <CardContent className="space-y-4">
            {/* Form error message */}
            {getError('form') && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {String(getError('form'))}
              </div>
            )}

            {/* Title field */}
            <div className="space-y-2">
              <Label htmlFor="title">
                Title <span className="text-red-500">*</span>
              </Label>
              <Input
                ref={titleRef}
                id="title"
                name="title"
                defaultValue={getValue('title') as string}
                aria-invalid={getError('title') ? true : undefined}
                aria-errormessage={
                  getError('title') ? "title-error" : undefined
                }
              />
              {getError('title') && (
                <p className="text-red-500 text-sm" id="title-error">
                  {String(getError('title'))}
                </p>
              )}
            </div>

            {/* Description field */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                rows={3}
                defaultValue={getValue('description') as string}
              />
            </div>

            {/* Location field */}
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                name="location"
                defaultValue={getValue('location') as string}
              />
            </div>

            {/* All Day checkbox */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isAllDay"
                name="isAllDay"
                checked={isAllDay}
                onCheckedChange={(checked) => setIsAllDay(checked === true)}
              />
              <Label htmlFor="isAllDay">All Day Event</Label>
            </div>

            {/* Date and Time fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">
                  Start Date <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="startDate"
                  name="startDate"
                  type="date"
                  defaultValue={getValue('startDate') as string || date}
                  aria-invalid={getError('startDate') ? true : undefined}
                  aria-errormessage={
                    getError('startDate') ? "startDate-error" : undefined
                  }
                />
                {getError('startDate') && (
                  <p className="text-red-500 text-sm" id="startDate-error">
                    {String(getError('startDate'))}
                  </p>
                )}
              </div>

              {!isAllDay && (
                <div className="space-y-2">
                  <Label htmlFor="startTime">
                    Start Time <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="startTime"
                    name="startTime"
                    type="time"
                    defaultValue={getValue('startTime') as string || defaultStartTime}
                    aria-invalid={getError('startTime') ? true : undefined}
                    aria-errormessage={
                      getError('startTime') ? "startTime-error" : undefined
                    }
                  />
                  {getError('startTime') && (
                    <p className="text-red-500 text-sm" id="startTime-error">
                      {String(getError('startTime'))}
                    </p>
                  )}
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="endDate">
                  End Date <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="endDate"
                  name="endDate"
                  type="date"
                  defaultValue={getValue('endDate') as string || date}
                  aria-invalid={getError('endDate') ? true : undefined}
                  aria-errormessage={
                    getError('endDate') ? "endDate-error" : undefined
                  }
                />
                {getError('endDate') && (
                  <p className="text-red-500 text-sm" id="endDate-error">
                    {actionData.errors.endDate}
                  </p>
                )}
              </div>

              {!isAllDay && (
                <div className="space-y-2">
                  <Label htmlFor="endTime">
                    End Time <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="endTime"
                    name="endTime"
                    type="time"
                    defaultValue={getValue('endTime') as string || defaultEndTime}
                    aria-invalid={getError('endTime') ? true : undefined}
                    aria-errormessage={
                      getError('endTime') ? "endTime-error" : undefined
                    }
                  />
                  {getError('endTime') && (
                    <p className="text-red-500 text-sm" id="endTime-error">
                      {String(getError('endTime'))}
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Color field */}
            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <Select
                name="color"
                defaultValue={getValue('color') as string || "#3B82F6"}
              >
                <SelectTrigger id="color">
                  <SelectValue placeholder="Select a color" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="#3B82F6">Blue</SelectItem>
                  <SelectItem value="#10B981">Green</SelectItem>
                  <SelectItem value="#F59E0B">Yellow</SelectItem>
                  <SelectItem value="#EF4444">Red</SelectItem>
                  <SelectItem value="#8B5CF6">Purple</SelectItem>
                  <SelectItem value="#EC4899">Pink</SelectItem>
                  <SelectItem value="#6B7280">Gray</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Link to="/calendar">
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Event"}
            </Button>
          </CardFooter>
        </Form>
      </Card>
    </div>
  );
}
