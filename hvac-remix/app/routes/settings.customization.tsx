import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, Form, useActionData, useNavigation } from "@remix-run/react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Checkbox } from "~/components/ui/checkbox";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Textarea } from "~/components/ui/textarea";
import { EntityType, FieldType, createCustomField, getCustomFields } from "~/models/metadata.server";
import { createViewDefinition, getViewDefinitionsForEntity } from "~/models/view-definitions.server";
import { createWorkflowDefinition, getWorkflowDefinitionsForEntity } from "~/models/workflow-definitions.server";
import { requireUser } from "~/session.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await requireUser(request);

  // Get all entity types that can be customized
  const entityTypes = [
    { id: 'CUSTOMER', name: 'Customer' },
    { id: 'DEVICE', name: 'Device' },
    { id: 'SERVICE_ORDER', name: 'Service Order' },
    { id: 'INVOICE', name: 'Invoice' },
    { id: 'OFFER', name: 'Offer' },
    { id: 'INVENTORY_PART', name: 'Inventory Part' }
  ];

  // Get field types
  const fieldTypes = [
    { id: 'TEXT', name: 'Text' },
    { id: 'NUMBER', name: 'Number' },
    { id: 'DATE', name: 'Date' },
    { id: 'BOOLEAN', name: 'Checkbox' },
    { id: 'SELECT', name: 'Dropdown' },
    { id: 'MULTI_SELECT', name: 'Multi-Select' },
    { id: 'RICH_TEXT', name: 'Rich Text' },
    { id: 'EMAIL', name: 'Email' },
    { id: 'PHONE', name: 'Phone' },
    { id: 'URL', name: 'URL' }
  ];

  // Get custom fields for each entity type
  const customFields = await Promise.all(
    entityTypes.map(async (entityType) => ({
      entityType: entityType.id,
      fields: await getCustomFields(entityType.id as EntityType, user.id)
    }))
  );

  // Get view definitions for each entity type
  const viewDefinitions = await Promise.all(
    entityTypes.map(async (entityType) => ({
      entityType: entityType.id,
      views: await getViewDefinitionsForEntity(entityType.id as EntityType, user.id)
    }))
  );

  // Get workflow definitions for each entity type
  const workflowDefinitions = await Promise.all(
    entityTypes.map(async (entityType) => ({
      entityType: entityType.id,
      workflows: await getWorkflowDefinitionsForEntity(entityType.id as EntityType, user.id)
    }))
  );

  return json({
    user,
    entityTypes,
    fieldTypes,
    customFields,
    viewDefinitions,
    workflowDefinitions
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const user = await requireUser(request);
  const formData = await request.formData();

  const action = formData.get("action") as string;

  if (action === "create-field") {
    const entityType = formData.get("entityType") as EntityType;
    const name = formData.get("name") as string;
    const label = formData.get("label") as string;
    const fieldType = formData.get("fieldType") as FieldType;
    const required = formData.get("required") === "on";
    const helpText = formData.get("helpText") as string;
    const placeholder = formData.get("placeholder") as string;
    const defaultValue = formData.get("defaultValue") as string;

    // For SELECT and MULTI_SELECT, get options
    let options: string[] | undefined;
    if (fieldType === 'SELECT' || fieldType === 'MULTI_SELECT') {
      const optionsString = formData.get("options") as string;
      options = optionsString.split('\n').map(o => o.trim()).filter(o => o);
    }

    // Create custom field
    const result = await createCustomField({
      entityType,
      name,
      label,
      fieldType,
      required,
      helpText,
      placeholder,
      defaultValue,
      options,
      createdBy: user.id
    });

    if (!result.success) {
      return json({
        error: result.error,
        values: Object.fromEntries(formData)
      });
    }

    return json({ success: true });
  }

  if (action === "create-view") {
    const entityType = formData.get("entityType") as EntityType;
    const name = formData.get("name") as string;
    const layout = formData.get("layout") as string;
    const isDefault = formData.get("isDefault") === "on";
    const isShared = formData.get("isShared") === "on";

    // Create view definition
    const result = await createViewDefinition({
      name,
      entityType,
      layout,
      isDefault,
      isShared,
      createdBy: user.id
    });

    if (!result.success) {
      return json({
        viewError: result.error,
        viewValues: Object.fromEntries(formData)
      });
    }

    return json({ viewSuccess: true });
  }

  if (action === "create-workflow") {
    const entityType = formData.get("entityType") as EntityType;
    const name = formData.get("name") as string;
    const description = formData.get("description") as string;
    const definition = formData.get("definition") as string;
    const isActive = formData.get("isActive") === "on";
    const isShared = formData.get("isShared") === "on";

    // Create workflow definition
    const result = await createWorkflowDefinition({
      name,
      description,
      entityType,
      definition,
      isActive,
      isShared,
      createdBy: user.id
    });

    if (!result.success) {
      return json({
        workflowError: result.error,
        workflowValues: Object.fromEntries(formData)
      });
    }

    return json({ workflowSuccess: true });
  }

  return json({ error: "Invalid action" });
};

export default function CustomizationSettings() {
  const { entityTypes, fieldTypes, customFields, viewDefinitions, workflowDefinitions } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Customize Your CRM</h1>

      <Tabs defaultValue="fields" className="space-y-6">
        <TabsList>
          <TabsTrigger value="fields">Custom Fields</TabsTrigger>
          <TabsTrigger value="views">Custom Views</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
        </TabsList>

        <TabsContent value="fields" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Add Custom Field</CardTitle>
              <CardDescription>
                Create custom fields for any entity in your CRM
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post" className="space-y-4">
                <input type="hidden" name="action" value="create-field" />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="entityType">Entity Type</Label>
                    <Select name="entityType" defaultValue={actionData?.values?.entityType || ""}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select entity type" />
                      </SelectTrigger>
                      <SelectContent>
                        {entityTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            {type.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="fieldType">Field Type</Label>
                    <Select name="fieldType" defaultValue={actionData?.values?.fieldType || ""}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select field type" />
                      </SelectTrigger>
                      <SelectContent>
                        {fieldTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            {type.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="name">Field Name</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="e.g., customerRating"
                      defaultValue={actionData?.values?.name || ""}
                      required
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Use camelCase without spaces (e.g., customerRating)
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="label">Display Label</Label>
                    <Input
                      id="label"
                      name="label"
                      placeholder="e.g., Customer Rating"
                      defaultValue={actionData?.values?.label || ""}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="placeholder">Placeholder</Label>
                    <Input
                      id="placeholder"
                      name="placeholder"
                      placeholder="e.g., Enter customer rating..."
                      defaultValue={actionData?.values?.placeholder || ""}
                    />
                  </div>

                  <div>
                    <Label htmlFor="defaultValue">Default Value</Label>
                    <Input
                      id="defaultValue"
                      name="defaultValue"
                      placeholder="e.g., 5"
                      defaultValue={actionData?.values?.defaultValue || ""}
                    />
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor="options">Options (for Select/Multi-Select)</Label>
                    <Textarea
                      id="options"
                      name="options"
                      placeholder="Enter one option per line"
                      defaultValue={actionData?.values?.options || ""}
                      rows={3}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      For Select and Multi-Select fields, enter one option per line
                    </p>
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor="helpText">Help Text</Label>
                    <Input
                      id="helpText"
                      name="helpText"
                      placeholder="Instructions for this field"
                      defaultValue={actionData?.values?.helpText || ""}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="required"
                      name="required"
                      defaultChecked={actionData?.values?.required === "on"}
                    />
                    <Label htmlFor="required">Required Field</Label>
                  </div>
                </div>

                {actionData?.error && (
                  <div className="text-destructive text-sm">
                    {actionData.error}
                  </div>
                )}

                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Creating..." : "Create Field"}
                </Button>
              </Form>
            </CardContent>
          </Card>

          {/* Display existing custom fields */}
          {customFields.map((entity) => (
            entity.fields.length > 0 && (
              <Card key={entity.entityType}>
                <CardHeader>
                  <CardTitle>
                    {entityTypes.find(t => t.id === entity.entityType)?.name} Custom Fields
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {entity.fields.map((field) => (
                      <Card key={field.id} className="p-4 border border-muted">
                        <h3 className="font-medium">{field.label}</h3>
                        <p className="text-sm text-muted-foreground">
                          {fieldTypes.find(t => t.id === field.fieldType)?.name}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Field name: {field.name}
                        </p>
                        <div className="flex justify-end mt-2">
                          <Button variant="outline" size="sm">Edit</Button>
                        </div>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )
          ))}
        </TabsContent>

        <TabsContent value="views" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Create Custom View</CardTitle>
              <CardDescription>
                Create and manage custom views for your data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post" className="space-y-4">
                <input type="hidden" name="action" value="create-view" />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="viewEntityType">Entity Type</Label>
                    <Select name="entityType" defaultValue={actionData?.viewValues?.entityType || ""}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select entity type" />
                      </SelectTrigger>
                      <SelectContent>
                        {entityTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            {type.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="viewName">View Name</Label>
                    <Input
                      id="viewName"
                      name="name"
                      placeholder="e.g., My Custom View"
                      defaultValue={actionData?.viewValues?.name || ""}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="layout">Layout Configuration</Label>
                  <Textarea
                    id="layout"
                    name="layout"
                    placeholder="Enter layout configuration in JSON format"
                    defaultValue={actionData?.viewValues?.layout || "{}"}
                    rows={5}
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Enter layout configuration in JSON format
                  </p>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isDefault"
                      name="isDefault"
                      defaultChecked={actionData?.viewValues?.isDefault === "on"}
                    />
                    <Label htmlFor="isDefault">Set as Default</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isShared"
                      name="isShared"
                      defaultChecked={actionData?.viewValues?.isShared === "on"}
                    />
                    <Label htmlFor="isShared">Share with Others</Label>
                  </div>
                </div>

                {actionData?.viewError && (
                  <div className="text-destructive text-sm">
                    {actionData.viewError}
                  </div>
                )}

                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Creating..." : "Create View"}
                </Button>
              </Form>
            </CardContent>
          </Card>

          {/* Display existing view definitions */}
          {viewDefinitions.map((entity) => (
            entity.views.length > 0 && (
              <Card key={entity.entityType}>
                <CardHeader>
                  <CardTitle>
                    {entityTypes.find(t => t.id === entity.entityType)?.name} Custom Views
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {entity.views.map((view) => (
                      <Card key={view.id} className="p-4 border border-muted">
                        <h3 className="font-medium">{view.name}</h3>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {view.isDefault && (
                            <span className="bg-primary/10 text-primary text-xs px-2 py-1 rounded-full">
                              Default
                            </span>
                          )}
                          {view.isShared && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              Shared
                            </span>
                          )}
                        </div>
                        <div className="flex justify-end mt-2">
                          <Button variant="outline" size="sm">Edit</Button>
                        </div>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )
          ))}
        </TabsContent>

        <TabsContent value="workflows" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Create Custom Workflow</CardTitle>
              <CardDescription>
                Create and manage custom workflows for your business processes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form method="post" className="space-y-4">
                <input type="hidden" name="action" value="create-workflow" />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="workflowEntityType">Entity Type</Label>
                    <Select name="entityType" defaultValue={actionData?.workflowValues?.entityType || ""}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select entity type" />
                      </SelectTrigger>
                      <SelectContent>
                        {entityTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            {type.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="workflowName">Workflow Name</Label>
                    <Input
                      id="workflowName"
                      name="name"
                      placeholder="e.g., New Customer Onboarding"
                      defaultValue={actionData?.workflowValues?.name || ""}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Describe what this workflow does"
                    defaultValue={actionData?.workflowValues?.description || ""}
                    rows={2}
                  />
                </div>

                <div>
                  <Label htmlFor="definition">Workflow Definition</Label>
                  <Textarea
                    id="definition"
                    name="definition"
                    placeholder="Enter workflow definition in JSON format"
                    defaultValue={actionData?.workflowValues?.definition || "{}"}
                    rows={5}
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Enter workflow definition in JSON format
                  </p>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isActive"
                      name="isActive"
                      defaultChecked={actionData?.workflowValues?.isActive !== "off"}
                    />
                    <Label htmlFor="isActive">Active</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isShared"
                      name="isShared"
                      defaultChecked={actionData?.workflowValues?.isShared === "on"}
                    />
                    <Label htmlFor="isShared">Share with Others</Label>
                  </div>
                </div>

                {actionData?.workflowError && (
                  <div className="text-destructive text-sm">
                    {actionData.workflowError}
                  </div>
                )}

                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Creating..." : "Create Workflow"}
                </Button>
              </Form>
            </CardContent>
          </Card>

          {/* Display existing workflow definitions */}
          {workflowDefinitions.map((entity) => (
            entity.workflows.length > 0 && (
              <Card key={entity.entityType}>
                <CardHeader>
                  <CardTitle>
                    {entityTypes.find(t => t.id === entity.entityType)?.name} Custom Workflows
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {entity.workflows.map((workflow) => (
                      <Card key={workflow.id} className="p-4 border border-muted">
                        <h3 className="font-medium">{workflow.name}</h3>
                        {workflow.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {workflow.description}
                          </p>
                        )}
                        <div className="flex flex-wrap gap-2 mt-2">
                          {workflow.isActive && (
                            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                              Active
                            </span>
                          )}
                          {!workflow.isActive && (
                            <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                              Inactive
                            </span>
                          )}
                          {workflow.isShared && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              Shared
                            </span>
                          )}
                        </div>
                        <div className="flex justify-end mt-2">
                          <Button variant="outline" size="sm">Edit</Button>
                        </div>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}