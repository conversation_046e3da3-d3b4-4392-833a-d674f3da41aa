import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { NotificationPreferences } from "~/components/molecules/notifications/notification-preferences";
import { getNotificationPreferences, updateNotificationPreferences } from "~/models/notification.server";
import { requireUser } from "~/session.server";
import type { NotificationSettings } from "~/types/shared";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await requireUser(request);

  try {
    const preferences = await getNotificationPreferences(user.id);

    return json({
      user,
      preferences
    });
  } catch (error) {
    console.error("Błąd podczas pobierania preferencji powiadomień:", error);

    // Domyślne preferencje w przypadku błędu
    return json({
      user,
      preferences: {
        emailNotifications: true,
        inAppNotifications: true,
        pushNotifications: false,
        serviceOrderUpdates: true,
        calendarReminders: true,
        systemUpdates: true
      } as NotificationSettings
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const user = await requireUser(request);
  const formData = await request.formData();
  const action = formData.get("action") as string;

  if (action === "updatePreferences") {
    // Podstawowe ustawienia
    const email = formData.get("email") === "true";
    const inApp = formData.get("inApp") === "true";
    const push = formData.get("push") === "true";
    const sms = formData.get("sms") === "true";
    const serviceOrderUpdates = formData.get("serviceOrderUpdates") === "true";
    const calendarReminders = formData.get("calendarReminders") === "true";
    const systemUpdates = formData.get("systemUpdates") === "true";
    const deviceAlerts = formData.get("deviceAlerts") === "true";
    const newMessages = formData.get("newMessages") === "true";

    // Podstawowe preferencje
    const preferences: any = {
      email,
      inApp,
      push,
      sms,
      serviceOrderUpdates,
      calendarReminders,
      systemUpdates,
      deviceAlerts,
      newMessages
    };

    // Sprawdź czy mamy zaawansowane ustawienia
    const isAdvanced = formData.get("advanced") === "true";

    if (isAdvanced) {
      // Kanały dla poszczególnych typów powiadomień
      try {
        const serviceOrderChannels = formData.get("serviceOrderChannels");
        if (serviceOrderChannels) {
          preferences.serviceOrderChannels = JSON.parse(serviceOrderChannels as string);
        }

        const calendarReminderChannels = formData.get("calendarReminderChannels");
        if (calendarReminderChannels) {
          preferences.calendarReminderChannels = JSON.parse(calendarReminderChannels as string);
        }

        const systemUpdateChannels = formData.get("systemUpdateChannels");
        if (systemUpdateChannels) {
          preferences.systemUpdateChannels = JSON.parse(systemUpdateChannels as string);
        }

        const deviceAlertChannels = formData.get("deviceAlertChannels");
        if (deviceAlertChannels) {
          preferences.deviceAlertChannels = JSON.parse(deviceAlertChannels as string);
        }

        const newMessageChannels = formData.get("newMessageChannels");
        if (newMessageChannels) {
          preferences.newMessageChannels = JSON.parse(newMessageChannels as string);
        }

        // Dane kontaktowe
        const notificationEmail = formData.get("notificationEmail") as string;
        if (notificationEmail) {
          preferences.notificationEmail = notificationEmail;
        }

        const notificationPhone = formData.get("notificationPhone") as string;
        if (notificationPhone) {
          preferences.notificationPhone = notificationPhone;
        }
      } catch (error) {
        console.error("Błąd podczas parsowania zaawansowanych ustawień:", error);
        return json({ error: "Nieprawidłowy format zaawansowanych ustawień" }, { status: 400 });
      }
    }

    try {
      await updateNotificationPreferences(user.id, preferences);

      return redirect("/settings/notifications?success=true");
    } catch (error) {
      console.error("Błąd podczas aktualizacji preferencji powiadomień:", error);
      return json({ error: "Wystąpił błąd podczas zapisywania preferencji" }, { status: 500 });
    }
  }

  return json({ error: "Nieznana akcja" }, { status: 400 });
};

export default function NotificationSettingsPage() {
  const { preferences } = useLoaderData<typeof loader>();
  const url = new URL(window.location.href);
  const success = url.searchParams.get("success") === "true";

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Ustawienia powiadomień</h1>

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6" role="alert">
          <p>Preferencje powiadomień zostały zaktualizowane pomyślnie.</p>
        </div>
      )}

      <div className="max-w-2xl mx-auto">
        <NotificationPreferences preferences={preferences} />
      </div>
    </div>
  );
}
