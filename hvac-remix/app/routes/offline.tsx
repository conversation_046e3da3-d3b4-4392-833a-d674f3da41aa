/**
 * Offline Page
 * 
 * This page is displayed when the user is offline and tries to access a page that requires an internet connection.
 */

import type { MetaFunction } from "@remix-run/node";
import { Link } from "@remix-run/react";
import { 
  WifiOffIcon, 
  RefreshCwIcon, 
  HomeIcon, 
  ClipboardListIcon,
  ArrowLeftIcon
} from "lucide-react";
import { useState, useEffect } from "react";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";

export const meta: MetaFunction = () => {
  return [
    { title: "Offline - HVAC CRM" },
    { name: "description", content: "You are currently offline" },
  ];
};

export default function OfflinePage() {
  const [online, setOnline] = useState(false);
  const [lastAttempt, setLastAttempt] = useState<Date | null>(null);
  
  // Check online status
  useEffect(() => {
    const handleOnline = () => setOnline(true);
    const handleOffline = () => setOnline(false);
    
    // Set initial state
    setOnline(navigator.onLine);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  // Handle retry connection
  const handleRetryConnection = () => {
    setLastAttempt(new Date());
    
    // Try to reload the page
    window.location.reload();
  };
  
  // Format last attempt time
  const formatLastAttempt = () => {
    if (!lastAttempt) return '';
    
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric',
    }).format(lastAttempt);
  };
  
  return (
    <div className="container mx-auto py-12 px-4">
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 bg-red-100 p-3 rounded-full w-16 h-16 flex items-center justify-center dark:bg-red-900/20">
              <WifiOffIcon className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
            <CardTitle className="text-2xl">
              {online ? 'Connection Restored!' : 'You are offline'}
            </CardTitle>
            <CardDescription>
              {online
                ? 'Your internet connection has been restored. You can now continue using all features.'
                : 'Your internet connection appears to be offline. Some features may be limited.'}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {!online && (
              <Alert variant="destructive">
                <AlertTitle>Limited Functionality</AlertTitle>
                <AlertDescription>
                  While offline, you can still view cached data and create new items, 
                  but they will be synchronized with the server when you reconnect.
                </AlertDescription>
              </Alert>
            )}
            
            {online && (
              <Alert variant="success" className="bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-900">
                <AlertTitle>Connection Restored</AlertTitle>
                <AlertDescription>
                  Your internet connection has been restored. Any changes made while offline 
                  will be synchronized with the server.
                </AlertDescription>
              </Alert>
            )}
            
            <div className="flex items-center justify-between py-2 border-b">
              <span className="font-medium">Status</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                online 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' 
                  : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
              }`}>
                {online ? 'Online' : 'Offline'}
              </span>
            </div>
            
            {lastAttempt && (
              <div className="flex items-center justify-between py-2 border-b">
                <span className="font-medium">Last check</span>
                <span className="text-gray-500 dark:text-gray-400 text-sm">
                  {formatLastAttempt()}
                </span>
              </div>
            )}
          </CardContent>
          
          <CardFooter className="flex flex-col space-y-2">
            {!online ? (
              <Button 
                className="w-full" 
                onClick={handleRetryConnection}
              >
                <RefreshCwIcon className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            ) : (
              <Button 
                className="w-full" 
                onClick={() => window.location.href = '/'}
              >
                <HomeIcon className="mr-2 h-4 w-4" />
                Go to Homepage
              </Button>
            )}
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => window.history.back()}
            >
              <ArrowLeftIcon className="mr-2 h-4 w-4" />
              Go Back
            </Button>
            
            {!online && (
              <Button 
                variant="outline" 
                className="w-full"
                asChild
              >
                <Link to="/settings/offline-data">
                  <ClipboardListIcon className="mr-2 h-4 w-4" />
                  View Offline Data
                </Link>
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
