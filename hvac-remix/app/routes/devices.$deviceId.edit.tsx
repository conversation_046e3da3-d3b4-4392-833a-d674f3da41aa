import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation, Link } from "@remix-run/react";
import type { Value } from "@udecode/plate";
import { useEffect, useRef, useState } from "react";
import * as React from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { RichTextEditor, plateValueToString, stringToPlateValue } from "~/components/ui/rich-text-editor";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "~/components/ui/select";
import { Textarea } from "~/components/ui/textarea";
import { getCustomers } from "~/services/customer.service";
import { getDeviceById, updateDevice } from "~/services/device.service";
import { requireUserId } from "~/session.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const { deviceId } = params;

  if (!deviceId) {
    throw new Response("Device ID is required", { status: 400 });
  }

  // Get device details
  const deviceResponse = await getDeviceById(deviceId, userId);

  if (!deviceResponse.success || !deviceResponse.data) {
    throw new Response(deviceResponse.error || "Device not found", { status: 404 });
  }

  // Get all customers for the dropdown
  const customersResponse = await getCustomers(userId);

  return json({
    device: deviceResponse.data,
    customers: customersResponse.data || []
  });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const { deviceId } = params;

  if (!deviceId) {
    throw new Response("Device ID is required", { status: 400 });
  }

  const formData = await request.formData();

  const name = formData.get("name") as string;
  const model = formData.get("model") as string;
  const serialNumber = formData.get("serialNumber") as string;
  const manufacturer = formData.get("manufacturer") as string;
  const installationDate = formData.get("installationDate") as string;
  const warrantyExpiryDate = formData.get("warrantyExpiryDate") as string;
  const notesJson = formData.get("notes") as string;
  const customerId = formData.get("customerId") as string;

  // Parse the rich text editor value for notes
  let notes: string | null = null;
  try {
    if (notesJson) {
      const notesValue = JSON.parse(notesJson) as Value;
      notes = plateValueToString(notesValue);
    }
  } catch (e) {
    console.error("Error parsing rich text content:", e);
    notes = notesJson;
  }

  // Validate required fields
  const errors = {
    name: name ? null : "Name is required",
    customerId: customerId ? null : "Customer is required",
  };

  const hasErrors = Object.values(errors).some(
    (errorMessage) => errorMessage !== null
  );

  if (hasErrors) {
    return json({ errors, values: Object.fromEntries(formData) });
  }

  // Update device
  const deviceResponse = await updateDevice(
    deviceId,
    {
      name,
      model: model || null,
      serialNumber: serialNumber || null,
      manufacturer: manufacturer || null,
      installationDate: installationDate ? new Date(installationDate) : null,
      warrantyExpiryDate: warrantyExpiryDate ? new Date(warrantyExpiryDate) : null,
      notes: notes || null,
      customerId,
    },
    userId
  );

  if (!deviceResponse.success) {
    return json({
      errors: {
        ...errors,
        form: deviceResponse.error || "Failed to update device",
      },
      values: Object.fromEntries(formData),
    });
  }

  return redirect(`/devices/${deviceId}`);
}

export default function EditDevicePage() {
  const { device, customers } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  // State for rich text editor
  const [notesValue, setNotesValue] = useState<Value>(
    device.notes ? stringToPlateValue(device.notes) : [{ type: 'p', children: [{ text: '' }] }]
  );

  const nameRef = useRef<HTMLInputElement>(null);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>(
    actionData?.values?.customerId as string || device.customerId
  );

  // Format date for input fields
  const formatDateForInput = (dateString: string | null) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };

  // Focus on the name field when there's an error
  useEffect(() => {
    if (actionData?.errors?.name) {
      nameRef.current?.focus();
    }
  }, [actionData]);

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to={`/devices/${device.id}`} className="text-blue-500 hover:underline">
          ← Back to Device Details
        </Link>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Edit Device</CardTitle>
          <CardDescription>
            Update device information
          </CardDescription>
        </CardHeader>

        <Form method="post">
          <CardContent className="space-y-4">
            {/* Form error message */}
            {actionData?.errors?.form && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {actionData.errors.form}
              </div>
            )}

            {/* Customer field */}
            <div className="space-y-2">
              <Label htmlFor="customerId">
                Customer <span className="text-red-500">*</span>
              </Label>
              <Select
                name="customerId"
                defaultValue={selectedCustomerId}
                onValueChange={setSelectedCustomerId}
              >
                <SelectTrigger
                  id="customerId"
                  className={actionData?.errors?.customerId ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select a customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {actionData?.errors?.customerId && (
                <p className="text-red-500 text-sm" id="customerId-error">
                  {actionData.errors.customerId}
                </p>
              )}
            </div>

            {/* Name field */}
            <div className="space-y-2">
              <Label htmlFor="name">
                Name <span className="text-red-500">*</span>
              </Label>
              <Input
                ref={nameRef}
                id="name"
                name="name"
                defaultValue={actionData?.values?.name as string || device.name}
                aria-invalid={actionData?.errors?.name ? true : undefined}
                aria-errormessage={
                  actionData?.errors?.name ? "name-error" : undefined
                }
              />
              {actionData?.errors?.name && (
                <p className="text-red-500 text-sm" id="name-error">
                  {actionData.errors.name}
                </p>
              )}
            </div>

            {/* Model field */}
            <div className="space-y-2">
              <Label htmlFor="model">Model</Label>
              <Input
                id="model"
                name="model"
                defaultValue={actionData?.values?.model as string || device.model || ""}
              />
            </div>

            {/* Serial Number field */}
            <div className="space-y-2">
              <Label htmlFor="serialNumber">Serial Number</Label>
              <Input
                id="serialNumber"
                name="serialNumber"
                defaultValue={actionData?.values?.serialNumber as string || device.serialNumber || ""}
              />
            </div>

            {/* Manufacturer field */}
            <div className="space-y-2">
              <Label htmlFor="manufacturer">Manufacturer</Label>
              <Input
                id="manufacturer"
                name="manufacturer"
                defaultValue={actionData?.values?.manufacturer as string || device.manufacturer || ""}
              />
            </div>

            {/* Installation Date field */}
            <div className="space-y-2">
              <Label htmlFor="installationDate">Installation Date</Label>
              <Input
                id="installationDate"
                name="installationDate"
                type="date"
                defaultValue={
                  actionData?.values?.installationDate as string ||
                  formatDateForInput(device.installationDate)
                }
              />
            </div>

            {/* Warranty Expiry Date field */}
            <div className="space-y-2">
              <Label htmlFor="warrantyExpiryDate">Warranty Expiry Date</Label>
              <Input
                id="warrantyExpiryDate"
                name="warrantyExpiryDate"
                type="date"
                defaultValue={
                  actionData?.values?.warrantyExpiryDate as string ||
                  formatDateForInput(device.warrantyExpiryDate)
                }
              />
            </div>

            {/* Notes field */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <RichTextEditor
                name="notes"
                value={notesValue}
                onChange={setNotesValue}
                minHeight="200px"
              />
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Link to={`/devices/${device.id}`}>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </CardFooter>
        </Form>
      </Card>
    </div>
  );
}
