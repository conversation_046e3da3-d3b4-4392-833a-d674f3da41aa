import type { MetaFunction } from "@remix-run/node";
import { useNavigate } from "@remix-run/react";
import { PageHeader } from "~/components/molecules/page-header";
import { ConflictResolution } from "~/components/organisms/conflict-resolution";

export const meta: MetaFunction = () => {
  return [
    { title: "Resolve Conflicts - HVAC CRM" },
    { name: "description", content: "Resolve data conflicts between your device and the server" },
  ];
};

export default function SyncConflictsPage() {
  const navigate = useNavigate();
  
  const handleResolved = () => {
    // Navigate to sync status page after all conflicts are resolved
    navigate("/settings/sync-status");
  };
  
  return (
    <div className="container py-8">
      <PageHeader
        title="Resolve Conflicts"
        description="Resolve data conflicts between your device and the server"
        backLink="/settings"
      />
      
      <div className="mt-8">
        <ConflictResolution onResolved={handleResolved} />
      </div>
    </div>
  );
}
