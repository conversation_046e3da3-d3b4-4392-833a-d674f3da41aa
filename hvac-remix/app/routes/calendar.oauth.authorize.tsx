import { redirect, type LoaderFunctionArgs } from "@remix-run/node";
import { generateStateParameter, getOutlookAuthUrl } from "~/services/outlook-calendar.server";
import { requireUserId } from "~/session.server";

/**
 * This route initiates the OAuth flow by redirecting to the Microsoft authorization endpoint.
 * It generates a state parameter for CSRF protection and constructs the authorization URL.
 */
export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  
  // Generate state parameter for CSRF protection
  const state = generateStateParameter();
  
  // TODO: Store state in session or database to validate on callback
  
  // Get authorization URL
  const authUrl = getOutlookAuthUrl(state);
  
  // Redirect to Microsoft authorization endpoint
  return redirect(authUrl);
}

// No component needed for this route as it just handles the redirect
export default function OAuthAuthorize() {
  return (
    <div>
      <h1>Redirecting to Microsoft</h1>
      <p>Please wait while we redirect you to Microsoft for authentication...</p>
    </div>
  );
}
