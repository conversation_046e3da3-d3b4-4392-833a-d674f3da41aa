import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import {
  markNotificationAsRead,
  markAllNotificationsAsRead,
  getUnreadNotificationsByUserId,
  getNotificationsByUserIdSince,
  updateNotificationPreferences
} from "~/models/notification.server";
import { requireUserId } from "~/session.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  const action = formData.get("action") as string;

  try {
    switch (action) {
      case "markAsRead": {
        const id = formData.get("id") as string;

        if (!id) {
          return json({ success: false, message: "Brak ID powiadomienia" }, { status: 400 });
        }

        await markNotificationAsRead(id);
        return json({ success: true });
      }

      case "markAllAsRead": {
        await markAllNotificationsAsRead(userId);
        return json({ success: true });
      }

      case "updatePreferences": {
        const emailNotifications = formData.get("emailNotifications") === "true";
        const inAppNotifications = formData.get("inAppNotifications") === "true";
        const pushNotifications = formData.get("pushNotifications") === "true";
        const serviceOrderUpdates = formData.get("serviceOrderUpdates") === "true";
        const calendarReminders = formData.get("calendarReminders") === "true";
        const systemUpdates = formData.get("systemUpdates") === "true";

        await updateNotificationPreferences(userId, {
          emailNotifications,
          inAppNotifications,
          pushNotifications,
          serviceOrderUpdates,
          calendarReminders,
          systemUpdates
        });

        return json({ success: true });
      }

      default:
        return json({ success: false, message: "Nieznana akcja" }, { status: 400 });
    }
  } catch (error) {
    console.error("Błąd podczas przetwarzania powiadomień:", error);
    return json({ success: false, message: "Wystąpił błąd podczas przetwarzania powiadomień" }, { status: 500 });
  }
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  const url = new URL(request.url);
  const since = url.searchParams.get("since");

  try {
    let notifications;

    if (since) {
      // Pobierz powiadomienia od określonego czasu
      const sinceDate = new Date(since);
      notifications = await getNotificationsByUserIdSince(userId, sinceDate);
    } else {
      // Pobierz wszystkie nieprzeczytane powiadomienia
      notifications = await getUnreadNotificationsByUserId(userId);
    }

    return json({ notifications });
  } catch (error) {
    console.error("Błąd podczas pobierania powiadomień:", error);
    return json({ notifications: [] }, { status: 500 });
  }
};