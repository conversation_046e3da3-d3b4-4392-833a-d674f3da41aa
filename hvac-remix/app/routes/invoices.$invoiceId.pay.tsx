import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData } from "@remix-run/react";
import { useState } from "react";
import invariant from "tiny-invariant";

import PaymentForm from "~/components/payment/PaymentForm";
import PaymentMethodSelector from "~/components/payment/PaymentMethodSelector";
import StripeProvider from "~/components/payment/StripeProvider";
import { prisma } from "~/db.server";
import { createPaymentIntent, getCustomerPaymentMethods, recordPayment } from "~/services/payment.server";
import { requireUser } from "~/session.server";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const user = await requireUser(request);
  const { invoiceId } = params;
  invariant(invoiceId, "Invoice ID is required");

  const invoice = await prisma.invoice.findUnique({
    where: { id: invoiceId },
    include: {
      customer: true,
      items: true,
      serviceOrder: true,
    },
  });

  if (!invoice) {
    throw new Response("Invoice not found", { status: 404 });
  }

  // Check if the invoice belongs to the user
  if (invoice.customer.userId !== user.id) {
    throw new Response("Not authorized", { status: 403 });
  }

  // Check if the invoice is already paid
  if (invoice.paymentStatus === "PAID") {
    return redirect(`/invoices/${invoiceId}`);
  }

  // Get payment methods for the customer
  const paymentMethods = await getCustomerPaymentMethods(invoice.customerId);

  // Create a payment intent
  const { clientSecret, paymentIntentId } = await createPaymentIntent(invoiceId);

  return json({
    invoice,
    paymentMethods,
    clientSecret,
    paymentIntentId,
  });
};

export const action = async ({ request, params }: ActionFunctionArgs) => {
  const user = await requireUser(request);
  const { invoiceId } = params;
  invariant(invoiceId, "Invoice ID is required");

  const formData = await request.formData();
  const paymentMethod = formData.get("paymentMethod") as string;
  const paymentType = formData.get("paymentType") as string;
  const amount = parseFloat(formData.get("amount") as string);

  // Validate the invoice
  const invoice = await prisma.invoice.findUnique({
    where: { id: invoiceId },
    include: { customer: true },
  });

  if (!invoice) {
    return json({ error: "Invoice not found" }, { status: 404 });
  }

  // Check if the invoice belongs to the user
  if (invoice.customer.userId !== user.id) {
    return json({ error: "Not authorized" }, { status: 403 });
  }

  // For manual payments (cash, bank transfer)
  if (paymentType === "manual") {
    try {
      await recordPayment({
        invoiceId,
        amount,
        paymentMethod,
        status: "PENDING",
      });

      return redirect(`/invoices/${invoiceId}?payment=success`);
    } catch (error) {
      return json({ error: "Failed to record payment" }, { status: 500 });
    }
  }

  // For Stripe payments, the payment is recorded via webhook
  return json({ success: true });
};

export default function InvoicePaymentPage() {
  const { invoice, paymentMethods, clientSecret } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [paymentType, setPaymentType] = useState<string>("card");
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  const handlePaymentSuccess = () => {
    setPaymentSuccess(true);
  };

  const handlePaymentMethodSelect = (paymentMethodId: string | null) => {
    setSelectedPaymentMethod(paymentMethodId);
  };

  return (
    <div className="mx-auto max-w-4xl p-6">
      <h1 className="text-2xl font-bold mb-6">Pay Invoice #{invoice.invoiceNumber || invoice.id}</h1>

      {paymentSuccess ? (
        <div className="bg-green-50 border border-green-200 rounded-md p-6 text-center">
          <h2 className="text-xl font-semibold text-green-800 mb-2">Payment Successful!</h2>
          <p className="text-green-700 mb-4">
            Your payment has been processed successfully. Thank you for your payment.
          </p>
          <a
            href={`/invoices/${invoice.id}`}
            className="inline-block rounded-md bg-green-600 px-4 py-2 text-white font-medium hover:bg-green-700"
          >
            Return to Invoice
          </a>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <div className="bg-white rounded-md shadow-sm border border-gray-200 p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Invoice Summary</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Invoice Number:</span>
                  <span className="font-medium">{invoice.invoiceNumber || invoice.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Issue Date:</span>
                  <span className="font-medium">
                    {invoice.issueDate ? new Date(invoice.issueDate).toLocaleDateString() : "N/A"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Due Date:</span>
                  <span className="font-medium">
                    {invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString() : "N/A"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Amount:</span>
                  <span className="font-bold text-lg">
                    {new Intl.NumberFormat("pl-PL", {
                      style: "currency",
                      currency: "PLN",
                    }).format(invoice.totalAmount || 0)}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-md shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold mb-4">Payment Method</h2>
              <div className="space-y-4">
                <div className="flex space-x-4">
                  <button
                    type="button"
                    className={`flex-1 py-2 px-4 rounded-md ${
                      paymentType === "card"
                        ? "bg-blue-600 text-white"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                    onClick={() => setPaymentType("card")}
                  >
                    Credit Card
                  </button>
                  <button
                    type="button"
                    className={`flex-1 py-2 px-4 rounded-md ${
                      paymentType === "bank"
                        ? "bg-blue-600 text-white"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                    onClick={() => setPaymentType("bank")}
                  >
                    Bank Transfer
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-md shadow-sm border border-gray-200 p-6">
            {paymentType === "card" ? (
              <div>
                <h2 className="text-xl font-semibold mb-4">Credit Card Payment</h2>
                {paymentMethods.length > 0 && (
                  <div className="mb-6">
                    <PaymentMethodSelector
                      paymentMethods={paymentMethods}
                      onSelect={handlePaymentMethodSelect}
                      selectedPaymentMethodId={selectedPaymentMethod}
                    />
                  </div>
                )}

                {!selectedPaymentMethod && (
                  <StripeProvider>
                    <PaymentForm
                      clientSecret={clientSecret}
                      invoiceId={invoice.id}
                      amount={invoice.totalAmount || 0}
                      onSuccess={handlePaymentSuccess}
                    />
                  </StripeProvider>
                )}
              </div>
            ) : (
              <div>
                <h2 className="text-xl font-semibold mb-4">Bank Transfer</h2>
                <div className="bg-gray-50 p-4 rounded-md mb-6">
                  <p className="mb-2">Please transfer the payment to the following account:</p>
                  <div className="space-y-2 font-mono">
                    <div>
                      <span className="text-gray-600">Bank:</span> Example Bank
                    </div>
                    <div>
                      <span className="text-gray-600">Account Number:</span> 1234 5678 9012 3456
                    </div>
                    <div>
                      <span className="text-gray-600">SWIFT/BIC:</span> EXAMPLEXXX
                    </div>
                    <div>
                      <span className="text-gray-600">Reference:</span> INV-{invoice.invoiceNumber || invoice.id}
                    </div>
                  </div>
                </div>

                <form method="post">
                  <input type="hidden" name="paymentType" value="manual" />
                  <input type="hidden" name="paymentMethod" value="BANK_TRANSFER" />
                  <input type="hidden" name="amount" value={invoice.totalAmount || 0} />
                  <button
                    type="submit"
                    className="w-full rounded-md bg-blue-600 px-4 py-2 text-white font-medium hover:bg-blue-700"
                  >
                    Mark as Paid
                  </button>
                </form>
              </div>
            )}

            {actionData?.error && (
              <div className="mt-4 text-red-500 text-sm">{actionData.error}</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
