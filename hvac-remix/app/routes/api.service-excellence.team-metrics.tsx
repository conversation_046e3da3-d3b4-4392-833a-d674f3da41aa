/**
 * 👥 SERVICE EXCELLENCE TEAM METRICS API
 * 
 * Provides team performance analytics and metrics
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { ServiceExcellenceService } from "~/models/service-excellence.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const timeRange = url.searchParams.get('timeRange') || 'month';

  try {
    // Calculate date range
    const now = new Date();
    let startDate: Date;

    switch (timeRange) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'quarter':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default: // month
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get all technicians (users with role that includes technician work)
    const technicians = await prisma.user.findMany({
      where: {
        isActive: true,
        // In a real system, you'd filter by role
      },
      include: {
        serviceOrders: {
          where: {
            assignedTechnicianId: {
              not: null
            },
            createdAt: {
              gte: startDate,
              lte: now
            }
          },
          include: {
            invoices: true
          }
        }
      }
    });

    // Filter to only technicians who have service orders
    const activeTechnicians = technicians.filter(tech => 
      tech.serviceOrders && tech.serviceOrders.length > 0
    );

    const totalTechnicians = activeTechnicians.length;
    let totalRevenue = 0;
    let totalJobs = 0;
    let totalCompletedJobs = 0;
    let totalRatings = 0;
    let ratingCount = 0;
    let topPerformerScore = 0;
    let topPerformer = 'N/A';

    // Calculate metrics for each technician
    const technicianMetrics = await Promise.all(
      activeTechnicians.map(async (technician) => {
        try {
          const metrics = await ServiceExcellenceService.calculateTechnicianPerformance(
            technician.id,
            startDate,
            now
          );

          // Accumulate totals
          totalJobs += metrics.metrics.totalJobs;
          totalCompletedJobs += metrics.metrics.completedJobs;
          totalRevenue += metrics.metrics.revenueGenerated;

          // Track ratings
          const techRatings = technician.serviceOrders
            .filter(so => so.customerRating)
            .map(so => so.customerRating!);
          
          if (techRatings.length > 0) {
            const avgRating = techRatings.reduce((sum, rating) => sum + rating, 0) / techRatings.length;
            totalRatings += avgRating;
            ratingCount++;
          }

          // Track top performer
          const performanceScore = (
            metrics.metrics.completionRate * 0.3 +
            metrics.metrics.customerSatisfactionScore * 0.3 +
            metrics.metrics.firstTimeFixRate * 0.2 +
            metrics.metrics.slaComplianceRate * 0.2
          );

          if (performanceScore > topPerformerScore) {
            topPerformerScore = performanceScore;
            topPerformer = technician.name || 'Unknown';
          }

          return {
            technicianId: technician.id,
            technicianName: technician.name || 'Unknown',
            metrics,
            performanceScore
          };

        } catch (error) {
          console.error(`Error calculating metrics for technician ${technician.id}:`, error);
          return null;
        }
      })
    );

    // Filter out failed calculations
    const validMetrics = technicianMetrics.filter(m => m !== null);

    // Calculate team averages
    const averagePerformanceScore = validMetrics.length > 0 ? 
      Math.round(validMetrics.reduce((sum, m) => sum + m!.performanceScore, 0) / validMetrics.length) : 0;

    const customerSatisfaction = ratingCount > 0 ? 
      Math.round((totalRatings / ratingCount) * 20) : 0; // Convert 1-5 scale to 0-100

    const firstTimeFixRate = totalJobs > 0 ? 
      Math.round((totalCompletedJobs / totalJobs) * 100) : 0;

    const teamMetrics = {
      totalTechnicians,
      averagePerformanceScore,
      topPerformer,
      totalRevenue,
      customerSatisfaction,
      firstTimeFixRate,
      timeRange,
      periodStart: startDate.toISOString(),
      periodEnd: now.toISOString(),
      technicianBreakdown: validMetrics.map(m => ({
        technicianId: m!.technicianId,
        technicianName: m!.technicianName,
        performanceScore: Math.round(m!.performanceScore),
        totalJobs: m!.metrics.metrics.totalJobs,
        completionRate: m!.metrics.metrics.completionRate,
        customerSatisfaction: m!.metrics.metrics.customerSatisfactionScore,
        revenue: m!.metrics.metrics.revenueGenerated
      }))
    };

    return json(teamMetrics);

  } catch (error) {
    console.error('Error fetching team metrics:', error);
    return json(
      { error: 'Failed to fetch team metrics' },
      { status: 500 }
    );
  }
}

// Handle POST requests for team-related actions
export async function action({ request }: LoaderFunctionArgs) {
  if (request.method !== 'POST') {
    return json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { action: actionType, data } = body;

    switch (actionType) {
      case 'schedule_team_meeting':
        // Create a team meeting calendar entry
        const meetingData = {
          title: data.title || 'Team Performance Review',
          description: data.description || 'Monthly team performance review meeting',
          scheduledDate: new Date(data.scheduledDate),
          type: 'MEETING',
          // Would create calendar entry in real implementation
        };

        return json({ 
          message: 'Team meeting scheduled',
          meeting: meetingData 
        });

      case 'generate_team_report':
        // Generate comprehensive team performance report
        const { timeRange: reportTimeRange = 'month', includeIndividual = false } = data;
        
        return json({
          message: 'Team report generated',
          reportId: `team-report-${Date.now()}`,
          timeRange: reportTimeRange,
          includeIndividual
        });

      case 'set_team_goals':
        // Set team performance goals
        const { goals } = data;
        
        // In production, would store goals in database
        return json({
          message: 'Team goals updated',
          goals
        });

      case 'assign_training':
        // Assign training to team members
        const { technicianIds, trainingModule } = data;
        
        if (!Array.isArray(technicianIds) || !trainingModule) {
          return json({ error: 'Invalid training assignment data' }, { status: 400 });
        }

        return json({
          message: 'Training assigned',
          assignedTo: technicianIds,
          training: trainingModule
        });

      default:
        return json({ error: 'Unknown action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Error processing team action:', error);
    return json(
      { error: 'Failed to process action' },
      { status: 500 }
    );
  }
}
