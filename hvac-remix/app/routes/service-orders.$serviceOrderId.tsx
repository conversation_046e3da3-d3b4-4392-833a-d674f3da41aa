import { json, type LoaderFunctionArgs, type ActionFunctionArgs, redirect } from "@remix-run/node";
import { useLoaderData, useNavigate, Link, useFetcher } from "@remix-run/react";
import { useEffect, useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { getServiceOrderById, deleteServiceOrder } from "~/services/service-order.service";
import { requireUserId } from "~/session.server";
import { getOfflineData , isOnline } from "~/utils/offline-storage";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const { serviceOrderId } = params;

  if (!serviceOrderId) {
    throw new Response("Service Order ID is required", { status: 400 });
  }

  const serviceOrderResponse = await getServiceOrderById(serviceOrderId, userId);

  if (!serviceOrderResponse.success || !serviceOrderResponse.data) {
    throw new Response(serviceOrderResponse.error || "Service Order not found", { status: 404 });
  }

  return json({ serviceOrder: serviceOrderResponse.data });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const { serviceOrderId } = params;

  if (!serviceOrderId) {
    throw new Response("Service Order ID is required", { status: 400 });
  }

  const formData = await request.formData();
  const intent = formData.get("intent");

  if (intent === "delete") {
    const deleteResponse = await deleteServiceOrder(serviceOrderId, userId);

    if (!deleteResponse.success) {
      return json({ error: deleteResponse.error }, { status: 400 });
    }

    return redirect("/service-orders");
  }

  return json({ error: "Invalid intent" }, { status: 400 });
}

export default function ServiceOrderDetailPage() {
  const { serviceOrder: initialServiceOrder } = useLoaderData<typeof loader>();
  const [serviceOrder, setServiceOrder] = useState(initialServiceOrder);
  const [isOffline, setIsOffline] = useState(!isOnline());
  const [offlineMessage, setOfflineMessage] = useState("");
  const navigate = useNavigate();
  const fetcher = useFetcher();

  // Check for offline status
  useEffect(() => {
    const checkOnlineStatus = () => {
      const online = isOnline();
      setIsOffline(!online);
      if (!online) {
        setOfflineMessage("You are currently offline. Some features may be limited.");
      } else {
        setOfflineMessage("");
      }
    };

    // Check initial status
    checkOnlineStatus();

    // Set up event listeners for online/offline events
    window.addEventListener('online', checkOnlineStatus);
    window.addEventListener('offline', checkOnlineStatus);

    return () => {
      window.removeEventListener('online', checkOnlineStatus);
      window.removeEventListener('offline', checkOnlineStatus);
    };
  }, []);

  // Check for offline data
  useEffect(() => {
    const checkOfflineData = async () => {
      if (isOffline && serviceOrder.id) {
        try {
          // Check if we have a more recent version in offline storage
          const offlineData = await getOfflineData(`offlineServiceOrders`, serviceOrder.id);
          if (offlineData && new Date(offlineData.updatedAt) > new Date(serviceOrder.updatedAt)) {
            setServiceOrder(offlineData);
            setOfflineMessage("Viewing offline version of this service order.");
          }
        } catch (error) {
          console.error("Error checking offline data:", error);
        }
      }
    };

    checkOfflineData();
  }, [isOffline, serviceOrder.id, serviceOrder.updatedAt]);

  // Handle delete confirmation
  const handleDelete = () => {
    if (isOffline) {
      alert("Cannot delete service orders while offline. Please try again when you're back online.");
      return;
    }

    if (confirm("Are you sure you want to delete this service order? This action cannot be undone.")) {
      const form = document.createElement("form");
      form.method = "post";
      form.appendChild(createHiddenInput("intent", "delete"));
      document.body.appendChild(form);
      form.submit();
    }
  };

  // Helper to create hidden input
  const createHiddenInput = (name: string, value: string) => {
    const input = document.createElement("input");
    input.type = "hidden";
    input.name = name;
    input.value = value;
    return input;
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to="/service-orders" className="text-blue-500 hover:underline">
          ← Back to Service Orders
        </Link>
      </div>

      {offlineMessage && (
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
          <p className="font-bold">Offline Mode</p>
          <p>{offlineMessage}</p>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{serviceOrder.title}</h1>
        <div className="flex gap-2">
          <Link to={`/service-orders/${serviceOrder.id}/edit`}>
            <Button variant="outline" disabled={isOffline}>Edit</Button>
          </Link>
          <Button variant="destructive" onClick={handleDelete} disabled={isOffline}>
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Service Order Information */}
        <Card>
          <CardHeader>
            <CardTitle>Service Order Details</CardTitle>
            <CardDescription className="flex gap-2">
              <span className={`px-2 py-1 rounded text-xs ${getStatusColor(serviceOrder.status)}`}>
                {serviceOrder.status.replace("_", " ")}
              </span>
              <span className={`px-2 py-1 rounded text-xs ${getPriorityColor(serviceOrder.priority)}`}>
                {serviceOrder.priority}
              </span>
              <span className={`px-2 py-1 rounded text-xs ${getTypeColor(serviceOrder.type)}`}>
                {serviceOrder.type}
              </span>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Title</Label>
              <p className="text-lg">{serviceOrder.title}</p>
            </div>

            <div>
              <Label>Description</Label>
              <p className="whitespace-pre-line">{serviceOrder.description || "No description provided"}</p>
            </div>

            <div>
              <Label>Scheduled Date</Label>
              <p>{formatDate(serviceOrder.scheduledDate)}</p>
            </div>

            <div>
              <Label>Completion Date</Label>
              <p>{formatDate(serviceOrder.completedDate)}</p>
            </div>

            {serviceOrder.notes && (
              <div>
                <Label>Notes</Label>
                <p className="whitespace-pre-line">{serviceOrder.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent>
            {serviceOrder.customer ? (
              <div className="space-y-4">
                <div>
                  <Label>Name</Label>
                  <p>
                    <Link to={`/customers/${serviceOrder.customer.id}`} className="text-blue-500 hover:underline">
                      {serviceOrder.customer.name}
                    </Link>
                  </p>
                </div>

                {serviceOrder.customer.email && (
                  <div>
                    <Label>Email</Label>
                    <p>{serviceOrder.customer.email}</p>
                  </div>
                )}

                {serviceOrder.customer.phone && (
                  <div>
                    <Label>Phone</Label>
                    <p>{serviceOrder.customer.phone}</p>
                  </div>
                )}

                {serviceOrder.customer.address && (
                  <div>
                    <Label>Address</Label>
                    <p>{serviceOrder.customer.address}</p>
                    {serviceOrder.customer.city && serviceOrder.customer.postalCode && (
                      <p>
                        {serviceOrder.customer.city}, {serviceOrder.customer.postalCode}
                        {serviceOrder.customer.country ? `, ${serviceOrder.customer.country}` : ""}
                      </p>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500">No customer information available</p>
            )}
          </CardContent>
        </Card>

        {/* Device Information */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Device Information</CardTitle>
          </CardHeader>
          <CardContent>
            {serviceOrder.device ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Name</Label>
                  <p>
                    <Link to={`/devices/${serviceOrder.device.id}`} className="text-blue-500 hover:underline">
                      {serviceOrder.device.name}
                    </Link>
                  </p>
                </div>

                {serviceOrder.device.model && (
                  <div>
                    <Label>Model</Label>
                    <p>{serviceOrder.device.model}</p>
                  </div>
                )}

                {serviceOrder.device.serialNumber && (
                  <div>
                    <Label>Serial Number</Label>
                    <p>{serviceOrder.device.serialNumber}</p>
                  </div>
                )}

                {serviceOrder.device.manufacturer && (
                  <div>
                    <Label>Manufacturer</Label>
                    <p>{serviceOrder.device.manufacturer}</p>
                  </div>
                )}

                {serviceOrder.device.installationDate && (
                  <div>
                    <Label>Installation Date</Label>
                    <p>{formatDate(serviceOrder.device.installationDate)}</p>
                  </div>
                )}

                {serviceOrder.device.warrantyExpiryDate && (
                  <div>
                    <Label>Warranty Expiry Date</Label>
                    <p>{formatDate(serviceOrder.device.warrantyExpiryDate)}</p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500">No device information available</p>
            )}
          </CardContent>
          {serviceOrder.device && (
            <CardFooter>
              <Link to={`/devices/${serviceOrder.device.id}`}>
                <Button variant="outline">View Device Details</Button>
              </Link>
            </CardFooter>
          )}
        </Card>
      </div>
    </div>
  );
}

// Helper function to get status color
function getStatusColor(status: string) {
  switch (status.toUpperCase()) {
    case "PENDING":
      return "bg-yellow-100 text-yellow-800";
    case "IN_PROGRESS":
      return "bg-blue-100 text-blue-800";
    case "COMPLETED":
      return "bg-green-100 text-green-800";
    case "CANCELLED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Helper function to get priority color
function getPriorityColor(priority: string) {
  switch (priority.toUpperCase()) {
    case "LOW":
      return "bg-green-100 text-green-800";
    case "MEDIUM":
      return "bg-blue-100 text-blue-800";
    case "HIGH":
      return "bg-orange-100 text-orange-800";
    case "URGENT":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Helper function to get type color
function getTypeColor(type: string) {
  switch (type?.toUpperCase()) {
    case "SERVICE":
      return "bg-blue-100 text-blue-800";
    case "INSTALLATION":
      return "bg-green-100 text-green-800";
    case "INSPECTION":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}
