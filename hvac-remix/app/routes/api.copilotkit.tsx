/**
 * CopilotKit Runtime API Route for HVAC-Remix
 * Handles CopilotKit runtime requests and integrates with TruBackend services
 */

import { CopilotRuntime, OpenAIAdapter } from '@copilotkit/runtime';
import type { ActionFunctionArgs } from '@remix-run/node';
import { getCopilotKitBridge } from '~/services/copilotkit-bridge.server';

// Initialize CopilotKit Runtime with TruBackend integration
export async function action({ request }: ActionFunctionArgs) {
  const bridge = getCopilotKitBridge();

  const runtime = new CopilotRuntime({
    // Use OpenAI as the primary LLM adapter
    adapter: new OpenAIAdapter({
      apiKey: process.env.OPENAI_API_KEY || '',
      model: 'gpt-4o'
    }),

    // HVAC-specific instructions for the AI assistant
    instructions: `
You are an expert HVAC CRM AI Assistant with access to advanced AI services through TruBackend.

CORE CAPABILITIES:
- Customer issue analysis using Bielik V3 (Polish/English support)
- Memory Bank search for customer history and patterns
- Professional response generation via Executive AI Assistant
- Service route optimization and scheduling
- Predictive maintenance analysis
- Document analysis (manuals, invoices, photos, reports)
- Real-time system health monitoring

AVAILABLE ACTIONS:
1. analyzeCustomerIssue - Comprehensive HVAC issue analysis
2. searchCustomerHistory - Customer interaction and service history search
3. askBielikExpert - Direct consultation with Bielik V3 AI model
4. generateProfessionalResponse - Professional email response generation
5. optimizeServiceRoute - Technician route optimization
6. predictMaintenanceNeeds - AI-powered maintenance predictions
7. analyzeDocument - Document analysis and data extraction
8. checkSystemStatus - TruBackend services health monitoring

INSTRUCTIONS:
- Always prioritize customer safety for HVAC issues
- Use Polish language when customers communicate in Polish
- Leverage Memory Bank for personalized, context-aware responses
- Provide confidence scores and reasoning for all analyses
- Suggest specific next actions based on analysis results
- For urgent issues (AC failures, heating problems), escalate immediately
- Include cost estimates and timelines when possible
- Reference relevant historical data and similar cases

RESPONSE FORMAT:
- Be concise but comprehensive
- Include confidence scores when available
- Highlight urgent issues clearly with 🚨 emoji
- Provide actionable recommendations with specific steps
- Reference relevant historical data when applicable
- Use appropriate emojis for better readability (🔧 for repairs, ❄️ for AC, 🔥 for heating)

SAFETY PROTOCOLS:
- For gas leaks or electrical issues: Immediate emergency response
- For carbon monoxide concerns: Urgent safety evacuation
- For system failures in extreme weather: Priority scheduling
- For commercial HVAC: Consider business impact and downtime

You have access to the full TruBackend AI/ML pipeline including Bielik V3, Memory Bank, and Executive Assistant. Be proactive, intelligent, and always customer-focused!
    `,

    // Custom actions for HVAC operations
    actions: [
      {
        name: 'analyzeCustomerIssue',
        description: 'Analyze customer HVAC issue with comprehensive AI analysis',
        parameters: {
          type: 'object',
          properties: {
            customerMessage: { type: 'string', description: 'Customer issue description' },
            customerId: { type: 'string', description: 'Customer ID (optional)' },
            urgency: { type: 'string', enum: ['low', 'medium', 'high', 'critical'], default: 'medium' }
          },
          required: ['customerMessage']
        },
        handler: async ({ customerMessage, customerId, urgency = 'medium' }) => {
          try {
            return await bridge.analyzeCustomerIssue(customerMessage, customerId, urgency);
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Analysis failed',
              message: 'Failed to analyze customer issue. Please try again.'
            };
          }
        }
      },

      {
        name: 'searchCustomerHistory',
        description: 'Search customer history and service records',
        parameters: {
          type: 'object',
          properties: {
            query: { type: 'string', description: 'Search query' },
            customerId: { type: 'string', description: 'Customer ID (optional)' },
            limit: { type: 'number', default: 10, minimum: 1, maximum: 50 }
          },
          required: ['query']
        },
        handler: async ({ query, customerId, limit = 10 }) => {
          try {
            return await bridge.searchMemoryBank(query, customerId, limit);
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Search failed',
              message: 'Failed to search customer history.'
            };
          }
        }
      },

      {
        name: 'askBielikExpert',
        description: 'Consult Bielik V3 AI for expert technical analysis',
        parameters: {
          type: 'object',
          properties: {
            question: { type: 'string', description: 'Technical question' },
            context: { type: 'string', description: 'Additional context (optional)' },
            analysisType: { 
              type: 'string', 
              enum: ['semantic_analysis', 'intent_detection', 'technical_classification'],
              default: 'technical_classification'
            }
          },
          required: ['question']
        },
        handler: async ({ question, context, analysisType = 'technical_classification' }) => {
          try {
            return await bridge.askBielik(
              question,
              context ? { additional_context: context } : undefined,
              analysisType
            );
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Bielik consultation failed',
              message: 'Failed to get expert analysis from Bielik V3.'
            };
          }
        }
      },

      {
        name: 'generateProfessionalResponse',
        description: 'Generate professional email response',
        parameters: {
          type: 'object',
          properties: {
            emailContext: { type: 'object', description: 'Email context' },
            tone: { 
              type: 'string', 
              enum: ['professional', 'friendly', 'urgent', 'apologetic'],
              default: 'professional'
            },
            includeScheduling: { type: 'boolean', default: false }
          },
          required: ['emailContext']
        },
        handler: async ({ emailContext, tone = 'professional', includeScheduling = false }) => {
          try {
            return await bridge.generateResponse(emailContext, tone, includeScheduling);
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Response generation failed',
              message: 'Failed to generate professional response.'
            };
          }
        }
      },

      {
        name: 'checkSystemStatus',
        description: 'Check TruBackend services health status',
        parameters: { type: 'object', properties: {}, required: [] },
        handler: async () => {
          try {
            return await bridge.getTruBackendStatus();
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Status check failed',
              message: 'Failed to check system status.'
            };
          }
        }
      }
    ]
  });

  return runtime.streamHttpServerResponse(request);
}

// Health check endpoint
export async function loader() {
  return new Response(JSON.stringify({
    status: 'healthy',
    service: 'HVAC-Remix CopilotKit Runtime',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    trubackend_integration: 'enabled'
  }), {
    headers: { 'Content-Type': 'application/json' }
  });
}