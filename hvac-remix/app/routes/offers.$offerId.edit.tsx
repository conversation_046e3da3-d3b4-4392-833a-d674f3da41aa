import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { OfferForm } from "~/components/organisms/OfferForm";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { prisma } from "~/db.server";
import { getOfferTemplates } from "~/services/offer-template.service";
import { getOffer, updateOffer } from "~/services/offer.service";
import { requireUserId } from "~/session.server";

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  const { offerId } = params;
  
  if (!offerId) {
    throw new Response("Offer ID is required", { status: 400 });
  }
  
  const offer = await getOffer({ id: offerId, userId });
  
  if (!offer) {
    throw new Response("Offer not found", { status: 404 });
  }
  
  const customers = await prisma.customer.findMany({
    where: { userId },
    select: { id: true, name: true },
    orderBy: { name: "asc" },
  });
  
  const templates = await getOfferTemplates();
  
  return json({ offer, customers, templates });
};

export const action = async ({ params, request }: ActionFunctionArgs) => {
  const userId = await requireUserId(request);
  const { offerId } = params;
  
  if (!offerId) {
    throw new Response("Offer ID is required", { status: 400 });
  }
  
  const formData = await request.formData();
  
  const title = formData.get("title") as string;
  const description = formData.get("description") as string;
  const status = formData.get("status") as string;
  const totalAmount = parseFloat(formData.get("totalAmount") as string);
  const taxAmount = formData.get("taxAmount") ? parseFloat(formData.get("taxAmount") as string) : null;
  const discountAmount = formData.get("discountAmount") ? parseFloat(formData.get("discountAmount") as string) : null;
  const validUntilStr = formData.get("validUntil") as string;
  const validUntil = validUntilStr ? new Date(validUntilStr) : null;
  const notes = formData.get("notes") as string;
  
  const errors: Record<string, string> = {};
  
  if (!title) errors.title = "Title is required";
  if (isNaN(totalAmount)) errors.totalAmount = "Total amount must be a number";
  
  if (Object.keys(errors).length > 0) {
    return json({ errors });
  }
  
  await updateOffer({
    id: offerId,
    title,
    description,
    status,
    totalAmount,
    taxAmount,
    discountAmount,
    validUntil,
    notes,
    userId,
  });
  
  return redirect(`/offers/${offerId}`);
};

export default function EditOfferPage() {
  const { offer, customers, templates } = useLoaderData<typeof loader>();
  
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Edit Offer</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>Offer Details</CardTitle>
        </CardHeader>
        <CardContent>
          <OfferForm
            defaultValues={{
              id: offer.id,
              title: offer.title,
              description: offer.description,
              status: offer.status,
              validUntil: offer.validUntil,
              totalAmount: offer.totalAmount,
              taxAmount: offer.taxAmount,
              discountAmount: offer.discountAmount,
              notes: offer.notes,
              customerId: offer.customerId,
              templateId: offer.templateId,
            }}
            customers={customers}
            templates={templates}
            mode="edit"
          />
        </CardContent>
      </Card>
    </div>
  );
}