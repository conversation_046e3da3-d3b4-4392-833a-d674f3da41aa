import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData, useSearchParams } from "@remix-run/react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { getCustomerPortalData } from "~/services/customer-portal.server";
import { getUser } from "~/session.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await getUser(request);

  if (!user || user.role !== "CUSTOMER") {
    throw new Response("Unauthorized", { status: 401 });
  }

  const customerResponse = await getCustomerPortalData(user.id);

  if (!customerResponse.success || !customerResponse.data) {
    throw new Response("Failed to load customer data", { status: 500 });
  }

  return json({ customer: customerResponse.data });
};

export default function CustomerPortalServiceOrders() {
  const { customer } = useLoaderData<typeof loader>();
  const [searchParams] = useSearchParams();
  const statusFilter = searchParams.get("status") || "all";

  // Filter service orders by status
  const filteredServiceOrders = statusFilter === "all"
    ? customer.serviceOrders
    : customer.serviceOrders.filter(order => order.status === statusFilter);

  // Group service orders by status for the tabs
  const pendingOrders = customer.serviceOrders.filter(order => order.status === "PENDING");
  const inProgressOrders = customer.serviceOrders.filter(order => order.status === "IN_PROGRESS");
  const completedOrders = customer.serviceOrders.filter(order => order.status === "COMPLETED");
  const cancelledOrders = customer.serviceOrders.filter(order => order.status === "CANCELLED");

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Service History</h1>
        <Button asChild>
          <Link to="/customer-portal/request-service">Request Service</Link>
        </Button>
      </div>

      <Tabs defaultValue={statusFilter === "all" ? "all" : statusFilter}>
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="all" asChild>
            <Link to="/customer-portal/service-orders">All ({customer.serviceOrders.length})</Link>
          </TabsTrigger>
          <TabsTrigger value="PENDING" asChild>
            <Link to="/customer-portal/service-orders?status=PENDING">Pending ({pendingOrders.length})</Link>
          </TabsTrigger>
          <TabsTrigger value="IN_PROGRESS" asChild>
            <Link to="/customer-portal/service-orders?status=IN_PROGRESS">In Progress ({inProgressOrders.length})</Link>
          </TabsTrigger>
          <TabsTrigger value="COMPLETED" asChild>
            <Link to="/customer-portal/service-orders?status=COMPLETED">Completed ({completedOrders.length})</Link>
          </TabsTrigger>
          <TabsTrigger value="CANCELLED" asChild>
            <Link to="/customer-portal/service-orders?status=CANCELLED">Cancelled ({cancelledOrders.length})</Link>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          {renderServiceOrdersList(customer.serviceOrders)}
        </TabsContent>

        <TabsContent value="PENDING" className="mt-6">
          {renderServiceOrdersList(pendingOrders)}
        </TabsContent>

        <TabsContent value="IN_PROGRESS" className="mt-6">
          {renderServiceOrdersList(inProgressOrders)}
        </TabsContent>

        <TabsContent value="COMPLETED" className="mt-6">
          {renderServiceOrdersList(completedOrders)}
        </TabsContent>

        <TabsContent value="CANCELLED" className="mt-6">
          {renderServiceOrdersList(cancelledOrders)}
        </TabsContent>
      </Tabs>
    </div>
  );
}

function renderServiceOrdersList(serviceOrders: any[]) {
  if (serviceOrders.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Service Orders Found</CardTitle>
          <CardDescription>
            No service orders match the selected filter.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {serviceOrders.map((order) => (
        <Card key={order.id}>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle>{order.title}</CardTitle>
                <CardDescription>
                  {order.scheduledDate
                    ? `Scheduled for ${new Date(order.scheduledDate).toLocaleDateString()}`
                    : "Not scheduled"}
                </CardDescription>
              </div>
              <Badge
                variant={
                  order.status === "COMPLETED" ? "success" :
                  order.status === "IN_PROGRESS" ? "warning" :
                  order.status === "PENDING" ? "default" :
                  "destructive"
                }
              >
                {order.status}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Device:</span>
                <span>{order.device?.name || "N/A"}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Priority:</span>
                <Badge variant="outline">{order.priority}</Badge>
              </div>
              {order.description && (
                <div>
                  <span className="text-sm text-muted-foreground">Description:</span>
                  <p className="mt-1">{order.description}</p>
                </div>
              )}
            </div>

            <div className="mt-4 flex justify-end">
              <Button asChild variant="outline" size="sm">
                <Link to={`/customer-portal/service-orders/${order.id}`}>View Details</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}