import { ArrowLeftIcon, DocumentTextIcon } from "@heroicons/react/24/outline";
import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { FileUpload } from "~/components/molecules/file-upload";
import { prisma } from "~/db.server";
import { uploadFile, validateOcrFile } from "~/services/ocr/file-upload.server";
import { processServiceReport } from "~/services/ocr/ocr.server";
import { requireUserId } from "~/session.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  await requireUserId(request);
  const { reportId } = params;
  
  if (!reportId) {
    throw new Response("Service Report ID is required", { status: 400 });
  }
  
  const report = await prisma.serviceReport.findUnique({
    where: { id: reportId },
    include: {
      serviceOrder: {
        include: {
          customer: true,
          device: true,
        },
      },
    },
  });
  
  if (!report) {
    throw new Response("Service Report not found", { status: 404 });
  }
  
  return json({ report });
}

export async function action({ request, params }: ActionFunctionArgs) {
  await requireUserId(request);
  const { reportId } = params;
  
  if (!reportId) {
    return json({ error: "Service Report ID is required" }, { status: 400 });
  }
  
  const formData = await request.formData();
  const reportFile = formData.get("reportFile") as File;
  
  if (!reportFile || reportFile.size === 0) {
    return json({ error: "Service Report file is required" }, { status: 400 });
  }
  
  if (!validateOcrFile(reportFile)) {
    return json({ 
      error: "Invalid file. Please upload a PDF or image file (JPEG, PNG, TIFF) under 10MB." 
    }, { status: 400 });
  }
  
  try {
    // Upload the file
    const fileUrl = await uploadFile(reportFile, "service-reports");
    
    // Process the service report with OCR
    await processServiceReport(reportId, fileUrl);
    
    return redirect(`/service-reports/${reportId}`);
  } catch (error) {
    console.error("Error processing service report:", error);
    return json({ 
      error: "An error occurred while processing the service report. Please try again." 
    }, { status: 500 });
  }
}

export default function ProcessServiceReport() {
  const { report } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  
  const isSubmitting = navigation.state === "submitting";
  
  return (
    <div>
      <div className="mb-6 flex items-center">
        <Link to={`/service-reports/${report.id}`} className="mr-4 text-gray-500 hover:text-gray-700">
          <ArrowLeftIcon className="h-5 w-5" />
        </Link>
        <h2 className="text-xl font-semibold">
          Process Service Report with OCR
        </h2>
      </div>
      
      <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Service Report Information</h3>
        </div>
        <div className="grid grid-cols-1 gap-6 px-6 py-4 md:grid-cols-2">
          <div>
            <p className="text-sm font-medium text-gray-500">Title</p>
            <p className="mt-1 text-sm text-gray-900">{report.title}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Service Order</p>
            <p className="mt-1 text-sm text-gray-900">
              <Link
                to={`/service-orders/${report.serviceOrder.id}`}
                className="text-blue-600 hover:text-blue-900"
              >
                {report.serviceOrder.title}
              </Link>
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Customer</p>
            <p className="mt-1 text-sm text-gray-900">{report.serviceOrder.customer.name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Current OCR Status</p>
            <p className="mt-1">
              <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                report.ocrProcessingStatus === "COMPLETED" 
                  ? "bg-green-100 text-green-800" 
                  : report.ocrProcessingStatus === "FAILED" 
                  ? "bg-red-100 text-red-800" 
                  : report.ocrProcessingStatus === "PROCESSING" 
                  ? "bg-blue-100 text-blue-800" 
                  : "bg-gray-100 text-gray-800"
              }`}>
                {report.ocrProcessingStatus}
              </span>
            </p>
          </div>
        </div>
      </div>
      
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Upload Service Report Document</h3>
        </div>
        <div className="px-6 py-4">
          <Form method="post" encType="multipart/form-data">
            <div className="mb-6">
              <FileUpload
                name="reportFile"
                label="Service Report Document"
                accept="application/pdf,image/jpeg,image/png,image/tiff"
                required
                description="Upload a PDF or image of the service report"
                error={actionData?.error}
              />
            </div>
            
            <div className="mt-6 flex justify-end">
              <Link
                to={`/service-reports/${report.id}`}
                className="mr-4 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
              >
                {isSubmitting ? (
                  <>Processing...</>
                ) : (
                  <>
                    <DocumentTextIcon className="mr-2 h-5 w-5" />
                    Process with OCR
                  </>
                )}
              </button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
}