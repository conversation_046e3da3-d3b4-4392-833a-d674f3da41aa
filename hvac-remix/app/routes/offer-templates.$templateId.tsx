import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, Link, useLoaderData } from "@remix-run/react";
import { ArrowLeft, Trash, Edit } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { getOfferTemplate, deleteOfferTemplate, updateOfferTemplate } from "~/services/offer-template.service";
import { requireUserId } from "~/session.server";
import { formatDate } from "~/utils";

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  await requireUserId(request);
  const { templateId } = params;
  
  if (!templateId) {
    throw new Response("Template ID is required", { status: 400 });
  }
  
  const template = await getOfferTemplate(templateId);
  
  if (!template) {
    throw new Response("Template not found", { status: 404 });
  }
  
  return json({ template });
};

export const action = async ({ params, request }: ActionFunctionArgs) => {
  await requireUserId(request);
  const { templateId } = params;
  
  if (!templateId) {
    throw new Response("Template ID is required", { status: 400 });
  }
  
  const formData = await request.formData();
  const action = formData.get("_action") as string;
  
  switch (action) {
    case "delete":
      await deleteOfferTemplate(templateId);
      return redirect("/offer-templates");
    
    case "set-default":
      await updateOfferTemplate({
        id: templateId,
        isDefault: true,
      });
      return json({ success: true });
    
    default:
      throw new Response(`Unknown action: ${action}`, { status: 400 });
  }
};

export default function OfferTemplateDetailPage() {
  const { template } = useLoaderData<typeof loader>();
  
  // Parse the template content
  let parsedContent;
  try {
    parsedContent = JSON.parse(template.content);
  } catch (error) {
    parsedContent = { error: "Invalid JSON content" };
  }
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link to="/offer-templates">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Templates
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">{template.name}</h1>
          {template.isDefault && (
            <span className="bg-primary/10 text-primary text-xs px-2 py-1 rounded">
              Default
            </span>
          )}
        </div>
        <div className="flex gap-2">
          <Link to={`/offer-templates/${template.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </Link>
          {!template.isDefault && (
            <Form method="post" className="inline">
              <input type="hidden" name="_action" value="set-default" />
              <Button type="submit" variant="outline">
                Set as Default
              </Button>
            </Form>
          )}
          <Form method="post" className="inline" onSubmit={(e) => {
            if (!confirm("Are you sure you want to delete this template?")) {
              e.preventDefault();
            }
          }}>
            <input type="hidden" name="_action" value="delete" />
            <Button type="submit" variant="destructive">
              <Trash className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </Form>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {parsedContent.sections?.map((section: any, index: number) => (
                  <div key={index} className="space-y-2">
                    <h3 className="text-lg font-medium">{section.title}</h3>
                    <div className="whitespace-pre-line">{section.content}</div>
                  </div>
                ))}
                
                {parsedContent.footer && (
                  <div className="border-t pt-4 mt-6">
                    <p className="text-sm">{parsedContent.footer}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Raw Template Content</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                {JSON.stringify(parsedContent, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </div>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {template.description && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                    <p className="mt-1">{template.description}</p>
                  </div>
                )}
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Created</h3>
                  <p className="mt-1">{formatDate(new Date(template.createdAt))}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Last Updated</h3>
                  <p className="mt-1">{formatDate(new Date(template.updatedAt))}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                  <p className="mt-1">
                    {template.isDefault ? "Default Template" : "Regular Template"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This template can be applied to offers during creation or editing.
                {template.isDefault && " It will be automatically selected for new offers."}
              </p>
              <div className="mt-4">
                <Link to="/offers/new">
                  <Button variant="outline" size="sm" className="w-full">
                    Create Offer with this Template
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}