import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { <PERSON>, useLoaderData } from "@remix-run/react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { requireUserId } from "~/session.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  return json({});
};

export default function VisualizationsIndexPage() {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Visualizations</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link to="/visualizations/service-order-flow" className="block h-full">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart className="h-5 w-5 mr-2" />
                Service Order Flow
              </CardTitle>
              <CardDescription>
                Visualize the flow of service orders through different statuses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This visualization shows how service orders move through your workflow,
                from creation to completion. Track status changes, identify bottlenecks,
                and analyze completion times.
              </p>
            </CardContent>
          </Card>
        </Link>
        
        <Link to="/visualizations/data-exploration" className="block h-full">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center">
                <PieChart className="h-5 w-5 mr-2" />
                Interactive Data Exploration
              </CardTitle>
              <CardDescription>
                Explore your business data with interactive visualizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Dive deep into your business data with interactive charts and graphs.
                Analyze trends, identify patterns, and gain insights into customer behavior,
                service performance, and more.
              </p>
            </CardContent>
          </Card>
        </Link>
        
        <Link to="/visualizations/technician-performance" className="block h-full">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center">
                <LineChart className="h-5 w-5 mr-2" />
                Technician Performance
              </CardTitle>
              <CardDescription>
                Analyze technician performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Track and compare technician performance with detailed metrics.
                View completion rates, customer satisfaction scores, average service times,
                and more to optimize your team's efficiency.
              </p>
              <div className="mt-4 text-xs text-muted-foreground italic">
                Coming soon
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
}