import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, Link, useLoaderData, useNavigation } from "@remix-run/react";
import { ArrowLeft } from "lucide-react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { getOffer, addOfferItem, recalculateOfferTotal } from "~/services/offer.service";
import { requireUserId } from "~/session.server";

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  const { offerId } = params;
  
  if (!offerId) {
    throw new Response("Offer ID is required", { status: 400 });
  }
  
  const offer = await getOffer({ id: offerId, userId });
  
  if (!offer) {
    throw new Response("Offer not found", { status: 404 });
  }
  
  return json({ offer });
};

export const action = async ({ params, request }: ActionFunctionArgs) => {
  const userId = await requireUserId(request);
  const { offerId } = params;
  
  if (!offerId) {
    throw new Response("Offer ID is required", { status: 400 });
  }
  
  const formData = await request.formData();
  
  const description = formData.get("description") as string;
  const quantity = parseFloat(formData.get("quantity") as string);
  const unitPrice = parseFloat(formData.get("unitPrice") as string);
  const taxRate = formData.get("taxRate") ? parseFloat(formData.get("taxRate") as string) : undefined;
  
  const errors: Record<string, string> = {};
  
  if (!description) errors.description = "Description is required";
  if (isNaN(quantity) || quantity <= 0) errors.quantity = "Quantity must be a positive number";
  if (isNaN(unitPrice) || unitPrice < 0) errors.unitPrice = "Unit price must be a non-negative number";
  if (taxRate !== undefined && (isNaN(taxRate) || taxRate < 0)) errors.taxRate = "Tax rate must be a non-negative number";
  
  if (Object.keys(errors).length > 0) {
    return json({ errors });
  }
  
  const totalPrice = quantity * unitPrice;
  
  await addOfferItem({
    offerId,
    description,
    quantity,
    unitPrice,
    totalPrice,
    taxRate,
    userId,
  });
  
  // Recalculate the offer total
  await recalculateOfferTotal({ offerId, userId });
  
  return redirect(`/offers/${offerId}`);
};

export default function NewOfferItemPage() {
  const { offer } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Link to={`/offers/${offer.id}`}>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Offer
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Add Item to Offer</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Item Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form method="post" className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  rows={3}
                  required
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    name="quantity"
                    type="number"
                    step="0.01"
                    min="0.01"
                    defaultValue="1"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="unitPrice">Unit Price</Label>
                  <Input
                    id="unitPrice"
                    name="unitPrice"
                    type="number"
                    step="0.01"
                    min="0"
                    defaultValue="0"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="taxRate">Tax Rate (%)</Label>
                  <Input
                    id="taxRate"
                    name="taxRate"
                    type="number"
                    step="0.01"
                    min="0"
                    defaultValue="23"
                  />
                </div>
              </div>
            </div>
            
            <div className="flex justify-end gap-2">
              <Link to={`/offers/${offer.id}`}>
                <Button variant="outline" type="button" disabled={isSubmitting}>
                  Cancel
                </Button>
              </Link>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Adding..." : "Add Item"}
              </Button>
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}