import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { CheckCircle2, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Checkbox } from "~/components/ui/checkbox";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { getUserSettings, updateDashboardSettings , convertToAppSettings } from "~/models/user-settings.server";
import { requireUser } from "~/session.server";
import type { UserRole } from "~/types/shared";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await requireUser(request);
  
  // Get user dashboard settings
  const dbSettings = await getUserSettings(user.id);
  const userSettings = dbSettings ? convertToAppSettings(dbSettings) : {
    theme: {
      theme: 'system',
      fontSize: 'medium',
      colorScheme: 'default'
    },
    dashboard: {
      showStats: true,
      showRecentOrders: true,
      showUpcomingEvents: true,
      showQuickActions: true,
      layout: 'grid',
      widgets: [
        { id: 'service-orders', position: 0, size: 'medium' },
        { id: 'device-types', position: 1, size: 'medium' },
        { id: 'revenue', position: 2, size: 'large' },
        { id: 'technician-performance', position: 3, size: 'medium' },
        { id: 'maintenance-forecast', position: 4, size: 'medium' }
      ],
      statsToShow: ['serviceOrders', 'customers', 'devices', 'revenue']
    },
    notifications: {
      email: true,
      inApp: true,
      push: false,
      serviceOrderUpdates: true,
      calendarReminders: true,
      systemUpdates: true
    }
  };
  
  // Get available widgets based on user role
  const availableWidgets = getAvailableWidgetsForRole(user.role as UserRole);
  
  return json({
    user,
    settings: userSettings,
    availableWidgets
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const user = await requireUser(request);
  const formData = await request.formData();
  
  const showStats = formData.get("showStats") === "on";
  const showRecentOrders = formData.get("showRecentOrders") === "on";
  const showUpcomingEvents = formData.get("showUpcomingEvents") === "on";
  const showQuickActions = formData.get("showQuickActions") === "on";
  const layout = formData.get("layout") as string || "grid";
  
  // Get stats to show
  const statsToShow = [];
  if (formData.get("showServiceOrders") === "on") statsToShow.push("serviceOrders");
  if (formData.get("showCustomers") === "on") statsToShow.push("customers");
  if (formData.get("showDevices") === "on") statsToShow.push("devices");
  if (formData.get("showRevenue") === "on") statsToShow.push("revenue");
  
  // Get widgets from form data
  const widgetsData = formData.get("widgetsData");
  let widgets = [];
  
  if (widgetsData) {
    try {
      widgets = JSON.parse(widgetsData as string);
    } catch (error) {
      console.error("Error parsing widgets data:", error);
    }
  }
  
  try {
    // Update dashboard settings
    await updateDashboardSettings(user.id, {
      showStats,
      showRecentOrders,
      showUpcomingEvents,
      showQuickActions,
      layout,
      widgets,
      statsToShow
    });
    
    return json({ success: true });
  } catch (error) {
    console.error("Error updating dashboard settings:", error);
    return json({ 
      success: false, 
      error: "Nie udało się zapisać ustawień dashboardu" 
    });
  }
};

// Helper function to get available widgets based on user role
function getAvailableWidgetsForRole(role: UserRole) {
  const baseWidgets = [
    { id: 'service-orders', name: 'Zlecenia serwisowe', description: 'Liczba zleceń serwisowych w czasie' },
    { id: 'device-types', name: 'Typy urządzeń', description: 'Podział urządzeń według typu' },
    { id: 'upcoming-service', name: 'Nadchodzące zlecenia', description: 'Nadchodzące zlecenia serwisowe' },
  ];
  
  const managerWidgets = [
    { id: 'revenue', name: 'Przychody', description: 'Przychody w czasie' },
    { id: 'technician-performance', name: 'Wydajność techników', description: 'Porównanie wydajności techników' },
    { id: 'customer-satisfaction', name: 'Zadowolenie klientów', description: 'Wskaźnik zadowolenia klientów' },
  ];
  
  const adminWidgets = [
    { id: 'system-health', name: 'Stan systemu', description: 'Monitorowanie stanu systemu' },
    { id: 'user-activity', name: 'Aktywność użytkowników', description: 'Aktywność użytkowników w czasie' },
  ];
  
  const technicianWidgets = [
    { id: 'my-performance', name: 'Moja wydajność', description: 'Twoje statystyki wydajności' },
    { id: 'my-schedule', name: 'Mój harmonogram', description: 'Twój harmonogram na najbliższy czas' },
  ];
  
  switch (role) {
    case 'ADMIN':
      return [...baseWidgets, ...managerWidgets, ...adminWidgets];
    case 'MANAGER':
      return [...baseWidgets, ...managerWidgets];
    case 'TECHNICIAN':
      return [...baseWidgets, ...technicianWidgets];
    default:
      return baseWidgets;
  }
}

export default function DashboardCustomizePage() {
  const { settings, availableWidgets } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Dostosuj swój dashboard</h1>
        
        {actionData?.success && (
          <Alert className="mb-6 bg-green-50 dark:bg-green-900/20">
            <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
            <AlertTitle>Sukces</AlertTitle>
            <AlertDescription>
              Ustawienia dashboardu zostały zapisane pomyślnie.
            </AlertDescription>
          </Alert>
        )}
        
        {actionData?.error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Błąd</AlertTitle>
            <AlertDescription>
              {actionData.error}
            </AlertDescription>
          </Alert>
        )}
        
        <Form method="post">
          <Tabs defaultValue="layout" className="space-y-6">
            <TabsList>
              <TabsTrigger value="layout">Układ</TabsTrigger>
              <TabsTrigger value="components">Komponenty</TabsTrigger>
              <TabsTrigger value="widgets">Widgety</TabsTrigger>
            </TabsList>
            
            <TabsContent value="layout" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Układ dashboardu</CardTitle>
                  <CardDescription>
                    Wybierz preferowany układ dashboardu
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup defaultValue={settings.dashboard.layout || "grid"} name="layout" className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="grid" id="layout-grid" />
                      <Label htmlFor="layout-grid">Siatka</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="columns" id="layout-columns" />
                      <Label htmlFor="layout-columns">Kolumny</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="masonry" id="layout-masonry" />
                      <Label htmlFor="layout-masonry">Masonry</Label>
                    </div>
                  </RadioGroup>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="components" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Komponenty dashboardu</CardTitle>
                  <CardDescription>
                    Wybierz, które komponenty mają być widoczne na dashboardzie
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="showStats" 
                      name="showStats" 
                      defaultChecked={settings.dashboard.showStats} 
                    />
                    <Label htmlFor="showStats">Pokaż statystyki</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="showRecentOrders" 
                      name="showRecentOrders" 
                      defaultChecked={settings.dashboard.showRecentOrders} 
                    />
                    <Label htmlFor="showRecentOrders">Pokaż ostatnie zlecenia</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="showUpcomingEvents" 
                      name="showUpcomingEvents" 
                      defaultChecked={settings.dashboard.showUpcomingEvents} 
                    />
                    <Label htmlFor="showUpcomingEvents">Pokaż nadchodzące wydarzenia</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="showQuickActions" 
                      name="showQuickActions" 
                      defaultChecked={settings.dashboard.showQuickActions} 
                    />
                    <Label htmlFor="showQuickActions">Pokaż szybkie akcje</Label>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Statystyki</CardTitle>
                  <CardDescription>
                    Wybierz, które statystyki mają być widoczne
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="showServiceOrders" 
                      name="showServiceOrders" 
                      defaultChecked={settings.dashboard.statsToShow.includes('serviceOrders')} 
                    />
                    <Label htmlFor="showServiceOrders">Zlecenia serwisowe</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="showCustomers" 
                      name="showCustomers" 
                      defaultChecked={settings.dashboard.statsToShow.includes('customers')} 
                    />
                    <Label htmlFor="showCustomers">Klienci</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="showDevices" 
                      name="showDevices" 
                      defaultChecked={settings.dashboard.statsToShow.includes('devices')} 
                    />
                    <Label htmlFor="showDevices">Urządzenia</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="showRevenue" 
                      name="showRevenue" 
                      defaultChecked={settings.dashboard.statsToShow.includes('revenue')} 
                    />
                    <Label htmlFor="showRevenue">Przychody</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="widgets" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Widgety</CardTitle>
                  <CardDescription>
                    Wybierz, które widgety mają być widoczne na dashboardzie
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Widgety można dodawać, usuwać i zmieniać ich rozmiar bezpośrednio na dashboardzie 
                    po włączeniu trybu edycji.
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {availableWidgets.map((widget) => (
                      <Card key={widget.id} className="border border-muted">
                        <CardHeader className="p-4">
                          <CardTitle className="text-base">{widget.name}</CardTitle>
                          <CardDescription>{widget.description}</CardDescription>
                        </CardHeader>
                      </Card>
                    ))}
                  </div>
                  
                  {/* Hidden input to store widgets data */}
                  <input 
                    type="hidden" 
                    name="widgetsData" 
                    value={JSON.stringify(settings.dashboard.widgets || [])} 
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          
          <div className="mt-6 flex justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Zapisywanie..." : "Zapisz ustawienia"}
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
}
