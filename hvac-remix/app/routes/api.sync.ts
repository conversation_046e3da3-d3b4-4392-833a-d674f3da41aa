/**
 * API Routes for Offline Sync
 * 
 * This file contains API endpoints for synchronizing offline data.
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { publishEvent, EventType } from "~/services/eventBus.server";
import { requireUserId } from "~/session.server";

/**
 * Loader function for retrieving data for offline use
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  
  const url = new URL(request.url);
  const type = url.searchParams.get("type");
  const lastSyncTime = url.searchParams.get("lastSync");
  
  try {
    // Parse last sync time if provided
    let lastSync: Date | undefined;
    if (lastSyncTime) {
      lastSync = new Date(lastSyncTime);
      if (isNaN(lastSync.getTime())) {
        return json({ error: "Invalid lastSync parameter" }, { status: 400 });
      }
    }
    
    // Get data based on type
    switch (type) {
      case "service-orders": {
        // Get service orders assigned to the user
        const serviceOrders = await prisma.serviceOrder.findMany({
          where: {
            assignedTechnicianId: userId,
            ...(lastSync ? { updatedAt: { gt: lastSync } } : {})
          },
          include: {
            customer: true,
            device: true,
          },
        });
        
        return json({ data: serviceOrders });
      }
      
      case "service-reports": {
        // Get service reports for service orders assigned to the user
        const serviceReports = await prisma.serviceReport.findMany({
          where: {
            serviceOrder: {
              assignedTechnicianId: userId
            },
            ...(lastSync ? { updatedAt: { gt: lastSync } } : {})
          },
          include: {
            serviceOrder: true,
          },
        });
        
        return json({ data: serviceReports });
      }
      
      case "customers": {
        // Get customers with service orders assigned to the user
        const customers = await prisma.customer.findMany({
          where: {
            serviceOrders: {
              some: {
                assignedTechnicianId: userId
              }
            },
            ...(lastSync ? { updatedAt: { gt: lastSync } } : {})
          },
        });
        
        return json({ data: customers });
      }
      
      case "devices": {
        // Get devices with service orders assigned to the user
        const devices = await prisma.device.findMany({
          where: {
            serviceOrders: {
              some: {
                assignedTechnicianId: userId
              }
            },
            ...(lastSync ? { updatedAt: { gt: lastSync } } : {})
          },
          include: {
            customer: true,
          },
        });
        
        return json({ data: devices });
      }
      
      case "reference-data": {
        // Get reference data for offline use
        const referenceData = {
          serviceTypes: [
            { id: "INSTALLATION", name: "Installation" },
            { id: "REPAIR", name: "Repair" },
            { id: "MAINTENANCE", name: "Maintenance" },
            { id: "INSPECTION", name: "Inspection" },
            { id: "EMERGENCY", name: "Emergency" },
          ],
          priorities: [
            { id: "LOW", name: "Low" },
            { id: "MEDIUM", name: "Medium" },
            { id: "HIGH", name: "High" },
            { id: "URGENT", name: "Urgent" },
          ],
          statuses: [
            { id: "PENDING", name: "Pending" },
            { id: "SCHEDULED", name: "Scheduled" },
            { id: "IN_PROGRESS", name: "In Progress" },
            { id: "COMPLETED", name: "Completed" },
            { id: "CANCELLED", name: "Cancelled" },
          ],
        };
        
        return json({ data: referenceData });
      }
      
      default:
        return json({ error: "Invalid type parameter" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error retrieving data for offline use:", error);
    return json({ error: "Failed to retrieve data" }, { status: 500 });
  }
}

/**
 * Action function for synchronizing offline data
 */
export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  
  // Only allow POST method
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }
  
  try {
    const body = await request.json();
    const { type, data } = body;
    
    if (!type || !data) {
      return json({ error: "Missing required fields: type, data" }, { status: 400 });
    }
    
    // Process data based on type
    switch (type) {
      case "service-orders": {
        // Handle service order sync
        const result = await syncServiceOrder(data, userId);
        return json(result);
      }
      
      case "service-reports": {
        // Handle service report sync
        const result = await syncServiceReport(data, userId);
        return json(result);
      }
      
      case "customers": {
        // Handle customer sync
        const result = await syncCustomer(data, userId);
        return json(result);
      }
      
      case "devices": {
        // Handle device sync
        const result = await syncDevice(data, userId);
        return json(result);
      }
      
      default:
        return json({ error: "Invalid type parameter" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error synchronizing offline data:", error);
    return json({ error: "Failed to synchronize data" }, { status: 500 });
  }
}

/**
 * Synchronize a service order
 */
async function syncServiceOrder(data: any, userId: string) {
  try {
    // Check if service order exists
    const existingServiceOrder = data.id 
      ? await prisma.serviceOrder.findUnique({ where: { id: data.id } })
      : null;
    
    if (existingServiceOrder) {
      // Check for conflicts
      if (new Date(existingServiceOrder.updatedAt) > new Date(data.updatedAt)) {
        return { 
          success: false, 
          conflict: true,
          serverData: existingServiceOrder,
          message: "Conflict detected: Server version is newer" 
        };
      }
      
      // Update existing service order
      const updatedServiceOrder = await prisma.serviceOrder.update({
        where: { id: data.id },
        data: {
          title: data.title,
          description: data.description,
          status: data.status,
          priority: data.priority,
          type: data.type,
          scheduledDate: data.scheduledDate ? new Date(data.scheduledDate) : null,
          completedDate: data.completedDate ? new Date(data.completedDate) : null,
          notes: data.notes,
          technicianNotes: data.technicianNotes,
          customerVisible: data.customerVisible,
          offlineId: data.offlineId,
          lastSyncedAt: new Date(),
        },
      });
      
      // Publish event
      await publishEvent(
        EventType.SERVICE_ORDER_UPDATED,
        { id: updatedServiceOrder.id },
        { userId }
      );
      
      return { 
        success: true, 
        data: updatedServiceOrder,
        message: "Service order updated successfully" 
      };
    } else {
      // Create new service order
      const newServiceOrder = await prisma.serviceOrder.create({
        data: {
          title: data.title,
          description: data.description,
          status: data.status || "PENDING",
          priority: data.priority || "MEDIUM",
          type: data.type || "SERVICE",
          scheduledDate: data.scheduledDate ? new Date(data.scheduledDate) : null,
          completedDate: data.completedDate ? new Date(data.completedDate) : null,
          notes: data.notes,
          assignedTechnicianId: userId,
          technicianNotes: data.technicianNotes,
          customerVisible: data.customerVisible ?? true,
          customerId: data.customerId,
          deviceId: data.deviceId,
          offlineId: data.offlineId,
          lastSyncedAt: new Date(),
        },
      });
      
      // Publish event
      await publishEvent(
        EventType.SERVICE_ORDER_CREATED,
        { id: newServiceOrder.id },
        { userId }
      );
      
      return { 
        success: true, 
        data: newServiceOrder,
        message: "Service order created successfully" 
      };
    }
  } catch (error) {
    console.error("Error synchronizing service order:", error);
    return { 
      success: false, 
      error: "Failed to synchronize service order",
      message: error instanceof Error ? error.message : "Unknown error" 
    };
  }
}

/**
 * Synchronize a service report
 */
async function syncServiceReport(data: any, userId: string) {
  // Implementation similar to syncServiceOrder
  return { success: true, message: "Service report synchronized successfully" };
}

/**
 * Synchronize a customer
 */
async function syncCustomer(data: any, userId: string) {
  // Implementation similar to syncServiceOrder
  return { success: true, message: "Customer synchronized successfully" };
}

/**
 * Synchronize a device
 */
async function syncDevice(data: any, userId: string) {
  // Implementation similar to syncServiceOrder
  return { success: true, message: "Device synchronized successfully" };
}
