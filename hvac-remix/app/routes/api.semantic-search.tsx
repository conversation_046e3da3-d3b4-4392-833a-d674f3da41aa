import { json, type ActionFunctionArgs } from "@remix-run/node";
import { unifiedSearch } from "~/services/search.server";
import { requireUserId } from "~/session.server";

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  
  try {
    // Parse the request body
    const body = await request.json();
    const { query, collection, limit = 10, useSemanticSearch = true } = body;
    
    if (!query) {
      return json({ success: false, error: "Query is required", data: [] }, { status: 400 });
    }
    
    // Perform the search
    const searchResponse = await unifiedSearch(query, userId, {
      useSemanticSearch,
      collection,
      limit,
    });
    
    return json(searchResponse);
  } catch (error) {
    console.error("Error in semantic search API:", error);
    return json(
      { 
        success: false, 
        error: `Failed to perform search: ${(error as Error).message}`,
        data: []
      },
      { status: 500 }
    );
  }
}