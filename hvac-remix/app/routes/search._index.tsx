import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSearchParams, useNavigation } from "@remix-run/react";
import { SemanticSearchForm, SearchResults } from "~/components/molecules/search";
import { unifiedSearch } from "~/services/search.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);
  const query = url.searchParams.get("q") || "";
  const collection = url.searchParams.get("collection") || "all";
  const limit = parseInt(url.searchParams.get("limit") || "10");
  const useSemanticSearch = url.searchParams.get("semantic") !== "false";
  
  if (!query) {
    return json({
      query,
      collection,
      limit,
      useSemanticSearch,
      results: [],
      hasResults: false,
    });
  }
  
  const searchResponse = await unifiedSearch(query, userId, {
    useSemanticSearch,
    collection,
    limit,
  });
  
  return json({
    query,
    collection,
    limit,
    useSemanticSearch,
    results: searchResponse.data || [],
    hasResults: (searchResponse.data?.length || 0) > 0,
    error: searchResponse.success ? null : searchResponse.error,
  });
}

export default function SearchPage() {
  const { query, collection, limit, useSemanticSearch, results, hasResults, error } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigation = useNavigation();
  const isSearching = navigation.state === "loading";
  
  const handleSearch = (
    newQuery: string,
    newCollection: string,
    newLimit: number,
    newUseSemanticSearch: boolean
  ) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("q", newQuery);
    newParams.set("collection", newCollection);
    newParams.set("limit", newLimit.toString());
    newParams.set("semantic", newUseSemanticSearch.toString());
    setSearchParams(newParams);
  };
  
  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Wyszukiwanie</h1>
      
      <SemanticSearchForm
        defaultQuery={query}
        defaultCollection={collection}
        defaultLimit={limit}
        onSearch={handleSearch}
      />
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}
      
      {query && !error && (
        <div className="mb-4">
          <h2 className="text-xl font-semibold">
            {hasResults 
              ? `Wyniki wyszukiwania dla "${query}"` 
              : `Brak wyników dla "${query}"`
            }
          </h2>
          {hasResults && (
            <p className="text-gray-500">
              Znaleziono {results.length} wyników
              {useSemanticSearch ? " (wyszukiwanie semantyczne)" : " (wyszukiwanie tradycyjne)"}
            </p>
          )}
        </div>
      )}
      
      <SearchResults results={results} isLoading={isSearching} />
      
      {!query && !error && (
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Wprowadź zapytanie, aby wyszukać klientów, urządzenia, zlecenia serwisowe i notatki.</p>
          <p className="text-gray-500">Możesz wyszukiwać według nazwy, adresu e-mail, telefonu, adresu, modelu, numeru seryjnego itp.</p>
          <p className="text-gray-500 mt-4">
            <strong>Wyszukiwanie semantyczne</strong> pozwala na znalezienie wyników na podstawie znaczenia, a nie tylko dokładnego dopasowania tekstu.
          </p>
        </div>
      )}
    </div>
  );
}