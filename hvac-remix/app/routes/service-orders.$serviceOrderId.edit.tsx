import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation, useNavigate, Link } from "@remix-run/react";
import * as React from "react";
import { useEffect, useRef, useState } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "~/components/ui/select";
import { Textarea } from "~/components/ui/textarea";
import { getCustomers } from "~/services/customer.service";
import { getDevices } from "~/services/device.service";
import { getServiceOrderById, updateServiceOrder } from "~/services/service-order.service";
import { requireUserId } from "~/session.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const { serviceOrderId } = params;

  if (!serviceOrderId) {
    throw new Response("Service Order ID is required", { status: 400 });
  }

  // Get service order details
  const serviceOrderResponse = await getServiceOrderById(serviceOrderId, userId);

  if (!serviceOrderResponse.success || !serviceOrderResponse.data) {
    throw new Response(serviceOrderResponse.error || "Service Order not found", { status: 404 });
  }

  const serviceOrder = serviceOrderResponse.data;

  // Get all customers for the dropdown
  const customersResponse = await getCustomers(userId);

  // Get devices for the selected customer
  const devicesResponse = await getDevices(
    userId,
    { pageSize: 100 },
    { customerId: serviceOrder.customerId }
  );

  return json({
    serviceOrder,
    customers: customersResponse.data || [],
    devices: devicesResponse.data || []
  });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const { serviceOrderId } = params;

  if (!serviceOrderId) {
    throw new Response("Service Order ID is required", { status: 400 });
  }

  const formData = await request.formData();

  const title = formData.get("title") as string;
  const description = formData.get("description") as string;
  const status = formData.get("status") as string;
  const priority = formData.get("priority") as string;
  const type = formData.get("type") as string;
  const scheduledDate = formData.get("scheduledDate") as string;
  const completionDate = formData.get("completionDate") as string;
  const notes = formData.get("notes") as string;
  const customerId = formData.get("customerId") as string;
  const deviceId = formData.get("deviceId") as string;

  // Validate required fields
  const errors = {
    title: title ? null : "Title is required",
    customerId: customerId ? null : "Customer is required",
    status: status ? null : "Status is required",
    priority: priority ? null : "Priority is required",
    type: type ? null : "Type is required",
  };

  const hasErrors = Object.values(errors).some(
    (errorMessage) => errorMessage !== null
  );

  if (hasErrors) {
    return json({ errors, values: Object.fromEntries(formData) });
  }

  // Update service order
  const serviceOrderResponse = await updateServiceOrder(
    serviceOrderId,
    {
      title,
      description: description || null,
      status,
      priority,
      type,
      scheduledDate: scheduledDate ? new Date(scheduledDate) : null,
      completedDate: completionDate ? new Date(completionDate) : null,
      notes: notes || null,
      customerId,
      deviceId: deviceId || null,
    },
    userId
  );

  if (!serviceOrderResponse.success) {
    return json({
      errors: {
        ...errors,
        form: serviceOrderResponse.error || "Failed to update service order",
      },
      values: Object.fromEntries(formData),
    });
  }

  return redirect(`/service-orders/${serviceOrderId}`);
}

export default function EditServiceOrderPage() {
  const { serviceOrder, customers, devices } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const titleRef = useRef<HTMLInputElement>(null);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>(
    actionData?.values?.customerId as string || serviceOrder.customerId
  );
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>(
    actionData?.values?.deviceId as string || serviceOrder.deviceId || ""
  );
  const [customerDevices, setCustomerDevices] = useState(devices);

  // Format date for input fields
  const formatDateForInput = (dateString: string | null) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };

  // Focus on the title field when there's an error
  useEffect(() => {
    if (actionData?.errors?.title) {
      titleRef.current?.focus();
    }
  }, [actionData]);

  // Fetch devices when customer changes
  useEffect(() => {
    const fetchDevices = async () => {
      if (selectedCustomerId) {
        const response = await fetch(`/api/customers/${selectedCustomerId}/devices`);
        if (response.ok) {
          const data = await response.json();
          setCustomerDevices(data.devices);
          // Clear selected device if it doesn't belong to the new customer
          if (selectedDeviceId && !data.devices.some((device: any) => device.id === selectedDeviceId)) {
            setSelectedDeviceId("");
          }
        } else {
          setCustomerDevices([]);
          setSelectedDeviceId("");
        }
      } else {
        setCustomerDevices([]);
        setSelectedDeviceId("");
      }
    };

    // Use the devices from the loader if the customer is the same as the service order
    if (selectedCustomerId === serviceOrder.customerId && devices.length > 0) {
      setCustomerDevices(devices);
    } else if (selectedCustomerId && selectedCustomerId !== serviceOrder.customerId) {
      fetchDevices();
    }
  }, [selectedCustomerId, serviceOrder.customerId, devices, selectedDeviceId]);

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to={`/service-orders/${serviceOrder.id}`} className="text-blue-500 hover:underline">
          ← Back to Service Order Details
        </Link>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Edit Service Order</CardTitle>
          <CardDescription>
            Update service order information
          </CardDescription>
        </CardHeader>

        <Form method="post">
          <CardContent className="space-y-4">


            {/* Customer field */}
            <div className="space-y-2">
              <Label htmlFor="customerId">
                Customer <span className="text-red-500">*</span>
              </Label>
              <Select
                name="customerId"
                defaultValue={selectedCustomerId}
                onValueChange={setSelectedCustomerId}
              >
                <SelectTrigger
                  id="customerId"
                  className={actionData?.errors?.customerId ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select a customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {actionData?.errors?.customerId && (
                <p className="text-red-500 text-sm" id="customerId-error">
                  {actionData.errors.customerId}
                </p>
              )}
            </div>

            {/* Device field */}
            <div className="space-y-2">
              <Label htmlFor="deviceId">Device</Label>
              <Select
                name="deviceId"
                defaultValue={selectedDeviceId}
                onValueChange={setSelectedDeviceId}
                disabled={!selectedCustomerId || customerDevices.length === 0}
              >
                <SelectTrigger id="deviceId">
                  <SelectValue placeholder={
                    !selectedCustomerId
                      ? "Select a customer first"
                      : customerDevices.length === 0
                        ? "No devices available"
                        : "Select a device"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {customerDevices.map((device) => (
                    <SelectItem key={device.id} value={device.id}>
                      {device.name} {device.model ? `(${device.model})` : ""}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Title field */}
            <div className="space-y-2">
              <Label htmlFor="title">
                Title <span className="text-red-500">*</span>
              </Label>
              <Input
                ref={titleRef}
                id="title"
                name="title"
                defaultValue={actionData?.values?.title as string || serviceOrder.title}
                aria-invalid={actionData?.errors?.title ? true : undefined}
                aria-errormessage={
                  actionData?.errors?.title ? "title-error" : undefined
                }
              />
              {actionData?.errors?.title && (
                <p className="text-red-500 text-sm" id="title-error">
                  {actionData.errors.title}
                </p>
              )}
            </div>

            {/* Description field */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                rows={4}
                defaultValue={actionData?.values?.description as string || serviceOrder.description || ""}
              />
            </div>

            {/* Status field */}
            <div className="space-y-2">
              <Label htmlFor="status">
                Status <span className="text-red-500">*</span>
              </Label>
              <Select
                name="status"
                defaultValue={actionData?.values?.status as string || serviceOrder.status}
              >
                <SelectTrigger
                  id="status"
                  className={actionData?.errors?.status ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select a status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              {actionData?.errors?.status && (
                <p className="text-red-500 text-sm" id="status-error">
                  {actionData.errors.status}
                </p>
              )}
            </div>

            {/* Priority field */}
            <div className="space-y-2">
              <Label htmlFor="priority">
                Priority <span className="text-red-500">*</span>
              </Label>
              <Select
                name="priority"
                defaultValue={actionData?.values?.priority as string || serviceOrder.priority}
              >
                <SelectTrigger
                  id="priority"
                  className={actionData?.errors?.priority ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select a priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">Low</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="URGENT">Urgent</SelectItem>
                </SelectContent>
              </Select>
              {actionData?.errors?.priority && (
                <p className="text-red-500 text-sm" id="priority-error">
                  {actionData.errors.priority}
                </p>
              )}
            </div>

            {/* Type field */}
            <div className="space-y-2">
              <Label htmlFor="type">
                Type <span className="text-red-500">*</span>
              </Label>
              <Select
                name="type"
                defaultValue={actionData?.values?.type as string || serviceOrder.type}
              >
                <SelectTrigger
                  id="type"
                  className={actionData?.errors?.type ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select a type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SERVICE">Service</SelectItem>
                  <SelectItem value="INSTALLATION">Installation</SelectItem>
                  <SelectItem value="INSPECTION">Inspection</SelectItem>
                </SelectContent>
              </Select>
              {actionData?.errors?.type && (
                <p className="text-red-500 text-sm" id="type-error">
                  {actionData.errors.type}
                </p>
              )}
            </div>

            {/* Scheduled Date field */}
            <div className="space-y-2">
              <Label htmlFor="scheduledDate">Scheduled Date</Label>
              <Input
                id="scheduledDate"
                name="scheduledDate"
                type="date"
                defaultValue={
                  actionData?.values?.scheduledDate as string ||
                  formatDateForInput(serviceOrder.scheduledDate)
                }
              />
            </div>

            {/* Completion Date field */}
            <div className="space-y-2">
              <Label htmlFor="completionDate">Completion Date</Label>
              <Input
                id="completionDate"
                name="completionDate"
                type="date"
                defaultValue={
                  actionData?.values?.completionDate as string ||
                  formatDateForInput(serviceOrder.completionDate)
                }
              />
            </div>

            {/* Notes field */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                rows={4}
                defaultValue={actionData?.values?.notes as string || serviceOrder.notes || ""}
              />
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Link to={`/service-orders/${serviceOrder.id}`}>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </CardFooter>
        </Form>
      </Card>
    </div>
  );
}
