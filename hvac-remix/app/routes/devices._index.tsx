import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSearchParams, Link } from "@remix-run/react";
import * as React from "react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { getDevices } from "~/services/device.service";
import { requireUserId } from "~/session.server";


export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);

  // Get pagination parameters from URL
  const page = parseInt(url.searchParams.get("page") || "1", 10);
  const pageSize = parseInt(url.searchParams.get("pageSize") || "10", 10);
  const orderBy = url.searchParams.get("orderBy") || "createdAt";
  const orderDirection = (url.searchParams.get("orderDirection") || "desc") as "asc" | "desc";

  // Get filter parameters from URL
  const search = url.searchParams.get("search") || undefined;
  const manufacturer = url.searchParams.get("manufacturer") || undefined;
  const customerId = url.searchParams.get("customerId") || undefined;

  // Get view mode
  const view = url.searchParams.get("view") || "default";

  // Get devices with pagination and filters
  const devicesResponse = await getDevices(
    userId,
    { page, pageSize, orderBy, orderDirection },
    { search, manufacturer, customerId }
  );

  return json({
    devices: devicesResponse.data || [],
    totalCount: devicesResponse.totalCount,
    totalPages: devicesResponse.totalPages,
    currentPage: devicesResponse.currentPage,
    error: devicesResponse.error,
    view,
  });
}

export default function DevicesIndexPage() {
  const { devices, totalCount, totalPages, currentPage, error, view } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get("search") || "");

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery) {
      newParams.set("search", searchQuery);
    } else {
      newParams.delete("search");
    }
    newParams.set("page", "1"); // Reset to first page on new search
    setSearchParams(newParams);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("page", newPage.toString());
    setSearchParams(newParams);
  };

  // Handle manufacturer filter
  const handleManufacturerFilter = (manufacturer: string | null) => {
    const newParams = new URLSearchParams(searchParams);
    if (manufacturer) {
      newParams.set("manufacturer", manufacturer);
    } else {
      newParams.delete("manufacturer");
    }
    newParams.set("page", "1"); // Reset to first page on filter change
    setSearchParams(newParams);
  };

  // Handle view change
  const handleViewChange = (newView: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (newView !== "default") {
      newParams.set("view", newView);
    } else {
      newParams.delete("view");
    }
    setSearchParams(newParams);
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Devices</h1>
          <div className="flex mt-2 space-x-2">
            <Button
              variant={view === "default" ? "default" : "outline"}
              size="sm"
              onClick={() => handleViewChange("default")}
            >
              Standard View
            </Button>
            <Button
              variant={view === "predictions" ? "default" : "outline"}
              size="sm"
              onClick={() => handleViewChange("predictions")}
            >
              Predictive Maintenance
            </Button>
          </div>
        </div>
        <Link to="/devices/new">
          <Button>Add Device</Button>
        </Link>
      </div>

      {/* Search form */}
      <form onSubmit={handleSearch} className="mb-6">
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              type="text"
              placeholder="Search devices..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button type="submit">Search</Button>
        </div>
      </form>

      {/* Manufacturer filters */}
      {devices.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-6">
          <Button
            variant={!searchParams.get("manufacturer") ? "default" : "outline"}
            size="sm"
            onClick={() => handleManufacturerFilter(null)}
          >
            All
          </Button>
          {Array.from(new Set(devices.map(device => device.manufacturer))).filter(Boolean).map((manufacturer) => (
            <Button
              key={manufacturer}
              variant={searchParams.get("manufacturer") === manufacturer ? "default" : "outline"}
              size="sm"
              onClick={() => handleManufacturerFilter(manufacturer)}
            >
              {manufacturer}
            </Button>
          ))}
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Predictive Maintenance Summary */}
      {view === "predictions" && (
        <Card className="mb-6 bg-blue-50 dark:bg-blue-900/20">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div>
                <h2 className="text-xl font-bold mb-2">Predictive Maintenance Dashboard</h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Monitor device health and predict maintenance needs before failures occur
                </p>
              </div>

              <div className="flex flex-wrap gap-4">
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                    <span className="text-sm font-medium">High Risk</span>
                  </div>
                  <p className="text-2xl font-bold mt-1">
                    {devices.filter(d =>
                      (d as any).predictions &&
                      (d as any).predictions.length > 0 &&
                      (d as any).predictions[0].failureProbability >= 0.7
                    ).length}
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                    <span className="text-sm font-medium">Medium Risk</span>
                  </div>
                  <p className="text-2xl font-bold mt-1">
                    {devices.filter(d =>
                      (d as any).predictions &&
                      (d as any).predictions.length > 0 &&
                      (d as any).predictions[0].failureProbability >= 0.3 &&
                      (d as any).predictions[0].failureProbability < 0.7
                    ).length}
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-sm font-medium">Low Risk</span>
                  </div>
                  <p className="text-2xl font-bold mt-1">
                    {devices.filter(d =>
                      (d as any).predictions &&
                      (d as any).predictions.length > 0 &&
                      (d as any).predictions[0].failureProbability < 0.3
                    ).length}
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-gray-300 mr-2"></div>
                    <span className="text-sm font-medium">No Data</span>
                  </div>
                  <p className="text-2xl font-bold mt-1">
                    {devices.filter(d =>
                      !(d as any).predictions ||
                      (d as any).predictions.length === 0
                    ).length}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Devices list */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {devices.length > 0 ? (
          devices.map((device) => (
            view === "predictions" ? (
              <PredictiveMaintenanceCard key={device.id} device={device} />
            ) : (
              <DeviceCard key={device.id} device={device} />
            )
          ))
        ) : (
          <div className="col-span-full text-center py-8">
            <p className="text-gray-500">No devices found</p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={page === currentPage ? "default" : "outline"}
                  onClick={() => handlePageChange(page)}
                  className="w-10"
                >
                  {page}
                </Button>
              ))}
            </div>

            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Summary */}
      <div className="mt-6 text-center text-gray-500">
        Showing {devices.length} of {totalCount} devices
      </div>
    </div>
  );
}

function DeviceCard({ device }: { device: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Link to={`/devices/${device.id}`} className="hover:underline">
            {device.name}
          </Link>
        </CardTitle>
        <CardDescription>
          {device.model || "No model information"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {device.manufacturer && (
            <div>
              <Label>Manufacturer</Label>
              <p>{device.manufacturer}</p>
            </div>
          )}
          {device.serialNumber && (
            <div>
              <Label>Serial Number</Label>
              <p>{device.serialNumber}</p>
            </div>
          )}
          {device.installationDate && (
            <div>
              <Label>Installation Date</Label>
              <p>{new Date(device.installationDate).toLocaleDateString()}</p>
            </div>
          )}
          {device.customer && (
            <div>
              <Label>Customer</Label>
              <p>
                <Link to={`/customers/${device.customer.id}`} className="text-blue-500 hover:underline">
                  {device.customer.name}
                </Link>
              </p>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link to={`/devices/${device.id}`}>
          <Button variant="outline">View Details</Button>
        </Link>
        <Link to={`/devices/${device.id}/edit`}>
          <Button variant="outline">Edit</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}

function PredictiveMaintenanceCard({ device }: { device: any }) {
  // Get the latest prediction if available
  const latestPrediction = (device as any).predictions && (device as any).predictions.length > 0
    ? (device as any).predictions[0]
    : null;

  // Determine status color based on failure probability
  const getStatusColor = (probability: number) => {
    if (probability < 0.3) return "bg-green-500";
    if (probability < 0.7) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Card className={latestPrediction ? `border-l-4 ${getStatusColor(latestPrediction.failureProbability)}` : ""}>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>
              <Link to={`/devices/${device.id}`} className="hover:underline">
                {device.name}
              </Link>
            </CardTitle>
            <CardDescription>
              {device.model || "No model information"}
            </CardDescription>
          </div>
          {latestPrediction && (
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full ${getStatusColor(latestPrediction.failureProbability)} mr-2`}></div>
              <span className="text-sm font-medium">
                {Math.round(latestPrediction.failureProbability * 100)}% Risk
              </span>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {device.customer && (
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Customer:</span>
              <span className="text-sm font-medium">
                <Link to={`/customers/${device.customer.id}`} className="text-blue-500 hover:underline">
                  {device.customer.name}
                </Link>
              </span>
            </div>
          )}

          {latestPrediction ? (
            <>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Prediction Date:</span>
                <span className="text-sm font-medium">{formatDate(latestPrediction.predictionDate)}</span>
              </div>

              {latestPrediction.predictedFailureDate && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Predicted Failure:</span>
                  <span className="text-sm font-medium">{formatDate(latestPrediction.predictedFailureDate)}</span>
                </div>
              )}

              {latestPrediction.predictedComponent && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Component:</span>
                  <span className="text-sm font-medium">{latestPrediction.predictedComponent}</span>
                </div>
              )}

              <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Maintenance Status:</span>
                  <Badge variant={latestPrediction.maintenancePerformed ? "outline" : "default"}>
                    {latestPrediction.maintenancePerformed ? "Performed" : "Pending"}
                  </Badge>
                </div>
              </div>
            </>
          ) : (
            <div className="py-4 text-center text-gray-500">
              <p>No prediction data available</p>
              <p className="text-sm mt-1">Record telemetry data to generate predictions</p>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link to={`/devices/${device.id}/telemetry`}>
          <Button variant="outline" size="sm">Telemetry</Button>
        </Link>
        <Link to={`/devices/${device.id}/predictions`}>
          <Button variant="outline" size="sm">Predictions</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
