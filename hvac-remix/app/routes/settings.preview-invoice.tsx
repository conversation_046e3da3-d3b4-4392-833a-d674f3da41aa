import { PrinterIcon, DocumentArrowDownIcon } from "@heroicons/react/24/outline";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useRef } from "react";
import { PrintableInvoice } from "~/components/templates/printable-invoice";
import { Button } from "~/components/ui/button";
import { getCompanyInfoForPrintableDocuments } from "~/services/company-settings.server";
import { requireUserId } from "~/session.server";
import { generatePDFFromElement, savePDF } from "~/utils/pdf-generator";

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const companyInfo = await getCompanyInfoForPrintableDocuments();
  
  return json({ companyInfo });
}

export default function PreviewInvoicePage() {
  const { companyInfo } = useLoaderData<typeof loader>();
  const printableInvoiceRef = useRef<HTMLDivElement>(null);
  
  // Sample invoice data for preview
  const sampleInvoice = {
    id: "sample-invoice",
    invoiceNumber: "INV-2023-001",
    issueDate: new Date().toISOString(),
    dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
    totalAmount: 1250.00,
    taxAmount: 250.00,
    status: "PENDING",
    notes: "This is a sample invoice for preview purposes.",
    sellerInfo: `${companyInfo.name}
${companyInfo.address}
Tel: ${companyInfo.phone}
Email: ${companyInfo.email}
NIP: ${companyInfo.taxId}`,
    buyerInfo: `Sample Customer
123 Customer Street
00-001 Warsaw, Poland
Tel: +48 ***********
Email: <EMAIL>`,
    items: [
      {
        id: "item1",
        description: "HVAC System Maintenance",
        quantity: 1,
        unitPrice: 500.00,
        totalPrice: 500.00,
        taxRate: 0.23
      },
      {
        id: "item2",
        description: "Replacement Filter",
        quantity: 2,
        unitPrice: 150.00,
        totalPrice: 300.00,
        taxRate: 0.23
      },
      {
        id: "item3",
        description: "Refrigerant Recharge",
        quantity: 1,
        unitPrice: 200.00,
        totalPrice: 200.00,
        taxRate: 0.23
      }
    ],
    serviceOrder: {
      id: "so1",
      title: "Annual HVAC Maintenance",
      description: "Regular annual maintenance of the HVAC system including filter replacement and refrigerant check.",
      customer: {
        id: "cust1",
        name: "Sample Customer",
        email: "<EMAIL>",
        phone: "+48 ***********",
        address: "123 Customer Street",
        city: "Warsaw",
        postalCode: "00-001",
        country: "Poland"
      }
    },
    companyInfo
  };

  // Handle print
  const handlePrint = () => {
    window.print();
  };

  // Handle PDF generation
  const handleGeneratePDF = async () => {
    if (!printableInvoiceRef.current) return;

    try {
      const pdf = await generatePDFFromElement(printableInvoiceRef.current, {
        filename: "sample-invoice.pdf",
        pageSize: 'A4',
        margin: { top: 10, right: 10, bottom: 10, left: 10 }
      });

      savePDF(pdf, "sample-invoice.pdf");
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('An error occurred while generating the PDF. Please try again.');
    }
  };

  return (
    <div className="mx-auto max-w-4xl">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Podgląd faktury</h1>
        <div className="flex space-x-2">
          <Button
            onClick={handlePrint}
            className="flex items-center"
          >
            <PrinterIcon className="mr-2 h-5 w-5" />
            Drukuj
          </Button>
          <Button
            onClick={handleGeneratePDF}
            className="flex items-center"
          >
            <DocumentArrowDownIcon className="mr-2 h-5 w-5" />
            Pobierz PDF
          </Button>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <PrintableInvoice
          ref={printableInvoiceRef}
          data={sampleInvoice}
        />
      </div>
    </div>
  );
}