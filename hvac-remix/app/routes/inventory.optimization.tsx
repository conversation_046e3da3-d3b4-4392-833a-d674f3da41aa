import { ArchiveBoxIcon, CpuChipIcon, LightBulbIcon, ArrowTrendingUpIcon } from "@heroicons/react/24/outline";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { prisma } from "~/db.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);

  // Get all inventory parts with their transaction history
  const parts = await prisma.inventoryPart.findMany({
    where: {
      isActive: true
    },
    include: {
      supplier: true,
      transactions: {
        orderBy: {
          createdAt: "desc"
        },
        take: 100, // Get last 100 transactions for analysis
        where: {
          type: "OUTBOUND"
        }
      }
    }
  });

  // Get service orders for the past year to analyze seasonal patterns
  const oneYearAgo = new Date();
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

  const serviceOrders = await prisma.serviceOrder.findMany({
    where: {
      createdAt: {
        gte: oneYearAgo
      }
    },
    include: {
      device: true
    },
    orderBy: {
      createdAt: "asc"
    }
  });

  // Get all inventory transactions for trend analysis
  const transactions = await prisma.inventoryTransaction.findMany({
    where: {
      createdAt: {
        gte: oneYearAgo
      }
    },
    include: {
      part: true
    },
    orderBy: {
      createdAt: "asc"
    }
  });

  // Process data for optimization insights
  const partOptimizationData = processInventoryData(parts, transactions, serviceOrders);

  return json({
    partOptimizationData,
    inventoryMetrics: calculateInventoryMetrics(parts),
    seasonalTrends: analyzeSeasonalTrends(transactions),
    optimizationSuggestions: generateOptimizationSuggestions(parts),
    leadTimeAnalysis: analyzeLeadTimes(parts, transactions)
  });
}

// Define types for the data structures
type PartWithTransactions = {
  id: string;
  name: string;
  partNumber: string | null;
  category: string | null;
  currentStock: number;
  minimumStock: number;
  reorderPoint: number;
  costPrice: number | null;
  supplierId: string | null;
  supplier: { id: string; name: string } | null;
  transactions: Array<{
    id: string;
    type: string;
    quantity: number;
    createdAt: Date;
    partId: string;
  }>;
};

type TransactionWithPart = {
  id: string;
  type: string;
  quantity: number;
  createdAt: Date;
  partId: string;
  part: {
    id: string;
    name: string;
  };
};

type ServiceOrderWithDevice = {
  id: string;
  createdAt: Date;
  device: {
    id: string;
    name: string;
  } | null;
};

// Process inventory data to generate optimization insights
function processInventoryData(parts: PartWithTransactions[], transactions: TransactionWithPart[], serviceOrders: ServiceOrderWithDevice[]) {
  return parts.map(part => {
    // Calculate average usage rate (units per month)
    const usageTransactions = part.transactions;
    let usageRate = 0;
    let totalUsed = 0;

    if (usageTransactions.length > 0) {
      totalUsed = usageTransactions.reduce((sum, t) => sum + Math.abs(t.quantity), 0);
      const oldestTransaction = usageTransactions[usageTransactions.length - 1];
      const daysSince = Math.max(1, Math.ceil((Date.now() - new Date(oldestTransaction.createdAt).getTime()) / (1000 * 60 * 60 * 24)));
      usageRate = (totalUsed / daysSince) * 30; // Convert to monthly rate
    }

    // Calculate stockout risk
    let stockoutRisk = "Low";
    if (part.currentStock <= part.minimumStock) {
      stockoutRisk = "High";
    } else if (part.currentStock <= part.reorderPoint) {
      stockoutRisk = "Medium";
    }

    // Calculate optimal stock level
    // Using "square root formula" from inventory management theory
    // optimum = sqrt((2 * annual demand * ordering cost) / (holding cost per unit per year))
    const annualDemand = usageRate * 12;
    const orderingCost = 50; // Default ordering cost
    const holdingCostPercentage = 0.25; // 25% of part cost
    const holdingCost = (part.costPrice || 1) * holdingCostPercentage;

    const economicOrderQuantity = Math.ceil(Math.sqrt((2 * annualDemand * orderingCost) / holdingCost));

    // Optimal reorder point = lead time demand + safety stock
    // Assuming lead time of 14 days and safety stock as sqrt of lead time demand
    const leadTimeDays = 14; // Default lead time since field doesn't exist in schema
    const leadTimeDemand = usageRate * (leadTimeDays / 30);
    const safetyStock = Math.ceil(Math.sqrt(leadTimeDemand) + 2); // Adding a buffer
    const optimalReorderPoint = Math.ceil(leadTimeDemand + safetyStock);

    // Calculate turnover rate (annual)
    const turnoverRate = part.currentStock > 0 ? annualDemand / part.currentStock : 0;

    // Calculate carrying cost
    const carryingCost = part.currentStock * holdingCost;

    // Calculate optimal stock level
    const optimalStockLevel = Math.ceil(optimalReorderPoint + economicOrderQuantity / 2);

    // Calculate potential savings
    const excessStock = Math.max(0, part.currentStock - optimalStockLevel);
    const potentialSavings = excessStock * holdingCost;

    // Calculate stock elasticity (how much stock changes relative to demand)
    // Higher elasticity means stock is responsive to changes in demand
    const stockElasticity = 0;

    // Calculate out-of-stock frequency (approximate based on zero stock transactions)
    const stockoutEvents = transactions
      .filter(t => t.partId === part.id && t.type === "OUTBOUND")
      .length;

    return {
      id: part.id,
      name: part.name,
      partNumber: part.partNumber,
      category: part.category,
      currentStock: part.currentStock,
      minimumStock: part.minimumStock,
      reorderPoint: part.reorderPoint,
      costPrice: part.costPrice,
      usageRate,
      stockoutRisk,
      economicOrderQuantity,
      optimalReorderPoint,
      optimalStockLevel,
      turnoverRate,
      carryingCost,
      potentialSavings,
      excessStock,
      stockElasticity,
      stockoutEvents
    };
  }).sort((a, b) => b.potentialSavings - a.potentialSavings); // Sort by potential savings
}

// Calculate overall inventory metrics
function calculateInventoryMetrics(parts: PartWithTransactions[]) {
  const totalParts = parts.length;
  const totalValue = parts.reduce((sum, part) => sum + ((part.costPrice || 0) * part.currentStock), 0);
  const totalStockUnits = parts.reduce((sum, part) => sum + part.currentStock, 0);

  let slowMovingValue = 0;
  let criticalStockValue = 0;
  let excessStockValue = 0;
  let healthyStockValue = 0;

  parts.forEach(part => {
    const partValue = (part.costPrice || 0) * part.currentStock;

    // Check if slow moving (no transactions in last 90 days)
    const hasRecentTransactions = part.transactions.some(t => {
      const date = new Date(t.createdAt);
      const daysDiff = Math.ceil((Date.now() - date.getTime()) / (1000 * 60 * 60 * 24));
      return daysDiff <= 90;
    });

    if (!hasRecentTransactions && part.currentStock > 0) {
      slowMovingValue += partValue;
    }

    // Check if critical stock
    if (part.currentStock <= part.minimumStock) {
      criticalStockValue += partValue;
    }
    // Check if excess stock (more than 50% above optimal)
    else if (part.currentStock > part.reorderPoint * 1.5) {
      excessStockValue += partValue;
    }
    // Otherwise healthy stock
    else {
      healthyStockValue += partValue;
    }
  });

  // Calculate inventory health score (0-100)
  const healthyStockPercentage = healthyStockValue / totalValue * 100;
  const inventoryHealthScore = Math.min(100, Math.max(0, Math.round(healthyStockPercentage)));

  return {
    totalParts,
    totalValue,
    totalStockUnits,
    slowMovingValue,
    criticalStockValue,
    excessStockValue,
    healthyStockValue,
    inventoryHealthScore
  };
}

// Analyze seasonal trends in inventory usage
function analyzeSeasonalTrends(transactions: TransactionWithPart[]) {
  // Group transactions by month
  const monthlyUsage: Record<string, {
    month: number;
    year: number;
    totalQuantity: number;
    partCounts: Record<string, number>;
  }> = {};

  transactions.forEach(transaction => {
    if (transaction.type !== "OUTBOUND") return;

    const date = new Date(transaction.createdAt);
    const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;

    if (!monthlyUsage[monthKey]) {
      monthlyUsage[monthKey] = {
        month: date.getMonth() + 1,
        year: date.getFullYear(),
        totalQuantity: 0,
        partCounts: {}
      };
    }

    monthlyUsage[monthKey].totalQuantity += Math.abs(transaction.quantity);

    // Track usage by part
    if (!monthlyUsage[monthKey].partCounts[transaction.partId]) {
      monthlyUsage[monthKey].partCounts[transaction.partId] = 0;
    }
    monthlyUsage[monthKey].partCounts[transaction.partId] += Math.abs(transaction.quantity);
  });

  // Convert to array and sort by date
  const monthlyData = Object.values(monthlyUsage).sort((a, b) => {
    if (a.year !== b.year) return a.year - b.year;
    return a.month - b.month;
  });

  // Find seasonal patterns (comparing same month in different years)
  const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

  // Group by month across years
  const monthGroups: Record<number, typeof monthlyData> = {};
  monthlyData.forEach(data => {
    if (!monthGroups[data.month]) monthGroups[data.month] = [];
    monthGroups[data.month].push(data);
  });

  // Identify months with significant usage increases
  const seasonalPatterns: Array<{
    month: number;
    monthName: string;
    averageUsage: number;
    maxUsage: number;
    seasonalityFactor: string;
  }> = [];

  Object.entries(monthGroups).forEach(([month, dataPoints]) => {
    if (dataPoints.length > 1) {
      const avgUsage = dataPoints.reduce((sum, dp) => sum + dp.totalQuantity, 0) / dataPoints.length;
      const maxUsage = Math.max(...dataPoints.map(dp => dp.totalQuantity));
      const usageRatio = maxUsage / (avgUsage || 1);

      if (usageRatio > 1.25) { // 25% higher than average indicates seasonality
        seasonalPatterns.push({
          month: parseInt(month),
          monthName: monthNames[parseInt(month) - 1],
          averageUsage: avgUsage,
          maxUsage: maxUsage,
          seasonalityFactor: usageRatio.toFixed(2)
        });
      }
    }
  });

  // Sort by seasonality factor
  seasonalPatterns.sort((a, b) => parseFloat(b.seasonalityFactor) - parseFloat(a.seasonalityFactor));

  return {
    monthlyData,
    seasonalPatterns
  };
}

// Generate optimization suggestions
function generateOptimizationSuggestions(parts: PartWithTransactions[]) {
  const suggestions: Array<{
    type: string;
    title: string;
    description: string;
    impact: string;
    parts?: any[];
    suppliers?: any[];
  }> = [];

  // Find parts with excess stock
  const excessStockParts = parts
    .filter(p => p.currentStock > p.reorderPoint * 1.5)
    .sort((a, b) => ((b.costPrice || 0) * b.currentStock) - ((a.costPrice || 0) * a.currentStock))
    .slice(0, 5); // Top 5 excess stock items

  if (excessStockParts.length > 0) {
    const totalExcessValue = excessStockParts.reduce((sum, p) => {
      const excessUnits = p.currentStock - p.reorderPoint;
      return sum + (excessUnits * (p.costPrice || 0));
    }, 0);

    suggestions.push({
      type: "excess_stock",
      title: "Reduce Excess Inventory",
      description: `You have ${excessStockParts.length} parts with excess stock worth approximately ${new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(totalExcessValue)}`,
      impact: "high",
      parts: excessStockParts.map(p => ({
        id: p.id,
        name: p.name,
        currentStock: p.currentStock,
        reorderPoint: p.reorderPoint,
        excessUnits: p.currentStock - p.reorderPoint,
        excessValue: (p.currentStock - p.reorderPoint) * (p.costPrice || 0)
      }))
    });
  }

  // Find slow-moving inventory
  const slowMovingParts = parts
    .filter(p => {
      if (p.currentStock <= 0) return false;

      // Check if no usage in last 180 days
      const hasRecentTransactions = p.transactions.some(t => {
        const date = new Date(t.createdAt);
        const daysDiff = Math.ceil((Date.now() - date.getTime()) / (1000 * 60 * 60 * 24));
        return daysDiff <= 180;
      });

      return !hasRecentTransactions;
    })
    .sort((a, b) => ((b.costPrice || 0) * b.currentStock) - ((a.costPrice || 0) * a.currentStock))
    .slice(0, 5); // Top 5 slow-moving items

  if (slowMovingParts.length > 0) {
    const totalSlowMovingValue = slowMovingParts.reduce((sum, p) => sum + ((p.costPrice || 0) * p.currentStock), 0);

    suggestions.push({
      type: "slow_moving",
      title: "Address Slow-Moving Inventory",
      description: `You have ${slowMovingParts.length} parts with no usage in the past 180 days, valued at ${new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(totalSlowMovingValue)}`,
      impact: "medium",
      parts: slowMovingParts.map(p => ({
        id: p.id,
        name: p.name,
        currentStock: p.currentStock,
        daysSinceLastUsage: p.transactions.length > 0 ?
          Math.ceil((Date.now() - new Date(p.transactions[0].createdAt).getTime()) / (1000 * 60 * 60 * 24)) :
          "No usage recorded",
        value: (p.costPrice || 0) * p.currentStock
      }))
    });
  }

  // Find critical stock items
  const criticalStockParts = parts
    .filter(p => p.currentStock <= p.minimumStock && p.currentStock > 0)
    .sort((a, b) => {
      // Calculate usage rate for sorting
      const aUsageRate = a.transactions.length > 0 ? a.transactions.reduce((sum, t) => sum + Math.abs(t.quantity), 0) : 0;
      const bUsageRate = b.transactions.length > 0 ? b.transactions.reduce((sum, t) => sum + Math.abs(t.quantity), 0) : 0;
      return bUsageRate - aUsageRate;
    })
    .slice(0, 5); // Top 5 critical stock items

  if (criticalStockParts.length > 0) {
    suggestions.push({
      type: "critical_stock",
      title: "Replenish Critical Stock Items",
      description: `You have ${criticalStockParts.length} parts at or below minimum stock levels that require immediate attention`,
      impact: "high",
      parts: criticalStockParts.map(p => {
        // Calculate usage rate for this part
        const usageRate = p.transactions.length > 0 ?
          p.transactions.reduce((sum, t) => sum + Math.abs(t.quantity), 0) / Math.max(1, p.transactions.length) : 0;

        return {
          id: p.id,
          name: p.name,
          currentStock: p.currentStock,
          minimumStock: p.minimumStock,
          estimatedDaysRemaining: usageRate > 0 ? Math.ceil((p.currentStock / usageRate) * 30) : "Unknown"
        };
      })
    });
  }

  // Reorder point optimization
  const reorderPointParts = parts
    .filter(p => {
      // Calculate potential new reorder point
      const usageTransactions = p.transactions;
      let usageRate = 0;

      if (usageTransactions.length > 0) {
        const totalUsed = usageTransactions.reduce((sum, t) => sum + Math.abs(t.quantity), 0);
        const oldestTransaction = usageTransactions[usageTransactions.length - 1];
        const daysSince = Math.max(1, Math.ceil((Date.now() - new Date(oldestTransaction.createdAt).getTime()) / (1000 * 60 * 60 * 24)));
        usageRate = (totalUsed / daysSince) * 30; // Convert to monthly rate
      }

      // Calculated optimal reorder point
      const leadTimeDays = 14; // Default since field doesn't exist
      const leadTimeDemand = usageRate * (leadTimeDays / 30);
      const safetyStock = Math.ceil(Math.sqrt(leadTimeDemand) + 2);
      const optimalReorderPoint = Math.ceil(leadTimeDemand + safetyStock);

      // Significant difference between current and optimal
      const difference = Math.abs(p.reorderPoint - optimalReorderPoint);
      const percentDifference = p.reorderPoint > 0 ? difference / p.reorderPoint : 1;

      return percentDifference > 0.25; // More than 25% difference
    })
    .sort((a, b) => {
      // Calculate usage rates for sorting
      const aUsageRate = a.transactions.length > 0 ? a.transactions.reduce((sum, t) => sum + Math.abs(t.quantity), 0) : 0;
      const bUsageRate = b.transactions.length > 0 ? b.transactions.reduce((sum, t) => sum + Math.abs(t.quantity), 0) : 0;
      return bUsageRate - aUsageRate;
    })
    .slice(0, 5); // Top 5 items

  if (reorderPointParts.length > 0) {
    suggestions.push({
      type: "reorder_point",
      title: "Optimize Reorder Points",
      description: `${reorderPointParts.length} parts have reorder points that could be optimized based on current usage patterns`,
      impact: "medium",
      parts: reorderPointParts.map(p => {
        // Calculate optimal reorder point again
        const usageTransactions = p.transactions;
        let usageRate = 0;

        if (usageTransactions.length > 0) {
          const totalUsed = usageTransactions.reduce((sum, t) => sum + Math.abs(t.quantity), 0);
          const oldestTransaction = usageTransactions[usageTransactions.length - 1];
          const daysSince = Math.max(1, Math.ceil((Date.now() - new Date(oldestTransaction.createdAt).getTime()) / (1000 * 60 * 60 * 24)));
          usageRate = (totalUsed / daysSince) * 30; // Convert to monthly rate
        }

        const leadTimeDays = 14; // Default since field doesn't exist
        const leadTimeDemand = usageRate * (leadTimeDays / 30);
        const safetyStock = Math.ceil(Math.sqrt(leadTimeDemand) + 2);
        const optimalReorderPoint = Math.ceil(leadTimeDemand + safetyStock);

        return {
          id: p.id,
          name: p.name,
          currentReorderPoint: p.reorderPoint,
          suggestedReorderPoint: optimalReorderPoint,
          currentStock: p.currentStock,
          monthlyUsageRate: usageRate.toFixed(2)
        };
      })
    });
  }

  // Supplier consolidation opportunity
  const supplierMap: Record<string, {
    supplier: { id: string; name: string } | null;
    parts: PartWithTransactions[];
    totalValue: number;
  }> = {};

  parts.forEach(p => {
    if (p.supplierId && p.supplier) {
      if (!supplierMap[p.supplierId]) {
        supplierMap[p.supplierId] = {
          supplier: p.supplier,
          parts: [],
          totalValue: 0
        };
      }
      supplierMap[p.supplierId].parts.push(p);
      supplierMap[p.supplierId].totalValue += ((p.costPrice || 0) * p.currentStock);
    }
  });

  const smallSuppliers = Object.values(supplierMap)
    .filter(s => s.parts.length < 3 && s.totalValue < 5000)
    .sort((a, b) => a.parts.length - b.parts.length);

  if (smallSuppliers.length > 1) {
    suggestions.push({
      type: "supplier_consolidation",
      title: "Consolidate Suppliers",
      description: `Consider consolidating ${smallSuppliers.length} suppliers with small order volumes to improve purchasing efficiency`,
      impact: "medium",
      suppliers: smallSuppliers.map(s => ({
        id: s.supplier?.id || '',
        name: s.supplier?.name || 'Unknown',
        partCount: s.parts.length,
        totalValue: s.totalValue
      }))
    });
  }

  // Return in order of impact priority
  return suggestions.sort((a, b) => {
    const impactScore = { "high": 3, "medium": 2, "low": 1 };
    return impactScore[b.impact] - impactScore[a.impact];
  });
}

// Analyze lead times
function analyzeLeadTimes(parts: PartWithTransactions[], transactions: TransactionWithPart[]) {
  // We need to identify purchase order related transactions and calculate time between order and receipt
  const leadTimeData: any[] = [];

  return {
    averageLeadTime: 14, // Default
    leadTimeVariability: 2,
    supplierPerformance: [],
    leadTimeByPart: [],
    leadTimeData
  };
}

export default function InventoryOptimization() {
  const {
    partOptimizationData,
    inventoryMetrics,
    seasonalTrends,
    optimizationSuggestions,
    leadTimeAnalysis
  } = useLoaderData<typeof loader>();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">AI-Powered Inventory Optimization</h1>
        <div className="flex gap-2">
          <Link
            to="/inventory"
            className="flex items-center gap-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
          >
            <ArchiveBoxIcon className="h-4 w-4" />
            Inventory List
          </Link>
        </div>
      </div>

      {/* Inventory Health Dashboard */}
      <div className="rounded-lg bg-white p-6 shadow">
        <h2 className="text-xl font-medium text-gray-900">Inventory Health Score</h2>
        <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="overflow-hidden rounded-lg bg-white shadow ring-1 ring-black ring-opacity-5">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CpuChipIcon className="h-6 w-6 text-blue-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="truncate text-sm font-medium text-gray-500">AI Inventory Health Score</dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">{inventoryMetrics.inventoryHealthScore}%</div>
                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${inventoryMetrics.inventoryHealthScore >= 70 ? 'text-green-600' : inventoryMetrics.inventoryHealthScore >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>
                        {inventoryMetrics.inventoryHealthScore >= 70 ? 'Healthy' : inventoryMetrics.inventoryHealthScore >= 50 ? 'Needs Attention' : 'Needs Improvement'}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div className="h-2" style={{ width: `${inventoryMetrics.inventoryHealthScore}%`, background: 'linear-gradient(90deg, rgba(239,68,68,1) 0%, rgba(234,179,8,1) 50%, rgba(34,197,94,1) 100%)' }}></div>
          </div>

          <div className="overflow-hidden rounded-lg bg-white shadow ring-1 ring-black ring-opacity-5">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ArchiveBoxIcon className="h-6 w-6 text-indigo-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="truncate text-sm font-medium text-gray-500">Total Inventory Value</dt>
                    <dd className="text-2xl font-semibold text-gray-900">
                      {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(inventoryMetrics.totalValue)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="overflow-hidden rounded-lg bg-white shadow ring-1 ring-black ring-opacity-5">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <LightBulbIcon className="h-6 w-6 text-yellow-500" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="truncate text-sm font-medium text-gray-500">Optimization Opportunities</dt>
                    <dd className="text-2xl font-semibold text-gray-900">
                      {optimizationSuggestions.length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="overflow-hidden rounded-lg bg-white shadow ring-1 ring-black ring-opacity-5">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ArrowTrendingUpIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="truncate text-sm font-medium text-gray-500">Potential Cost Savings</dt>
                    <dd className="text-2xl font-semibold text-gray-900">
                      {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(
                        partOptimizationData.reduce((sum, part) => sum + part.potentialSavings, 0)
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Optimization Suggestions */}
      <div className="rounded-lg bg-white p-6 shadow">
        <h2 className="text-xl font-medium text-gray-900">AI-Generated Optimization Suggestions</h2>
        <p className="mt-1 text-sm text-gray-500">These recommendations are based on usage patterns, lead times, and inventory turnover analysis</p>

        <div className="mt-6 space-y-4">
          {optimizationSuggestions.length === 0 ? (
            <div className="rounded-md bg-green-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800">No optimization suggestions at this time. Your inventory is well-managed!</p>
                </div>
              </div>
            </div>
          ) : optimizationSuggestions.map((suggestion, index) => (
            <div key={index} className={`rounded-md p-4 ${suggestion.impact === 'high' ? 'bg-red-50' : suggestion.impact === 'medium' ? 'bg-yellow-50' : 'bg-blue-50'}`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  {suggestion.impact === 'high' ? (
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.707a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : suggestion.impact === 'medium' ? (
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <div className="ml-3 w-full">
                  <h3 className={`text-sm font-medium ${suggestion.impact === 'high' ? 'text-red-800' : suggestion.impact === 'medium' ? 'text-yellow-800' : 'text-blue-800'}`}>
                    {suggestion.title}
                  </h3>
                  <div className={`mt-2 text-sm ${suggestion.impact === 'high' ? 'text-red-700' : suggestion.impact === 'medium' ? 'text-yellow-700' : 'text-blue-700'}`}>
                    <p>{suggestion.description}</p>
                  </div>

                  {/* Suggestion details */}
                  {suggestion.type === 'excess_stock' && (
                    <div className="mt-4 overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className={suggestion.impact === 'high' ? 'bg-red-100' : suggestion.impact === 'medium' ? 'bg-yellow-100' : 'bg-blue-100'}>
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Part</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Current Stock</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Reorder Point</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Excess Units</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Excess Value</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white">
                          {suggestion.parts.map((part, idx) => (
                            <tr key={idx}>
                              <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                                <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                                  {part.name}
                                </Link>
                              </td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.currentStock}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.reorderPoint}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.excessUnits}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                                {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(part.excessValue)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}

                  {suggestion.type === 'slow_moving' && (
                    <div className="mt-4 overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className={suggestion.impact === 'high' ? 'bg-red-100' : suggestion.impact === 'medium' ? 'bg-yellow-100' : 'bg-blue-100'}>
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Part</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Current Stock</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Days Since Last Usage</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Value</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white">
                          {suggestion.parts.map((part, idx) => (
                            <tr key={idx}>
                              <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                                <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                                  {part.name}
                                </Link>
                              </td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.currentStock}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.daysSinceLastUsage}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                                {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(part.value)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}

                  {suggestion.type === 'critical_stock' && (
                    <div className="mt-4 overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className={suggestion.impact === 'high' ? 'bg-red-100' : suggestion.impact === 'medium' ? 'bg-yellow-100' : 'bg-blue-100'}>
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Part</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Current Stock</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Minimum Stock</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Est. Days Remaining</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white">
                          {suggestion.parts.map((part, idx) => (
                            <tr key={idx}>
                              <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                                <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                                  {part.name}
                                </Link>
                              </td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.currentStock}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.minimumStock}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.estimatedDaysRemaining}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}

                  {suggestion.type === 'reorder_point' && (
                    <div className="mt-4 overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className={suggestion.impact === 'high' ? 'bg-red-100' : suggestion.impact === 'medium' ? 'bg-yellow-100' : 'bg-blue-100'}>
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Part</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Current Reorder Point</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Suggested Reorder Point</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Monthly Usage Rate</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white">
                          {suggestion.parts.map((part, idx) => (
                            <tr key={idx}>
                              <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                                <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                                  {part.name}
                                </Link>
                              </td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.currentReorderPoint}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.suggestedReorderPoint}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.monthlyUsageRate}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}

                  {suggestion.type === 'supplier_consolidation' && (
                    <div className="mt-4 overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className={suggestion.impact === 'high' ? 'bg-red-100' : suggestion.impact === 'medium' ? 'bg-yellow-100' : 'bg-blue-100'}>
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Supplier</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Part Count</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Total Value</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white">
                          {suggestion.suppliers.map((supplier, idx) => (
                            <tr key={idx}>
                              <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">{supplier.name}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{supplier.partCount}</td>
                              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                                {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(supplier.totalValue)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}

                  <div className="mt-4">
                    <Link
                      to={suggestion.type === 'excess_stock' ? '/inventory/reports?tab=value' :
                          suggestion.type === 'slow_moving' ? '/inventory/reports?tab=usage' :
                          suggestion.type === 'critical_stock' ? '/inventory/advanced?tab=reorder' :
                          suggestion.type === 'reorder_point' ? '/inventory/advanced?tab=reorder' :
                          '/inventory/reports'}
                      className={`inline-flex rounded-md px-3 py-1.5 text-sm font-medium ${suggestion.impact === 'high' ? 'bg-red-100 text-red-800 hover:bg-red-200' : suggestion.impact === 'medium' ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' : 'bg-blue-100 text-blue-800 hover:bg-blue-200'}`}
                    >
                      Take Action
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Inventory Optimization Analysis */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Seasonal Trends */}
        <div className="rounded-lg bg-white p-6 shadow">
          <h2 className="text-xl font-medium text-gray-900">Seasonal Demand Patterns</h2>
          <p className="mt-1 text-sm text-gray-500">AI-detected seasonal variations in inventory usage</p>

          {seasonalTrends.seasonalPatterns.length === 0 ? (
            <div className="mt-4 rounded-md bg-gray-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-gray-700">Insufficient data to detect seasonal patterns</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="mt-4 space-y-2">
              {seasonalTrends.seasonalPatterns.map((pattern, idx) => (
                <div key={idx} className="flex items-center rounded-md bg-gray-50 p-3">
                  <div className="mr-4 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                    <span className="text-lg font-semibold">{pattern.month}</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{pattern.monthName}</p>
                    <p className="text-sm text-gray-500">
                      Usage increases by {((parseFloat(pattern.seasonalityFactor) - 1) * 100).toFixed(0)}% compared to average
                    </p>
                  </div>
                  <div className="ml-auto">
                    <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                      Factor: {pattern.seasonalityFactor}x
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-900">Recommended Actions</h3>
            <ul className="mt-2 list-disc space-y-1 pl-5 text-sm text-gray-500">
              <li>Increase stock levels before peak seasons</li>
              <li>Schedule preventive maintenance before high-demand periods</li>
              <li>Plan promotional activities during low-demand months</li>
              <li>Adjust reorder points seasonally for affected parts</li>
            </ul>
          </div>
        </div>

        {/* Inventory Value Distribution */}
        <div className="rounded-lg bg-white p-6 shadow">
          <h2 className="text-xl font-medium text-gray-900">Inventory Health Distribution</h2>
          <p className="mt-1 text-sm text-gray-500">Value allocation across inventory health categories</p>

          <div className="mt-6">
            <div className="grid grid-cols-1 gap-4">
              <div className="rounded-md bg-white">
                <div className="relative overflow-hidden rounded-full bg-gray-200">
                  <div className="h-4 rounded-full bg-green-500" style={{ width: `${(inventoryMetrics.healthyStockValue / inventoryMetrics.totalValue) * 100}%` }}></div>
                </div>
                <div className="mt-2 flex items-center justify-between text-sm">
                  <span className="font-medium text-gray-900">Healthy Stock</span>
                  <span className="text-gray-500">
                    {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(inventoryMetrics.healthyStockValue)}
                    {' '}({((inventoryMetrics.healthyStockValue / inventoryMetrics.totalValue) * 100).toFixed(1)}%)
                  </span>
                </div>
              </div>

              <div className="rounded-md bg-white">
                <div className="relative overflow-hidden rounded-full bg-gray-200">
                  <div className="h-4 rounded-full bg-red-500" style={{ width: `${(inventoryMetrics.criticalStockValue / inventoryMetrics.totalValue) * 100}%` }}></div>
                </div>
                <div className="mt-2 flex items-center justify-between text-sm">
                  <span className="font-medium text-gray-900">Critical Stock</span>
                  <span className="text-gray-500">
                    {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(inventoryMetrics.criticalStockValue)}
                    {' '}({((inventoryMetrics.criticalStockValue / inventoryMetrics.totalValue) * 100).toFixed(1)}%)
                  </span>
                </div>
              </div>

              <div className="rounded-md bg-white">
                <div className="relative overflow-hidden rounded-full bg-gray-200">
                  <div className="h-4 rounded-full bg-yellow-500" style={{ width: `${(inventoryMetrics.excessStockValue / inventoryMetrics.totalValue) * 100}%` }}></div>
                </div>
                <div className="mt-2 flex items-center justify-between text-sm">
                  <span className="font-medium text-gray-900">Excess Stock</span>
                  <span className="text-gray-500">
                    {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(inventoryMetrics.excessStockValue)}
                    {' '}({((inventoryMetrics.excessStockValue / inventoryMetrics.totalValue) * 100).toFixed(1)}%)
                  </span>
                </div>
              </div>

              <div className="rounded-md bg-white">
                <div className="relative overflow-hidden rounded-full bg-gray-200">
                  <div className="h-4 rounded-full bg-purple-500" style={{ width: `${(inventoryMetrics.slowMovingValue / inventoryMetrics.totalValue) * 100}%` }}></div>
                </div>
                <div className="mt-2 flex items-center justify-between text-sm">
                  <span className="font-medium text-gray-900">Slow-Moving Stock</span>
                  <span className="text-gray-500">
                    {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(inventoryMetrics.slowMovingValue)}
                    {' '}({((inventoryMetrics.slowMovingValue / inventoryMetrics.totalValue) * 100).toFixed(1)}%)
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-900">Target Distribution</h3>
            <p className="mt-1 text-sm text-gray-500">Ideal inventory health breakdown:</p>
            <ul className="mt-2 list-disc space-y-1 pl-5 text-sm text-gray-500">
              <li>Healthy Stock: 80-85%</li>
              <li>Critical Stock: &lt;5%</li>
              <li>Excess Stock: &lt;10%</li>
              <li>Slow-Moving Stock: &lt;5%</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Optimized Parts List */}
      <div className="rounded-lg bg-white p-6 shadow">
        <h2 className="text-xl font-medium text-gray-900">Top Optimization Opportunities</h2>
        <p className="mt-1 text-sm text-gray-500">Parts with the highest potential for inventory optimization</p>

        <div className="mt-4 overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Part Name</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Category</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Current Stock</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Optimal Level</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Monthly Usage</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Turnover Rate</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Potential Savings</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {partOptimizationData.slice(0, 10).map((part, idx) => (
                <tr key={idx}>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                      {part.name}
                    </Link>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.category || 'Uncategorized'}</td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.currentStock}</td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.optimalStockLevel}</td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.usageRate.toFixed(2)}</td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">{part.turnoverRate.toFixed(2)}</td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(part.potentialSavings)}
                  </td>
                </tr>
              ))}

              {partOptimizationData.length === 0 && (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    No optimization opportunities found at this time.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* FAQ and Recommendations */}
      <div className="rounded-lg bg-white p-6 shadow">
        <h2 className="text-xl font-medium text-gray-900">AI-Powered Inventory Management FAQ</h2>

        <div className="mt-4 space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">How is the Inventory Health Score calculated?</h3>
            <p className="mt-1 text-sm text-gray-500">
              The Inventory Health Score combines multiple factors including stock level optimization, turnover rates, supplier performance,
              and lead time consistency. A higher score indicates better inventory management practices with reduced carrying costs.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900">What is Economic Order Quantity (EOQ)?</h3>
            <p className="mt-1 text-sm text-gray-500">
              EOQ is the optimal order quantity that minimizes the total costs of inventory, including holding costs, ordering costs, and stockout costs.
              It's calculated using the formula: EOQ = √(2DS/H), where D is annual demand, S is ordering cost, and H is holding cost per unit.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900">How can I implement these recommendations?</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start with the high-impact suggestions first. For excess inventory, consider reducing order quantities or running promotions.
              For critical stock, adjust reorder points and order frequencies. The system automatically updates recommendations based on
              inventory changes and transaction patterns.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900">How often should I review the optimization suggestions?</h3>
            <p className="mt-1 text-sm text-gray-500">
              For optimal inventory management, review the AI suggestions at least monthly. However, critical stock alerts should be addressed
              immediately. After implementing changes, allow 2-3 inventory cycles to evaluate their effectiveness before making additional adjustments.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}