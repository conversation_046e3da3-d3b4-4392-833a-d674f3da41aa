import { json } from '@remix-run/node';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { useLoaderData } from '@remix-run/react';
import { InfoIcon } from 'lucide-react';
import { ServiceAvailabilityPanel } from '~/components/molecules/service-availability-panel';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { requireUser } from '~/session.server';

export async function loader({ request }: LoaderFunctionArgs) {
  // Require user to be logged in
  const user = await requireUser(request);
  
  return json({ user });
}

export default function ServiceStatusPage() {
  const { user } = useLoaderData<typeof loader>();
  
  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Service Status</h1>
      
      <div className="grid gap-6">
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>About Service Status</AlertTitle>
          <AlertDescription>
            This page shows the status of backend services that power advanced features of the application.
            If a service is unavailable, related features will still work with limited functionality.
          </AlertDescription>
        </Alert>
        
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="mt-6">
            <ServiceAvailabilityPanel />
          </TabsContent>
          
          <TabsContent value="details" className="mt-6">
            <div className="grid gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Bielik LLM</CardTitle>
                  <CardDescription>
                    Powers AI features like predictive maintenance, semantic search, and OCR
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    When Bielik LLM is unavailable, the following features will have limited functionality:
                  </p>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>Predictive maintenance will show historical data only</li>
                    <li>OCR will use basic text extraction without AI enhancement</li>
                    <li>Semantic search will fall back to keyword search</li>
                    <li>AI-powered recommendations will be unavailable</li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>OCR Service</CardTitle>
                  <CardDescription>
                    Processes documents and extracts text and structured data
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    When OCR Service is unavailable, the following features will have limited functionality:
                  </p>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>Document processing will require manual data entry</li>
                    <li>Invoice scanning will be unavailable</li>
                    <li>Service report digitization will be unavailable</li>
                  </ul>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Predictive Maintenance</CardTitle>
                  <CardDescription>
                    Analyzes device telemetry and predicts maintenance needs
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    When Predictive Maintenance is unavailable, the following features will have limited functionality:
                  </p>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>Maintenance predictions will show historical data only</li>
                    <li>Failure probability calculations will be unavailable</li>
                    <li>Automated maintenance scheduling will be unavailable</li>
                    <li>Component-level diagnostics will be unavailable</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
