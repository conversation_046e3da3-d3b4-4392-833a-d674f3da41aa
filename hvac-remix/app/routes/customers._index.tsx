import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSearchParams, Link } from "@remix-run/react";
import * as React from "react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { getCustomers, type Customer } from "~/services/customer.service";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);

  // Get pagination parameters from URL
  const page = parseInt(url.searchParams.get("page") || "1", 10);
  const pageSize = parseInt(url.searchParams.get("pageSize") || "10", 10);
  const orderBy = url.searchParams.get("orderBy") || "createdAt";
  const orderDirection = (url.searchParams.get("orderDirection") || "desc") as "asc" | "desc";

  // Get filter parameters from URL
  const search = url.searchParams.get("search") || undefined;
  const status = url.searchParams.get("status") || undefined;

  // Get customers with pagination and filters
  const customersResponse = await getCustomers(
    userId,
    { page, pageSize, orderBy, orderDirection },
    { search, status }
  );

  return json({
    customers: customersResponse.data || [],
    totalCount: customersResponse.totalCount,
    totalPages: customersResponse.totalPages,
    currentPage: customersResponse.currentPage,
    error: customersResponse.error,
  });
}

export default function CustomersIndexPage() {
  const { customers, totalCount, totalPages, currentPage, error } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get("search") || "");

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery) {
      newParams.set("search", searchQuery);
    } else {
      newParams.delete("search");
    }
    newParams.set("page", "1"); // Reset to first page on new search
    setSearchParams(newParams);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set("page", newPage.toString());
    setSearchParams(newParams);
  };

  return (
    <div className="container py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Customers</h1>
        <Link to="/customers/new">
          <Button>Add Customer</Button>
        </Link>
      </div>

      {/* Search form */}
      <form onSubmit={handleSearch} className="mb-6">
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              type="text"
              placeholder="Search customers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button type="submit">Search</Button>
        </div>
      </form>

      {/* Error message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Customers list */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {customers.length > 0 ? (
          customers.map((customer) => (
            <CustomerCard key={customer.id} customer={customer} />
          ))
        ) : (
          <div className="col-span-full text-center py-8">
            <p className="text-gray-500">No customers found</p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={page === currentPage ? "default" : "outline"}
                  onClick={() => handlePageChange(page)}
                  className="w-10"
                >
                  {page}
                </Button>
              ))}
            </div>

            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Summary */}
      <div className="mt-6 text-center text-gray-500">
        Showing {customers.length} of {totalCount} customers
      </div>
    </div>
  );
}

function CustomerCard({ customer }: { customer: Customer }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Link to={`/customers/${customer.id}`} className="hover:underline">
            {customer.name}
          </Link>
        </CardTitle>
        <CardDescription>
          {customer.email || "No email"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {customer.phone && (
            <div>
              <Label>Phone</Label>
              <p>{customer.phone}</p>
            </div>
          )}
          {customer.address && (
            <div>
              <Label>Address</Label>
              <p>{customer.address}</p>
              {customer.city && customer.postalCode && (
                <p>
                  {customer.city}, {customer.postalCode}
                  {customer.country ? `, ${customer.country}` : ""}
                </p>
              )}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link to={`/customers/${customer.id}`}>
          <Button variant="outline">View Details</Button>
        </Link>
        <Link to={`/customers/${customer.id}/edit`}>
          <Button variant="outline">Edit</Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
