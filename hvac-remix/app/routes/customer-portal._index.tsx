import { type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, Card<PERSON>eader, CardTitle } from "~/components/ui/card";
import { getCustomerPortalData } from "~/services/customer-portal.server";
import { getUser } from "~/session.server";

// Define types for customer data
interface Device {
  id: string;
  type: string;
  name: string;
  model: string;
  serialNumber: string;
  installationDate: string;
  status: string;
}

interface ServiceOrder {
  id: string;
  title: string;
  description: string;
  status: string;
  date: string;
  scheduledDate?: string;
  deviceId: string;
  device?: {
    name: string;
  };
}

interface Invoice {
  id: string;
  number: string;
  amount: number;
  status: string;
  dueDate: string;
  serviceOrderId: string;
}

// These types are used for type casting in the loader

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await getUser(request);

  if (!user || user.role !== "CUSTOMER") {
    throw new Response("Unauthorized", { status: 401 });
  }

  const customerResponse = await getCustomerPortalData(user.id);

  if (!customerResponse.success || !customerResponse.data) {
    throw new Response("Failed to load customer data", { status: 500 });
  }

  // Cast the data to our type and return it
  return new Response(JSON.stringify({
    customer: customerResponse.data as unknown as {
      id: string;
      name: string;
      email: string;
      phone: string;
      address: string;
      city: string;
      postalCode: string;
      country: string;
      devices: Device[];
      serviceOrders: ServiceOrder[];
      invoices: Invoice[];
    }
  }), {
    headers: {
      "Content-Type": "application/json",
      "Cache-Control": "max-age=60, stale-while-revalidate=180"
    }
  });
};


export default function CustomerPortalDashboard() {
  const { customer } = useLoaderData<typeof loader>();

  // Count devices by type
  const deviceTypes = customer.devices.reduce((acc: Record<string, number>, device: Device) => {
    const type = device.type || "Unknown";
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  // Count service orders by status
  const serviceOrderStatuses = customer.serviceOrders.reduce((acc: Record<string, number>, order: ServiceOrder) => {
    acc[order.status] = (acc[order.status] || 0) + 1;
    return acc;
  }, {});

  // Get pending invoices
  const pendingInvoices = customer.invoices.filter((invoice: Invoice) => invoice.status === "PENDING");

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8 shadow-lg border border-primary/10">
        <h1 className="text-3xl font-bold mb-2">Welcome, {customer.name}</h1>
        <p className="text-muted-foreground">Manage your HVAC equipment and service requests in one place</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="overflow-hidden">
          <div className="absolute top-0 right-0 left-0 h-1 bg-info"></div>
          <CardHeader>
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-info" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect>
                <polyline points="17 2 12 7 7 2"></polyline>
              </svg>
              <CardTitle>Devices</CardTitle>
            </div>
            <CardDescription>Your registered HVAC equipment</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-info">{customer.devices.length}</div>
            <div className="mt-4 space-y-2">
              {Object.entries(deviceTypes).map(([type, count]) => (
                <div key={type} className="flex justify-between items-center py-1 border-b border-border/20">
                  <span className="text-sm">{type}</span>
                  <Badge variant="outline" className="bg-info/10 text-info border-info/20">{String(count)}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" size="sm" className="w-full">
              <Link to="/customer-portal/devices">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
                View All Devices
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card className="overflow-hidden">
          <div className="absolute top-0 right-0 left-0 h-1 bg-primary"></div>
          <CardHeader>
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
              <CardTitle>Service Orders</CardTitle>
            </div>
            <CardDescription>Your maintenance and repair history</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-primary">{customer.serviceOrders.length}</div>
            <div className="mt-4 space-y-2">
              {Object.entries(serviceOrderStatuses).map(([status, count]) => (
                <div key={status} className="flex justify-between items-center py-1 border-b border-border/20">
                  <span className="text-sm">{status}</span>
                  <Badge
                    className={
                      status === "COMPLETED" ? "bg-success/10 text-success border-success/20" :
                      status === "IN_PROGRESS" ? "bg-warning/10 text-warning border-warning/20" :
                      status === "PENDING" ? "bg-primary/10 text-primary border-primary/20" :
                      "bg-destructive/10 text-destructive border-destructive/20"
                    }
                  >
                    {String(count)}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" size="sm" className="w-full">
              <Link to="/customer-portal/service-orders">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="9 11 12 14 22 4"></polyline>
                  <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>
                </svg>
                View Service History
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card className="overflow-hidden">
          <div className="absolute top-0 right-0 left-0 h-1 bg-warning"></div>
          <CardHeader>
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-warning" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                <line x1="1" y1="10" x2="23" y2="10"></line>
              </svg>
              <CardTitle>Invoices</CardTitle>
            </div>
            <CardDescription>Your billing information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-warning">{customer.invoices.length}</div>
            <div className="mt-4 space-y-2">
              <div className="flex justify-between items-center py-1 border-b border-border/20">
                <span className="text-sm">Pending</span>
                <Badge className="bg-warning/10 text-warning border-warning/20">{pendingInvoices.length}</Badge>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" size="sm" className="w-full">
              <Link to="/customer-portal/invoices">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="12" y1="1" x2="12" y2="23"></line>
                  <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                </svg>
                View Invoices
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
              </svg>
              <CardTitle>Recent Service Orders</CardTitle>
            </div>
            <CardDescription>Your most recent service requests</CardDescription>
          </CardHeader>
          <CardContent>
            {customer.serviceOrders.length > 0 ? (
              <div className="space-y-4">
                {customer.serviceOrders.slice(0, 5).map((order: ServiceOrder) => (
                  <Link key={order.id} to={`/customer-portal/service-orders/${order.id}`} className="block">
                    <div className="p-3 rounded-xl border border-border/30 hover:border-primary/30 hover:bg-primary/5 transition-all">
                      <div className="flex justify-between items-start">
                        <h3 className="font-medium">{order.title}</h3>
                        <Badge
                          className={
                            order.status === "COMPLETED" ? "bg-success/10 text-success border-success/20" :
                            order.status === "IN_PROGRESS" ? "bg-warning/10 text-warning border-warning/20" :
                            order.status === "PENDING" ? "bg-primary/10 text-primary border-primary/20" :
                            "bg-destructive/10 text-destructive border-destructive/20"
                          }
                        >
                          {order.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"></circle>
                          <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        {order.scheduledDate ? new Date(order.scheduledDate).toLocaleDateString() : "Not scheduled"}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-muted-foreground/50 mb-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <p className="text-muted-foreground">No service orders found.</p>
                <Button asChild variant="outline" size="sm" className="mt-4">
                  <Link to="/customer-portal/request-service">Request Service</Link>
                </Button>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" size="sm" className="w-full">
              <Link to="/customer-portal/service-orders">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="8" y1="6" x2="21" y2="6"></line>
                  <line x1="8" y1="12" x2="21" y2="12"></line>
                  <line x1="8" y1="18" x2="21" y2="18"></line>
                  <line x1="3" y1="6" x2="3.01" y2="6"></line>
                  <line x1="3" y1="12" x2="3.01" y2="12"></line>
                  <line x1="3" y1="18" x2="3.01" y2="18"></line>
                </svg>
                View All Service Orders
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card className="bg-gradient-to-br from-primary/5 to-accent/5">
          <CardHeader>
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
              </svg>
              <CardTitle>Quick Actions</CardTitle>
            </div>
            <CardDescription>Common tasks and actions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button asChild className="w-full group">
              <Link to="/customer-portal/request-service" className="flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 group-hover:animate-pulse" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                Request Service
              </Link>
            </Button>

            <Button asChild variant="outline" className="w-full">
              <Link to="/customer-portal/profile" className="flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
                Update Profile
              </Link>
            </Button>

            <Button asChild variant="outline" className="w-full">
              <Link to="/customer-portal/devices" className="flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect>
                  <polyline points="17 2 12 7 7 2"></polyline>
                </svg>
                View Devices
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}