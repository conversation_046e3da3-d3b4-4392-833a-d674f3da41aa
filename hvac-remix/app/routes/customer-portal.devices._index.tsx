import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { getCustomerPortalData } from "~/services/customer-portal.server";
import { getUser } from "~/session.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await getUser(request);
  
  if (!user || user.role !== "CUSTOMER") {
    throw new Response("Unauthorized", { status: 401 });
  }
  
  const customerResponse = await getCustomerPortalData(user.id);
  
  if (!customerResponse.success || !customerResponse.data) {
    throw new Response("Failed to load customer data", { status: 500 });
  }
  
  return json({ customer: customerResponse.data });
};

export default function CustomerPortalDevices() {
  const { customer } = useLoaderData<typeof loader>();
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">My Devices</h1>
      </div>
      
      {customer.devices.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {customer.devices.map((device) => (
            <Card key={device.id}>
              <CardHeader>
                <CardTitle>{device.name}</CardTitle>
                <CardDescription>
                  {device.manufacturer} {device.model}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Type:</span>
                    <span>{device.type || "N/A"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Serial Number:</span>
                    <span>{device.serialNumber || "N/A"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Installation Date:</span>
                    <span>
                      {device.installationDate
                        ? new Date(device.installationDate).toLocaleDateString()
                        : "N/A"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Warranty Expiry:</span>
                    <span>
                      {device.warrantyExpiryDate
                        ? new Date(device.warrantyExpiryDate).toLocaleDateString()
                        : "N/A"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Last Maintenance:</span>
                    <span>
                      {device.lastMaintenanceDate
                        ? new Date(device.lastMaintenanceDate).toLocaleDateString()
                        : "N/A"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Next Maintenance:</span>
                    <span>
                      {device.nextMaintenanceDate
                        ? new Date(device.nextMaintenanceDate).toLocaleDateString()
                        : "N/A"}
                    </span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button asChild variant="outline" size="sm">
                  <Link to={`/customer-portal/devices/${device.id}`}>View Details</Link>
                </Button>
                <Button asChild size="sm">
                  <Link to={`/customer-portal/request-service?deviceId=${device.id}`}>Request Service</Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>No Devices Found</CardTitle>
            <CardDescription>
              You don't have any registered devices yet.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              Please contact your HVAC service provider to register your devices.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}