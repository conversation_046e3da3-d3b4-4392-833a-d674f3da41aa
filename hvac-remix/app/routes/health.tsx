import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { getRedisClient } from "~/utils/redis.server";

/**
 * Health check endpoint for the application
 * Used by monitoring systems and container orchestration platforms
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const startTime = Date.now();
  const checks = {
    database: { status: "unknown" },
    redis: { status: "unknown" },
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  };

  // Check database connection
  try {
    // Simple query to check if database is responsive
    await prisma.$queryRaw`SELECT 1`;
    checks.database = {
      status: "healthy",
      responseTime: `${Date.now() - startTime}ms`,
    };
  } catch (error) {
    checks.database = {
      status: "unhealthy",
      error: error instanceof Error ? error.message : String(error),
      responseTime: `${Date.now() - startTime}ms`,
    };
  }

  // Check Redis connection
  try {
    const redis = await getRedisClient();
    await redis.ping();
    checks.redis = {
      status: "healthy",
      responseTime: `${Date.now() - startTime}ms`,
    };
  } catch (error) {
    checks.redis = {
      status: "unhealthy",
      error: error instanceof Error ? error.message : String(error),
      responseTime: `${Date.now() - startTime}ms`,
    };
  }

  // Overall status
  const isHealthy =
    checks.database.status === "healthy" &&
    checks.redis.status === "healthy";

  const status = isHealthy ? 200 : 503;

  return json(
    {
      status: isHealthy ? "healthy" : "unhealthy",
      checks,
      totalResponseTime: `${Date.now() - startTime}ms`,
    },
    { status }
  );
}

// No UI needed for health check endpoint
export default function HealthCheck() {
  return null;
}
