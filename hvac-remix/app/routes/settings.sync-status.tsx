import type { MetaFunction } from "@remix-run/node";
import { useNavigate } from "@remix-run/react";
import { PageHeader } from "~/components/molecules/page-header";
import { SyncStatus as SyncStatusComponent } from "~/components/organisms/sync-status";

export const meta: MetaFunction = () => {
  return [
    { title: "Sync Status - HVAC CRM" },
    { name: "description", content: "Synchronize your offline data with the server" },
  ];
};

export default function SyncStatusPage() {
  const navigate = useNavigate();
  
  const handleComplete = () => {
    // Refresh the page after a short delay to show the updated status
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };
  
  return (
    <div className="container py-8">
      <PageHeader
        title="Sync Status"
        description="Synchronize your offline data with the server"
        backLink="/settings"
      />
      
      <div className="mt-8">
        <SyncStatusComponent onComplete={handleComplete} />
      </div>
    </div>
  );
}
