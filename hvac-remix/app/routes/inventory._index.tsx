import { PlusIcon, ArrowPathIcon, TableCellsIcon, ArchiveBoxIcon, Bars3BottomLeftIcon } from "@heroicons/react/24/outline";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { prisma } from "~/db.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);

  // Get query parameters for filtering
  const url = new URL(request.url);
  const category = url.searchParams.get("category");
  const search = url.searchParams.get("search");
  const lowStock = url.searchParams.get("lowStock") === "true";
  const sortBy = url.searchParams.get("sortBy") || "name";
  const sortDir = url.searchParams.get("sortDir") || "asc";
  const perPage = Number(url.searchParams.get("perPage") || "25");
  const page = Number(url.searchParams.get("page") || "1");

  // Build filter conditions
  const where: any = {
    isActive: true
  };

  if (category) {
    where.category = category;
  }

  if (search) {
    where.OR = [
      { name: { contains: search, mode: "insensitive" } },
      { description: { contains: search, mode: "insensitive" } },
      { partNumber: { contains: search, mode: "insensitive" } },
      { sku: { contains: search, mode: "insensitive" } },
    ];
  }

  if (lowStock) {
    where.currentStock = {
      lte: { reorderPoint: true }
    };
  }

  // Build order condition
  const orderBy: any = {};
  orderBy[sortBy] = sortDir;

  // Get inventory parts with pagination
  const parts = await prisma.inventoryPart.findMany({
    where,
    orderBy,
    take: perPage,
    skip: (page - 1) * perPage,
    include: {
      supplier: {
        select: {
          name: true
        }
      },
      inventoryLocations: {
        include: {
          location: {
            select: {
              name: true
            }
          }
        }
      }
    }
  });

  // Get total count for pagination
  const totalParts = await prisma.inventoryPart.count({ where });

  // Get all categories for filter dropdown
  const categories = await prisma.inventoryPart.groupBy({
    by: ["category"],
    _count: {
      id: true
    },
    where: {
      isActive: true,
      category: {
        not: null
      }
    }
  });

  // Get low stock parts count
  const lowStockCount = await prisma.inventoryPart.count({
    where: {
      isActive: true,
      currentStock: {
        lte: { reorderPoint: true }
      }
    }
  });

  const pagination = {
    totalItems: totalParts,
    totalPages: Math.ceil(totalParts / perPage),
    currentPage: page,
    perPage
  };

  return json({
    parts,
    categories,
    lowStockCount,
    pagination,
    filters: {
      category,
      search,
      lowStock,
      sortBy,
      sortDir
    }
  });
}

export default function InventoryIndex() {
  const { parts, categories, lowStockCount, pagination, filters } = useLoaderData<typeof loader>();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Inventory Management</h1>
        <div className="flex gap-2">
          <Link
            to="/inventory/parts/new"
            className="flex items-center gap-1 rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700"
          >
            <PlusIcon className="h-4 w-4" />
            Add Part
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="flex items-center space-x-4 rounded-lg border border-gray-200 p-4 shadow">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 text-blue-600">
            <ArchiveBoxIcon className="h-6 w-6" />
          </div>
          <div>
            <div className="text-sm text-gray-500">Total Parts</div>
            <div className="text-2xl font-semibold">{pagination.totalItems}</div>
          </div>
        </div>

        <div className="flex items-center space-x-4 rounded-lg border border-gray-200 p-4 shadow">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100 text-red-600">
            <ArrowPathIcon className="h-6 w-6" />
          </div>
          <div>
            <div className="text-sm text-gray-500">Low Stock</div>
            <div className="text-2xl font-semibold">{lowStockCount}</div>
          </div>
        </div>

        <div className="flex items-center space-x-4 rounded-lg border border-gray-200 p-4 shadow">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 text-green-600">
            <TableCellsIcon className="h-6 w-6" />
          </div>
          <div>
            <div className="text-sm text-gray-500">Categories</div>
            <div className="text-2xl font-semibold">{categories.length}</div>
          </div>
        </div>

        <div className="flex items-center space-x-4 rounded-lg border border-gray-200 p-4 shadow">
          <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 text-purple-600">
            <Bars3BottomLeftIcon className="h-6 w-6" />
          </div>
          <div>
            <div className="text-sm text-gray-500">Value</div>
            <div className="text-2xl font-semibold">
              {new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' })
                .format(parts.reduce((sum, part) => sum + ((part.costPrice || 0) * part.currentStock), 0))}
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-md border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 p-4">
          <form className="flex flex-col gap-4 sm:flex-row sm:items-center">
            {/* Search input */}
            <div className="w-full sm:w-1/3">
              <input
                type="text"
                name="search"
                placeholder="Search parts..."
                defaultValue={filters.search || ''}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              />
            </div>

            {/* Category filter */}
            <div className="w-full sm:w-1/4">
              <select
                name="category"
                defaultValue={filters.category || ''}
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.category} value={category.category}>
                    {category.category} ({category._count.id})
                  </option>
                ))}
              </select>
            </div>

            {/* Low stock filter */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="lowStock"
                name="lowStock"
                value="true"
                defaultChecked={filters.lowStock}
                className="h-4 w-4 rounded border-gray-300 text-blue-600"
              />
              <label htmlFor="lowStock" className="ml-2 text-sm text-gray-700">
                Low Stock ({lowStockCount})
              </label>
            </div>

            <div className="flex items-center gap-2">
              <button
                type="submit"
                className="rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700"
              >
                Filter
              </button>

              <Link
                to="/inventory"
                className="rounded-md border border-gray-300 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Reset
              </Link>
            </div>
          </form>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  <div className="flex items-center gap-1">
                    Part Name
                    <Link to={`?sortBy=name&sortDir=${filters.sortBy === 'name' && filters.sortDir === 'asc' ? 'desc' : 'asc'}`} className="text-gray-400 hover:text-gray-700">
                      {filters.sortBy === 'name' ? (
                        filters.sortDir === 'asc' ? '↑' : '↓'
                      ) : '↕'}
                    </Link>
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  <div className="flex items-center gap-1">
                    Part Number
                    <Link to={`?sortBy=partNumber&sortDir=${filters.sortBy === 'partNumber' && filters.sortDir === 'asc' ? 'desc' : 'asc'}`} className="text-gray-400 hover:text-gray-700">
                      {filters.sortBy === 'partNumber' ? (
                        filters.sortDir === 'asc' ? '↑' : '↓'
                      ) : '↕'}
                    </Link>
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  <div className="flex items-center gap-1">
                    Category
                    <Link to={`?sortBy=category&sortDir=${filters.sortBy === 'category' && filters.sortDir === 'asc' ? 'desc' : 'asc'}`} className="text-gray-400 hover:text-gray-700">
                      {filters.sortBy === 'category' ? (
                        filters.sortDir === 'asc' ? '↑' : '↓'
                      ) : '↕'}
                    </Link>
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  <div className="flex items-center gap-1">
                    Stock
                    <Link to={`?sortBy=currentStock&sortDir=${filters.sortBy === 'currentStock' && filters.sortDir === 'asc' ? 'desc' : 'asc'}`} className="text-gray-400 hover:text-gray-700">
                      {filters.sortBy === 'currentStock' ? (
                        filters.sortDir === 'asc' ? '↑' : '↓'
                      ) : '↕'}
                    </Link>
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  <div className="flex items-center gap-1">
                    Price
                    <Link to={`?sortBy=sellingPrice&sortDir=${filters.sortBy === 'sellingPrice' && filters.sortDir === 'asc' ? 'desc' : 'asc'}`} className="text-gray-400 hover:text-gray-700">
                      {filters.sortBy === 'sellingPrice' ? (
                        filters.sortDir === 'asc' ? '↑' : '↓'
                      ) : '↕'}
                    </Link>
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Location
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Supplier
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {parts.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-4 text-center text-sm text-gray-500">
                    No parts found. <Link to="/inventory/parts/new" className="text-blue-600 hover:underline">Add a part</Link>
                  </td>
                </tr>
              ) : (
                parts.map((part) => (
                  <tr key={part.id} className="hover:bg-gray-50">
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      <Link to={`/inventory/parts/${part.id}`} className="text-blue-600 hover:underline">
                        {part.name}
                      </Link>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {part.partNumber || '-'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {part.category || '-'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <span
                          className={`mr-2 h-2 w-2 rounded-full ${part.currentStock <= part.minimumStock ? 'bg-red-500' :
                            part.currentStock <= part.reorderPoint ? 'bg-yellow-500' : 'bg-green-500'}`}
                        ></span>
                        {part.currentStock} {part.unitOfMeasure}
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {part.sellingPrice
                        ? new Intl.NumberFormat('pl-PL', { style: 'currency', currency: 'PLN' }).format(part.sellingPrice)
                        : '-'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {part.inventoryLocations.length > 0
                        ? part.inventoryLocations[0].location.name
                        : part.location || '-'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {part.supplier?.name || '-'}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      <div className="flex space-x-2">
                        <Link
                          to={`/inventory/parts/${part.id}/edit`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Edit
                        </Link>
                        <Link
                          to={`/inventory/parts/${part.id}`}
                          className="text-green-600 hover:text-green-900"
                        >
                          View
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="border-t border-gray-200 bg-gray-50 px-4 py-3 sm:px-6">
          <div className="flex items-center justify-between">
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{((pagination.currentPage - 1) * pagination.perPage) + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(pagination.currentPage * pagination.perPage, pagination.totalItems)}
                  </span>{' '}
                  of <span className="font-medium">{pagination.totalItems}</span> results
                </p>
              </div>
              <div>
                <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                  <Link
                    to={`?page=${Math.max(1, pagination.currentPage - 1)}`}
                    className={`relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 ${pagination.currentPage === 1 ? 'cursor-not-allowed' : 'hover:text-gray-700'}`}
                  >
                    <span className="sr-only">Previous</span>
                    ←
                  </Link>

                  {/* Generate page links */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    // Logic to show pages around current page
                    let pageNum = i + 1;
                    if (pagination.totalPages > 5) {
                      if (pagination.currentPage > 3) {
                        pageNum = pagination.currentPage - 3 + i;
                      }
                      if (pageNum > pagination.totalPages - 2) {
                        pageNum = pagination.totalPages - 4 + i;
                      }
                    }

                    return (
                      <Link
                        key={pageNum}
                        to={`?page=${pageNum}`}
                        className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${pageNum === pagination.currentPage
                          ? 'z-10 bg-blue-600 text-white'
                          : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50'}`}
                      >
                        {pageNum}
                      </Link>
                    );
                  })}

                  <Link
                    to={`?page=${Math.min(pagination.totalPages, pagination.currentPage + 1)}`}
                    className={`relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 ${pagination.currentPage === pagination.totalPages ? 'cursor-not-allowed' : 'hover:text-gray-700'}`}
                  >
                    <span className="sr-only">Next</span>
                    →
                  </Link>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}