import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { prisma } from "~/db.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  
  const customers = await prisma.customer.findMany({
    where: { userId },
    orderBy: { name: "asc" },
  });
  
  const serviceOrders = await prisma.serviceOrder.findMany({
    where: { 
      userId,
      status: { not: "CANCELLED" },
    },
    include: {
      customer: true,
    },
    orderBy: { createdAt: "desc" },
  });
  
  return json({ customers, serviceOrders });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  
  const formData = await request.formData();
  const customerId = formData.get("customerId") as string;
  const serviceOrderId = formData.get("serviceOrderId") as string || undefined;
  const invoiceNumber = formData.get("invoiceNumber") as string || undefined;
  const issueDate = formData.get("issueDate") as string || undefined;
  const dueDate = formData.get("dueDate") as string || undefined;
  const totalAmount = formData.get("totalAmount") as string || undefined;
  const taxAmount = formData.get("taxAmount") as string || undefined;
  const status = formData.get("status") as string || "PENDING";
  const notes = formData.get("notes") as string || undefined;
  
  if (!customerId) {
    return json({ error: "Customer is required" }, { status: 400 });
  }
  
  try {
    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber,
        issueDate: issueDate ? new Date(issueDate) : undefined,
        dueDate: dueDate ? new Date(dueDate) : undefined,
        totalAmount: totalAmount ? parseFloat(totalAmount) : undefined,
        taxAmount: taxAmount ? parseFloat(taxAmount) : undefined,
        status,
        notes,
        ocrProcessingStatus: "PENDING",
        customer: { connect: { id: customerId } },
        ...(serviceOrderId ? { serviceOrder: { connect: { id: serviceOrderId } } } : {}),
        user: { connect: { id: userId } },
      },
    });
    
    return redirect(`/invoices/${invoice.id}`);
  } catch (error) {
    console.error("Error creating invoice:", error);
    return json({ error: "An error occurred while creating the invoice" }, { status: 500 });
  }
}

export default function NewInvoice() {
  const { customers, serviceOrders } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  
  const isSubmitting = navigation.state === "submitting";
  
  return (
    <div>
      <div className="mb-6 flex items-center">
        <Link to="/invoices" className="mr-4 text-gray-500 hover:text-gray-700">
          <ArrowLeftIcon className="h-5 w-5" />
        </Link>
        <h2 className="text-xl font-semibold">New Invoice</h2>
      </div>
      
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Invoice Information</h3>
        </div>
        <div className="px-6 py-4">
          <Form method="post">
            {actionData?.error && (
              <div className="mb-4 rounded-md bg-red-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">{actionData.error}</h3>
                  </div>
                </div>
              </div>
            )}
            
            <div className="mb-4">
              <label htmlFor="customerId" className="block text-sm font-medium text-gray-700">
                Customer <span className="text-red-500">*</span>
              </label>
              <select
                id="customerId"
                name="customerId"
                required
                className="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="">Select a customer</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-4">
              <label htmlFor="serviceOrderId" className="block text-sm font-medium text-gray-700">
                Service Order
              </label>
              <select
                id="serviceOrderId"
                name="serviceOrderId"
                className="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              >
                <option value="">None</option>
                {serviceOrders.map((order) => (
                  <option key={order.id} value={order.id}>
                    {order.title} - {order.customer.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-4">
              <label htmlFor="invoiceNumber" className="block text-sm font-medium text-gray-700">
                Invoice Number
              </label>
              <input
                type="text"
                id="invoiceNumber"
                name="invoiceNumber"
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              />
            </div>
            
            <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label htmlFor="issueDate" className="block text-sm font-medium text-gray-700">
                  Issue Date
                </label>
                <input
                  type="date"
                  id="issueDate"
                  name="issueDate"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>
              
              <div>
                <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700">
                  Due Date
                </label>
                <input
                  type="date"
                  id="dueDate"
                  name="dueDate"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                />
              </div>
            </div>
            
            <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label htmlFor="totalAmount" className="block text-sm font-medium text-gray-700">
                  Total Amount
                </label>
                <div className="relative mt-1 rounded-md shadow-sm">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    id="totalAmount"
                    name="totalAmount"
                    step="0.01"
                    min="0"
                    className="block w-full rounded-md border border-gray-300 pl-7 pr-12 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder="0.00"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="taxAmount" className="block text-sm font-medium text-gray-700">
                  Tax Amount
                </label>
                <div className="relative mt-1 rounded-md shadow-sm">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    id="taxAmount"
                    name="taxAmount"
                    step="0.01"
                    min="0"
                    className="block w-full rounded-md border border-gray-300 pl-7 pr-12 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder="0.00"
                  />
                </div>
              </div>
            </div>
            
            <div className="mb-4">
              <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                id="status"
                name="status"
                className="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                defaultValue="PENDING"
              >
                <option value="PENDING">Pending</option>
                <option value="PAID">Paid</option>
                <option value="OVERDUE">Overdue</option>
                <option value="CANCELLED">Cancelled</option>
              </select>
            </div>
            
            <div className="mb-4">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                rows={3}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
              />
            </div>
            
            <div className="mt-6 flex justify-end">
              <Link
                to="/invoices"
                className="mr-4 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSubmitting}
                className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
              >
                {isSubmitting ? "Creating..." : "Create Invoice"}
              </button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
}