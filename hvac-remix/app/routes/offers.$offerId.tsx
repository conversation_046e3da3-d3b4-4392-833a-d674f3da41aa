import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { ArrowLeft, Send, Check, X, FileText } from "lucide-react";
import { OfferStatusBadge } from "~/components/molecules/OfferStatusBadge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { getOffer, markOfferAsSent, respondToOffer, createServiceOrderFromOffer } from "~/services/offer.service";
import { requireUserId } from "~/session.server";
import { formatCurrency, formatDate } from "~/utils";

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  const { offerId } = params;
  
  if (!offerId) {
    throw new Response("Offer ID is required", { status: 400 });
  }
  
  const offer = await getOffer({ id: offerId, userId });
  
  if (!offer) {
    throw new Response("Offer not found", { status: 404 });
  }
  
  return json({ offer });
};

export const action = async ({ params, request }: ActionFunctionArgs) => {
  const userId = await requireUserId(request);
  const { offerId } = params;
  
  if (!offerId) {
    throw new Response("Offer ID is required", { status: 400 });
  }
  
  const formData = await request.formData();
  const action = formData.get("_action") as string;
  
  switch (action) {
    case "mark-as-sent":
      await markOfferAsSent({ id: offerId, userId });
      return json({ success: true });
    
    case "accept-offer":
      await respondToOffer({ id: offerId, status: "ACCEPTED", userId });
      return json({ success: true });
    
    case "reject-offer":
      await respondToOffer({ id: offerId, status: "REJECTED", userId });
      return json({ success: true });
    
    case "create-service-order":
      const serviceOrder = await createServiceOrderFromOffer({ offerId, userId });
      return redirect(`/service-orders/${serviceOrder.id}`);
    
    default:
      throw new Response(`Unknown action: ${action}`, { status: 400 });
  }
};

export default function OfferDetailPage() {
  const { offer } = useLoaderData<typeof loader>();
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link to="/offers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Offers
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">{offer.title}</h1>
          <OfferStatusBadge status={offer.status} />
        </div>
        <div className="flex gap-2">
          <Link to={`/offers/${offer.id}/edit`}>
            <Button variant="outline">Edit Offer</Button>
          </Link>
          {offer.status === "DRAFT" && (
            <form method="post">
              <input type="hidden" name="_action" value="mark-as-sent" />
              <Button type="submit">
                <Send className="h-4 w-4 mr-1" />
                Mark as Sent
              </Button>
            </form>
          )}
          {offer.status === "SENT" && (
            <div className="flex gap-2">
              <form method="post">
                <input type="hidden" name="_action" value="accept-offer" />
                <Button type="submit" variant="success">
                  <Check className="h-4 w-4 mr-1" />
                  Accept
                </Button>
              </form>
              <form method="post">
                <input type="hidden" name="_action" value="reject-offer" />
                <Button type="submit" variant="destructive">
                  <X className="h-4 w-4 mr-1" />
                  Reject
                </Button>
              </form>
            </div>
          )}
          {offer.status === "ACCEPTED" && (
            <form method="post">
              <input type="hidden" name="_action" value="create-service-order" />
              <Button type="submit">
                <FileText className="h-4 w-4 mr-1" />
                Create Service Order
              </Button>
            </form>
          )}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Offer Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {offer.description && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                    <p className="mt-1">{offer.description}</p>
                  </div>
                )}
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Total Amount</h3>
                    <p className="mt-1 font-medium">{formatCurrency(offer.totalAmount)}</p>
                  </div>
                  
                  {offer.taxAmount && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Tax Amount</h3>
                      <p className="mt-1">{formatCurrency(offer.taxAmount)}</p>
                    </div>
                  )}
                  
                  {offer.discountAmount && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Discount</h3>
                      <p className="mt-1">{formatCurrency(offer.discountAmount)}</p>
                    </div>
                  )}
                  
                  {offer.validUntil && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Valid Until</h3>
                      <p className="mt-1">{formatDate(new Date(offer.validUntil))}</p>
                    </div>
                  )}
                </div>
                
                {offer.notes && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
                    <p className="mt-1">{offer.notes}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Offer Items</CardTitle>
            </CardHeader>
            <CardContent>
              {offer.items.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">No items added to this offer yet.</p>
                  <Link to={`/offers/${offer.id}/items/new`} className="mt-2 inline-block">
                    <Button variant="outline" size="sm">Add Items</Button>
                  </Link>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2 font-medium">Description</th>
                        <th className="text-right py-2 font-medium">Quantity</th>
                        <th className="text-right py-2 font-medium">Unit Price</th>
                        <th className="text-right py-2 font-medium">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {offer.items.map((item) => (
                        <tr key={item.id} className="border-b">
                          <td className="py-2">{item.description}</td>
                          <td className="py-2 text-right">{item.quantity}</td>
                          <td className="py-2 text-right">{formatCurrency(item.unitPrice)}</td>
                          <td className="py-2 text-right">{formatCurrency(item.totalPrice)}</td>
                        </tr>
                      ))}
                      <tr className="font-medium">
                        <td colSpan={3} className="py-2 text-right">Total:</td>
                        <td className="py-2 text-right">{formatCurrency(offer.totalAmount)}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
          
          {offer.variants.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Offer Variants</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {offer.variants.map((variant) => (
                    <div key={variant.id} className="border rounded-md p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium">{variant.name}</h3>
                        {variant.isSelected && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Selected</span>
                        )}
                      </div>
                      {variant.description && <p className="text-sm mb-2">{variant.description}</p>}
                      <p className="font-medium">Total: {formatCurrency(variant.totalAmount)}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
          
          {offer.serviceOrders.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Related Service Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {offer.serviceOrders.map((serviceOrder) => (
                    <li key={serviceOrder.id} className="flex justify-between items-center">
                      <Link to={`/service-orders/${serviceOrder.id}`} className="hover:underline">
                        {serviceOrder.title}
                      </Link>
                      <span className="text-sm text-muted-foreground">
                        {formatDate(new Date(serviceOrder.createdAt))}
                      </span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Name</h3>
                  <p className="mt-1">
                    <Link to={`/customers/${offer.customer.id}`} className="hover:underline">
                      {offer.customer.name}
                    </Link>
                  </p>
                </div>
                
                {offer.customer.email && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
                    <p className="mt-1">{offer.customer.email}</p>
                  </div>
                )}
                
                {offer.customer.phone && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Phone</h3>
                    <p className="mt-1">{offer.customer.phone}</p>
                  </div>
                )}
                
                {offer.customer.address && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Address</h3>
                    <p className="mt-1">{offer.customer.address}</p>
                    <p className="mt-1">
                      {offer.customer.city}
                      {offer.customer.postalCode && `, ${offer.customer.postalCode}`}
                      {offer.customer.country && `, ${offer.customer.country}`}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Offer Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Created</span>
                  <span className="text-sm">{formatDate(new Date(offer.createdAt))}</span>
                </div>
                
                {offer.sentAt && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Sent</span>
                    <span className="text-sm">{formatDate(new Date(offer.sentAt))}</span>
                  </div>
                )}
                
                {offer.viewedAt && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Viewed</span>
                    <span className="text-sm">{formatDate(new Date(offer.viewedAt))}</span>
                  </div>
                )}
                
                {offer.respondedAt && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Responded</span>
                    <span className="text-sm">{formatDate(new Date(offer.respondedAt))}</span>
                  </div>
                )}
                
                {offer.updatedAt && offer.updatedAt !== offer.createdAt && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Last Updated</span>
                    <span className="text-sm">{formatDate(new Date(offer.updatedAt))}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}