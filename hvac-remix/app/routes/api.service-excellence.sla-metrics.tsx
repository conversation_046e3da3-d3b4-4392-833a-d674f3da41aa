/**
 * 🎯 SERVICE EXCELLENCE SLA METRICS API
 * 
 * Provides comprehensive SLA tracking and compliance metrics
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { ServiceExcellenceService } from "~/models/service-excellence.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const timeRange = url.searchParams.get('timeRange') || 'month';

  try {
    // Calculate date range
    const now = new Date();
    let startDate: Date;

    switch (timeRange) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'quarter':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default: // month
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get service orders for the time period
    const serviceOrders = await prisma.serviceOrder.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: now
        }
      },
      include: {
        customer: true
      },
      orderBy: { createdAt: 'desc' }
    });

    const totalServiceOrders = serviceOrders.length;
    let slaCompliantOrders = 0;
    let totalResponseTime = 0;
    let totalResolutionTime = 0;
    let criticalSLABreaches = 0;
    let responseTimeCount = 0;
    let resolutionTimeCount = 0;

    // Analyze each service order for SLA compliance
    for (const serviceOrder of serviceOrders) {
      try {
        const slaCompliance = await ServiceExcellenceService.checkSLACompliance(serviceOrder.id);
        
        if (slaCompliance.isCompliant) {
          slaCompliantOrders++;
        }

        // Track response times
        if (slaCompliance.details.responseTimeActual > 0) {
          totalResponseTime += slaCompliance.details.responseTimeActual;
          responseTimeCount++;
        }

        // Track resolution times
        if (slaCompliance.details.resolutionTimeActual) {
          totalResolutionTime += slaCompliance.details.resolutionTimeActual;
          resolutionTimeCount++;
        }

        // Count critical breaches (urgent/high priority orders that missed SLA)
        if (!slaCompliance.isCompliant && 
            (serviceOrder.priority === 'URGENT' || serviceOrder.priority === 'HIGH')) {
          criticalSLABreaches++;
        }

      } catch (error) {
        console.error(`Error checking SLA compliance for order ${serviceOrder.id}:`, error);
        // Continue processing other orders
      }
    }

    // Calculate averages
    const complianceRate = totalServiceOrders > 0 ? 
      Math.round((slaCompliantOrders / totalServiceOrders) * 100) : 100;

    const averageResponseTime = responseTimeCount > 0 ? 
      Math.round((totalResponseTime / responseTimeCount) * 100) / 100 : 0;

    const averageResolutionTime = resolutionTimeCount > 0 ? 
      Math.round((totalResolutionTime / resolutionTimeCount) * 100) / 100 : 0;

    const slaMetrics = {
      totalServiceOrders,
      slaCompliantOrders,
      complianceRate,
      averageResponseTime,
      averageResolutionTime,
      criticalSLABreaches,
      timeRange,
      periodStart: startDate.toISOString(),
      periodEnd: now.toISOString()
    };

    return json(slaMetrics);

  } catch (error) {
    console.error('Error fetching SLA metrics:', error);
    return json(
      { error: 'Failed to fetch SLA metrics' },
      { status: 500 }
    );
  }
}

// Handle POST requests for SLA-related actions
export async function action({ request }: LoaderFunctionArgs) {
  if (request.method !== 'POST') {
    return json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { action: actionType, data } = body;

    switch (actionType) {
      case 'check_sla_compliance':
        if (!data.serviceOrderId) {
          return json({ error: 'Service order ID required' }, { status: 400 });
        }
        
        const compliance = await ServiceExcellenceService.checkSLACompliance(data.serviceOrderId);
        return json({ compliance });

      case 'bulk_sla_check':
        // Check SLA compliance for multiple orders
        const { serviceOrderIds } = data;
        if (!Array.isArray(serviceOrderIds)) {
          return json({ error: 'Service order IDs array required' }, { status: 400 });
        }

        const complianceResults = await Promise.all(
          serviceOrderIds.map(async (id: string) => {
            try {
              const compliance = await ServiceExcellenceService.checkSLACompliance(id);
              return { serviceOrderId: id, compliance };
            } catch (error) {
              return { serviceOrderId: id, error: 'Failed to check compliance' };
            }
          })
        );

        return json({ results: complianceResults });

      case 'update_sla_targets':
        // Update SLA targets (would need to store in database)
        const { targets } = data;
        
        // For now, just return success - in production, would store in database
        return json({ 
          message: 'SLA targets updated',
          targets 
        });

      case 'generate_sla_report':
        // Generate detailed SLA report
        const { timeRange: reportTimeRange = 'month' } = data;
        
        // This would generate a comprehensive report
        // For now, return a placeholder
        return json({
          message: 'SLA report generated',
          reportId: `sla-report-${Date.now()}`,
          timeRange: reportTimeRange
        });

      default:
        return json({ error: 'Unknown action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Error processing SLA action:', error);
    return json(
      { error: 'Failed to process action' },
      { status: 500 }
    );
  }
}
