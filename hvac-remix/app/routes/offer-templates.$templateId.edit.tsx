import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { ArrowLeft } from "lucide-react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Checkbox } from "~/components/ui/checkbox";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { getOfferTemplate, updateOfferTemplate } from "~/services/offer-template.service";
import { requireUserId } from "~/session.server";

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  await requireUserId(request);
  const { templateId } = params;
  
  if (!templateId) {
    throw new Response("Template ID is required", { status: 400 });
  }
  
  const template = await getOfferTemplate(templateId);
  
  if (!template) {
    throw new Response("Template not found", { status: 404 });
  }
  
  return json({ template });
};

export const action = async ({ params, request }: ActionFunctionArgs) => {
  await requireUserId(request);
  const { templateId } = params;
  
  if (!templateId) {
    throw new Response("Template ID is required", { status: 400 });
  }
  
  const formData = await request.formData();
  
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  const content = formData.get("content") as string;
  const isDefault = formData.get("isDefault") === "on";
  
  const errors: Record<string, string> = {};
  
  if (!name) errors.name = "Name is required";
  if (!content) errors.content = "Content is required";
  
  try {
    // Validate that content is valid JSON
    JSON.parse(content);
  } catch (error) {
    errors.content = "Content must be valid JSON";
  }
  
  if (Object.keys(errors).length > 0) {
    return json({ errors });
  }
  
  await updateOfferTemplate({
    id: templateId,
    name,
    description,
    content,
    isDefault,
  });
  
  return redirect(`/offer-templates/${templateId}`);
};

export default function EditOfferTemplatePage() {
  const { template } = useLoaderData<typeof loader>();
  const actionData = useActionData<{ errors?: Record<string, string> }>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Link to={`/offer-templates/${template.id}`}>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Template
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Edit Template</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Template Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form method="post" className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  name="name"
                  defaultValue={template.name}
                  required
                  aria-invalid={actionData?.errors?.name ? true : undefined}
                  aria-errormessage={actionData?.errors?.name ? "name-error" : undefined}
                />
                {actionData?.errors?.name && (
                  <div className="text-destructive text-sm pt-1" id="name-error">
                    {actionData.errors.name}
                  </div>
                )}
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  defaultValue={template.description || ""}
                  rows={3}
                />
              </div>
              
              <div>
                <Label htmlFor="content">Template Content (JSON)</Label>
                <Textarea
                  id="content"
                  name="content"
                  defaultValue={template.content}
                  rows={15}
                  required
                  aria-invalid={actionData?.errors?.content ? true : undefined}
                  aria-errormessage={actionData?.errors?.content ? "content-error" : undefined}
                />
                {actionData?.errors?.content && (
                  <div className="text-destructive text-sm pt-1" id="content-error">
                    {actionData.errors.content}
                  </div>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Enter the template content in JSON format. This will be used to generate offers.
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isDefault"
                  name="isDefault"
                  defaultChecked={template.isDefault}
                />
                <Label htmlFor="isDefault" className="cursor-pointer">
                  Set as default template
                </Label>
              </div>
            </div>
            
            <div className="flex justify-end gap-2">
              <Link to={`/offer-templates/${template.id}`}>
                <Button variant="outline" type="button" disabled={isSubmitting}>
                  Cancel
                </Button>
              </Link>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}