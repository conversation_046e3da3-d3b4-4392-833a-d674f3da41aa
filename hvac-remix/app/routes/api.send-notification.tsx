import { json, type ActionFunctionArgs } from "@remix-run/node";
import { sendMultiChannelNotification } from "~/services/notification.server";
import { requireUser } from "~/session.server";
import type { NotificationType, NotificationPriority } from "~/types/shared";

/**
 * API endpoint for sending notifications
 * 
 * This endpoint allows sending notifications through multiple channels
 * based on user preferences.
 * 
 * Required permissions: Depends on the notification type
 */
export async function action({ request }: ActionFunctionArgs) {
  // Require authenticated user
  const user = await requireUser(request);
  
  // Only allow POST requests
  if (request.method !== "POST") {
    return json({ success: false, message: "Method not allowed" }, { status: 405 });
  }
  
  try {
    // Parse request body
    const formData = await request.formData();
    const body = Object.fromEntries(formData);
    
    // Validate required fields
    if (!body.type || !body.title || !body.message || !body.userId) {
      return json(
        { 
          success: false, 
          message: "Missing required fields: type, title, message, userId" 
        }, 
        { status: 400 }
      );
    }
    
    // Check permissions based on notification type
    const type = body.type as NotificationType;
    const targetUserId = body.userId as string;
    
    // Only allow sending notifications to self or if user is admin/manager
    const isAdminOrManager = user.role === 'ADMIN' || user.role === 'MANAGER';
    const isSelf = user.id === targetUserId;
    
    if (!isAdminOrManager && !isSelf) {
      return json(
        { 
          success: false, 
          message: "You don't have permission to send notifications to other users" 
        }, 
        { status: 403 }
      );
    }
    
    // Prepare notification data
    const notificationData: any = {
      type,
      title: body.title as string,
      message: body.message as string,
      userId: targetUserId,
      priority: (body.priority as NotificationPriority) || 'medium',
    };
    
    // Add optional fields if provided
    if (body.link) notificationData.link = body.link as string;
    if (body.serviceOrderId) notificationData.serviceOrderId = body.serviceOrderId as string;
    if (body.deviceId) notificationData.deviceId = body.deviceId as string;
    if (body.deviceName) notificationData.deviceName = body.deviceName as string;
    if (body.eventId) notificationData.eventId = body.eventId as string;
    
    // Parse date fields if provided
    if (body.eventDate) {
      try {
        notificationData.eventDate = new Date(body.eventDate as string);
      } catch (error) {
        return json(
          { 
            success: false, 
            message: "Invalid eventDate format. Use ISO format (YYYY-MM-DDTHH:MM:SS.sssZ)" 
          }, 
          { status: 400 }
        );
      }
    }
    
    if (body.location) notificationData.location = body.location as string;
    
    // Send notification
    const notification = await sendMultiChannelNotification(notificationData);
    
    if (!notification) {
      return json(
        { 
          success: false, 
          message: "Failed to send notification. User may have disabled notifications of this type." 
        }, 
        { status: 400 }
      );
    }
    
    return json({ 
      success: true, 
      message: "Notification sent successfully", 
      notification 
    });
  } catch (error) {
    console.error("Error sending notification:", error);
    return json(
      { 
        success: false, 
        message: "An error occurred while sending the notification" 
      }, 
      { status: 500 }
    );
  }
}
