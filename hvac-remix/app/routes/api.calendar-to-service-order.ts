import { json } from "@remix-run/node";
import type { ActionFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { 
  createServiceOrderFromCalendarEntry, 
  processUnlinkedCalendarEntries 
} from "~/services/auto-service-order.server";
import { requireUserId } from "~/session.server";

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  
  // Check if user has admin role
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { role: true }
  });
  
  if (!user || user.role !== "ADMIN") {
    return json({ success: false, message: "Unauthorized" }, { status: 403 });
  }
  
  const formData = await request.formData();
  const action = formData.get("action");
  
  // Process a single calendar entry
  if (action === "process-single") {
    const calendarEntryId = formData.get("calendarEntryId");
    
    if (!calendarEntryId || typeof calendarEntryId !== "string") {
      return json({ success: false, message: "Calendar entry ID is required" }, { status: 400 });
    }
    
    // Get the calendar entry
    const calendarEntry = await prisma.calendarEntry.findUnique({
      where: { id: calendarEntryId }
    });
    
    if (!calendarEntry) {
      return json({ success: false, message: "Calendar entry not found" }, { status: 404 });
    }
    
    // Create service order from calendar entry
    const result = await createServiceOrderFromCalendarEntry(calendarEntry);
    
    return json(result);
  }
  
  // Process all unlinked calendar entries
  if (action === "process-all") {
    const result = await processUnlinkedCalendarEntries();
    
    return json(result);
  }
  
  return json({ success: false, message: "Invalid action" }, { status: 400 });
}