/**
 * 📄 DOCUMENT MANAGEMENT ANALYTICS API
 * 
 * Provides document analytics and management capabilities
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { DocumentManagementService } from "~/models/document-management.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get('customerId');

  try {
    // Get document analytics
    const analytics = await DocumentManagementService.getDocumentAnalytics(customerId || undefined);

    return json(analytics);

  } catch (error) {
    console.error('Error fetching document analytics:', error);
    return json(
      { error: 'Failed to fetch document analytics' },
      { status: 500 }
    );
  }
}

// Handle POST requests for document actions
export async function action({ request }: LoaderFunctionArgs) {
  if (request.method !== 'POST') {
    return json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { action: actionType, data } = body;

    switch (actionType) {
      case 'upload_document':
        // Upload document (in production, handle file upload)
        const document = await DocumentManagementService.uploadDocument(
          {
            customerId: data.customerId,
            equipmentId: data.equipmentId,
            serviceOrderId: data.serviceOrderId,
            type: data.type,
            title: data.title,
            description: data.description,
            fileName: data.fileName,
            filePath: '', // Will be set by upload service
            fileSize: data.fileSize,
            mimeType: data.mimeType,
            uploadedBy: data.uploadedBy,
            isPublic: data.isPublic || false,
            tags: data.tags || [],
            metadata: data.metadata || {}
          },
          data.file // File object
        );

        return json({ success: true, document });

      case 'search_documents':
        // Search documents
        const searchResults = await DocumentManagementService.searchDocuments(
          data.query,
          {
            customerId: data.customerId,
            type: data.type,
            category: data.category,
            dateRange: data.dateRange
          }
        );

        return json({ success: true, results: searchResults });

      case 'share_document':
        // Share document
        const share = await DocumentManagementService.shareDocument(
          data.documentId,
          {
            sharedBy: data.sharedBy,
            sharedWith: data.sharedWith,
            accessLevel: data.accessLevel,
            expirationDate: data.expirationDate ? new Date(data.expirationDate) : undefined,
            password: data.password,
            maxDownloads: data.maxDownloads,
            isActive: true
          }
        );

        return json({ success: true, share });

      case 'generate_from_template':
        // Generate document from template
        const generatedDocument = await DocumentManagementService.generateDocumentFromTemplate(
          data.templateId,
          data.templateData,
          data.customerId
        );

        return json({ success: true, document: generatedDocument });

      default:
        return json({ error: 'Unknown action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Error processing document action:', error);
    return json(
      { error: 'Failed to process action' },
      { status: 500 }
    );
  }
}
