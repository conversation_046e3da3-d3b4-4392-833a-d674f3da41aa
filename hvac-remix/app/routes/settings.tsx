import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, Form, useActionData, Link } from "@remix-run/react";
import { But<PERSON> } from "~/components/ui/button";
import { Card } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { getUserSettings, upsertUserSettings, convertToAppSettings } from "~/models/user-settings.server";
import { requireUser } from "~/session.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await requireUser(request);

  // Pobierz ustawienia użytkownika lub użyj do<PERSON>lnych
  const dbSettings = await getUserSettings(user.id);

  let userSettings;

  if (dbSettings) {
    userSettings = convertToAppSettings(dbSettings);
  } else {
    // Domyślne ustawienia
    userSettings = {
      theme: {
        theme: 'system',
        fontSize: 'medium',
        colorScheme: 'default'
      },
      dashboard: {
        showStats: true,
        showRecentOrders: true,
        showUpcomingEvents: true,
        showQuickActions: true,
        statsToShow: ['serviceOrders', 'customers', 'devices', 'revenue']
      },
      notifications: {
        email: true,
        inApp: true,
        push: false,
        serviceOrderUpdates: true,
        calendarReminders: true,
        systemUpdates: true
      }
    };
  }

  return json({
    user,
    settings: userSettings
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const user = await requireUser(request);
  const formData = await request.formData();

  const settingsType = formData.get("settingsType") as string;

  try {
    switch (settingsType) {
      case "theme": {
        const theme = formData.get("theme") as string;
        const fontSize = formData.get("fontSize") as string;
        const colorScheme = formData.get("colorScheme") as string;

        await upsertUserSettings(user.id, {
          theme,
          fontSize,
          colorScheme
        });

        break;
      }
      case "dashboard": {
        const showStats = formData.get("showStats") === "on";
        const showRecentOrders = formData.get("showRecentOrders") === "on";
        const showUpcomingEvents = formData.get("showUpcomingEvents") === "on";
        const showQuickActions = formData.get("showQuickActions") === "on";

        // Pobierz wybrane statystyki
        const statsToShow = [];
        if (formData.get("statsServiceOrders") === "on") statsToShow.push("serviceOrders");
        if (formData.get("statsCustomers") === "on") statsToShow.push("customers");
        if (formData.get("statsDevices") === "on") statsToShow.push("devices");
        if (formData.get("statsRevenue") === "on") statsToShow.push("revenue");

        await upsertUserSettings(user.id, {
          showStats,
          showRecentOrders,
          showUpcomingEvents,
          showQuickActions,
          statsToShow: JSON.stringify(statsToShow)
        });

        break;
      }
      case "notifications": {
        const emailNotifications = formData.get("emailNotifications") === "on";
        const inAppNotifications = formData.get("inAppNotifications") === "on";
        const pushNotifications = formData.get("pushNotifications") === "on";
        const serviceOrderUpdates = formData.get("serviceOrderUpdates") === "on";
        const calendarReminders = formData.get("calendarReminders") === "on";
        const systemUpdates = formData.get("systemUpdates") === "on";

        await upsertUserSettings(user.id, {
          emailNotifications,
          inAppNotifications,
          pushNotifications,
          serviceOrderUpdates,
          calendarReminders,
          systemUpdates
        });

        break;
      }
    }

    return json({ success: true, message: "Ustawienia zostały zaktualizowane" });
  } catch (error) {
    console.error("Błąd podczas aktualizacji ustawień:", error);
    return json({ success: false, message: "Wystąpił błąd podczas aktualizacji ustawień" }, { status: 400 });
  }
};

export default function SettingsPage() {
  const { user, settings } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Ustawienia</h1>

      {actionData?.message && (
        <div className={`p-4 mb-6 rounded-md ${actionData.success ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}`}>
          {actionData.message}
        </div>
      )}

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="profile">Profil</TabsTrigger>
          <TabsTrigger value="appearance">Wygląd</TabsTrigger>
          <TabsTrigger value="notifications">Powiadomienia</TabsTrigger>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="offline">Tryb offline</TabsTrigger>
          <TabsTrigger value="company">Firma</TabsTrigger>
          <TabsTrigger value="security">Bezpieczeństwo</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Profil użytkownika</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Zarządzaj swoimi danymi osobowymi i preferencjami konta.
            </p>

            <div className="space-y-4">
              <div>
                <p><strong>Email:</strong> {user.email}</p>
              </div>
              <div>
                <p><strong>Rola:</strong> {user.role}</p>
              </div>
              <div>
                <p><strong>Ostatnie logowanie:</strong> {user.lastLogin ? new Date(user.lastLogin).toLocaleString() : 'Brak danych'}</p>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="appearance">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Wygląd</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Dostosuj wygląd interfejsu do swoich preferencji.
            </p>

            <Form method="post" className="space-y-6">
              <input type="hidden" name="settingsType" value="theme" />

              <div className="space-y-4">
                <div>
                  <Label htmlFor="theme">Motyw</Label>
                  <Select name="theme" defaultValue={settings.theme.theme}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Wybierz motyw" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Jasny</SelectItem>
                      <SelectItem value="dark">Ciemny</SelectItem>
                      <SelectItem value="system">Systemowy</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="fontSize">Rozmiar czcionki</Label>
                  <Select name="fontSize" defaultValue={settings.theme.fontSize}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Wybierz rozmiar czcionki" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">Mały</SelectItem>
                      <SelectItem value="medium">Średni</SelectItem>
                      <SelectItem value="large">Duży</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="colorScheme">Schemat kolorów</Label>
                  <Select name="colorScheme" defaultValue={settings.theme.colorScheme}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Wybierz schemat kolorów" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Domyślny</SelectItem>
                      <SelectItem value="blue">Niebieski</SelectItem>
                      <SelectItem value="green">Zielony</SelectItem>
                      <SelectItem value="purple">Fioletowy</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button type="submit">Zapisz ustawienia wyglądu</Button>
            </Form>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Powiadomienia</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Zarządzaj preferencjami powiadomień.
            </p>

            <Form method="post" className="space-y-6">
              <input type="hidden" name="settingsType" value="notifications" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Kanały powiadomień</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="emailNotifications">Powiadomienia email</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia na swój adres email
                    </p>
                  </div>
                  <Switch
                    name="emailNotifications"
                    id="emailNotifications"
                    defaultChecked={settings.notifications.email}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="inAppNotifications">Powiadomienia w aplikacji</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia w aplikacji
                    </p>
                  </div>
                  <Switch
                    name="inAppNotifications"
                    id="inAppNotifications"
                    defaultChecked={settings.notifications.inApp}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="pushNotifications">Powiadomienia push</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Otrzymuj powiadomienia push na urządzeniu mobilnym
                    </p>
                  </div>
                  <Switch
                    name="pushNotifications"
                    id="pushNotifications"
                    defaultChecked={settings.notifications.push}
                  />
                </div>

                <h3 className="text-lg font-medium mt-6">Typy powiadomień</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="serviceOrderUpdates">Aktualizacje zleceń serwisowych</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Powiadomienia o nowych i zaktualizowanych zleceniach
                    </p>
                  </div>
                  <Switch
                    name="serviceOrderUpdates"
                    id="serviceOrderUpdates"
                    defaultChecked={settings.notifications.serviceOrderUpdates}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="calendarReminders">Przypomnienia kalendarza</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Przypomnienia o nadchodzących wydarzeniach
                    </p>
                  </div>
                  <Switch
                    name="calendarReminders"
                    id="calendarReminders"
                    defaultChecked={settings.notifications.calendarReminders}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="systemUpdates">Aktualizacje systemowe</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Powiadomienia o aktualizacjach i zmianach w systemie
                    </p>
                  </div>
                  <Switch
                    name="systemUpdates"
                    id="systemUpdates"
                    defaultChecked={settings.notifications.systemUpdates}
                  />
                </div>
              </div>

              <Button type="submit">Zapisz ustawienia powiadomień</Button>
            </Form>
          </Card>
        </TabsContent>

        <TabsContent value="dashboard">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Dashboard</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Dostosuj swój dashboard i widżety.
            </p>

            <Form method="post" className="space-y-6">
              <input type="hidden" name="settingsType" value="dashboard" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Widżety</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="showStats">Statystyki</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Pokaż karty statystyk na dashboardzie
                    </p>
                  </div>
                  <Switch
                    name="showStats"
                    id="showStats"
                    defaultChecked={settings.dashboard.showStats}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="showRecentOrders">Ostatnie zlecenia</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Pokaż ostatnie zlecenia serwisowe
                    </p>
                  </div>
                  <Switch
                    name="showRecentOrders"
                    id="showRecentOrders"
                    defaultChecked={settings.dashboard.showRecentOrders}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="showUpcomingEvents">Nadchodzące wydarzenia</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Pokaż nadchodzące wydarzenia z kalendarza
                    </p>
                  </div>
                  <Switch
                    name="showUpcomingEvents"
                    id="showUpcomingEvents"
                    defaultChecked={settings.dashboard.showUpcomingEvents}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="showQuickActions">Szybkie akcje</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Pokaż przyciski szybkich akcji
                    </p>
                  </div>
                  <Switch
                    name="showQuickActions"
                    id="showQuickActions"
                    defaultChecked={settings.dashboard.showQuickActions}
                  />
                </div>

                <h3 className="text-lg font-medium mt-6">Statystyki do wyświetlenia</h3>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="statsServiceOrders"
                      name="statsServiceOrders"
                      defaultChecked={settings.dashboard.statsToShow.includes('serviceOrders')}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="statsServiceOrders">Zlecenia serwisowe</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="statsCustomers"
                      name="statsCustomers"
                      defaultChecked={settings.dashboard.statsToShow.includes('customers')}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="statsCustomers">Klienci</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="statsDevices"
                      name="statsDevices"
                      defaultChecked={settings.dashboard.statsToShow.includes('devices')}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="statsDevices">Urządzenia</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="statsRevenue"
                      name="statsRevenue"
                      defaultChecked={settings.dashboard.statsToShow.includes('revenue')}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="statsRevenue">Przychód</Label>
                  </div>
                </div>
              </div>

              <Button type="submit">Zapisz ustawienia dashboardu</Button>
            </Form>
          </Card>
        </TabsContent>

        <TabsContent value="offline">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Tryb offline</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Zarządzaj danymi offline i synchronizacją.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div className="border rounded-lg p-6 hover:border-primary transition-colors">
                <h3 className="text-lg font-medium mb-2">Status synchronizacji</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Sprawdź status synchronizacji danych i synchronizuj dane offline.
                </p>
                <Button asChild className="w-full">
                  <Link to="/settings/sync-status">Przejdź do synchronizacji</Link>
                </Button>
              </div>

              <div className="border rounded-lg p-6 hover:border-primary transition-colors">
                <h3 className="text-lg font-medium mb-2">Dane offline</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Przeglądaj i zarządzaj danymi przechowywanymi offline.
                </p>
                <Button asChild className="w-full">
                  <Link to="/settings/offline-data">Zarządzaj danymi offline</Link>
                </Button>
              </div>

              <div className="border rounded-lg p-6 hover:border-primary transition-colors">
                <h3 className="text-lg font-medium mb-2">Konflikty danych</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Rozwiązuj konflikty między danymi lokalnymi a serwerowymi.
                </p>
                <Button asChild className="w-full">
                  <Link to="/settings/sync-conflicts">Rozwiąż konflikty</Link>
                </Button>
              </div>

              <div className="border rounded-lg p-6 hover:border-primary transition-colors">
                <h3 className="text-lg font-medium mb-2">Instalacja PWA</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Zainstaluj aplikację na urządzeniu, aby korzystać z niej offline.
                </p>
                <Button
                  className="w-full"
                  onClick={() => {
                    // Show PWA installation prompt if available
                    if (window.deferredPrompt) {
                      window.deferredPrompt.prompt();
                    } else {
                      alert('Instalacja PWA nie jest obecnie dostępna. Upewnij się, że używasz obsługiwanej przeglądarki.');
                    }
                  }}
                >
                  Zainstaluj aplikację
                </Button>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="company">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Ustawienia firmy</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Zarządzaj informacjami o firmie, które będą wyświetlane na fakturach, raportach serwisowych i innych dokumentach.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div className="border rounded-lg p-6 hover:border-primary transition-colors">
                <h3 className="text-lg font-medium mb-2">Informacje o firmie</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Skonfiguruj dane firmy, logo, kolory i inne ustawienia.
                </p>
                <Button asChild className="w-full">
                  <Link to="/settings/company">Edytuj informacje o firmie</Link>
                </Button>
              </div>

              <div className="border rounded-lg p-6 hover:border-primary transition-colors">
                <h3 className="text-lg font-medium mb-2">Podgląd dokumentów</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Zobacz, jak będą wyglądać faktury i raporty serwisowe z aktualnymi ustawieniami.
                </p>
                <div className="flex space-x-2">
                  <Button asChild className="flex-1">
                    <Link to="/settings/preview-invoice">Podgląd faktury</Link>
                  </Button>
                  <Button asChild className="flex-1">
                    <Link to="/settings/preview-report">Podgląd raportu</Link>
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Bezpieczeństwo</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Zarządzaj ustawieniami bezpieczeństwa swojego konta.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div className="border rounded-lg p-6 hover:border-primary transition-colors">
                <h3 className="text-lg font-medium mb-2">Uwierzytelnianie dwuskładnikowe (2FA)</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Dodaj dodatkową warstwę zabezpieczeń do swojego konta.
                </p>
                <div className="flex items-center justify-between mb-4">
                  <span>Status:</span>
                  <span className={`px-2 py-1 rounded text-sm ${user.mfaEnabled ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'}`}>
                    {user.mfaEnabled ? 'Włączone' : 'Wyłączone'}
                  </span>
                </div>
                <Button asChild className="w-full">
                  <Link to="/settings/mfa">
                    {user.mfaEnabled ? 'Zarządzaj 2FA' : 'Włącz 2FA'}
                  </Link>
                </Button>
              </div>

              <div className="border rounded-lg p-6 hover:border-primary transition-colors">
                <h3 className="text-lg font-medium mb-2">Zmiana hasła</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Zmień swoje hasło, aby zwiększyć bezpieczeństwo konta.
                </p>
                <Button asChild className="w-full">
                  <Link to="/settings/change-password">Zmień hasło</Link>
                </Button>
              </div>

              <div className="border rounded-lg p-6 hover:border-primary transition-colors">
                <h3 className="text-lg font-medium mb-2">Historia logowań</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Przeglądaj historię logowań do swojego konta.
                </p>
                <Button asChild className="w-full">
                  <Link to="/settings/login-history">Zobacz historię</Link>
                </Button>
              </div>

              <div className="border rounded-lg p-6 hover:border-primary transition-colors">
                <h3 className="text-lg font-medium mb-2">Sesje aktywne</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Zarządzaj aktywnymi sesjami i wyloguj się z innych urządzeń.
                </p>
                <Button asChild className="w-full">
                  <Link to="/settings/active-sessions">Zarządzaj sesjami</Link>
                </Button>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}