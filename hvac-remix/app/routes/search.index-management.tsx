import { json, type ActionFunctionArgs } from "@remix-run/node";
import { useActionData, Form } from "@remix-run/react";
import { CheckCircle, AlertCircle, RefreshCw } from "lucide-react";
import { useState } from "react";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { requireUserId } from "~/session.server";
import { reindexAllEntities } from "~/utils/indexing.server";

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  
  const formData = await request.formData();
  const entityType = formData.get("entityType") as string;
  const userScope = formData.get("userScope") as string;
  
  if (!entityType) {
    return json({ success: false, error: "Entity type is required" });
  }
  
  // Validate entity type
  const validEntityTypes = ['customer', 'device', 'serviceOrder', 'note'];
  if (!validEntityTypes.includes(entityType)) {
    return json({ 
      success: false, 
      error: `Invalid entity type. Must be one of: ${validEntityTypes.join(', ')}` 
    });
  }
  
  // Determine if we should reindex only the user's entities or all entities
  const scopedUserId = userScope === 'own' ? userId : undefined;
  
  // Perform the reindexing
  const result = await reindexAllEntities(entityType, scopedUserId);
  
  return json(result);
}

export default function IndexManagementPage() {
  const actionData = useActionData<typeof action>();
  const [entityType, setEntityType] = useState("customer");
  const [userScope, setUserScope] = useState("own");
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleSubmit = (e: React.FormEvent) => {
    setIsSubmitting(true);
    // Form will be submitted normally, and the action function will handle it
  };
  
  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Zarządzanie indeksem wyszukiwania</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Przeindeksuj encje</CardTitle>
            <CardDescription>
              Przeindeksuj wszystkie encje danego typu, aby zaktualizować indeks wyszukiwania semantycznego.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form method="post" onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="entityType">Typ encji</Label>
                <Select
                  name="entityType"
                  value={entityType}
                  onValueChange={setEntityType}
                >
                  <SelectTrigger id="entityType">
                    <SelectValue placeholder="Wybierz typ encji" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="customer">Klienci</SelectItem>
                    <SelectItem value="device">Urządzenia</SelectItem>
                    <SelectItem value="serviceOrder">Zlecenia serwisowe</SelectItem>
                    <SelectItem value="note">Notatki</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="userScope">Zakres</Label>
                <Select
                  name="userScope"
                  value={userScope}
                  onValueChange={setUserScope}
                >
                  <SelectTrigger id="userScope">
                    <SelectValue placeholder="Wybierz zakres" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="own">Tylko moje encje</SelectItem>
                    <SelectItem value="all">Wszystkie encje</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Button type="submit" disabled={isSubmitting} className="w-full">
                {isSubmitting ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Przeindeksowanie...
                  </>
                ) : (
                  "Przeindeksuj"
                )}
              </Button>
            </Form>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Status indeksu</CardTitle>
            <CardDescription>
              Informacje o ostatniej operacji przeindeksowania.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {actionData ? (
              <div className="space-y-4">
                {actionData.success ? (
                  <Alert variant="success" className="bg-green-50 border-green-200">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertTitle>Sukces</AlertTitle>
                    <AlertDescription>
                      Przeindeksowano {actionData.indexedCount} z {actionData.totalCount} encji typu {actionData.entityType}.
                      {actionData.failedCount > 0 && (
                        <p className="mt-2 text-amber-600">
                          Nie udało się przeindeksować {actionData.failedCount} encji.
                        </p>
                      )}
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Błąd</AlertTitle>
                    <AlertDescription>
                      {actionData.error || "Wystąpił nieznany błąd podczas przeindeksowania."}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Brak danych o ostatniej operacji przeindeksowania.</p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <div className="text-sm text-gray-500">
              <p className="font-medium">Wskazówki:</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Przeindeksowanie jest wymagane po masowych zmianach w danych.</li>
                <li>Nowe encje są indeksowane automatycznie.</li>
                <li>Przeindeksowanie dużej liczby encji może zająć dłuższą chwilę.</li>
              </ul>
            </div>
          </CardFooter>
        </Card>
      </div>
      
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>O wyszukiwaniu semantycznym</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p>
                Wyszukiwanie semantyczne pozwala na znalezienie wyników na podstawie znaczenia, a nie tylko dokładnego dopasowania tekstu.
                Wykorzystuje ono model językowy Bielik do generowania wektorów embedingowych, które reprezentują znaczenie tekstu.
              </p>
              
              <p>
                Dzięki temu możliwe jest wyszukiwanie encji na podstawie kontekstu i znaczenia, nawet jeśli dokładne słowa nie występują w tekście.
                Na przykład, wyszukując "problemy z ogrzewaniem", możesz znaleźć zlecenia serwisowe dotyczące "awarii kotła" lub "niesprawnego grzejnika".
              </p>
              
              <h3 className="text-lg font-medium mt-4">Jak to działa?</h3>
              <ol className="list-decimal list-inside space-y-2">
                <li>Każda encja (klient, urządzenie, zlecenie serwisowe, notatka) jest indeksowana w bazie wektorowej Qdrant.</li>
                <li>Podczas indeksowania, tekst encji jest przekształcany na wektor embedingowy przy użyciu modelu Bielik.</li>
                <li>Podczas wyszukiwania, zapytanie jest również przekształcane na wektor embedingowy.</li>
                <li>System znajduje encje, których wektory są najbardziej podobne do wektora zapytania.</li>
                <li>Wyniki są sortowane według stopnia podobieństwa (score).</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}