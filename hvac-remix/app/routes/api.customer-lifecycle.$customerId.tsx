/**
 * 🎯 CUSTOMER LIFECYCLE PROFILE API
 * 
 * Provides detailed customer lifecycle analytics for individual customers
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { 
  CustomerLifecycleService, 
  CustomerAnalyticsProfile,
  CommunicationChannel 
} from "~/models/customer-lifecycle.server";

export async function loader({ params }: LoaderFunctionArgs) {
  const { customerId } = params;

  if (!customerId) {
    return json({ error: 'Customer ID required' }, { status: 400 });
  }

  try {
    // Get customer with all related data
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        serviceOrders: {
          orderBy: { createdAt: 'desc' },
          include: {
            invoices: true
          }
        },
        invoices: {
          orderBy: { createdAt: 'desc' }
        },
        devices: {
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!customer) {
      return json({ error: 'Customer not found' }, { status: 404 });
    }

    // Calculate lifecycle metrics
    const lifecycleStage = await CustomerLifecycleService.determineLifecycleStage(customerId);
    const segment = await CustomerLifecycleService.segmentCustomer(customerId);
    const healthScore = await CustomerLifecycleService.calculateHealthScore(customerId);

    // Calculate financial metrics
    const paidInvoices = customer.invoices.filter(inv => inv.paymentStatus === 'PAID');
    const totalRevenue = paidInvoices.reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);
    const averageJobValue = customer.serviceOrders.length > 0 ? 
      totalRevenue / customer.serviceOrders.length : 0;

    // Calculate customer lifetime value (enhanced calculation)
    const monthsSinceFirstService = customer.serviceOrders.length > 0 ? 
      Math.max(1, Math.floor((Date.now() - customer.serviceOrders[customer.serviceOrders.length - 1].createdAt.getTime()) / (1000 * 60 * 60 * 24 * 30))) : 1;
    const monthlyRevenue = totalRevenue / monthsSinceFirstService;
    const projectedLifetimeMonths = 36; // 3 years average
    const customerLifetimeValue = monthlyRevenue * projectedLifetimeMonths;

    // Calculate churn risk
    const now = new Date();
    const threeMonthsAgo = new Date(now.getTime() - 3 * 30 * 24 * 60 * 60 * 1000);
    const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);
    
    const recentServices = customer.serviceOrders.filter(so => so.createdAt > threeMonthsAgo).length;
    const servicesLastSixMonths = customer.serviceOrders.filter(so => so.createdAt > sixMonthsAgo).length;
    
    let churnRisk = 0;
    if (recentServices === 0 && servicesLastSixMonths > 0) {
      churnRisk = 80; // High risk if no recent services but had services before
    } else if (recentServices === 0) {
      churnRisk = 95; // Very high risk if no services at all recently
    } else {
      churnRisk = Math.max(0, 50 - (recentServices * 10) - (healthScore.overallScore * 0.3));
    }

    // Determine loyalty tier
    let loyaltyTier: 'BRONZE' | 'SILVER' | 'GOLD' | 'PLATINUM' = 'BRONZE';
    if (totalRevenue >= 50000 && healthScore.overallScore >= 90) {
      loyaltyTier = 'PLATINUM';
    } else if (totalRevenue >= 25000 && healthScore.overallScore >= 80) {
      loyaltyTier = 'GOLD';
    } else if (totalRevenue >= 10000 && healthScore.overallScore >= 70) {
      loyaltyTier = 'SILVER';
    }

    // Calculate satisfaction score from ratings
    const ratedServices = customer.serviceOrders.filter(so => so.customerRating);
    const satisfactionScore = ratedServices.length > 0 ? 
      ratedServices.reduce((sum, so) => sum + (so.customerRating || 0), 0) / ratedServices.length : undefined;

    // Find service dates
    const completedServices = customer.serviceOrders.filter(so => so.status === 'COMPLETED');
    const lastServiceDate = completedServices.length > 0 ? 
      completedServices[0].completedDate : undefined;

    // Find next scheduled service
    const scheduledServices = customer.serviceOrders.filter(so => 
      so.status === 'SCHEDULED' && so.scheduledDate && so.scheduledDate > now
    );
    const nextScheduledService = scheduledServices.length > 0 ? 
      scheduledServices.sort((a, b) => a.scheduledDate!.getTime() - b.scheduledDate!.getTime())[0].scheduledDate : undefined;

    // Calculate maintenance compliance
    const scheduledMaintenanceServices = customer.serviceOrders.filter(so => 
      so.type === 'MAINTENANCE' && so.scheduledDate
    );
    const completedMaintenanceServices = scheduledMaintenanceServices.filter(so => 
      so.status === 'COMPLETED'
    );
    const maintenanceCompliance = scheduledMaintenanceServices.length > 0 ? 
      Math.round((completedMaintenanceServices.length / scheduledMaintenanceServices.length) * 100) : 100;

    // Determine communication preference (simplified)
    const communicationPreference = customer.email ? 
      CommunicationChannel.EMAIL : CommunicationChannel.PHONE;

    // Count referrals (would need a referral tracking system)
    const referralCount = 0; // Placeholder

    // Find preferred technician (most frequent)
    const technicianCounts = customer.serviceOrders.reduce((acc, so) => {
      if (so.assignedTechnicianId) {
        acc[so.assignedTechnicianId] = (acc[so.assignedTechnicianId] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);
    
    const preferredTechnicianId = Object.entries(technicianCounts).length > 0 ? 
      Object.entries(technicianCounts).sort(([,a], [,b]) => b - a)[0][0] : undefined;

    // Get preferred technician name
    let preferredTechnician: string | undefined;
    if (preferredTechnicianId) {
      const tech = await prisma.user.findUnique({
        where: { id: preferredTechnicianId },
        select: { name: true }
      });
      preferredTechnician = tech?.name || undefined;
    }

    // Calculate preferred service time (simplified)
    const preferredServiceTime = "9:00 AM - 12:00 PM"; // Placeholder

    // Last interaction date (most recent service order or invoice)
    const lastInteractionDate = Math.max(
      customer.serviceOrders.length > 0 ? customer.serviceOrders[0].createdAt.getTime() : 0,
      customer.invoices.length > 0 ? customer.invoices[0].createdAt.getTime() : 0
    );

    const customerProfile: CustomerAnalyticsProfile = {
      customerId: customer.id,
      lifecycleStage,
      segment,
      healthScore,
      totalRevenue,
      averageJobValue,
      lastServiceDate,
      nextScheduledService,
      customerLifetimeValue,
      acquisitionDate: customer.createdAt,
      churnRisk: Math.round(churnRisk),
      satisfactionScore,
      loyaltyTier,
      preferredTechnician,
      preferredServiceTime,
      communicationPreference,
      equipmentCount: customer.devices.length,
      maintenanceCompliance,
      referralCount,
      lastInteractionDate: lastInteractionDate > 0 ? new Date(lastInteractionDate) : undefined,
      updatedAt: new Date()
    };

    return json(customerProfile);

  } catch (error) {
    console.error('Error fetching customer lifecycle profile:', error);
    return json(
      { error: 'Failed to fetch customer profile' },
      { status: 500 }
    );
  }
}

// Handle POST requests for customer-specific actions
export async function action({ params, request }: LoaderFunctionArgs) {
  const { customerId } = params;

  if (!customerId) {
    return json({ error: 'Customer ID required' }, { status: 400 });
  }

  if (request.method !== 'POST') {
    return json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { action: actionType, data } = body;

    switch (actionType) {
      case 'update_communication_preference':
        await prisma.customer.update({
          where: { id: customerId },
          data: {
            // Would need to add communication preference field to schema
            notes: `Communication preference: ${data.preference}`
          }
        });
        return json({ success: true });

      case 'schedule_follow_up':
        // Create a follow-up service order or calendar entry
        const followUpOrder = await prisma.serviceOrder.create({
          data: {
            title: 'Follow-up Call',
            description: data.notes || 'Customer follow-up',
            status: 'PENDING',
            priority: 'MEDIUM',
            type: 'FOLLOW_UP',
            scheduledDate: new Date(data.scheduledDate),
            customerId,
            userId: data.userId || 'system'
          }
        });
        return json({ followUpOrder });

      case 'update_loyalty_tier':
        // Update customer loyalty tier (would need schema update)
        await prisma.customer.update({
          where: { id: customerId },
          data: {
            notes: `Loyalty tier updated to: ${data.tier}`
          }
        });
        return json({ success: true });

      default:
        return json({ error: 'Unknown action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Error processing customer action:', error);
    return json(
      { error: 'Failed to process action' },
      { status: 500 }
    );
  }
}
