import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { prisma } from "~/db.server";
import { getUser } from "~/session.server";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const user = await getUser(request);
  
  if (!user || user.role !== "CUSTOMER") {
    throw new Response("Unauthorized", { status: 401 });
  }
  
  const { serviceOrderId } = params;
  
  if (!serviceOrderId) {
    throw new Response("Service order ID is required", { status: 400 });
  }
  
  // Get the service order with related data
  const serviceOrder = await prisma.serviceOrder.findUnique({
    where: { id: serviceOrderId },
    include: {
      customer: true,
      device: true,
      serviceReports: true,
      invoices: true,
    },
  });
  
  if (!serviceOrder) {
    throw new Response("Service order not found", { status: 404 });
  }
  
  // Check if the service order belongs to the customer
  if (serviceOrder.customer.id !== user.customerId) {
    throw new Response("Unauthorized", { status: 401 });
  }
  
  return json({ serviceOrder });
};

export default function CustomerPortalServiceOrderDetail() {
  const { serviceOrder } = useLoaderData<typeof loader>();
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{serviceOrder.title}</h1>
          <p className="text-muted-foreground">
            Service Order #{serviceOrder.id.substring(0, 8)}
          </p>
        </div>
        <Badge 
          variant={
            serviceOrder.status === "COMPLETED" ? "success" :
            serviceOrder.status === "IN_PROGRESS" ? "warning" :
            serviceOrder.status === "PENDING" ? "default" :
            "destructive"
          }
          className="text-base py-1 px-3"
        >
          {serviceOrder.status}
        </Badge>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Service Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                <p>{serviceOrder.status}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Priority</h3>
                <p>{serviceOrder.priority}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Scheduled Date</h3>
                <p>
                  {serviceOrder.scheduledDate
                    ? new Date(serviceOrder.scheduledDate).toLocaleDateString()
                    : "Not scheduled"}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Completed Date</h3>
                <p>
                  {serviceOrder.completedDate
                    ? new Date(serviceOrder.completedDate).toLocaleDateString()
                    : "Not completed"}
                </p>
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
              <p className="mt-1">{serviceOrder.description || "No description provided."}</p>
            </div>
            
            {serviceOrder.notes && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
                <p className="mt-1">{serviceOrder.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Device Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {serviceOrder.device ? (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Device Name</h3>
                    <p>{serviceOrder.device.name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Model</h3>
                    <p>{serviceOrder.device.model || "N/A"}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Manufacturer</h3>
                    <p>{serviceOrder.device.manufacturer || "N/A"}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Serial Number</h3>
                    <p>{serviceOrder.device.serialNumber || "N/A"}</p>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <Button asChild variant="outline" size="sm">
                    <Link to={`/customer-portal/devices/${serviceOrder.device.id}`}>
                      View Device Details
                    </Link>
                  </Button>
                </div>
              </>
            ) : (
              <p className="text-muted-foreground">No device associated with this service order.</p>
            )}
          </CardContent>
        </Card>
      </div>
      
      {serviceOrder.serviceReports.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Service Reports</CardTitle>
            <CardDescription>
              Reports submitted by technicians
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {serviceOrder.serviceReports.map((report) => (
                <div key={report.id} className="border p-4 rounded-md">
                  <h3 className="font-medium">{report.title}</h3>
                  <p className="text-sm text-muted-foreground">
                    {new Date(report.createdAt).toLocaleDateString()}
                  </p>
                  
                  {report.description && (
                    <div className="mt-2">
                      <p>{report.description}</p>
                    </div>
                  )}
                  
                  {report.workPerformed && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium">Work Performed</h4>
                      <p>{report.workPerformed}</p>
                    </div>
                  )}
                  
                  {report.partsUsed && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium">Parts Used</h4>
                      <p>{report.partsUsed}</p>
                    </div>
                  )}
                  
                  {report.recommendations && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium">Recommendations</h4>
                      <p>{report.recommendations}</p>
                    </div>
                  )}
                  
                  {report.photoUrls && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium">Photos</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                        {JSON.parse(report.photoUrls).map((url: string, index: number) => (
                          <a
                            key={index}
                            href={url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="block"
                          >
                            <img
                              src={url}
                              alt={`Service photo ${index + 1}`}
                              className="w-full h-24 object-cover rounded-md"
                            />
                          </a>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      
      {serviceOrder.invoices.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Invoices</CardTitle>
            <CardDescription>
              Invoices associated with this service order
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {serviceOrder.invoices.map((invoice) => (
                <div key={invoice.id} className="border p-4 rounded-md">
                  <div className="flex justify-between">
                    <div>
                      <h3 className="font-medium">
                        Invoice #{invoice.invoiceNumber || invoice.id.substring(0, 8)}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {invoice.issueDate
                          ? new Date(invoice.issueDate).toLocaleDateString()
                          : "No issue date"}
                      </p>
                    </div>
                    <Badge 
                      variant={
                        invoice.status === "PAID" ? "success" :
                        invoice.status === "PENDING" ? "warning" :
                        "destructive"
                      }
                    >
                      {invoice.status}
                    </Badge>
                  </div>
                  
                  <div className="mt-2 flex justify-between">
                    <span>Total Amount:</span>
                    <span className="font-medium">
                      ${invoice.totalAmount?.toFixed(2) || "N/A"}
                    </span>
                  </div>
                  
                  <div className="mt-4 flex justify-end">
                    <Button asChild variant="outline" size="sm">
                      <Link to={`/customer-portal/invoices/${invoice.id}`}>
                        View Invoice
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      
      <div className="flex justify-between">
        <Button asChild variant="outline">
          <Link to="/customer-portal/service-orders">Back to Service Orders</Link>
        </Button>
        
        {serviceOrder.status === "COMPLETED" && (
          <Button asChild>
            <Link to={`/customer-portal/service-orders/${serviceOrder.id}/feedback`}>
              Provide Feedback
            </Link>
          </Button>
        )}
      </div>
    </div>
  );
}