import { json, redirect, type LoaderFunctionArgs } from "@remix-run/node";
import { setupOutlookIntegration } from "~/services/outlook-calendar.server";
import { requireUserId } from "~/session.server";

/**
 * This route handles the OAuth callback from Microsoft.
 * It exchanges the authorization code for access and refresh tokens,
 * then stores them in the database.
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");
  const error = url.searchParams.get("error");
  const errorDescription = url.searchParams.get("error_description");
  
  // Check for errors
  if (error) {
    return redirect(`/calendar/settings?error=${encodeURIComponent(error)}&error_description=${encodeURIComponent(errorDescription || "")}`);
  }
  
  // Validate code and state
  if (!code || !state) {
    return redirect("/calendar/settings?error=invalid_request&error_description=Missing+code+or+state");
  }
  
  // TODO: Validate state parameter against stored state to prevent CSRF attacks
  
  // Exchange code for tokens and set up integration
  const setupResponse = await setupOutlookIntegration(userId, code);
  
  if (setupResponse.success) {
    return redirect("/calendar/settings?success=true");
  } else {
    return redirect(`/calendar/settings?error=setup_failed&error_description=${encodeURIComponent(setupResponse.message)}`);
  }
}

// No component needed for this route as it just handles the redirect
export default function OAuthCallback() {
  return (
    <div>
      <h1>Processing OAuth Callback</h1>
      <p>Please wait while we process your authentication...</p>
    </div>
  );
}
