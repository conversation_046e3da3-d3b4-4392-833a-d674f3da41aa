/**
 * Admin Events Dashboard
 * 
 * This page displays a dashboard of events in the system.
 * It allows administrators to view, filter, and manage events.
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useSearchParams } from "@remix-run/react";
import { useState } from "react";

import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "~/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { prisma } from "~/db.server";
import { requirePermission } from "~/middleware/auth.server";
import { getAllEvents, getEventsByType, EventType } from "~/services/eventBus.server";
import { requireUserId } from "~/session.server";

/**
 * Loader function for retrieving events
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  
  // Only users with admin permission can access events
  await requirePermission(userId, "ADMIN_ACCESS");
  
  const url = new URL(request.url);
  const type = url.searchParams.get("type");
  const count = url.searchParams.get("count") ? parseInt(url.searchParams.get("count")!) : 100;
  
  try {
    // Get events from Redis
    let redisEvents = [];
    if (type) {
      redisEvents = await getEventsByType(type as EventType, count);
    } else {
      redisEvents = await getAllEvents(count);
    }
    
    // Get events from database
    const dbEvents = await prisma.event.findMany({
      orderBy: { createdAt: 'desc' },
      take: count,
      ...(type ? { where: { type } } : {}),
    });
    
    // Map database events to the same format as Redis events
    const mappedDbEvents = dbEvents.map(event => ({
      id: event.id,
      type: event.type,
      data: JSON.parse(event.data),
      metadata: JSON.parse(event.metadata),
    }));
    
    // Combine events from both sources
    const allEvents = [...redisEvents, ...mappedDbEvents];
    
    // Sort by timestamp (newest first)
    allEvents.sort((a, b) => {
      const timestampA = a.metadata?.timestamp || 0;
      const timestampB = b.metadata?.timestamp || 0;
      return timestampB - timestampA;
    });
    
    // Get all event types for the filter
    const eventTypes = Object.values(EventType);
    
    return json({ events: allEvents, eventTypes });
  } catch (error) {
    console.error("Error retrieving events:", error);
    return json({ events: [], eventTypes: Object.values(EventType), error: "Failed to retrieve events" });
  }
}

export default function EventsDashboardPage() {
  const { events, eventTypes } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  
  // Filter events by search term
  const filteredEvents = events.filter(event => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      event.type.toLowerCase().includes(searchLower) ||
      event.id.toLowerCase().includes(searchLower) ||
      JSON.stringify(event.data).toLowerCase().includes(searchLower)
    );
  });
  
  // Handle event type filter change
  const handleTypeChange = (value: string) => {
    const params = new URLSearchParams(searchParams);
    if (value === "all") {
      params.delete("type");
    } else {
      params.set("type", value);
    }
    setSearchParams(params);
  };
  
  // Format date for display
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Event Dashboard</h1>
      
      <div className="flex items-center gap-4 mb-6">
        <div className="w-1/3">
          <Label htmlFor="search">Search Events</Label>
          <Input
            id="search"
            placeholder="Search by type, ID, or data..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="w-1/3">
          <Label htmlFor="type-filter">Filter by Type</Label>
          <Select
            value={searchParams.get("type") || "all"}
            onValueChange={handleTypeChange}
          >
            <SelectTrigger id="type-filter">
              <SelectValue placeholder="All Events" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Events</SelectItem>
              {eventTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Tabs defaultValue="list" className="w-full">
        <TabsList>
          <TabsTrigger value="list">List View</TabsTrigger>
          <TabsTrigger value="json">JSON View</TabsTrigger>
        </TabsList>
        
        <TabsContent value="list">
          <div className="space-y-4">
            {filteredEvents.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-center text-gray-500">No events found</p>
                </CardContent>
              </Card>
            ) : (
              filteredEvents.map((event) => (
                <Card key={event.id} className="overflow-hidden">
                  <CardHeader className="bg-gray-50 dark:bg-gray-800">
                    <div className="flex justify-between items-center">
                      <div>
                        <CardTitle className="text-lg">{event.type}</CardTitle>
                        <CardDescription>ID: {event.id}</CardDescription>
                      </div>
                      <div className="text-sm text-gray-500">
                        {event.metadata?.timestamp ? formatDate(event.metadata.timestamp) : "Unknown date"}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium mb-1">Event Data</h3>
                        <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-auto max-h-40">
                          {JSON.stringify(event.data, null, 2)}
                        </pre>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium mb-1">Metadata</h3>
                        <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-auto max-h-40">
                          {JSON.stringify(event.metadata, null, 2)}
                        </pre>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="json">
          <Card>
            <CardContent className="pt-6">
              <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm overflow-auto max-h-[600px]">
                {JSON.stringify(filteredEvents, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
