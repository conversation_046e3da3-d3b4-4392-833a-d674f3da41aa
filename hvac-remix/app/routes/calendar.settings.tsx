import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation, Link } from "@remix-run/react";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";
import { prisma } from "~/db.server";
import { setupOutlookIntegration, synchronizeOutlookCalendar } from "~/services/outlook-calendar.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  
  // If code is present, set up Outlook integration
  if (code) {
    const setupResponse = await setupOutlookIntegration(userId, code);
    
    if (setupResponse.success) {
      return redirect("/calendar/settings?success=true");
    } else {
      return redirect(`/calendar/settings?error=${encodeURIComponent(setupResponse.message)}`);
    }
  }
  
  // Get user's Outlook integration settings
  const outlookIntegration = await prisma.outlookIntegration.findUnique({
    where: { userId },
  });
  
  // Get calendar sync settings
  const calendarSyncSettings = await prisma.calendarSyncSettings.findUnique({
    where: { userId },
  });
  
  // Get success or error messages from URL
  const success = url.searchParams.get("success");
  const error = url.searchParams.get("error");
  
  return json({
    outlookIntegration: outlookIntegration ? {
      isConnected: true,
      lastSyncTime: outlookIntegration.lastSyncTime,
    } : null,
    calendarSyncSettings: calendarSyncSettings || {
      autoSync: false,
      syncFrequency: 30, // Default to 30 minutes
      syncStartTime: "07:00", // Default to 7:00 AM
      syncEndTime: "17:00", // Default to 5:00 PM
    },
    success: success === "true",
    error: error || null,
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  const intent = formData.get("intent") as string;
  
  if (intent === "sync") {
    // Manually sync Outlook Calendar
    const syncResponse = await synchronizeOutlookCalendar(userId);
    
    return json({
      success: syncResponse.success,
      message: syncResponse.message,
      syncedEvents: syncResponse.syncedEvents,
    });
  } else if (intent === "disconnect") {
    // Disconnect Outlook integration
    await prisma.outlookIntegration.delete({
      where: { userId },
    });
    
    return json({
      success: true,
      message: "Successfully disconnected Outlook integration",
    });
  } else if (intent === "save-settings") {
    // Save calendar sync settings
    const autoSync = formData.get("autoSync") === "on";
    const syncFrequency = parseInt(formData.get("syncFrequency") as string, 10);
    const syncStartTime = formData.get("syncStartTime") as string;
    const syncEndTime = formData.get("syncEndTime") as string;
    
    // Validate settings
    if (syncFrequency < 15 || syncFrequency > 120) {
      return json({
        success: false,
        message: "Sync frequency must be between 15 and 120 minutes",
      });
    }
    
    // Save settings
    await prisma.calendarSyncSettings.upsert({
      where: { userId },
      update: {
        autoSync,
        syncFrequency,
        syncStartTime,
        syncEndTime,
      },
      create: {
        userId,
        autoSync,
        syncFrequency,
        syncStartTime,
        syncEndTime,
      },
    });
    
    return json({
      success: true,
      message: "Successfully saved calendar sync settings",
    });
  }
  
  return json({
    success: false,
    message: "Invalid intent",
  });
}

export default function CalendarSettingsPage() {
  const { 
    outlookIntegration, 
    calendarSyncSettings, 
    success, 
    error 
  } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  // Generate Outlook authorization URL
  const outlookAuthUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id=${process.env.AZURE_CLIENT_ID}&response_type=code&redirect_uri=${encodeURIComponent(process.env.AZURE_REDIRECT_URI || "")}&scope=offline_access%20Calendars.Read`;
  
  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to="/calendar" className="text-blue-500 hover:underline">
          ← Back to Calendar
        </Link>
      </div>
      
      <h1 className="text-3xl font-bold mb-6">Calendar Settings</h1>
      
      {/* Success or error messages */}
      {(success || actionData?.success) && (
        <Alert className="mb-6 bg-green-50 border-green-200">
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>
            {actionData?.message || "Operation completed successfully"}
          </AlertDescription>
        </Alert>
      )}
      
      {(error || actionData?.success === false) && (
        <Alert className="mb-6 bg-red-50 border-red-200">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {actionData?.message || error || "An error occurred"}
          </AlertDescription>
        </Alert>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Outlook Integration */}
        <Card>
          <CardHeader>
            <CardTitle>Microsoft Outlook Integration</CardTitle>
            <CardDescription>
              Connect your Outlook Calendar to sync events
            </CardDescription>
          </CardHeader>
          <CardContent>
            {outlookIntegration?.isConnected ? (
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                  <span>Connected to Microsoft Outlook</span>
                </div>
                
                {outlookIntegration.lastSyncTime && (
                  <div>
                    <Label>Last Synchronized</Label>
                    <p>{new Date(outlookIntegration.lastSyncTime).toLocaleString()}</p>
                  </div>
                )}
                
                <Form method="post" className="space-y-4">
                  <input type="hidden" name="intent" value="sync" />
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting && navigation.formData?.get("intent") === "sync" 
                      ? "Syncing..." 
                      : "Sync Now"
                    }
                  </Button>
                </Form>
                
                <Form method="post">
                  <input type="hidden" name="intent" value="disconnect" />
                  <Button 
                    type="submit" 
                    variant="outline" 
                    disabled={isSubmitting}
                  >
                    {isSubmitting && navigation.formData?.get("intent") === "disconnect" 
                      ? "Disconnecting..." 
                      : "Disconnect"
                    }
                  </Button>
                </Form>
              </div>
            ) : (
              <div className="space-y-4">
                <p>Connect your Microsoft Outlook Calendar to sync events between Outlook and the HVAC CRM system.</p>
                
                <a href={outlookAuthUrl}>
                  <Button>Connect to Outlook</Button>
                </a>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Sync Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Sync Settings</CardTitle>
            <CardDescription>
              Configure how calendar events are synchronized
            </CardDescription>
          </CardHeader>
          <Form method="post">
            <input type="hidden" name="intent" value="save-settings" />
            
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch 
                  id="autoSync" 
                  name="autoSync" 
                  defaultChecked={calendarSyncSettings.autoSync}
                />
                <Label htmlFor="autoSync">Enable Automatic Sync</Label>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="syncFrequency">Sync Frequency (minutes)</Label>
                <Input
                  id="syncFrequency"
                  name="syncFrequency"
                  type="number"
                  min="15"
                  max="120"
                  defaultValue={calendarSyncSettings.syncFrequency}
                />
                <p className="text-sm text-gray-500">
                  How often to sync calendar events (15-120 minutes)
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="syncStartTime">Sync Start Time</Label>
                <Input
                  id="syncStartTime"
                  name="syncStartTime"
                  type="time"
                  defaultValue={calendarSyncSettings.syncStartTime}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="syncEndTime">Sync End Time</Label>
                <Input
                  id="syncEndTime"
                  name="syncEndTime"
                  type="time"
                  defaultValue={calendarSyncSettings.syncEndTime}
                />
                <p className="text-sm text-gray-500">
                  Calendar sync will only occur between these hours
                </p>
              </div>
            </CardContent>
            
            <CardFooter>
              <Button 
                type="submit" 
                disabled={isSubmitting}
              >
                {isSubmitting && navigation.formData?.get("intent") === "save-settings" 
                  ? "Saving..." 
                  : "Save Settings"
                }
              </Button>
            </CardFooter>
          </Form>
        </Card>
      </div>
    </div>
  );
}
