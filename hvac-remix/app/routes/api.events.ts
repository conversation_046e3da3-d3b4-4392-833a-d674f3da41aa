/**
 * API Routes for Event Management
 * 
 * This file contains API endpoints for managing events in the event bus.
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { requirePermission } from "~/middleware/auth.server";
import { 
  getAllEvents, 
  getEventsByType, 
  publishEvent, 
  EventType 
} from "~/services/eventBus.server";
import { requireUserId } from "~/session.server";

/**
 * Loader function for retrieving events
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  
  // Only users with admin permission can access events
  await requirePermission(userId, "ADMIN_ACCESS");
  
  const url = new URL(request.url);
  const type = url.searchParams.get("type");
  const count = url.searchParams.get("count") ? parseInt(url.searchParams.get("count")!) : 100;
  
  try {
    if (type) {
      // Validate that the type is a valid EventType
      if (!Object.values(EventType).includes(type as EventType)) {
        return json({ error: "Invalid event type" }, { status: 400 });
      }
      
      const events = await getEventsByType(type as EventType, count);
      return json({ events });
    } else {
      const events = await getAllEvents(count);
      return json({ events });
    }
  } catch (error) {
    console.error("Error retrieving events:", error);
    return json({ error: "Failed to retrieve events" }, { status: 500 });
  }
}

/**
 * Action function for publishing events
 */
export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  
  // Only users with admin permission can publish events
  await requirePermission(userId, "ADMIN_ACCESS");
  
  // Only allow POST method
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }
  
  try {
    const formData = await request.formData();
    const type = formData.get("type") as string;
    const dataJson = formData.get("data") as string;
    
    // Validate required fields
    if (!type || !dataJson) {
      return json({ error: "Missing required fields: type, data" }, { status: 400 });
    }
    
    // Validate that the type is a valid EventType
    if (!Object.values(EventType).includes(type as EventType)) {
      return json({ error: "Invalid event type" }, { status: 400 });
    }
    
    // Parse data JSON
    let data;
    try {
      data = JSON.parse(dataJson);
    } catch (error) {
      return json({ error: "Invalid JSON in data field" }, { status: 400 });
    }
    
    // Publish the event
    const eventId = await publishEvent(type as EventType, data, { userId });
    
    return json({ success: true, eventId });
  } catch (error) {
    console.error("Error publishing event:", error);
    return json({ error: "Failed to publish event" }, { status: 500 });
  }
}
