import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { getDevices } from "~/services/device.service";
import { requireUserId } from "~/session.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const { customerId } = params;
  
  if (!customerId) {
    return json({ error: "Customer ID is required" }, { status: 400 });
  }
  
  // Get devices for the customer
  const devicesResponse = await getDevices(
    userId,
    { pageSize: 100 },
    { customerId }
  );
  
  if (!devicesResponse.success) {
    return json({ error: devicesResponse.error }, { status: 400 });
  }
  
  return json({ devices: devicesResponse.data });
}
