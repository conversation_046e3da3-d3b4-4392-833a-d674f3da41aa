import { ArrowLeftIcon, DocumentTextIcon, PencilIcon, DocumentArrowDownIcon, PrinterIcon } from "@heroicons/react/24/outline";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { useRef, useState } from "react";
import { PrintableInvoice } from "~/components/templates/printable-invoice";
import { prisma } from "~/db.server";
import { formatDate, formatCurrency } from "~/lib/utils";
import { getCompanyInfoForPrintableDocuments } from "~/services/company-settings.server";
import { requireUserId } from "~/session.server";
import { generatePDFFromElement, savePDF } from "~/utils/pdf-generator";

export async function loader({ request, params }: LoaderFunctionArgs) {
  await requireUserId(request);
  const { invoiceId } = params;

  if (!invoiceId) {
    throw new Response("Invoice ID is required", { status: 400 });
  }

  const invoice = await prisma.invoice.findUnique({
    where: { id: invoiceId },
    include: {
      customer: true,
      serviceOrder: true,
      items: true,
    },
  });

  if (!invoice) {
    throw new Response("Invoice not found", { status: 404 });
  }

  // Get company info for printable documents
  const companyInfo = await getCompanyInfoForPrintableDocuments();

  return json({ invoice, companyInfo });
}

export default function InvoiceDetails() {
  const { invoice, companyInfo } = useLoaderData<typeof loader>();
  const [isPrintModalOpen, setIsPrintModalOpen] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const printableInvoiceRef = useRef<HTMLDivElement>(null);

  // Handle PDF generation
  const handleGeneratePDF = async () => {
    if (!printableInvoiceRef.current) return;

    try {
      setIsGeneratingPDF(true);
      const pdf = await generatePDFFromElement(printableInvoiceRef.current, {
        filename: `invoice-${invoice.invoiceNumber || invoice.id}.pdf`,
        pageSize: 'A4',
        margin: { top: 10, right: 10, bottom: 10, left: 10 }
      });

      savePDF(pdf, `invoice-${invoice.invoiceNumber || invoice.id}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('An error occurred while generating the PDF. Please try again.');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Handle print
  const handlePrint = () => {
    setIsPrintModalOpen(true);
  };

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Link to="/invoices" className="mr-4 text-gray-500 hover:text-gray-700">
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h2 className="text-xl font-semibold">
            Invoice {invoice.invoiceNumber || "N/A"}
          </h2>
        </div>
        <div className="flex space-x-2">
          <Link
            to={`/invoices/${invoice.id}/edit`}
            className="flex items-center rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
          >
            <PencilIcon className="mr-2 h-5 w-5" />
            Edit
          </Link>
          {invoice.paymentStatus !== "PAID" && (
            <Link
              to={`/invoices/${invoice.id}/pay`}
              className="flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              Process Payment
            </Link>
          )}
          <button
            onClick={handlePrint}
            className="flex items-center rounded-md bg-green-600 px-4 py-2 text-sm font-medium text-white hover:bg-green-700"
          >
            <PrinterIcon className="mr-2 h-5 w-5" />
            Print
          </button>
          <button
            onClick={handleGeneratePDF}
            disabled={isGeneratingPDF}
            className="flex items-center rounded-md bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 disabled:bg-purple-400"
          >
            <DocumentArrowDownIcon className="mr-2 h-5 w-5" />
            {isGeneratingPDF ? 'Generating...' : 'Download PDF'}
          </button>
          <Link
            to={`/invoices/${invoice.id}/process`}
            className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
          >
            <DocumentTextIcon className="mr-2 h-5 w-5" />
            Process with OCR
          </Link>
        </div>
      </div>

      <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Invoice Details</h3>
        </div>
        <div className="grid grid-cols-2 gap-6 px-6 py-4 md:grid-cols-4">
          <div>
            <p className="text-sm font-medium text-gray-500">Invoice Number</p>
            <p className="mt-1 text-sm text-gray-900">{invoice.invoiceNumber || "N/A"}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Status</p>
            <p className="mt-1">
              <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                invoice.status === "PAID"
                  ? "bg-green-100 text-green-800"
                  : invoice.status === "OVERDUE"
                  ? "bg-red-100 text-red-800"
                  : "bg-yellow-100 text-yellow-800"
              }`}>
                {invoice.status}
              </span>
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Payment Status</p>
            <p className="mt-1">
              <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                invoice.paymentStatus === "PAID"
                  ? "bg-green-100 text-green-800"
                  : invoice.paymentStatus === "PARTIALLY_PAID"
                  ? "bg-blue-100 text-blue-800"
                  : invoice.paymentStatus === "REFUNDED"
                  ? "bg-purple-100 text-purple-800"
                  : "bg-yellow-100 text-yellow-800"
              }`}>
                {invoice.paymentStatus}
              </span>
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Issue Date</p>
            <p className="mt-1 text-sm text-gray-900">{formatDate(invoice.issueDate)}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Due Date</p>
            <p className="mt-1 text-sm text-gray-900">{formatDate(invoice.dueDate)}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Customer</p>
            <p className="mt-1 text-sm text-gray-900">{invoice.customer.name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Service Order</p>
            <p className="mt-1 text-sm text-gray-900">
              {invoice.serviceOrder ? (
                <Link
                  to={`/service-orders/${invoice.serviceOrder.id}`}
                  className="text-blue-600 hover:text-blue-900"
                >
                  {invoice.serviceOrder.title}
                </Link>
              ) : (
                "N/A"
              )}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Total Amount</p>
            <p className="mt-1 text-sm font-medium text-gray-900">{formatCurrency(invoice.totalAmount)}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Tax Amount</p>
            <p className="mt-1 text-sm text-gray-900">{formatCurrency(invoice.taxAmount)}</p>
          </div>
        </div>
      </div>

      {(invoice.sellerInfo || invoice.buyerInfo) && (
        <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <h3 className="text-lg font-medium text-gray-900">Seller & Buyer Information</h3>
          </div>
          <div className="grid grid-cols-1 gap-6 px-6 py-4 md:grid-cols-2">
            {invoice.sellerInfo && (
              <div>
                <p className="text-sm font-medium text-gray-500">Seller Information</p>
                <pre className="mt-2 whitespace-pre-wrap rounded-md bg-gray-50 p-3 text-sm text-gray-900">
                  {invoice.sellerInfo}
                </pre>
              </div>
            )}
            {invoice.buyerInfo && (
              <div>
                <p className="text-sm font-medium text-gray-500">Buyer Information</p>
                <pre className="mt-2 whitespace-pre-wrap rounded-md bg-gray-50 p-3 text-sm text-gray-900">
                  {invoice.buyerInfo}
                </pre>
              </div>
            )}
          </div>
        </div>
      )}

      {invoice.items && invoice.items.length > 0 && (
        <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <h3 className="text-lg font-medium text-gray-900">Invoice Items</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Description
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Quantity
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Unit Price
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Tax Rate
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Total
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {invoice.items.map((item) => (
                  <tr key={item.id}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                      {item.description}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {item.quantity}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {formatCurrency(item.unitPrice)}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {item.taxRate ? `${(item.taxRate * 100).toFixed(2)}%` : "N/A"}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      {formatCurrency(item.totalPrice)}
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot className="bg-gray-50">
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-right text-sm font-medium text-gray-900">
                    Subtotal:
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {formatCurrency(
                      invoice.items.reduce((sum, item) => sum + item.totalPrice, 0)
                    )}
                  </td>
                </tr>
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-right text-sm font-medium text-gray-900">
                    Tax:
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {formatCurrency(invoice.taxAmount)}
                  </td>
                </tr>
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-right text-sm font-medium text-gray-900">
                    Total:
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {formatCurrency(invoice.totalAmount)}
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      )}

      {invoice.notes && (
        <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <h3 className="text-lg font-medium text-gray-900">Notes</h3>
          </div>
          <div className="px-6 py-4">
            <p className="text-sm text-gray-900">{invoice.notes}</p>
          </div>
        </div>
      )}

      <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">OCR Processing</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <p className="text-sm font-medium text-gray-500">Processing Status</p>
              <p className="mt-1">
                <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                  invoice.ocrProcessingStatus === "COMPLETED"
                    ? "bg-green-100 text-green-800"
                    : invoice.ocrProcessingStatus === "FAILED"
                    ? "bg-red-100 text-red-800"
                    : invoice.ocrProcessingStatus === "PROCESSING"
                    ? "bg-blue-100 text-blue-800"
                    : "bg-gray-100 text-gray-800"
                }`}>
                  {invoice.ocrProcessingStatus}
                </span>
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Processed At</p>
              <p className="mt-1 text-sm text-gray-900">
                {formatDate(invoice.ocrProcessedAt) || "N/A"}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Confidence Score</p>
              <p className="mt-1 text-sm text-gray-900">
                {invoice.ocrConfidenceScore
                  ? `${(invoice.ocrConfidenceScore * 100).toFixed(2)}%`
                  : "N/A"}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Error Message</p>
              <p className="mt-1 text-sm text-gray-900">
                {invoice.ocrErrorMessage || "N/A"}
              </p>
            </div>
          </div>

          {invoice.originalDocumentUrl && (
            <div className="mt-6">
              <p className="mb-2 text-sm font-medium text-gray-500">Original Document</p>
              <a
                href={invoice.originalDocumentUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
              >
                <DocumentTextIcon className="mr-2 h-5 w-5" />
                View Document
              </a>
            </div>
          )}
        </div>
      </div>

      {/* Print Modal */}
      {isPrintModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative max-h-[90vh] w-[800px] overflow-auto bg-white p-6 shadow-xl">
            <button
              onClick={() => setIsPrintModalOpen(false)}
              className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="mb-4 flex justify-between">
              <h2 className="text-xl font-bold">Print Invoice</h2>
              <button
                onClick={() => window.print()}
                className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
              >
                <PrinterIcon className="mr-2 h-5 w-5" />
                Print
              </button>
            </div>

            <PrintableInvoice
              ref={printableInvoiceRef}
              data={{
                id: invoice.id,
                invoiceNumber: invoice.invoiceNumber,
                issueDate: invoice.issueDate,
                dueDate: invoice.dueDate,
                totalAmount: invoice.totalAmount,
                taxAmount: invoice.taxAmount,
                status: invoice.status,
                notes: invoice.notes,
                sellerInfo: invoice.sellerInfo,
                buyerInfo: invoice.buyerInfo,
                items: invoice.items,
                serviceOrder: invoice.serviceOrder,
                companyInfo
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}