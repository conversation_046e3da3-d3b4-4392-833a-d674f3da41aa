import { json, redirect, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, Outlet, useLoaderData } from "@remix-run/react";
import { ModeToggle } from "~/components/mode-toggle";
import { Button } from "~/components/ui/button";
import { getCustomerPortalData } from "~/services/customer-portal.server";
import { getUser } from "~/session.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await getUser(request);
  
  // Redirect to login if not logged in
  if (!user) {
    return redirect("/login");
  }
  
  // Redirect to admin dashboard if not a customer
  if (user.role !== "CUSTOMER") {
    return redirect("/dashboard");
  }
  
  // Get customer data
  const customerResponse = await getCustomerPortalData(user.id);
  
  if (!customerResponse.success || !customerResponse.data) {
    return redirect("/login");
  }
  
  return json({ user, customer: customerResponse.data });
};

export default function CustomerPortalLayout() {
  const { user, customer } = useLoaderData<typeof loader>();
  
  return (
    <div className="flex min-h-screen flex-col bg-background/50 bg-gradient-to-br from-background to-background/80">
      <header className="bg-primary/90 text-primary-foreground shadow-lg backdrop-blur-md sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Link to="/customer-portal" className="text-xl font-bold flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
              ServiceTool Portal
            </Link>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium bg-white/20 px-3 py-1.5 rounded-full">
              Welcome, {user.name || customer.name}
            </span>
            <ModeToggle />
            <form action="/logout" method="post">
              <Button type="submit" variant="secondary" size="sm">
                Logout
              </Button>
            </form>
          </div>
        </div>
      </header>
      
      <div className="flex flex-1 container mx-auto px-6 py-8">
        <aside className="w-64 hidden md:block">
          <div className="sticky top-24 bg-card/80 backdrop-blur-sm shadow-lg rounded-2xl p-5 border border-border/40">
            <h3 className="text-lg font-semibold mb-4 pb-2 border-b border-border/30">Menu</h3>
            <nav className="space-y-1">
              <Link
                to="/customer-portal"
                className="flex items-center p-3 rounded-xl text-foreground/80 hover:text-primary hover:bg-primary/5 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="3" width="7" height="7"></rect>
                  <rect x="14" y="3" width="7" height="7"></rect>
                  <rect x="14" y="14" width="7" height="7"></rect>
                  <rect x="3" y="14" width="7" height="7"></rect>
                </svg>
                Dashboard
              </Link>
              <Link
                to="/customer-portal/devices"
                className="flex items-center p-3 rounded-xl text-foreground/80 hover:text-primary hover:bg-primary/5 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect>
                  <polyline points="17 2 12 7 7 2"></polyline>
                </svg>
                My Devices
              </Link>
              <Link
                to="/customer-portal/service-orders"
                className="flex items-center p-3 rounded-xl text-foreground/80 hover:text-primary hover:bg-primary/5 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
                Service History
              </Link>
              <Link
                to="/customer-portal/invoices"
                className="flex items-center p-3 rounded-xl text-foreground/80 hover:text-primary hover:bg-primary/5 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7"></path>
                  <line x1="16" y1="8" x2="8" y2="16"></line>
                  <line x1="16" y1="16" x2="8" y2="8"></line>
                </svg>
                Invoices
              </Link>
              <Link
                to="/customer-portal/request-service"
                className="flex items-center p-3 rounded-xl text-foreground/80 hover:text-primary hover:bg-primary/5 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                Request Service
              </Link>
              <Link
                to="/customer-portal/profile"
                className="flex items-center p-3 rounded-xl text-foreground/80 hover:text-primary hover:bg-primary/5 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
                My Profile
              </Link>
            </nav>
          </div>
        </aside>
        
        <main className="flex-1 ml-0 md:ml-8">
          <Outlet />
        </main>
      </div>
      
      <footer className="bg-secondary/90 text-secondary-foreground py-6 text-center text-sm backdrop-blur-md">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              &copy; {new Date().getFullYear()} ServiceTool HVAC CRM. All rights reserved.
            </div>
            <div className="flex space-x-4">
              <a href="#" className="hover:text-primary transition-colors">Privacy Policy</a>
              <a href="#" className="hover:text-primary transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-primary transition-colors">Contact Support</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}