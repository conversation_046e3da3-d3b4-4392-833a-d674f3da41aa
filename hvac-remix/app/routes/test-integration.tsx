/**
 * Test Integration Page
 * 
 * Tests the communication between HVAC-Remix frontend and GoBackend-Kratos
 * Demonstrates tRPC integration and real-time capabilities
 */

import type { MetaFunction } from "@remix-run/node";
import { useState } from "react";
import { trpc } from "~/providers/trpc-provider";

export const meta: MetaFunction = () => {
  return [
    { title: "Test Integration - HVAC CRM" },
    { name: "description", content: "Test page for GoBackend-Kratos integration" },
  ];
};

export default function TestIntegration() {
  const [testResults, setTestResults] = useState<string[]>([]);

  // tRPC queries
  const healthQuery = trpc.system.health.useQuery();
  const customersQuery = trpc.customer.list.useQuery({ limit: 5 });

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testHealthCheck = async () => {
    try {
      addTestResult("Testing health check...");
      await healthQuery.refetch();
      if (healthQuery.data) {
        addTestResult(`✅ Health check passed: ${healthQuery.data.status}`);
      } else {
        addTestResult("❌ Health check failed: No data received");
      }
    } catch (error) {
      addTestResult(`❌ Health check failed: ${error}`);
    }
  };

  const testCustomerList = async () => {
    try {
      addTestResult("Testing customer list...");
      await customersQuery.refetch();
      if (customersQuery.data) {
        addTestResult(`✅ Customer list loaded: ${customersQuery.data.data.length} customers`);
      } else {
        addTestResult("❌ Customer list failed: No data received");
      }
    } catch (error) {
      addTestResult(`❌ Customer list failed: ${error}`);
    }
  };

  const testDirectAPI = async () => {
    try {
      addTestResult("Testing direct API call...");
      const response = await fetch('http://localhost:8081/health');
      if (response.ok) {
        const data = await response.json();
        addTestResult(`✅ Direct API call successful: ${JSON.stringify(data)}`);
      } else {
        addTestResult(`❌ Direct API call failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      addTestResult(`❌ Direct API call failed: ${error}`);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">🧪 HVAC CRM Integration Test</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Test Controls */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
            
            <div className="space-y-3">
              <button
                onClick={testHealthCheck}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
                disabled={healthQuery.isLoading}
              >
                {healthQuery.isLoading ? "Testing..." : "Test Health Check"}
              </button>
              
              <button
                onClick={testCustomerList}
                className="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors"
                disabled={customersQuery.isLoading}
              >
                {customersQuery.isLoading ? "Testing..." : "Test Customer List"}
              </button>
              
              <button
                onClick={testDirectAPI}
                className="w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md transition-colors"
              >
                Test Direct API
              </button>
              
              <button
                onClick={() => setTestResults([])}
                className="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors"
              >
                Clear Results
              </button>
            </div>
          </div>

          {/* Current Status */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Current Status</h2>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Health Check:</span>
                <span className={`px-2 py-1 rounded text-sm ${
                  healthQuery.isSuccess ? 'bg-green-100 text-green-800' :
                  healthQuery.isError ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {healthQuery.isLoading ? 'Loading...' :
                   healthQuery.isError ? 'Error' :
                   healthQuery.data?.status || 'Unknown'}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span>Customer Query:</span>
                <span className={`px-2 py-1 rounded text-sm ${
                  customersQuery.isSuccess ? 'bg-green-100 text-green-800' :
                  customersQuery.isError ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {customersQuery.isLoading ? 'Loading...' :
                   customersQuery.isError ? 'Error' :
                   customersQuery.data ? `${customersQuery.data.data.length} customers` : 'No data'}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span>Backend URL:</span>
                <span className="text-sm text-gray-600">localhost:8081</span>
              </div>
            </div>
          </div>
        </div>

        {/* Test Results */}
        <div className="mt-6 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          
          <div className="bg-gray-50 rounded-md p-4 max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500 italic">No test results yet. Click a test button to start.</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Raw Data Display */}
        {(healthQuery.data || customersQuery.data) && (
          <div className="mt-6 bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Raw Data</h2>
            
            {healthQuery.data && (
              <div className="mb-4">
                <h3 className="font-medium mb-2">Health Data:</h3>
                <pre className="bg-gray-50 p-3 rounded text-sm overflow-x-auto">
                  {JSON.stringify(healthQuery.data, null, 2)}
                </pre>
              </div>
            )}
            
            {customersQuery.data && (
              <div>
                <h3 className="font-medium mb-2">Customer Data:</h3>
                <pre className="bg-gray-50 p-3 rounded text-sm overflow-x-auto">
                  {JSON.stringify(customersQuery.data, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}

        {/* Error Display */}
        {(healthQuery.error || customersQuery.error) && (
          <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-red-800 mb-4">Errors</h2>
            
            {healthQuery.error && (
              <div className="mb-4">
                <h3 className="font-medium text-red-700 mb-2">Health Check Error:</h3>
                <pre className="bg-red-100 p-3 rounded text-sm overflow-x-auto text-red-800">
                  {JSON.stringify(healthQuery.error, null, 2)}
                </pre>
              </div>
            )}
            
            {customersQuery.error && (
              <div>
                <h3 className="font-medium text-red-700 mb-2">Customer Query Error:</h3>
                <pre className="bg-red-100 p-3 rounded text-sm overflow-x-auto text-red-800">
                  {JSON.stringify(customersQuery.error, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
