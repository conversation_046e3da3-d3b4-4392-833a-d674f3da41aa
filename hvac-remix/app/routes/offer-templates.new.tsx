import { json, redirect, type ActionFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useNavigation } from "@remix-run/react";
import { But<PERSON> } from "~/components/ui/button";
import { <PERSON>, <PERSON>Content, <PERSON>H<PERSON>er, CardTitle } from "~/components/ui/card";
import { Checkbox } from "~/components/ui/checkbox";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { createOfferTemplate } from "~/services/offer-template.service";
import { requireUserId } from "~/session.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  await requireUserId(request);
  const formData = await request.formData();
  
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  const content = formData.get("content") as string;
  const isDefault = formData.get("isDefault") === "on";
  
  const errors: Record<string, string> = {};
  
  if (!name) errors.name = "Name is required";
  if (!content) errors.content = "Content is required";
  
  try {
    // Validate that content is valid JSON
    JSON.parse(content);
  } catch (error) {
    errors.content = "Content must be valid JSON";
  }
  
  if (Object.keys(errors).length > 0) {
    return json({ errors });
  }
  
  const template = await createOfferTemplate({
    name,
    description,
    content,
    isDefault,
  });
  
  return redirect(`/offer-templates/${template.id}`);
};

export default function NewOfferTemplatePage() {
  const actionData = useActionData<{ errors?: Record<string, string> }>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  // Default template content
  const defaultContent = JSON.stringify({
    sections: [
      {
        title: "Introduction",
        content: "Thank you for considering our services. We are pleased to provide this offer for your consideration."
      },
      {
        title: "Scope of Work",
        content: "This section describes the scope of work to be performed."
      },
      {
        title: "Terms and Conditions",
        content: "1. This offer is valid for 30 days from the date of issue.\n2. Payment terms: 50% upfront, 50% upon completion.\n3. All prices are exclusive of VAT unless otherwise stated."
      }
    ],
    footer: "We look forward to working with you. Please contact us if you have any questions."
  }, null, 2);
  
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Create New Offer Template</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>Template Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form method="post" className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  name="name"
                  required
                  aria-invalid={actionData?.errors?.name ? true : undefined}
                  aria-errormessage={actionData?.errors?.name ? "name-error" : undefined}
                />
                {actionData?.errors?.name && (
                  <div className="text-destructive text-sm pt-1" id="name-error">
                    {actionData.errors.name}
                  </div>
                )}
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  rows={3}
                />
              </div>
              
              <div>
                <Label htmlFor="content">Template Content (JSON)</Label>
                <Textarea
                  id="content"
                  name="content"
                  rows={15}
                  defaultValue={defaultContent}
                  required
                  aria-invalid={actionData?.errors?.content ? true : undefined}
                  aria-errormessage={actionData?.errors?.content ? "content-error" : undefined}
                />
                {actionData?.errors?.content && (
                  <div className="text-destructive text-sm pt-1" id="content-error">
                    {actionData.errors.content}
                  </div>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Enter the template content in JSON format. This will be used to generate offers.
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox id="isDefault" name="isDefault" />
                <Label htmlFor="isDefault" className="cursor-pointer">
                  Set as default template
                </Label>
              </div>
            </div>
            
            <div className="flex justify-end gap-2">
              <Button variant="outline" type="reset" disabled={isSubmitting}>
                Reset
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Creating..." : "Create Template"}
              </Button>
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}