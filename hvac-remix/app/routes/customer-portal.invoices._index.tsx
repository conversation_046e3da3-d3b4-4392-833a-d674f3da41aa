import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData, useSearchParams } from "@remix-run/react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { getCustomerPortalData } from "~/services/customer-portal.server";
import { getUser } from "~/session.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await getUser(request);
  
  if (!user || user.role !== "CUSTOMER") {
    throw new Response("Unauthorized", { status: 401 });
  }
  
  const customerResponse = await getCustomerPortalData(user.id);
  
  if (!customerResponse.success || !customerResponse.data) {
    throw new Response("Failed to load customer data", { status: 500 });
  }
  
  return json({ customer: customerResponse.data });
};

export default function CustomerPortalInvoices() {
  const { customer } = useLoaderData<typeof loader>();
  const [searchParams] = useSearchParams();
  const statusFilter = searchParams.get("status") || "all";
  
  // Filter invoices by status
  const filteredInvoices = statusFilter === "all"
    ? customer.invoices
    : customer.invoices.filter((invoice: any) => invoice.status === statusFilter);
  
  // Group invoices by status for the tabs
  const pendingInvoices = customer.invoices.filter((invoice: any) => invoice.status === "PENDING");
  const paidInvoices = customer.invoices.filter((invoice: any) => invoice.status === "PAID");
  const overdueInvoices = customer.invoices.filter((invoice: any) => invoice.status === "OVERDUE");
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Invoices</h1>
      </div>
      
      <Tabs defaultValue={statusFilter === "all" ? "all" : statusFilter}>
        <TabsList className="grid grid-cols-4 w-full">
          <TabsTrigger value="all" asChild>
            <Link to="/customer-portal/invoices">All ({customer.invoices.length})</Link>
          </TabsTrigger>
          <TabsTrigger value="PENDING" asChild>
            <Link to="/customer-portal/invoices?status=PENDING">Pending ({pendingInvoices.length})</Link>
          </TabsTrigger>
          <TabsTrigger value="PAID" asChild>
            <Link to="/customer-portal/invoices?status=PAID">Paid ({paidInvoices.length})</Link>
          </TabsTrigger>
          <TabsTrigger value="OVERDUE" asChild>
            <Link to="/customer-portal/invoices?status=OVERDUE">Overdue ({overdueInvoices.length})</Link>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="mt-6">
          {renderInvoicesList(customer.invoices)}
        </TabsContent>
        
        <TabsContent value="PENDING" className="mt-6">
          {renderInvoicesList(pendingInvoices)}
        </TabsContent>
        
        <TabsContent value="PAID" className="mt-6">
          {renderInvoicesList(paidInvoices)}
        </TabsContent>
        
        <TabsContent value="OVERDUE" className="mt-6">
          {renderInvoicesList(overdueInvoices)}
        </TabsContent>
      </Tabs>
    </div>
  );
}

function renderInvoicesList(invoices: any[]) {
  if (invoices.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Invoices Found</CardTitle>
          <CardDescription>
            No invoices match the selected filter.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  return (
    <div className="space-y-4">
      {invoices.map((invoice) => (
        <Card key={invoice.id}>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle>
                  Invoice #{invoice.invoiceNumber || invoice.id.substring(0, 8)}
                </CardTitle>
                <CardDescription>
                  {invoice.issueDate
                    ? `Issued on ${new Date(invoice.issueDate).toLocaleDateString()}`
                    : "No issue date"}
                </CardDescription>
              </div>
              <Badge 
                variant={
                  invoice.status === "PAID" ? "success" :
                  invoice.status === "PENDING" ? "warning" :
                  "destructive"
                }
              >
                {invoice.status}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Amount:</span>
                <span className="font-medium">${invoice.totalAmount?.toFixed(2) || "N/A"}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Due Date:</span>
                <span>
                  {invoice.dueDate
                    ? new Date(invoice.dueDate).toLocaleDateString()
                    : "N/A"}
                </span>
              </div>
              {invoice.serviceOrderId && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Service Order:</span>
                  <Link
                    to={`/customer-portal/service-orders/${invoice.serviceOrderId}`}
                    className="text-primary hover:underline"
                  >
                    View Service Order
                  </Link>
                </div>
              )}
            </div>
            
            <div className="mt-4 flex justify-end space-x-2">
              <Button asChild variant="outline" size="sm">
                <Link to={`/customer-portal/invoices/${invoice.id}`}>View Details</Link>
              </Button>
              
              {invoice.status === "PENDING" && (
                <Button asChild size="sm">
                  <Link to={`/customer-portal/invoices/${invoice.id}/pay`}>Pay Now</Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}