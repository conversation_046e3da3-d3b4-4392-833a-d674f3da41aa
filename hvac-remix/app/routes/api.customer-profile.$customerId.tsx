/**
 * 👤 CUSTOMER PROFILE API
 * 
 * Enhanced customer profile management API with comprehensive
 * customer data, preferences, and relationship information.
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { CustomerProfileManagementService } from "~/models/customer-profile-management.server";

export async function loader({ params }: LoaderFunctionArgs) {
  const { customerId } = params;

  if (!customerId) {
    return json({ error: 'Customer ID required' }, { status: 400 });
  }

  try {
    // Get enhanced customer profile
    const customerProfile = await CustomerProfileManagementService.getEnhancedCustomerProfile(customerId);

    if (!customerProfile) {
      return json({ error: 'Customer not found' }, { status: 404 });
    }

    return json(customerProfile);

  } catch (error) {
    console.error('Error fetching customer profile:', error);
    return json(
      { error: 'Failed to fetch customer profile' },
      { status: 500 }
    );
  }
}

// Handle POST requests for customer profile actions
export async function action({ params, request }: LoaderFunctionArgs) {
  const { customerId } = params;

  if (!customerId) {
    return json({ error: 'Customer ID required' }, { status: 400 });
  }

  if (request.method !== 'POST') {
    return json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { action: actionType, data } = body;

    switch (actionType) {
      case 'update_basic_info':
        // Update customer basic information
        const updatedCustomer = await prisma.customer.update({
          where: { id: customerId },
          data: {
            name: data.name,
            email: data.email,
            phone: data.phone,
            address: data.address,
            city: data.city,
            state: data.state,
            zipCode: data.zipCode,
            type: data.type,
            notes: data.notes
          }
        });
        return json({ success: true, customer: updatedCustomer });

      case 'add_contact_person':
        // Add new contact person (would need contact persons table)
        const contactPerson = {
          id: `contact_${Date.now()}`,
          customerId,
          ...data,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        return json({ success: true, contactPerson });

      case 'update_preferences':
        // Update customer preferences (would need preferences table)
        const preferences = data.preferences.map((pref: any) => ({
          id: `pref_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          customerId,
          ...pref,
          createdAt: new Date(),
          updatedAt: new Date()
        }));
        return json({ success: true, preferences });

      case 'add_service_note':
        // Add service note (would need service notes table)
        const serviceNote = {
          id: `note_${Date.now()}`,
          customerId,
          userId: data.userId,
          noteType: data.noteType || 'GENERAL',
          title: data.title,
          content: data.content,
          isPrivate: data.isPrivate || false,
          isPinned: data.isPinned || false,
          tags: data.tags || [],
          createdAt: new Date(),
          updatedAt: new Date()
        };
        return json({ success: true, serviceNote });

      case 'update_business_hours':
        // Update business hours for commercial customers
        await prisma.customer.update({
          where: { id: customerId },
          data: {
            notes: `Business hours updated: ${JSON.stringify(data.businessHours)}`
          }
        });
        return json({ success: true });

      case 'set_preferred_technician':
        // Set preferred technician
        await prisma.customer.update({
          where: { id: customerId },
          data: {
            notes: `Preferred technician: ${data.technicianId}`
          }
        });
        return json({ success: true });

      case 'update_communication_preferences':
        // Update communication preferences
        const commPreferences = {
          preferredChannel: data.preferredChannel,
          preferredTime: data.preferredTime,
          frequency: data.frequency,
          optOut: data.optOut || []
        };
        
        await prisma.customer.update({
          where: { id: customerId },
          data: {
            notes: `Communication preferences: ${JSON.stringify(commPreferences)}`
          }
        });
        return json({ success: true, preferences: commPreferences });

      case 'add_relationship':
        // Add customer relationship (referral, related account, etc.)
        const relationship = {
          id: `rel_${Date.now()}`,
          fromCustomerId: customerId,
          toCustomerId: data.relatedCustomerId,
          relationshipType: data.relationshipType,
          notes: data.notes,
          createdAt: new Date()
        };
        return json({ success: true, relationship });

      case 'calculate_customer_value':
        // Recalculate customer lifetime value
        const customer = await prisma.customer.findUnique({
          where: { id: customerId },
          include: {
            invoices: { where: { paymentStatus: 'PAID' } },
            serviceOrders: true
          }
        });

        if (!customer) {
          return json({ error: 'Customer not found' }, { status: 404 });
        }

        const totalRevenue = customer.invoices.reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);
        const averageJobValue = customer.serviceOrders.length > 0 ? 
          totalRevenue / customer.serviceOrders.length : 0;
        
        // Simple CLV calculation (can be enhanced with more sophisticated models)
        const monthsSinceFirst = customer.serviceOrders.length > 0 ? 
          Math.max(1, Math.floor((Date.now() - customer.serviceOrders[customer.serviceOrders.length - 1].createdAt.getTime()) / (1000 * 60 * 60 * 24 * 30))) : 1;
        const monthlyValue = totalRevenue / monthsSinceFirst;
        const projectedLifetimeMonths = 36; // 3 years
        const customerLifetimeValue = monthlyValue * projectedLifetimeMonths;

        return json({
          success: true,
          customerValue: {
            totalRevenue,
            averageJobValue,
            monthlyValue,
            customerLifetimeValue,
            calculatedAt: new Date()
          }
        });

      case 'export_customer_data':
        // Export customer data (GDPR compliance)
        const exportData = await CustomerProfileManagementService.getEnhancedCustomerProfile(customerId);
        
        return json({
          success: true,
          exportData,
          exportedAt: new Date(),
          format: data.format || 'json'
        });

      default:
        return json({ error: 'Unknown action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Error processing customer profile action:', error);
    return json(
      { error: 'Failed to process action' },
      { status: 500 }
    );
  }
}
