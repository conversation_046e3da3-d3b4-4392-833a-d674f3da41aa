import { ArrowLeftIcon, DocumentTextIcon, PencilIcon, DocumentArrowDownIcon, PrinterIcon } from "@heroicons/react/24/outline";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { useRef, useState } from "react";
import { PrintableServiceReport } from "~/components/templates/printable-service-report";
import { prisma } from "~/db.server";
import { formatDate } from "~/lib/utils";
import { getCompanyInfoForPrintableDocuments } from "~/services/company-settings.server";
import { requireUserId } from "~/session.server";
import { generatePDFFromElement, savePDF } from "~/utils/pdf-generator";

export async function loader({ request, params }: LoaderFunctionArgs) {
  await requireUserId(request);
  const { reportId } = params;

  if (!reportId) {
    throw new Response("Service Report ID is required", { status: 400 });
  }

  const report = await prisma.serviceReport.findUnique({
    where: { id: reportId },
    include: {
      serviceOrder: {
        include: {
          customer: true,
          device: true,
        },
      },
    },
  });

  if (!report) {
    throw new Response("Service Report not found", { status: 404 });
  }

  // Get company info for printable documents
  const companyInfo = await getCompanyInfoForPrintableDocuments();

  return json({ report, companyInfo });
}

export default function ServiceReportDetails() {
  const { report, companyInfo } = useLoaderData<typeof loader>();
  const [isPrintModalOpen, setIsPrintModalOpen] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const printableReportRef = useRef<HTMLDivElement>(null);

  // Parse photo URLs if they exist
  const photoUrls = report.photoUrls ? JSON.parse(report.photoUrls) : [];

  // Handle PDF generation
  const handleGeneratePDF = async () => {
    if (!printableReportRef.current) return;

    try {
      setIsGeneratingPDF(true);
      const pdf = await generatePDFFromElement(printableReportRef.current, {
        filename: `service-report-${report.id}.pdf`,
        pageSize: 'A4',
        margin: { top: 10, right: 10, bottom: 10, left: 10 }
      });

      savePDF(pdf, `service-report-${report.id}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('An error occurred while generating the PDF. Please try again.');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Handle print
  const handlePrint = () => {
    setIsPrintModalOpen(true);
  };

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Link to="/service-reports" className="mr-4 text-gray-500 hover:text-gray-700">
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h2 className="text-xl font-semibold">
            {report.title}
          </h2>
        </div>
        <div className="flex space-x-2">
          <Link
            to={`/service-reports/${report.id}/edit`}
            className="flex items-center rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
          >
            <PencilIcon className="mr-2 h-5 w-5" />
            Edit
          </Link>
          <button
            onClick={handlePrint}
            className="flex items-center rounded-md bg-green-600 px-4 py-2 text-sm font-medium text-white hover:bg-green-700"
          >
            <PrinterIcon className="mr-2 h-5 w-5" />
            Print
          </button>
          <button
            onClick={handleGeneratePDF}
            disabled={isGeneratingPDF}
            className="flex items-center rounded-md bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 disabled:bg-purple-400"
          >
            <DocumentArrowDownIcon className="mr-2 h-5 w-5" />
            {isGeneratingPDF ? 'Generating...' : 'Download PDF'}
          </button>
          <Link
            to={`/service-reports/${report.id}/process`}
            className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
          >
            <DocumentTextIcon className="mr-2 h-5 w-5" />
            Process with OCR
          </Link>
        </div>
      </div>

      <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Service Report Details</h3>
        </div>
        <div className="grid grid-cols-1 gap-6 px-6 py-4 md:grid-cols-2">
          <div>
            <p className="text-sm font-medium text-gray-500">Service Order</p>
            <p className="mt-1 text-sm text-gray-900">
              <Link
                to={`/service-orders/${report.serviceOrder.id}`}
                className="text-blue-600 hover:text-blue-900"
              >
                {report.serviceOrder.title}
              </Link>
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Customer</p>
            <p className="mt-1 text-sm text-gray-900">
              <Link
                to={`/customers/${report.serviceOrder.customer.id}`}
                className="text-blue-600 hover:text-blue-900"
              >
                {report.serviceOrder.customer.name}
              </Link>
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Device</p>
            <p className="mt-1 text-sm text-gray-900">
              {report.serviceOrder.device ? (
                <Link
                  to={`/devices/${report.serviceOrder.device.id}`}
                  className="text-blue-600 hover:text-blue-900"
                >
                  {report.serviceOrder.device.name}
                </Link>
              ) : (
                "N/A"
              )}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Created</p>
            <p className="mt-1 text-sm text-gray-900">{formatDate(report.createdAt)}</p>
          </div>
        </div>
      </div>

      <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Work Details</h3>
        </div>
        <div className="px-6 py-4">
          <div className="mb-6">
            <p className="text-sm font-medium text-gray-500">Work Performed</p>
            <p className="mt-1 whitespace-pre-wrap text-sm text-gray-900">{report.workPerformed || "N/A"}</p>
          </div>
          <div className="mb-6">
            <p className="text-sm font-medium text-gray-500">Parts Used</p>
            <p className="mt-1 whitespace-pre-wrap text-sm text-gray-900">{report.partsUsed || "N/A"}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Recommendations</p>
            <p className="mt-1 whitespace-pre-wrap text-sm text-gray-900">{report.recommendations || "N/A"}</p>
          </div>
        </div>
      </div>

      {photoUrls.length > 0 && (
        <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <h3 className="text-lg font-medium text-gray-900">Photos</h3>
          </div>
          <div className="grid grid-cols-1 gap-4 p-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {photoUrls.map((url: string, index: number) => (
              <a
                key={index}
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="overflow-hidden rounded-lg border border-gray-200 shadow transition-transform hover:scale-105"
              >
                <img src={url} alt={`Service photo ${index + 1}`} className="h-48 w-full object-cover" />
              </a>
            ))}
          </div>
        </div>
      )}

      {(report.technicianSignatureUrl || report.customerSignatureUrl) && (
        <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
          <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
            <h3 className="text-lg font-medium text-gray-900">Signatures</h3>
          </div>
          <div className="grid grid-cols-1 gap-6 px-6 py-4 md:grid-cols-2">
            {report.technicianSignatureUrl && (
              <div>
                <p className="mb-2 text-sm font-medium text-gray-500">Technician Signature</p>
                <img
                  src={report.technicianSignatureUrl}
                  alt="Technician Signature"
                  className="max-h-32 rounded-md border border-gray-200"
                />
              </div>
            )}
            {report.customerSignatureUrl && (
              <div>
                <p className="mb-2 text-sm font-medium text-gray-500">Customer Signature</p>
                <img
                  src={report.customerSignatureUrl}
                  alt="Customer Signature"
                  className="max-h-32 rounded-md border border-gray-200"
                />
              </div>
            )}
            {report.signedAt && (
              <div className="col-span-full">
                <p className="text-sm font-medium text-gray-500">Signed At</p>
                <p className="mt-1 text-sm text-gray-900">{formatDate(report.signedAt)}</p>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">OCR Processing</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <p className="text-sm font-medium text-gray-500">Processing Status</p>
              <p className="mt-1">
                <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                  report.ocrProcessingStatus === "COMPLETED"
                    ? "bg-green-100 text-green-800"
                    : report.ocrProcessingStatus === "FAILED"
                    ? "bg-red-100 text-red-800"
                    : report.ocrProcessingStatus === "PROCESSING"
                    ? "bg-blue-100 text-blue-800"
                    : "bg-gray-100 text-gray-800"
                }`}>
                  {report.ocrProcessingStatus}
                </span>
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Processed At</p>
              <p className="mt-1 text-sm text-gray-900">
                {formatDate(report.ocrProcessedAt) || "N/A"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Print Modal */}
      {isPrintModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative max-h-[90vh] w-[800px] overflow-auto bg-white p-6 shadow-xl">
            <button
              onClick={() => setIsPrintModalOpen(false)}
              className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="mb-4 flex justify-between">
              <h2 className="text-xl font-bold">Print Service Report</h2>
              <button
                onClick={() => window.print()}
                className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
              >
                <PrinterIcon className="mr-2 h-5 w-5" />
                Print
              </button>
            </div>

            <PrintableServiceReport
              ref={printableReportRef}
              data={{
                id: report.id,
                title: report.title,
                description: report.description,
                workPerformed: report.workPerformed,
                partsUsed: report.partsUsed,
                recommendations: report.recommendations,
                technicianSignatureUrl: report.technicianSignatureUrl,
                customerSignatureUrl: report.customerSignatureUrl,
                signedAt: report.signedAt,
                photoUrls: photoUrls,
                createdAt: report.createdAt,
                serviceOrder: report.serviceOrder,
                companyInfo
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}