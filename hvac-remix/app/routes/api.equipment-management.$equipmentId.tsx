/**
 * 🔧 EQUIPMENT MANAGEMENT API
 * 
 * Comprehensive equipment management API with lifecycle tracking,
 * performance analytics, and maintenance scheduling.
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { EquipmentManagementService } from "~/models/equipment-management.server";

export async function loader({ params }: LoaderFunctionArgs) {
  const { equipmentId } = params;

  if (!equipmentId) {
    return json({ error: 'Equipment ID required' }, { status: 400 });
  }

  try {
    // Get comprehensive equipment record
    const equipmentRecord = await EquipmentManagementService.getComprehensiveEquipmentRecord(equipmentId);

    if (!equipmentRecord) {
      return json({ error: 'Equipment not found' }, { status: 404 });
    }

    return json(equipmentRecord);

  } catch (error) {
    console.error('Error fetching equipment record:', error);
    return json(
      { error: 'Failed to fetch equipment record' },
      { status: 500 }
    );
  }
}

// Handle POST requests for equipment management actions
export async function action({ params, request }: LoaderFunctionArgs) {
  const { equipmentId } = params;

  if (!equipmentId) {
    return json({ error: 'Equipment ID required' }, { status: 400 });
  }

  if (request.method !== 'POST') {
    return json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { action: actionType, data } = body;

    switch (actionType) {
      case 'update_equipment_info':
        // Update equipment basic information
        const updatedEquipment = await prisma.device.update({
          where: { id: equipmentId },
          data: {
            name: data.name,
            description: data.description,
            location: data.location,
            manufacturer: data.manufacturer,
            model: data.model,
            serialNumber: data.serialNumber,
            capacity: data.capacity,
            efficiency: data.efficiency,
            voltage: data.voltage,
            amperage: data.amperage,
            notes: data.notes
          }
        });
        return json({ success: true, equipment: updatedEquipment });

      case 'update_performance_metrics':
        // Update performance metrics (would integrate with IoT sensors)
        const performanceMetrics = await EquipmentManagementService.calculateCurrentPerformanceMetrics(equipmentId);
        
        // In production, save metrics to time-series database
        return json({ 
          success: true, 
          metrics: performanceMetrics,
          updatedAt: new Date()
        });

      case 'schedule_maintenance':
        // Schedule maintenance for equipment
        const maintenanceOrder = await prisma.serviceOrder.create({
          data: {
            title: data.title || 'Scheduled Maintenance',
            description: data.description || 'Preventive maintenance service',
            status: 'SCHEDULED',
            priority: data.priority || 'MEDIUM',
            type: 'MAINTENANCE',
            scheduledDate: new Date(data.scheduledDate),
            customerId: data.customerId,
            deviceId: equipmentId,
            userId: data.userId || 'system',
            estimatedDuration: data.estimatedDuration || 120
          }
        });

        // Update equipment next maintenance date
        await prisma.device.update({
          where: { id: equipmentId },
          data: {
            nextMaintenanceDate: new Date(data.scheduledDate)
          }
        });

        return json({ success: true, maintenanceOrder });

      case 'generate_maintenance_schedule':
        // Generate preventive maintenance schedule
        const schedules = await EquipmentManagementService.generatePreventiveMaintenanceSchedule(equipmentId);
        
        // In production, save schedules to database
        return json({ success: true, schedules });

      case 'analyze_equipment_health':
        // Analyze equipment health and predict failures
        const healthAnalysis = await EquipmentManagementService.analyzeEquipmentHealth(equipmentId);
        
        return json({ success: true, analysis: healthAnalysis });

      case 'update_warranty_info':
        // Update warranty information
        await prisma.device.update({
          where: { id: equipmentId },
          data: {
            warrantyStartDate: data.warrantyStartDate ? new Date(data.warrantyStartDate) : undefined,
            warrantyEndDate: data.warrantyEndDate ? new Date(data.warrantyEndDate) : undefined,
            notes: `Warranty updated: ${data.warrantyType} - ${data.terms}`
          }
        });
        return json({ success: true });

      case 'record_service_completion':
        // Record completed service
        const serviceOrder = await prisma.serviceOrder.findUnique({
          where: { id: data.serviceOrderId }
        });

        if (serviceOrder) {
          await prisma.serviceOrder.update({
            where: { id: data.serviceOrderId },
            data: {
              status: 'COMPLETED',
              completedDate: new Date(),
              actualDuration: data.actualDuration,
              notes: data.serviceNotes,
              customerRating: data.customerRating
            }
          });

          // Update equipment last maintenance date
          await prisma.device.update({
            where: { id: equipmentId },
            data: {
              lastMaintenanceDate: new Date()
            }
          });
        }

        return json({ success: true });

      case 'add_equipment_alert':
        // Add equipment alert/notification
        const alert = {
          id: `alert_${Date.now()}`,
          equipmentId,
          type: data.type,
          severity: data.severity,
          message: data.message,
          details: data.details || {},
          isResolved: false,
          createdAt: new Date()
        };

        // In production, save to alerts table and trigger notifications
        return json({ success: true, alert });

      case 'resolve_equipment_alert':
        // Resolve equipment alert
        const alertId = data.alertId;
        
        // In production, update alert in database
        return json({ 
          success: true, 
          alertId,
          resolvedAt: new Date(),
          resolvedBy: data.resolvedBy
        });

      case 'update_equipment_location':
        // Update equipment location
        await prisma.device.update({
          where: { id: equipmentId },
          data: {
            location: data.location,
            notes: `Location updated to: ${data.location}. ${data.notes || ''}`
          }
        });
        return json({ success: true });

      case 'decommission_equipment':
        // Decommission equipment
        await prisma.device.update({
          where: { id: equipmentId },
          data: {
            isActive: false,
            notes: `Decommissioned on ${new Date().toISOString()}. Reason: ${data.reason}`
          }
        });

        // Cancel any scheduled maintenance
        await prisma.serviceOrder.updateMany({
          where: {
            deviceId: equipmentId,
            status: 'SCHEDULED'
          },
          data: {
            status: 'CANCELLED',
            notes: 'Cancelled due to equipment decommissioning'
          }
        });

        return json({ success: true });

      case 'generate_qr_code':
        // Generate QR code for equipment
        const qrCodeData = {
          equipmentId,
          customerId: data.customerId,
          url: `${process.env.BASE_URL}/equipment/${equipmentId}`,
          generatedAt: new Date()
        };

        // In production, generate actual QR code image
        const qrCode = `data:image/svg+xml;base64,${Buffer.from(`<svg>QR Code for ${equipmentId}</svg>`).toString('base64')}`;

        return json({ 
          success: true, 
          qrCode,
          qrCodeData 
        });

      case 'export_equipment_data':
        // Export equipment data
        const equipmentData = await EquipmentManagementService.getComprehensiveEquipmentRecord(equipmentId);
        
        return json({
          success: true,
          exportData: equipmentData,
          exportedAt: new Date(),
          format: data.format || 'json'
        });

      case 'clone_equipment':
        // Clone equipment configuration for similar installations
        const originalEquipment = await prisma.device.findUnique({
          where: { id: equipmentId }
        });

        if (!originalEquipment) {
          return json({ error: 'Original equipment not found' }, { status: 404 });
        }

        const clonedEquipment = await prisma.device.create({
          data: {
            ...originalEquipment,
            id: undefined, // Let database generate new ID
            name: `${originalEquipment.name} (Copy)`,
            serialNumber: data.newSerialNumber,
            customerId: data.newCustomerId || originalEquipment.customerId,
            location: data.newLocation || originalEquipment.location,
            installationDate: data.installationDate ? new Date(data.installationDate) : new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });

        return json({ success: true, clonedEquipment });

      default:
        return json({ error: 'Unknown action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Error processing equipment management action:', error);
    return json(
      { error: 'Failed to process action' },
      { status: 500 }
    );
  }
}
