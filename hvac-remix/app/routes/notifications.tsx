import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { NotificationItem } from "~/components/molecules/notifications/notification-item";
import { Card } from "~/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "~/components/ui/tabs";
import { requireUser } from "~/session.server";
import type { Notification } from "~/types/shared";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const user = await requireUser(request);
  
  // Przykładowe dane powiadomień - w przyszłości będą pobierane z bazy danych
  const notifications: Notification[] = [
    {
      id: "1",
      type: "SERVICE_ORDER_CREATED",
      title: "Nowe zlecenie serwisowe",
      message: "Utworzono nowe zlecenie serwisowe #12345 dla k<PERSON><PERSON>",
      createdAt: new Date().toISOString(),
      priority: "medium",
      status: "unread",
      link: "/service-orders/12345",
      userId: user.id
    },
    {
      id: "2",
      type: "SERVICE_ORDER_UPDATED",
      title: "Aktualizacja zlecenia",
      message: "Zlecenie #12340 zostało zaktualizowane przez technika Adam Nowak",
      createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 godzina temu
      priority: "low",
      status: "unread",
      link: "/service-orders/12340",
      userId: user.id
    },
    {
      id: "3",
      type: "CALENDAR_REMINDER",
      title: "Przypomnienie o wizycie",
      message: "Jutro o 10:00 masz zaplanowaną wizytę u klienta Firma ABC",
      createdAt: new Date(Date.now() - 7200000).toISOString(), // 2 godziny temu
      priority: "high",
      status: "read",
      link: "/calendar/event123",
      userId: user.id
    },
    {
      id: "4",
      type: "DEVICE_ALERT",
      title: "Alert urządzenia",
      message: "Urządzenie LG-12345 zgłasza błąd: Niskie ciśnienie czynnika",
      createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 dzień temu
      priority: "high",
      status: "read",
      link: "/devices/LG-12345",
      userId: user.id
    },
    {
      id: "5",
      type: "SYSTEM_UPDATE",
      title: "Aktualizacja systemu",
      message: "System został zaktualizowany do wersji 2.5.0",
      createdAt: new Date(Date.now() - 172800000).toISOString(), // 2 dni temu
      priority: "low",
      status: "read",
      link: "/settings",
      userId: user.id
    }
  ];
  
  return json({
    user,
    notifications
  });
};

export default function NotificationsPage() {
  const { notifications } = useLoaderData<typeof loader>();
  
  const unreadNotifications = notifications.filter(n => n.status === 'unread');
  const readNotifications = notifications.filter(n => n.status === 'read');
  
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Powiadomienia</h1>
      
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="all">Wszystkie</TabsTrigger>
          <TabsTrigger value="unread">Nieprzeczytane ({unreadNotifications.length})</TabsTrigger>
          <TabsTrigger value="read">Przeczytane</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all">
          <Card className="p-4">
            <div className="space-y-2">
              {notifications.length > 0 ? (
                notifications.map(notification => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                  />
                ))
              ) : (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  Brak powiadomień
                </div>
              )}
            </div>
          </Card>
        </TabsContent>
        
        <TabsContent value="unread">
          <Card className="p-4">
            <div className="space-y-2">
              {unreadNotifications.length > 0 ? (
                unreadNotifications.map(notification => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                  />
                ))
              ) : (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  Brak nieprzeczytanych powiadomień
                </div>
              )}
            </div>
          </Card>
        </TabsContent>
        
        <TabsContent value="read">
          <Card className="p-4">
            <div className="space-y-2">
              {readNotifications.length > 0 ? (
                readNotifications.map(notification => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                  />
                ))
              ) : (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  Brak przeczytanych powiadomień
                </div>
              )}
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}