/**
 * 🚀 CUSTOMER LIFECYCLE METRICS API
 * 
 * Provides comprehensive customer lifecycle analytics and metrics
 * for the enhanced CRM dashboard.
 */

import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { 
  CustomerLifecycleService, 
  CustomerLifecycleStage, 
  CustomerSegment 
} from "~/models/customer-lifecycle.server";

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Get all customers with their related data
    const customers = await prisma.customer.findMany({
      include: {
        serviceOrders: {
          orderBy: { createdAt: 'desc' }
        },
        invoices: {
          where: { paymentStatus: 'PAID' }
        },
        devices: true
      }
    });

    // Calculate lifecycle stages for all customers
    const customerAnalytics = await Promise.all(
      customers.map(async (customer) => {
        const lifecycleStage = await CustomerLifecycleService.determineLifecycleStage(customer.id);
        const segment = await CustomerLifecycleService.segmentCustomer(customer.id);
        const healthScore = await CustomerLifecycleService.calculateHealthScore(customer.id);
        
        const totalRevenue = customer.invoices.reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);
        const averageJobValue = customer.serviceOrders.length > 0 ? 
          totalRevenue / customer.serviceOrders.length : 0;

        // Calculate customer lifetime value (simplified)
        const customerLifetimeValue = totalRevenue * 1.5; // Assuming 50% future value

        // Calculate churn risk (simplified)
        const now = new Date();
        const threeMonthsAgo = new Date(now.getTime() - 3 * 30 * 24 * 60 * 60 * 1000);
        const recentActivity = customer.serviceOrders.filter(so => so.createdAt > threeMonthsAgo).length;
        const churnRisk = Math.max(0, 100 - (recentActivity * 20 + healthScore.overallScore * 0.5));

        return {
          customerId: customer.id,
          lifecycleStage,
          segment,
          healthScore,
          totalRevenue,
          averageJobValue,
          customerLifetimeValue,
          churnRisk: Math.round(churnRisk),
          equipmentCount: customer.devices.length
        };
      })
    );

    // Calculate aggregate metrics
    const totalCustomers = customers.length;
    
    // Stage distribution
    const stageDistribution = customerAnalytics.reduce((acc, customer) => {
      acc[customer.lifecycleStage] = (acc[customer.lifecycleStage] || 0) + 1;
      return acc;
    }, {} as Record<CustomerLifecycleStage, number>);

    // Segment distribution
    const segmentDistribution = customerAnalytics.reduce((acc, customer) => {
      acc[customer.segment] = (acc[customer.segment] || 0) + 1;
      return acc;
    }, {} as Record<CustomerSegment, number>);

    // Average health score
    const averageHealthScore = customerAnalytics.length > 0 ? 
      Math.round(customerAnalytics.reduce((sum, c) => sum + c.healthScore.overallScore, 0) / customerAnalytics.length) : 0;

    // At-risk customers (churn risk > 70%)
    const atRiskCustomers = customerAnalytics.filter(c => c.churnRisk > 70).length;

    // Loyal customers
    const loyalCustomers = customerAnalytics.filter(c => 
      c.lifecycleStage === CustomerLifecycleStage.LOYAL_CUSTOMER
    ).length;

    // Total lifetime value
    const totalLifetimeValue = customerAnalytics.reduce((sum, c) => sum + c.customerLifetimeValue, 0);

    // Churn rate (simplified calculation)
    const churnedCustomers = customerAnalytics.filter(c => 
      c.lifecycleStage === CustomerLifecycleStage.CHURNED
    ).length;
    const churnRate = totalCustomers > 0 ? Math.round((churnedCustomers / totalCustomers) * 100) : 0;

    const metrics = {
      totalCustomers,
      stageDistribution,
      segmentDistribution,
      averageHealthScore,
      atRiskCustomers,
      loyalCustomers,
      totalLifetimeValue,
      churnRate
    };

    return json(metrics);

  } catch (error) {
    console.error('Error fetching customer lifecycle metrics:', error);
    return json(
      { error: 'Failed to fetch customer lifecycle metrics' },
      { status: 500 }
    );
  }
}

// Handle POST requests for triggering lifecycle updates
export async function action({ request }: LoaderFunctionArgs) {
  if (request.method !== 'POST') {
    return json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const body = await request.json();
    const { action: actionType, customerId } = body;

    switch (actionType) {
      case 'recalculate_health_score':
        if (!customerId) {
          return json({ error: 'Customer ID required' }, { status: 400 });
        }
        
        const healthScore = await CustomerLifecycleService.calculateHealthScore(customerId);
        return json({ healthScore });

      case 'update_lifecycle_stage':
        if (!customerId) {
          return json({ error: 'Customer ID required' }, { status: 400 });
        }
        
        const lifecycleStage = await CustomerLifecycleService.determineLifecycleStage(customerId);
        return json({ lifecycleStage });

      case 'segment_customer':
        if (!customerId) {
          return json({ error: 'Customer ID required' }, { status: 400 });
        }
        
        const segment = await CustomerLifecycleService.segmentCustomer(customerId);
        return json({ segment });

      case 'bulk_recalculate':
        // Trigger bulk recalculation for all customers (background job)
        // This would typically be handled by a job queue in production
        return json({ message: 'Bulk recalculation initiated' });

      default:
        return json({ error: 'Unknown action' }, { status: 400 });
    }

  } catch (error) {
    console.error('Error processing customer lifecycle action:', error);
    return json(
      { error: 'Failed to process action' },
      { status: 500 }
    );
  }
}
