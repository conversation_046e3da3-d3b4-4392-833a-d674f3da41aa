import { 
  ArchiveBoxIcon, 
  BuildingStorefrontIcon, 
  CubeIcon,
  MapPinIcon,
  ArrowTrendingUpIcon,
  ArrowPathIcon,
  ClockIcon,
  QrCodeIcon,
  ChartBarIcon,
  BeakerIcon
} from "@heroicons/react/24/outline";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, NavLink, Outlet, useLocation } from "@remix-run/react";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  // Make sure user is authenticated
  await requireUserId(request);
  return json({});
}

export default function InventoryLayout() {
  const location = useLocation();
  
  const navigation = [
    { name: "Dashboard", path: "/inventory", icon: ArrowTrendingUpIcon, exact: true },
    { name: "Parts", path: "/inventory/parts", icon: CubeIcon, exact: false },
    { name: "Advanced Management", path: "/inventory/advanced", icon: ArchiveBoxIcon, exact: false },
    { name: "Visual Map", path: "/inventory/visualmap", icon: MapPinIcon, exact: false },
    { name: "Reports", path: "/inventory/reports", icon: ChartBarIcon, exact: false },
    { name: "Transactions", path: "/inventory/transactions", icon: ArrowPathIcon, exact: false },
    { name: "Scanner", path: "/inventory/scanner", icon: QrCodeIcon, exact: false },
    { name: "AI Optimization", path: "/inventory/optimization", icon: BeakerIcon, exact: false },
    { name: "Suppliers", path: "/inventory/suppliers", icon: BuildingStorefrontIcon, exact: false },
    { name: "Locations", path: "/inventory/locations", icon: MapPinIcon, exact: false },
    { name: "Low Stock", path: "/inventory?lowStock=true", icon: ClockIcon, exact: false },
  ];
  
  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Inventory Management</h1>
        <p className="mt-2 text-gray-600">Track and manage your inventory, parts, suppliers, and more</p>
      </div>
      
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar */}
        <div className="w-full lg:w-64 space-y-1 bg-white rounded-lg border border-gray-200 p-4 shadow">
          <p className="text-xs font-medium uppercase tracking-wider text-gray-500 mb-3">Inventory</p>
          
          {navigation.map((item) => {
            const isActive = item.exact 
              ? location.pathname === item.path
              : location.pathname.startsWith(item.path.split('?')[0]);
              
            return (
              <NavLink
                key={item.name}
                to={item.path}
                className={({ isActive: linkActive }) =>
                  `flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                    isActive
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`
                }
              >
                <item.icon className="mr-3 h-5 w-5 flex-shrink-0" aria-hidden="true" />
                {item.name}
              </NavLink>
            );
          })}
          
          <div className="pt-6">
            <p className="text-xs font-medium uppercase tracking-wider text-gray-500 mb-3">Actions</p>
            
            <Link
              to="/inventory/parts/new"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-900"
            >
              <span className="mr-3 h-5 w-5 flex-shrink-0 text-gray-500">+</span>
              Add Part
            </Link>
            
            <Link
              to="/inventory/transactions"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-900"
            >
              <span className="mr-3 h-5 w-5 flex-shrink-0 text-gray-500">+</span>
              Add Transaction
            </Link>
            
            <Link
              to="/inventory/suppliers/new"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-900"
            >
              <span className="mr-3 h-5 w-5 flex-shrink-0 text-gray-500">+</span>
              Add Supplier
            </Link>
            
            <Link
              to="/inventory/locations/new"
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-100 hover:text-gray-900"
            >
              <span className="mr-3 h-5 w-5 flex-shrink-0 text-gray-500">+</span>
              Add Location
            </Link>
          </div>
        </div>
        
        {/* Main content */}
        <div className="flex-1">
          <Outlet />
        </div>
      </div>
    </div>
  );
}