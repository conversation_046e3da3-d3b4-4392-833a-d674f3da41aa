import { ArrowLeftIcon, DocumentTextIcon } from "@heroicons/react/24/outline";
import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { FileUpload } from "~/components/molecules/file-upload";
import { prisma } from "~/db.server";
import { uploadFile, validateOcrFile } from "~/services/ocr/file-upload.server";
import { processInvoice } from "~/services/ocr/ocr.server";
import { requireUserId } from "~/session.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  await requireUserId(request);
  const { invoiceId } = params;
  
  if (!invoiceId) {
    throw new Response("Invoice ID is required", { status: 400 });
  }
  
  const invoice = await prisma.invoice.findUnique({
    where: { id: invoiceId },
    include: {
      customer: true,
    },
  });
  
  if (!invoice) {
    throw new Response("Invoice not found", { status: 404 });
  }
  
  return json({ invoice });
}

export async function action({ request, params }: ActionFunctionArgs) {
  await requireUserId(request);
  const { invoiceId } = params;
  
  if (!invoiceId) {
    return json({ error: "Invoice ID is required" }, { status: 400 });
  }
  
  const formData = await request.formData();
  const invoiceFile = formData.get("invoiceFile") as File;
  
  if (!invoiceFile || invoiceFile.size === 0) {
    return json({ error: "Invoice file is required" }, { status: 400 });
  }
  
  if (!validateOcrFile(invoiceFile)) {
    return json({ 
      error: "Invalid file. Please upload a PDF or image file (JPEG, PNG, TIFF) under 10MB." 
    }, { status: 400 });
  }
  
  try {
    // Upload the file
    const fileUrl = await uploadFile(invoiceFile, "invoices");
    
    // Update the invoice with the original document URL
    await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        originalDocumentUrl: fileUrl,
      },
    });
    
    // Process the invoice with OCR
    await processInvoice(invoiceId, fileUrl);
    
    return redirect(`/invoices/${invoiceId}`);
  } catch (error) {
    console.error("Error processing invoice:", error);
    return json({ 
      error: "An error occurred while processing the invoice. Please try again." 
    }, { status: 500 });
  }
}

export default function ProcessInvoice() {
  const { invoice } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  
  const isSubmitting = navigation.state === "submitting";
  
  return (
    <div>
      <div className="mb-6 flex items-center">
        <Link to={`/invoices/${invoice.id}`} className="mr-4 text-gray-500 hover:text-gray-700">
          <ArrowLeftIcon className="h-5 w-5" />
        </Link>
        <h2 className="text-xl font-semibold">
          Process Invoice with OCR
        </h2>
      </div>
      
      <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Invoice Information</h3>
        </div>
        <div className="grid grid-cols-1 gap-6 px-6 py-4 md:grid-cols-2">
          <div>
            <p className="text-sm font-medium text-gray-500">Invoice Number</p>
            <p className="mt-1 text-sm text-gray-900">{invoice.invoiceNumber || "N/A"}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Customer</p>
            <p className="mt-1 text-sm text-gray-900">{invoice.customer.name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Current OCR Status</p>
            <p className="mt-1">
              <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                invoice.ocrProcessingStatus === "COMPLETED" 
                  ? "bg-green-100 text-green-800" 
                  : invoice.ocrProcessingStatus === "FAILED" 
                  ? "bg-red-100 text-red-800" 
                  : invoice.ocrProcessingStatus === "PROCESSING" 
                  ? "bg-blue-100 text-blue-800" 
                  : "bg-gray-100 text-gray-800"
              }`}>
                {invoice.ocrProcessingStatus}
              </span>
            </p>
          </div>
        </div>
      </div>
      
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Upload Invoice Document</h3>
        </div>
        <div className="px-6 py-4">
          <Form method="post" encType="multipart/form-data">
            <div className="mb-6">
              <FileUpload
                name="invoiceFile"
                label="Invoice Document"
                accept="application/pdf,image/jpeg,image/png,image/tiff"
                required
                description="Upload a PDF or image of the invoice"
                error={actionData?.error}
              />
            </div>
            
            <div className="mt-6 flex justify-end">
              <Link
                to={`/invoices/${invoice.id}`}
                className="mr-4 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
              >
                {isSubmitting ? (
                  <>Processing...</>
                ) : (
                  <>
                    <DocumentTextIcon className="mr-2 h-5 w-5" />
                    Process with OCR
                  </>
                )}
              </button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
}