import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, Form } from "@remix-run/react";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { Card } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Switch } from "~/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { getAdminSettings, updateAdminSettings } from "~/models/admin-settings.server";

// Dummy loader/action for demonstration; replace with real logic as needed
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const settings = await getAdminSettings();
  return json({ settings });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const settings = {
    maintenanceMode: formData.has("maintenanceMode"),
    logLevel: String(formData.get("logLevel") || "info"),
    calendarIntegration: formData.has("calendarIntegration"),
    emailIntegration: formData.has("emailIntegration"),
    externalApi: String(formData.get("externalApi") || "none"),
    enablePredictive: formData.has("enablePredictive"),
    enableOffline: formData.has("enableOffline"),
    enableAdvancedReports: formData.has("enableAdvancedReports"),
    dataRetention: Number(formData.get("dataRetention") || 90),
    apiTimeout: Number(formData.get("apiTimeout") || 30000),
  };
  await updateAdminSettings(settings);
  return json({ success: true, message: "Settings saved successfully!" });
};

export default function AdminSettingsDashboard() {
  const { settings } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [formState, setFormState] = useState({
    maintenanceMode: settings.maintenanceMode,
    logLevel: settings.logLevel,
    calendarIntegration: settings.calendarIntegration,
    emailIntegration: settings.emailIntegration,
    externalApi: settings.externalApi,
    enablePredictive: settings.enablePredictive,
    enableOffline: settings.enableOffline,
    enableAdvancedReports: settings.enableAdvancedReports,
    dataRetention: settings.dataRetention,
    apiTimeout: settings.apiTimeout,
  });

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Admin Settings Dashboard</h1>
      {actionData?.message && (
        <div className={`p-4 mb-6 rounded-md ${actionData.success ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'}`}>
          {actionData.message}
        </div>
      )}
      <Form method="post">
        <Tabs defaultValue="system" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="system">System</TabsTrigger>
            <TabsTrigger value="integrations">Integrations</TabsTrigger>
            <TabsTrigger value="features">Feature Toggles</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
            <TabsTrigger value="importexport">Export/Import</TabsTrigger>
          </TabsList>

          <TabsContent value="system">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">System Settings</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
                  <Switch id="maintenanceMode" name="maintenanceMode" checked={formState.maintenanceMode} onCheckedChange={v => setFormState(f => ({...f, maintenanceMode: v}))} />
                </div>
                <div>
                  <Label htmlFor="logLevel">Log Level</Label>
                  <Select name="logLevel" value={formState.logLevel} onValueChange={v => setFormState(f => ({...f, logLevel: v}))}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select log level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="debug">Debug</SelectItem>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="warn">Warn</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="integrations">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Integrations</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="calendarIntegration">Calendar Integration</Label>
                  <Switch id="calendarIntegration" name="calendarIntegration" checked={formState.calendarIntegration} onCheckedChange={v => setFormState(f => ({...f, calendarIntegration: v}))} />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="emailIntegration">Email Integration</Label>
                  <Switch id="emailIntegration" name="emailIntegration" checked={formState.emailIntegration} onCheckedChange={v => setFormState(f => ({...f, emailIntegration: v}))} />
                </div>
                <div>
                  <Label htmlFor="externalApi">External API</Label>
                  <Select name="externalApi" value={formState.externalApi} onValueChange={v => setFormState(f => ({...f, externalApi: v}))}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select API" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="bielik">Bielik</SelectItem>
                      <SelectItem value="openai">OpenAI</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="features">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Feature Toggles</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="enablePredictive">Predictive Maintenance</Label>
                  <Switch id="enablePredictive" name="enablePredictive" checked={formState.enablePredictive} onCheckedChange={v => setFormState(f => ({...f, enablePredictive: v}))} />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="enableOffline">Offline Mode</Label>
                  <Switch id="enableOffline" name="enableOffline" checked={formState.enableOffline} onCheckedChange={v => setFormState(f => ({...f, enableOffline: v}))} />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="enableAdvancedReports">Advanced Reports</Label>
                  <Switch id="enableAdvancedReports" name="enableAdvancedReports" checked={formState.enableAdvancedReports} onCheckedChange={v => setFormState(f => ({...f, enableAdvancedReports: v}))} />
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="advanced">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Advanced Settings</h2>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="dataRetention">Data Retention (days)</Label>
                  <input type="number" id="dataRetention" name="dataRetention" className="w-full border rounded p-2" value={formState.dataRetention} min={1} onChange={e => setFormState(f => ({...f, dataRetention: Number(e.target.value)}))} />
                </div>
                <div>
                  <Label htmlFor="apiTimeout">API Timeout (ms)</Label>
                  <input type="number" id="apiTimeout" name="apiTimeout" className="w-full border rounded p-2" value={formState.apiTimeout} min={1000} onChange={e => setFormState(f => ({...f, apiTimeout: Number(e.target.value)}))} />
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="importexport">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Export / Import Settings</h2>
              <div className="space-y-4">
                <Button variant="outline" type="button">Export Settings</Button>
                <Button variant="outline" type="button">Import Settings</Button>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
        <div className="mt-8 flex justify-end">
          <Button type="submit">Save All Settings</Button>
        </div>
      </Form>
    </div>
  );
}
