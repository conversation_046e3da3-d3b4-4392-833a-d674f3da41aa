import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData, useSearchParams } from "@remix-run/react";
import { ArrowLeft, Calendar } from "lucide-react";
import { DataExplorationVisualization } from "~/components/organisms/DataExplorationVisualization";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { getDataExplorationData } from "~/services/visualization.service";
import { requireUserId } from "~/session.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  const url = new URL(request.url);
  
  // Parse date filters
  const startDateStr = url.searchParams.get("startDate");
  const endDateStr = url.searchParams.get("endDate");
  
  const startDate = startDateStr ? new Date(startDateStr) : undefined;
  const endDate = endDateStr ? new Date(endDateStr) : undefined;
  
  const visualizationData = await getDataExplorationData({
    userId,
    startDate,
    endDate,
  });
  
  return json({ visualizationData });
};

export default function DataExplorationPage() {
  const { visualizationData } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const startDate = searchParams.get("startDate") || "";
  const endDate = searchParams.get("endDate") || "";
  
  const handleFilterSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    const newStartDate = formData.get("startDate") as string;
    const newEndDate = formData.get("endDate") as string;
    
    const newParams = new URLSearchParams(searchParams);
    
    if (newStartDate) {
      newParams.set("startDate", newStartDate);
    } else {
      newParams.delete("startDate");
    }
    
    if (newEndDate) {
      newParams.set("endDate", newEndDate);
    } else {
      newParams.delete("endDate");
    }
    
    setSearchParams(newParams);
  };
  
  const clearFilters = () => {
    const newParams = new URLSearchParams();
    setSearchParams(newParams);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Link to="/visualizations">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Visualizations
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Interactive Data Exploration</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Date Range Filter
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleFilterSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  name="startDate"
                  type="date"
                  defaultValue={startDate}
                />
              </div>
              <div>
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  name="endDate"
                  type="date"
                  defaultValue={endDate}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={clearFilters}>
                Clear Filters
              </Button>
              <Button type="submit">
                Apply Filters
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      
      <DataExplorationVisualization
        serviceOrdersByMonth={visualizationData.serviceOrdersByMonth}
        topCustomers={visualizationData.topCustomers}
        topDevices={visualizationData.topDevices}
        serviceOrderTypes={visualizationData.serviceOrderTypes}
      />
    </div>
  );
}