import { PrinterIcon, DocumentArrowDownIcon } from "@heroicons/react/24/outline";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useRef } from "react";
import { PrintableServiceReport } from "~/components/templates/printable-service-report";
import { Button } from "~/components/ui/button";
import { getCompanyInfoForPrintableDocuments } from "~/services/company-settings.server";
import { requireUserId } from "~/session.server";
import { generatePDFFromElement, savePDF } from "~/utils/pdf-generator";

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  const companyInfo = await getCompanyInfoForPrintableDocuments();
  
  return json({ companyInfo });
}

export default function PreviewServiceReportPage() {
  const { companyInfo } = useLoaderData<typeof loader>();
  const printableReportRef = useRef<HTMLDivElement>(null);
  
  // Sample service report data for preview
  const sampleReport = {
    id: "sample-report",
    title: "HVAC System Maintenance Report",
    description: "Annual maintenance service for HVAC system",
    workPerformed: `1. Inspected all components of the HVAC system
2. Cleaned air filters and replaced as needed
3. Checked refrigerant levels and recharged
4. Tested thermostat functionality
5. Inspected ductwork for leaks
6. Cleaned condenser coils
7. Checked electrical connections
8. Lubricated moving parts`,
    partsUsed: `- Air filter (2x)
- Refrigerant (500g)
- Thermostat batteries
- Condenser fan belt`,
    recommendations: `1. Consider replacing the air handler unit within the next 12 months due to age and efficiency concerns.
2. Schedule duct cleaning service as dust accumulation was observed.
3. Install a programmable thermostat to improve energy efficiency.`,
    technicianSignatureUrl: "https://via.placeholder.com/300x100?text=Technician+Signature",
    customerSignatureUrl: "https://via.placeholder.com/300x100?text=Customer+Signature",
    signedAt: new Date().toISOString(),
    photoUrls: [
      "https://via.placeholder.com/400x300?text=HVAC+System+Photo+1",
      "https://via.placeholder.com/400x300?text=HVAC+System+Photo+2",
      "https://via.placeholder.com/400x300?text=HVAC+System+Photo+3",
      "https://via.placeholder.com/400x300?text=HVAC+System+Photo+4"
    ],
    createdAt: new Date().toISOString(),
    serviceOrder: {
      id: "so1",
      title: "Annual HVAC Maintenance",
      status: "COMPLETED",
      priority: "MEDIUM",
      type: "MAINTENANCE",
      scheduledDate: new Date().toISOString(),
      completedDate: new Date().toISOString(),
      customer: {
        id: "cust1",
        name: "Sample Customer",
        email: "<EMAIL>",
        phone: "+48 ***********",
        address: "123 Customer Street",
        city: "Warsaw",
        postalCode: "00-001",
        country: "Poland"
      },
      device: {
        id: "dev1",
        name: "Central Air Conditioning System",
        model: "CoolMax 5000",
        serialNumber: "CM5000-12345",
        manufacturer: "CoolTech",
        type: "Split System"
      }
    },
    companyInfo
  };

  // Handle print
  const handlePrint = () => {
    window.print();
  };

  // Handle PDF generation
  const handleGeneratePDF = async () => {
    if (!printableReportRef.current) return;

    try {
      const pdf = await generatePDFFromElement(printableReportRef.current, {
        filename: "sample-service-report.pdf",
        pageSize: 'A4',
        margin: { top: 10, right: 10, bottom: 10, left: 10 }
      });

      savePDF(pdf, "sample-service-report.pdf");
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('An error occurred while generating the PDF. Please try again.');
    }
  };

  return (
    <div className="mx-auto max-w-4xl">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Podgląd raportu serwisowego</h1>
        <div className="flex space-x-2">
          <Button
            onClick={handlePrint}
            className="flex items-center"
          >
            <PrinterIcon className="mr-2 h-5 w-5" />
            Drukuj
          </Button>
          <Button
            onClick={handleGeneratePDF}
            className="flex items-center"
          >
            <DocumentArrowDownIcon className="mr-2 h-5 w-5" />
            Pobierz PDF
          </Button>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <PrintableServiceReport
          ref={printableReportRef}
          data={sampleReport}
        />
      </div>
    </div>
  );
}