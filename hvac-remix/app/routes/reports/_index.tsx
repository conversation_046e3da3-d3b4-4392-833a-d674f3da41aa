import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { <PERSON>, useLoaderData } from "@remix-run/react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FileText, TrendingUp, Users, Package } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { getReportsByUserId } from "~/models/report.server";
import { requireUserId } from "~/session.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  
  // Get user's saved reports
  const savedReports = await getReportsByUserId(userId);
  
  return json({ savedReports });
};

export default function ReportsIndexPage() {
  const { savedReports } = useLoaderData<typeof loader>();
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Reports</h1>
        <Link 
          to="/reports/new" 
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          Create New Report
        </Link>
      </div>
      
      {savedReports.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Saved Reports</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {savedReports.map((report) => (
              <Link key={report.id} to={`/reports/${report.id}`} className="block">
                <Card className="h-full hover:shadow-md transition-shadow">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">{report.name}</CardTitle>
                    <CardDescription>
                      {report.description || "No description"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">
                        {new Date(report.createdAt).toLocaleDateString()}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                        {report.type}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      )}
      
      <h2 className="text-xl font-semibold mb-4">Report Categories</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link to="/reports/financial" className="block h-full">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Financial Reports
              </CardTitle>
              <CardDescription>
                Revenue, profitability, and financial performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Analyze revenue by service type, customer profitability, cost analysis,
                and monthly financial trends to optimize your business performance.
              </p>
            </CardContent>
          </Card>
        </Link>
        
        <Link to="/reports/operational" className="block h-full">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart className="h-5 w-5 mr-2" />
                Operational Reports
              </CardTitle>
              <CardDescription>
                Service order metrics and technician performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Track service order completion rates, technician performance metrics,
                service type distribution, and monthly operational trends.
              </p>
            </CardContent>
          </Card>
        </Link>
        
        <Link to="/reports/customer" className="block h-full">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Customer Analytics
              </CardTitle>
              <CardDescription>
                Customer engagement and satisfaction metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Analyze customer acquisition, retention, lifetime value, service history,
                and satisfaction scores to improve customer relationships.
              </p>
            </CardContent>
          </Card>
        </Link>
        
        <Link to="/reports/inventory" className="block h-full">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="h-5 w-5 mr-2" />
                Inventory Reports
              </CardTitle>
              <CardDescription>
                Inventory usage, stock levels, and parts analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Track inventory usage by service type, stock levels, reorder recommendations,
                and parts performance to optimize your inventory management.
              </p>
            </CardContent>
          </Card>
        </Link>
        
        <Link to="/reports/custom" className="block h-full">
          <Card className="h-full transition-all hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Custom Reports
              </CardTitle>
              <CardDescription>
                Build your own custom reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Create custom reports with your own metrics, filters, and visualizations
                to meet your specific business needs.
              </p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
}
