import { json, redirect, type ActionFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useNavigation, Link } from "@remix-run/react";
import { useEffect, useRef } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { createCustomer } from "~/services/customer.service";
import { requireUserId } from "~/session.server";

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  
  const name = formData.get("name") as string;
  const email = formData.get("email") as string;
  const phone = formData.get("phone") as string;
  const address = formData.get("address") as string;
  const city = formData.get("city") as string;
  const postalCode = formData.get("postalCode") as string;
  const country = formData.get("country") as string;
  const notes = formData.get("notes") as string;
  
  // Validate required fields
  const errors = {
    name: name ? null : "Name is required",
  };
  
  const hasErrors = Object.values(errors).some(
    (errorMessage) => errorMessage !== null
  );
  
  if (hasErrors) {
    return json({ errors, values: Object.fromEntries(formData) });
  }
  
  // Create customer
  const customerResponse = await createCustomer(
    {
      name,
      email: email || null,
      phone: phone || null,
      address: address || null,
      city: city || null,
      postalCode: postalCode || null,
      country: country || null,
      notes: notes || null,
    },
    userId
  );
  
  if (!customerResponse.success) {
    return json({
      errors: {
        ...errors,
        form: customerResponse.error || "Failed to create customer",
      },
      values: Object.fromEntries(formData),
    });
  }
  
  return redirect(`/customers/${customerResponse.data?.id}`);
}

export default function NewCustomerPage() {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  const nameRef = useRef<HTMLInputElement>(null);
  
  // Focus on the name field when there's an error
  useEffect(() => {
    if (actionData?.errors?.name) {
      nameRef.current?.focus();
    }
  }, [actionData]);
  
  return (
    <div className="container py-8">
      <div className="mb-6">
        <Link to="/customers" className="text-blue-500 hover:underline">
          ← Back to Customers
        </Link>
      </div>
      
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Add New Customer</CardTitle>
          <CardDescription>
            Create a new customer record in the system
          </CardDescription>
        </CardHeader>
        
        <Form method="post">
          <CardContent className="space-y-4">
            {/* Form error message */}
            {actionData?.errors?.form && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {actionData.errors.form}
              </div>
            )}
            
            {/* Name field */}
            <div className="space-y-2">
              <Label htmlFor="name">
                Name <span className="text-red-500">*</span>
              </Label>
              <Input
                ref={nameRef}
                id="name"
                name="name"
                defaultValue={actionData?.values?.name as string}
                aria-invalid={actionData?.errors?.name ? true : undefined}
                aria-errormessage={
                  actionData?.errors?.name ? "name-error" : undefined
                }
              />
              {actionData?.errors?.name && (
                <p className="text-red-500 text-sm" id="name-error">
                  {actionData.errors.name}
                </p>
              )}
            </div>
            
            {/* Email field */}
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                defaultValue={actionData?.values?.email as string}
              />
            </div>
            
            {/* Phone field */}
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                name="phone"
                defaultValue={actionData?.values?.phone as string}
              />
            </div>
            
            {/* Address field */}
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                name="address"
                defaultValue={actionData?.values?.address as string}
              />
            </div>
            
            {/* City field */}
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                name="city"
                defaultValue={actionData?.values?.city as string}
              />
            </div>
            
            {/* Postal Code field */}
            <div className="space-y-2">
              <Label htmlFor="postalCode">Postal Code</Label>
              <Input
                id="postalCode"
                name="postalCode"
                defaultValue={actionData?.values?.postalCode as string}
              />
            </div>
            
            {/* Country field */}
            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Input
                id="country"
                name="country"
                defaultValue={actionData?.values?.country as string}
              />
            </div>
            
            {/* Notes field */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                rows={4}
                defaultValue={actionData?.values?.notes as string}
              />
            </div>
          </CardContent>
          
          <CardFooter className="flex justify-between">
            <Link to="/customers">
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Customer"}
            </Button>
          </CardFooter>
        </Form>
      </Card>
    </div>
  );
}
