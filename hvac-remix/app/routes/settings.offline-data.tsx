import type { MetaFunction } from "@remix-run/node";
import { <PERSON> } from "@remix-run/react";
import { 
  <PERSON><PERSON>ex<PERSON>, 
  <PERSON>, 
  <PERSON>ch, 
  Trash2, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Al<PERSON>Triangle 
} from "lucide-react";
import { useState, useEffect } from "react";
import { PageHeader } from "~/components/molecules/page-header";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "~/components/ui/alert-dialog";
import { Button } from "~/components/ui/button";
import { Card } from "~/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "~/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { 
  getAllOfflineData, 
  deleteOfflineData, 
  STORES 
} from "~/utils/offline-storage";

export const meta: MetaFunction = () => {
  return [
    { title: "Offline Data - HVAC CRM" },
    { name: "description", content: "Manage your offline data" },
  ];
};

export default function OfflineDataPage() {
  const [loading, setLoading] = useState(true);
  const [offlineData, setOfflineData] = useState<Record<string, any[]>>({
    [STORES.SERVICE_ORDERS]: [],
    [STORES.SERVICE_REPORTS]: [],
    [STORES.CUSTOMERS]: [],
    [STORES.DEVICES]: [],
  });
  const [deleting, setDeleting] = useState<string | null>(null);
  
  // Load offline data
  useEffect(() => {
    const loadOfflineData = async () => {
      try {
        setLoading(true);
        
        const serviceOrders = await getAllOfflineData(STORES.SERVICE_ORDERS);
        const serviceReports = await getAllOfflineData(STORES.SERVICE_REPORTS);
        const customers = await getAllOfflineData(STORES.CUSTOMERS);
        const devices = await getAllOfflineData(STORES.DEVICES);
        
        setOfflineData({
          [STORES.SERVICE_ORDERS]: serviceOrders,
          [STORES.SERVICE_REPORTS]: serviceReports,
          [STORES.CUSTOMERS]: customers,
          [STORES.DEVICES]: devices,
        });
      } catch (error) {
        console.error('Error loading offline data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadOfflineData();
  }, []);
  
  // Delete an offline item
  const handleDelete = async (storeName: string, id: string) => {
    try {
      setDeleting(id);
      await deleteOfflineData(storeName, id);
      
      // Update state
      setOfflineData(prev => ({
        ...prev,
        [storeName]: prev[storeName].filter(item => item.offlineId !== id)
      }));
    } catch (error) {
      console.error('Error deleting offline data:', error);
    } finally {
      setDeleting(null);
    }
  };
  
  // Clear all offline data for a store
  const handleClearStore = async (storeName: string) => {
    try {
      setDeleting(storeName);
      
      // Delete each item individually
      for (const item of offlineData[storeName]) {
        await deleteOfflineData(storeName, item.offlineId);
      }
      
      // Update state
      setOfflineData(prev => ({
        ...prev,
        [storeName]: []
      }));
    } catch (error) {
      console.error('Error clearing offline data:', error);
    } finally {
      setDeleting(null);
    }
  };
  
  if (loading) {
    return (
      <div className="container py-8">
        <PageHeader
          title="Offline Data"
          description="Manage your offline data"
          backLink="/settings"
        />
        
        <div className="flex justify-center items-center p-8">
          <RefreshCw className="animate-spin h-8 w-8 text-primary" />
          <span className="ml-2">Loading offline data...</span>
        </div>
      </div>
    );
  }
  
  const totalItems = Object.values(offlineData).reduce(
    (sum, items) => sum + items.length, 
    0
  );
  
  return (
    <div className="container py-8">
      <PageHeader
        title="Offline Data"
        description="Manage your offline data"
        backLink="/settings"
      />
      
      <div className="mt-8">
        <Card className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Offline Data</h2>
            
            <div className="flex space-x-2">
              <Button asChild variant="outline">
                <Link to="/settings/sync-status">Sync Data</Link>
              </Button>
              
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button 
                    variant="destructive" 
                    disabled={totalItems === 0}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Clear All
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will permanently delete all offline data. This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => {
                        Object.keys(offlineData).forEach(storeName => {
                          handleClearStore(storeName);
                        });
                      }}
                      className="bg-red-500 hover:bg-red-600"
                    >
                      Delete All
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
          
          {totalItems === 0 ? (
            <div className="flex flex-col items-center justify-center py-8">
              <AlertTriangle className="h-12 w-12 text-amber-500 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Offline Data</h3>
              <p className="text-gray-500 dark:text-gray-400 text-center max-w-md">
                You don't have any data stored offline. When you work offline, your data will be stored here until it can be synchronized with the server.
              </p>
            </div>
          ) : (
            <Tabs defaultValue={STORES.SERVICE_ORDERS} className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value={STORES.SERVICE_ORDERS} className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Service Orders ({offlineData[STORES.SERVICE_ORDERS].length})
                </TabsTrigger>
                <TabsTrigger value={STORES.SERVICE_REPORTS} className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Service Reports ({offlineData[STORES.SERVICE_REPORTS].length})
                </TabsTrigger>
                <TabsTrigger value={STORES.CUSTOMERS} className="flex items-center">
                  <Users className="mr-2 h-4 w-4" />
                  Customers ({offlineData[STORES.CUSTOMERS].length})
                </TabsTrigger>
                <TabsTrigger value={STORES.DEVICES} className="flex items-center">
                  <Wrench className="mr-2 h-4 w-4" />
                  Devices ({offlineData[STORES.DEVICES].length})
                </TabsTrigger>
              </TabsList>
              
              {Object.entries(offlineData).map(([storeName, items]) => (
                <TabsContent key={storeName} value={storeName} className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">
                      {storeName === STORES.SERVICE_ORDERS ? 'Service Orders' :
                       storeName === STORES.SERVICE_REPORTS ? 'Service Reports' :
                       storeName === STORES.CUSTOMERS ? 'Customers' :
                       storeName === STORES.DEVICES ? 'Devices' : storeName}
                    </h3>
                    
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm"
                          disabled={items.length === 0}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Clear All
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will permanently delete all offline {
                              storeName === STORES.SERVICE_ORDERS ? 'service orders' :
                              storeName === STORES.SERVICE_REPORTS ? 'service reports' :
                              storeName === STORES.CUSTOMERS ? 'customers' :
                              storeName === STORES.DEVICES ? 'devices' : 'data'
                            }. This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleClearStore(storeName)}
                            className="bg-red-500 hover:bg-red-600"
                          >
                            Delete All
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                  
                  {items.length === 0 ? (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                      No offline data available
                    </div>
                  ) : (
                    <div className="border rounded-md overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ID</TableHead>
                            <TableHead>Name/Title</TableHead>
                            <TableHead>Created</TableHead>
                            <TableHead className="w-[100px]">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {items.map(item => (
                            <TableRow key={item.offlineId}>
                              <TableCell className="font-mono text-xs">
                                {item.offlineId}
                              </TableCell>
                              <TableCell>
                                {item.name || item.title || item.description || 'Unnamed'}
                              </TableCell>
                              <TableCell>
                                {new Date(item.createdAt).toLocaleString()}
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDelete(storeName, item.offlineId)}
                                  disabled={deleting === item.offlineId}
                                >
                                  {deleting === item.offlineId ? (
                                    <RefreshCw className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                  )}
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </TabsContent>
              ))}
            </Tabs>
          )}
        </Card>
      </div>
    </div>
  );
}
