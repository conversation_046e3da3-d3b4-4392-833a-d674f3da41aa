import { json, redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { OfferForm } from "~/components/organisms/OfferForm";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { prisma } from "~/db.server";
import { getOfferTemplates } from "~/services/offer-template.service";
import { createOffer } from "~/services/offer.service";
import { requireUserId } from "~/session.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const userId = await requireUserId(request);
  
  const customers = await prisma.customer.findMany({
    where: { userId },
    select: { id: true, name: true },
    orderBy: { name: "asc" },
  });
  
  const templates = await getOfferTemplates();
  
  return json({ customers, templates });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  
  const title = formData.get("title") as string;
  const description = formData.get("description") as string;
  const customerId = formData.get("customerId") as string;
  const templateId = formData.get("templateId") as string;
  const totalAmount = parseFloat(formData.get("totalAmount") as string);
  const taxAmount = formData.get("taxAmount") ? parseFloat(formData.get("taxAmount") as string) : undefined;
  const discountAmount = formData.get("discountAmount") ? parseFloat(formData.get("discountAmount") as string) : undefined;
  const validUntilStr = formData.get("validUntil") as string;
  const validUntil = validUntilStr ? new Date(validUntilStr) : undefined;
  const notes = formData.get("notes") as string;
  
  const errors: Record<string, string> = {};
  
  if (!title) errors.title = "Title is required";
  if (!customerId) errors.customerId = "Customer is required";
  if (isNaN(totalAmount)) errors.totalAmount = "Total amount must be a number";
  
  if (Object.keys(errors).length > 0) {
    return json({ errors });
  }
  
  // For now, create an offer without items
  // In a real implementation, you would handle items in a multi-step form
  const offer = await createOffer({
    title,
    description,
    customerId,
    userId,
    templateId: templateId || undefined,
    totalAmount,
    taxAmount,
    discountAmount,
    validUntil,
    notes,
    items: [],
  });
  
  return redirect(`/offers/${offer.id}`);
};

export default function NewOfferPage() {
  const { customers, templates } = useLoaderData<typeof loader>();
  
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Create New Offer</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>Offer Details</CardTitle>
        </CardHeader>
        <CardContent>
          <OfferForm
            customers={customers}
            templates={templates}
            mode="create"
          />
        </CardContent>
      </Card>
    </div>
  );
}