import { json, type ActionFunctionArgs } from "@remix-run/node";
import { prisma } from "~/db.server";
import { requireUserId } from "~/session.server";

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  
  // Only allow POST requests
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }
  
  try {
    const formData = await request.formData();
    const predictionId = formData.get("predictionId") as string;
    const notes = formData.get("notes") as string;
    
    if (!predictionId) {
      return json({ success: false, error: "Prediction ID is required" }, { status: 400 });
    }
    
    // Get the prediction to verify access
    const prediction = await prisma.maintenancePrediction.findUnique({
      where: { id: predictionId },
      include: { device: true },
    });
    
    if (!prediction) {
      return json({ success: false, error: "Prediction not found" }, { status: 404 });
    }
    
    // Verify the user has access to the device
    if (prediction.device.userId !== userId) {
      return json({ success: false, error: "Access denied" }, { status: 403 });
    }
    
    // Update the prediction to mark maintenance as performed
    const updatedPrediction = await prisma.maintenancePrediction.update({
      where: { id: predictionId },
      data: {
        maintenancePerformed: true,
        maintenanceDate: new Date(),
        maintenanceNotes: notes || undefined,
      },
    });
    
    return json({ success: true, prediction: updatedPrediction });
  } catch (error) {
    console.error("Error marking maintenance as performed:", error);
    return json(
      { success: false, error: "Failed to mark maintenance as performed" },
      { status: 500 }
    );
  }
}
