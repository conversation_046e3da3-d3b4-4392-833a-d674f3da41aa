import { json, redirect } from "@remix-run/node";
import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, Form, useActionData, useNavigation } from "@remix-run/react";
import { Refresh<PERSON><PERSON>, Check, <PERSON>ertCircle, AlertTriangle } from "lucide-react";
import { useState } from "react";
import { Alert, AlertTitle, AlertDescription } from "~/components/ui/alert";
import { Button } from "~/components/ui/button";
import { Card } from "~/components/ui/card";
import { Separator } from "~/components/ui/separator";

import { prisma } from "~/db.server";
import { processUnlinkedCalendarEntries } from "~/services/auto-service-order.server";
import { requireUserId } from "~/session.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  
  // Check if user has admin role
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { role: true }
  });
  
  if (!user || user.role !== "ADMIN") {
    return redirect("/");
  }
  
  // Get calendar entry statistics
  const totalCalendarEntries = await prisma.calendarEntry.count();
  
  const analyzedCalendarEntries = await prisma.calendarEntry.count({
    where: {
      semanticAnalysis: {
        not: null
      }
    }
  });
  
  const unlinkedAnalyzedEntries = await prisma.calendarEntry.count({
    where: {
      semanticAnalysis: {
        not: null
      },
      serviceOrderId: null
    }
  });
  
  const linkedToServiceOrders = await prisma.calendarEntry.count({
    where: {
      serviceOrderId: {
        not: null
      }
    }
  });
  
  // Get recent calendar entries that have been analyzed but not linked to service orders
  const recentUnlinkedEntries = await prisma.calendarEntry.findMany({
    where: {
      semanticAnalysis: {
        not: null
      },
      serviceOrderId: null
    },
    orderBy: {
      updatedAt: "desc"
    },
    take: 5,
    select: {
      id: true,
      title: true,
      startTime: true,
      type: true,
      customer: true,
      analysisTimestamp: true
    }
  });
  
  return json({
    totalCalendarEntries,
    analyzedCalendarEntries,
    unlinkedAnalyzedEntries,
    linkedToServiceOrders,
    recentUnlinkedEntries
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  
  // Check if user has admin role
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { role: true }
  });
  
  if (!user || user.role !== "ADMIN") {
    return json({ success: false, message: "Unauthorized" }, { status: 403 });
  }
  
  const formData = await request.formData();
  const action = formData.get("action");
  
  if (action === "process-all") {
    const result = await processUnlinkedCalendarEntries();
    return json(result);
  }
  
  return json({ success: false, message: "Invalid action" }, { status: 400 });
}

export default function CalendarProcessingPage() {
  const {
    totalCalendarEntries,
    analyzedCalendarEntries,
    unlinkedAnalyzedEntries,
    linkedToServiceOrders,
    recentUnlinkedEntries
  } = useLoaderData<typeof loader>();
  
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  
  // Reset success alert when navigation state changes
  if (navigation.state === "idle" && showSuccessAlert) {
    setShowSuccessAlert(false);
  }
  
  const isProcessing = navigation.state === "submitting" && 
    navigation.formData?.get("action") === "process-all";
  
  return (
    <div className="p-4">
      <h1 className="mb-6 text-2xl font-bold">
        Calendar Processing
      </h1>
      
      {actionData && actionData.success && (
        <Alert className="mb-6 bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-900">
          <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertTitle>Processing complete!</AlertTitle>
          <AlertDescription>
            Processed {actionData.processed} calendar entries, created {actionData.created} service orders.
          </AlertDescription>
        </Alert>
      )}
      
      {actionData && !actionData.success && (
        <Alert className="mb-6 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-900" variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Processing failed!</AlertTitle>
          <AlertDescription>{actionData.message}</AlertDescription>
        </Alert>
      )}
      
      <div className="mb-8 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-md shadow-sm">
          <div className="text-sm font-medium text-gray-500">Total Calendar Entries</div>
          <div className="text-2xl font-semibold">{totalCalendarEntries}</div>
        </div>
        
        <div className="bg-white p-4 rounded-md shadow-sm">
          <div className="text-sm font-medium text-gray-500">Analyzed Entries</div>
          <div className="text-2xl font-semibold">{analyzedCalendarEntries}</div>
          <div className="text-xs text-gray-500">
            {totalCalendarEntries > 0 
              ? `${Math.round((analyzedCalendarEntries / totalCalendarEntries) * 100)}%`
              : "0%"}
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-md shadow-sm">
          <div className="text-sm font-medium text-gray-500">Linked to Service Orders</div>
          <div className="text-2xl font-semibold">{linkedToServiceOrders}</div>
          <div className="text-xs text-gray-500">
            {analyzedCalendarEntries > 0 
              ? `${Math.round((linkedToServiceOrders / analyzedCalendarEntries) * 100)}%`
              : "0%"}
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-md shadow-sm">
          <div className="text-sm font-medium text-gray-500">Unlinked Analyzed Entries</div>
          <div className="text-2xl font-semibold">{unlinkedAnalyzedEntries}</div>
          <div className="text-xs text-gray-500">Ready for processing</div>
        </div>
      </div>
      
      <Card className="mb-8">
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900">Process Calendar Entries</h3>
        </div>
        
        <div className="px-6 py-4">
          <p className="mb-4">
            This will process all calendar entries that have been semantically analyzed but don't have service orders yet.
            The system will automatically create service orders for entries that qualify based on their content.
          </p>
          
          <p className="mb-2 font-bold">
            Entries ready for processing: {unlinkedAnalyzedEntries}
          </p>
        </div>
        
        <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
          <Form method="post">
            <input type="hidden" name="action" value="process-all" />
            <Button 
              type="submit" 
              disabled={isProcessing || unlinkedAnalyzedEntries === 0}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isProcessing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Process All Unlinked Entries'
              )}
            </Button>
          </Form>
        </div>
      </Card>
      
      <h2 className="mb-4 text-xl font-semibold">
        Recent Unlinked Analyzed Entries
      </h2>
      
      {recentUnlinkedEntries.length === 0 ? (
        <p>No unlinked analyzed entries found.</p>
      ) : (
        <div className="space-y-4">
          {recentUnlinkedEntries.map((entry) => (
            <Card key={entry.id} className="border border-gray-200">
              <div className="p-4">
                <div className="mb-2 flex justify-between items-center">
                  <h3 className="text-sm font-semibold">{entry.title}</h3>
                  <span className={`inline-flex rounded-full px-2 text-xs font-semibold ${
                    entry.type ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                  }`}>
                    {entry.type || "No Type"}
                  </span>
                </div>
                
                <p className="mb-2 text-sm text-gray-600">
                  {new Date(entry.startTime).toLocaleString()}
                </p>
                
                {entry.customer && (
                  <p className="text-sm">
                    <strong>Customer:</strong> {entry.customer}
                  </p>
                )}
                
                <Separator className="my-2" />
                
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">
                    Analyzed: {entry.analysisTimestamp 
                      ? new Date(entry.analysisTimestamp).toLocaleString() 
                      : "Unknown"}
                  </span>
                  
                  <a 
                    href={`/calendar/${entry.id}`}
                    className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 hover:bg-blue-100"
                  >
                    View Details
                  </a>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}