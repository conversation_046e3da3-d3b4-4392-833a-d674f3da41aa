/**
 * Database Server Module - Divine Quality Estate Foundation
 * Provides centralized database connection and management
 */

import { PrismaClient } from '@prisma/client';

declare global {
  var __db__: PrismaClient;
}

let prisma: PrismaClient;

// This is needed because in development we don't want to restart
// the server with every change, but we want to make sure we don't
// create a new connection to the DB with every change either.
// In production, we'll have a single connection to the DB.
if (process.env.NODE_ENV === 'production') {
  prisma = getClient();
} else {
  if (!global.__db__) {
    global.__db__ = getClient();
  }
  prisma = global.__db__;
}

function getClient() {
  const { DATABASE_URL } = process.env;
  
  if (!DATABASE_URL) {
    throw new Error('DATABASE_URL environment variable is required');
  }

  const databaseUrl = new URL(DATABASE_URL);

  const isLocalHost = databaseUrl.hostname === 'localhost';

  const PRIMARY_REGION = isLocalHost ? null : process.env.PRIMARY_REGION;
  const FLY_REGION = isLocalHost ? null : process.env.FLY_REGION;

  const isReadReplicaRegion = !PRIMARY_REGION || PRIMARY_REGION === FLY_REGION;

  if (!isLocalHost && !isReadReplicaRegion) {
    // 5433 is the read-replica port
    databaseUrl.port = '5433';
    // Ensure read-replica URL is used for read operations
    databaseUrl.host = `${databaseUrl.hostname}`;
  }

  console.log(`🔌 Setting up database connection to ${databaseUrl.host}`);

  const client = new PrismaClient({
    datasources: {
      db: {
        url: databaseUrl.toString(),
      },
    },
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
  });

  // Connect eagerly
  client.$connect();

  return client;
}

/**
 * Database connection with connection pooling and error handling
 */
export { prisma };

/**
 * Health check for database connection
 */
export async function healthCheck() {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    console.error('Database health check failed:', error);
    return { 
      status: 'unhealthy', 
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString() 
    };
  }
}

/**
 * Graceful shutdown
 */
export async function shutdown() {
  await prisma.$disconnect();
}

/**
 * Database transaction wrapper with error handling
 */
export async function withTransaction<T>(
  fn: (prisma: PrismaClient) => Promise<T>
): Promise<T> {
  try {
    return await prisma.$transaction(fn);
  } catch (error) {
    console.error('Transaction failed:', error);
    throw error;
  }
}

/**
 * Database metrics for monitoring
 */
export async function getDatabaseMetrics() {
  try {
    const [
      userCount,
      customerCount,
      jobCount,
      deviceCount
    ] = await Promise.all([
      prisma.user.count(),
      prisma.customer.count(),
      prisma.job.count(),
      prisma.device.count()
    ]);

    return {
      users: userCount,
      customers: customerCount,
      jobs: jobCount,
      devices: deviceCount,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Failed to get database metrics:', error);
    throw error;
  }
}

/**
 * Database cleanup utilities
 */
export const cleanup = {
  /**
   * Clean up old sessions
   */
  async sessions(olderThanDays = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    return await prisma.session.deleteMany({
      where: {
        updatedAt: {
          lt: cutoffDate
        }
      }
    });
  },

  /**
   * Clean up old logs
   */
  async logs(olderThanDays = 90) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    return await prisma.auditLog.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate
        }
      }
    });
  }
};

// Export default for compatibility
export default prisma;
