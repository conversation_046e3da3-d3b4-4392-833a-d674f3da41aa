import { makeExecutableSchema } from '@graphql-tools/schema';
import type { PrismaClient } from '@prisma/client';
import * as bielikService from '../services/bielik.server';
import * as ocrService from '../services/ocr/ocr.server';
import * as qdrantService from '../services/qdrant.server';

const typeDefs = `#graphql
  type User {
    id: ID!
    email: String!
    name: String
    role: String!
    createdAt: String!
    updatedAt: String!
    notes: [Note!]
    customers: [Customer!]
    devices: [Device!]
    serviceOrders: [ServiceOrder!]
    calendarEntries: [CalendarEntry!]
    invoices: [Invoice!]
    serviceReports: [ServiceReport!]
  }

  type Note {
    id: ID!
    title: String!
    body: String!
    createdAt: String!
    updatedAt: String!
    user: User!
  }

  type Customer {
    id: ID!
    name: String!
    email: String
    phone: String
    address: String
    city: String
    postalCode: String
    country: String
    notes: String
    createdAt: String!
    updatedAt: String!
    devices: [Device!]
    serviceOrders: [ServiceOrder!]
    invoices: [Invoice!]
    user: User!
  }

  type Device {
    id: ID!
    name: String!
    model: String
    serialNumber: String
    manufacturer: String
    installationDate: String
    warrantyExpiryDate: String
    notes: String
    createdAt: String!
    updatedAt: String!
    customer: Customer!
    serviceOrders: [ServiceOrder!]
    telemetry: [DeviceTelemetry!]
    predictions: [MaintenancePrediction!]
    user: User!
  }

  type DeviceTelemetry {
    id: ID!
    timestamp: String!
    temperature: Float
    humidity: Float
    pressure: Float
    vibration: Float
    noise: Float
    powerUsage: Float
    runtime: Float
    cycles: Int
    errorCodes: String
    customData: String
    createdAt: String!
    updatedAt: String!
    device: Device!
  }

  type MaintenancePrediction {
    id: ID!
    predictionDate: String!
    failureProbability: Float!
    predictedFailureDate: String
    predictedComponent: String
    recommendedAction: String
    confidence: Float!
    modelVersion: String!
    modelFeatures: String
    maintenancePerformed: Boolean!
    maintenanceDate: String
    maintenanceNotes: String
    createdAt: String!
    updatedAt: String!
    device: Device!
  }

  type ServiceOrder {
    id: ID!
    title: String!
    description: String
    status: String!
    priority: String!
    scheduledDate: String
    completedDate: String
    notes: String
    createdAt: String!
    updatedAt: String!
    customer: Customer!
    device: Device
    user: User!
    serviceReports: [ServiceReport!]
    invoices: [Invoice!]
  }

  type CalendarEntry {
    id: ID!
    title: String!
    description: String
    startTime: String!
    endTime: String!
    location: String
    isAllDay: Boolean!
    createdAt: String!
    updatedAt: String!
    user: User!
  }

  type VectorEmbedding {
    id: ID!
    objectType: String!
    objectId: String!
    content: String!
    createdAt: String!
    updatedAt: String!
  }

  type BielikCompletion {
    text: String!
    created: String!
  }

  type BielikEmbedding {
    embedding: [Float!]!
    created: String!
  }

  type SentimentAnalysis {
    sentiment: String!
    confidence: Float!
    analysis: String!
  }

  type SearchResult {
    id: String!
    score: Float!
    payload: String!
    collection: String!
  }

  type Invoice {
    id: ID!
    invoiceNumber: String
    issueDate: String
    dueDate: String
    totalAmount: Float
    taxAmount: Float
    status: String!
    notes: String
    originalDocumentUrl: String
    processedDocumentUrl: String
    ocrProcessingStatus: String!
    ocrConfidenceScore: Float
    ocrProcessedAt: String
    ocrErrorMessage: String
    createdAt: String!
    updatedAt: String!
    customer: Customer!
    serviceOrder: ServiceOrder
    items: [InvoiceItem!]
  }

  type InvoiceItem {
    id: ID!
    description: String!
    quantity: Float!
    unitPrice: Float!
    totalPrice: Float!
    taxRate: Float
    createdAt: String!
    updatedAt: String!
    invoice: Invoice!
  }

  type ServiceReport {
    id: ID!
    title: String!
    description: String
    workPerformed: String
    partsUsed: String
    recommendations: String
    technicianSignatureUrl: String
    customerSignatureUrl: String
    signedAt: String
    photoUrls: String
    ocrProcessingStatus: String!
    ocrProcessedAt: String
    createdAt: String!
    updatedAt: String!
    serviceOrder: ServiceOrder!
  }

  type OCRResult {
    success: Boolean!
    message: String
    confidence: Float
    text: String
    extractedData: String
  }

  type Query {
    me: User
    user(id: ID!): User
    users: [User!]!

    note(id: ID!): Note
    notes: [Note!]!

    customer(id: ID!): Customer
    customers: [Customer!]!

    device(id: ID!): Device
    devices: [Device!]!
    devicesByCustomer(customerId: ID!): [Device!]!

    serviceOrder(id: ID!): ServiceOrder
    serviceOrders: [ServiceOrder!]!
    serviceOrdersByCustomer(customerId: ID!): [ServiceOrder!]!
    serviceOrdersByDevice(deviceId: ID!): [ServiceOrder!]!

    calendarEntry(id: ID!): CalendarEntry
    calendarEntries: [CalendarEntry!]!
    calendarEntriesByDateRange(startDate: String!, endDate: String!): [CalendarEntry!]!

    invoice(id: ID!): Invoice
    invoices: [Invoice!]!
    invoicesByCustomer(customerId: ID!): [Invoice!]!
    invoicesByServiceOrder(serviceOrderId: ID!): [Invoice!]!

    serviceReport(id: ID!): ServiceReport
    serviceReports: [ServiceReport!]!
    serviceReportsByServiceOrder(serviceOrderId: ID!): [ServiceReport!]!

    deviceTelemetry(deviceId: ID!): DeviceTelemetry
    deviceTelemetryByDevice(deviceId: ID!, limit: Int): [DeviceTelemetry!]!

    maintenancePrediction(id: ID!): MaintenancePrediction
    maintenancePredictionsByDevice(deviceId: ID!, limit: Int): [MaintenancePrediction!]!

    searchVectorDatabase(query: String!, collection: String!, limit: Int): [String!]!

    # Bielik LLM related queries
    generateCompletion(prompt: String!, maxTokens: Int): BielikCompletion!
    generateChatCompletion(messages: [String!]!, roles: [String!]!, maxTokens: Int): BielikCompletion!
    analyzeSentiment(text: String!): SentimentAnalysis!
    getTechnicianAssistance(problem: String!, deviceModel: String!): BielikCompletion!
    semanticSearch(query: String!, collection: String, limit: Int): [SearchResult!]!
  }

  type Mutation {
    createNote(title: String!, body: String!): Note!
    updateNote(id: ID!, title: String, body: String): Note!
    deleteNote(id: ID!): Boolean!

    createCustomer(name: String!, email: String, phone: String, address: String, city: String, postalCode: String, country: String, notes: String): Customer!
    updateCustomer(id: ID!, name: String, email: String, phone: String, address: String, city: String, postalCode: String, country: String, notes: String): Customer!
    deleteCustomer(id: ID!): Boolean!

    createDevice(name: String!, customerId: ID!, model: String, serialNumber: String, manufacturer: String, installationDate: String, warrantyExpiryDate: String, notes: String): Device!
    updateDevice(id: ID!, name: String, customerId: ID, model: String, serialNumber: String, manufacturer: String, installationDate: String, warrantyExpiryDate: String, notes: String): Device!
    deleteDevice(id: ID!): Boolean!

    createServiceOrder(title: String!, customerId: ID!, deviceId: ID, description: String, status: String, priority: String, scheduledDate: String, notes: String): ServiceOrder!
    updateServiceOrder(id: ID!, title: String, customerId: ID, deviceId: ID, description: String, status: String, priority: String, scheduledDate: String, completedDate: String, notes: String): ServiceOrder!
    deleteServiceOrder(id: ID!): Boolean!

    createCalendarEntry(title: String!, startTime: String!, endTime: String!, description: String, location: String, isAllDay: Boolean): CalendarEntry!
    updateCalendarEntry(id: ID!, title: String, startTime: String, endTime: String, description: String, location: String, isAllDay: Boolean): CalendarEntry!
    deleteCalendarEntry(id: ID!): Boolean!

    createInvoice(customerId: ID!, serviceOrderId: ID, invoiceNumber: String, issueDate: String, dueDate: String, totalAmount: Float, taxAmount: Float, status: String, notes: String): Invoice!
    updateInvoice(id: ID!, customerId: ID, serviceOrderId: ID, invoiceNumber: String, issueDate: String, dueDate: String, totalAmount: Float, taxAmount: Float, status: String, notes: String): Invoice!
    deleteInvoice(id: ID!): Boolean!

    createInvoiceItem(invoiceId: ID!, description: String!, quantity: Float!, unitPrice: Float!, totalPrice: Float!, taxRate: Float): InvoiceItem!
    updateInvoiceItem(id: ID!, description: String, quantity: Float, unitPrice: Float, totalPrice: Float, taxRate: Float): InvoiceItem!
    deleteInvoiceItem(id: ID!): Boolean!

    createServiceReport(serviceOrderId: ID!, title: String!, description: String, workPerformed: String, partsUsed: String, recommendations: String): ServiceReport!
    updateServiceReport(id: ID!, title: String, description: String, workPerformed: String, partsUsed: String, recommendations: String): ServiceReport!
    deleteServiceReport(id: ID!): Boolean!

    recordDeviceTelemetry(
      deviceId: ID!,
      timestamp: String,
      temperature: Float,
      humidity: Float,
      pressure: Float,
      vibration: Float,
      noise: Float,
      powerUsage: Float,
      runtime: Float,
      cycles: Int,
      errorCodes: String,
      customData: String
    ): DeviceTelemetry!

    markMaintenancePerformed(
      predictionId: ID!,
      maintenanceDate: String,
      maintenanceNotes: String
    ): MaintenancePrediction!

    processInvoiceWithOCR(invoiceId: ID!, documentUrl: String!): OCRResult!
    processServiceReportWithOCR(reportId: ID!, documentUrl: String!): OCRResult!

    createVectorEmbedding(objectType: String!, objectId: String!, content: String!): VectorEmbedding!
    deleteVectorEmbedding(id: ID!): Boolean!

    # Bielik LLM related mutations
    indexCustomerInQdrant(customerId: ID!): Boolean!
    indexDeviceInQdrant(deviceId: ID!): Boolean!
    indexServiceOrderInQdrant(serviceOrderId: ID!): Boolean!
    indexNoteInQdrant(noteId: ID!): Boolean!
    generateEmbedding(text: String!): BielikEmbedding!
  }
`;

export type GraphQLContext = {
  db: PrismaClient;
  userId: string | null;
};

const resolvers = {
  Query: {
    me: async (_: any, __: any, { db, userId }: GraphQLContext) => {
      if (!userId) return null;
      return db.user.findUnique({ where: { id: userId } });
    },
    user: async (_: any, { id }: { id: string }, { db }: GraphQLContext) => {
      return db.user.findUnique({ where: { id } });
    },
    users: async (_: any, __: any, { db }: GraphQLContext) => {
      return db.user.findMany();
    },

    note: async (_: any, { id }: { id: string }, { db }: GraphQLContext) => {
      return db.note.findUnique({ where: { id } });
    },
    notes: async (_: any, __: any, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.note.findMany({ where: { userId } });
    },

    customer: async (_: any, { id }: { id: string }, { db }: GraphQLContext) => {
      return db.customer.findUnique({ where: { id } });
    },
    customers: async (_: any, __: any, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.customer.findMany({ where: { userId } });
    },

    device: async (_: any, { id }: { id: string }, { db }: GraphQLContext) => {
      return db.device.findUnique({ where: { id } });
    },
    devices: async (_: any, __: any, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.device.findMany({ where: { userId } });
    },
    devicesByCustomer: async (_: any, { customerId }: { customerId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.device.findMany({ where: { customerId, userId } });
    },

    serviceOrder: async (_: any, { id }: { id: string }, { db }: GraphQLContext) => {
      return db.serviceOrder.findUnique({ where: { id } });
    },
    serviceOrders: async (_: any, __: any, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.serviceOrder.findMany({ where: { userId } });
    },
    serviceOrdersByCustomer: async (_: any, { customerId }: { customerId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.serviceOrder.findMany({ where: { customerId, userId } });
    },
    serviceOrdersByDevice: async (_: any, { deviceId }: { deviceId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.serviceOrder.findMany({ where: { deviceId, userId } });
    },

    calendarEntry: async (_: any, { id }: { id: string }, { db }: GraphQLContext) => {
      return db.calendarEntry.findUnique({ where: { id } });
    },
    calendarEntries: async (_: any, __: any, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.calendarEntry.findMany({ where: { userId } });
    },
    calendarEntriesByDateRange: async (_: any, { startDate, endDate }: { startDate: string, endDate: string }, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.calendarEntry.findMany({
        where: {
          userId,
          startTime: { gte: new Date(startDate) },
          endTime: { lte: new Date(endDate) }
        }
      });
    },

    invoice: async (_: any, { id }: { id: string }, { db }: GraphQLContext) => {
      return db.invoice.findUnique({ where: { id } });
    },
    invoices: async (_: any, __: any, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.invoice.findMany({ where: { userId } });
    },
    invoicesByCustomer: async (_: any, { customerId }: { customerId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.invoice.findMany({ where: { customerId, userId } });
    },
    invoicesByServiceOrder: async (_: any, { serviceOrderId }: { serviceOrderId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.invoice.findMany({ where: { serviceOrderId, userId } });
    },

    serviceReport: async (_: any, { id }: { id: string }, { db }: GraphQLContext) => {
      return db.serviceReport.findUnique({ where: { id } });
    },
    serviceReports: async (_: any, __: any, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.serviceReport.findMany({ where: { userId } });
    },
    serviceReportsByServiceOrder: async (_: any, { serviceOrderId }: { serviceOrderId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) return [];
      return db.serviceReport.findMany({ where: { serviceOrderId, userId } });
    },

    deviceTelemetry: async (_: any, { deviceId }: { deviceId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) return null;

      // Verify device belongs to user
      const device = await db.device.findUnique({ where: { id: deviceId, userId } });
      if (!device) throw new Error("Device not found or access denied");

      return db.deviceTelemetry.findFirst({
        where: { deviceId },
        orderBy: { timestamp: 'desc' }
      });
    },

    deviceTelemetryByDevice: async (_: any, { deviceId, limit = 10 }: { deviceId: string, limit?: number }, { db, userId }: GraphQLContext) => {
      if (!userId) return [];

      // Verify device belongs to user
      const device = await db.device.findUnique({ where: { id: deviceId, userId } });
      if (!device) throw new Error("Device not found or access denied");

      return db.deviceTelemetry.findMany({
        where: { deviceId },
        orderBy: { timestamp: 'desc' },
        take: limit
      });
    },

    maintenancePrediction: async (_: any, { id }: { id: string }, { db, userId }: GraphQLContext) => {
      if (!userId) return null;

      const prediction = await db.maintenancePrediction.findUnique({
        where: { id },
        include: { device: true }
      });

      // Verify device belongs to user
      if (!prediction || prediction.device.userId !== userId) {
        throw new Error("Prediction not found or access denied");
      }

      return prediction;
    },

    maintenancePredictionsByDevice: async (_: any, { deviceId, limit = 5 }: { deviceId: string, limit?: number }, { db, userId }: GraphQLContext) => {
      if (!userId) return [];

      // Verify device belongs to user
      const device = await db.device.findUnique({ where: { id: deviceId, userId } });
      if (!device) throw new Error("Device not found or access denied");

      return db.maintenancePrediction.findMany({
        where: { deviceId },
        orderBy: { predictionDate: 'desc' },
        take: limit
      });
    },

    // This is a placeholder for Qdrant vector search - will be implemented with the semanticSearch resolver
    searchVectorDatabase: async (_: any, { query, collection, limit }: { query: string, collection: string, limit?: number }, { db }: GraphQLContext) => {
      // This is kept for backward compatibility
      const results = await qdrantService.searchSimilar(collection, query, limit || 5);
      return results.map(result => JSON.stringify(result.payload));
    },

    // Bielik LLM related resolvers
    generateCompletion: async (_: any, { prompt, maxTokens }: { prompt: string, maxTokens?: number }) => {
      const text = await bielikService.generateCompletion(prompt, { max_tokens: maxTokens });
      return {
        text,
        created: new Date().toISOString(),
      };
    },

    generateChatCompletion: async (_: any, { messages, roles, maxTokens }: { messages: string[], roles: string[], maxTokens?: number }) => {
      // Convert messages and roles arrays to the format expected by the Bielik service
      const formattedMessages = messages.map((content, index) => ({
        role: roles[index] as 'system' | 'user' | 'assistant',
        content,
      }));

      const text = await bielikService.generateChatCompletion(formattedMessages, { max_tokens: maxTokens });
      return {
        text,
        created: new Date().toISOString(),
      };
    },

    analyzeSentiment: async (_: any, { text }: { text: string }) => {
      return await bielikService.analyzeSentiment(text);
    },

    getTechnicianAssistance: async (_: any, { problem, deviceModel }: { problem: string, deviceModel: string }) => {
      const text = await bielikService.getTechnicianAssistance(problem, deviceModel);
      return {
        text,
        created: new Date().toISOString(),
      };
    },

    semanticSearch: async (_: any, { query, collection, limit }: { query: string, collection?: string, limit?: number }) => {
      try {
        let results;

        if (collection) {
          // Search in a specific collection
          results = await qdrantService.searchSimilar(collection, query, limit || 5);
          results = results.map(result => ({
            ...result,
            collection,
          }));
        } else {
          // Search across all collections
          results = await qdrantService.semanticSearch(query, limit || 5);
        }

        // Format the results
        return results.map(result => ({
          id: result.id,
          score: result.score,
          payload: JSON.stringify(result.payload),
          collection: result.collection,
        }));
      } catch (error) {
        console.error('Error performing semantic search:', error);
        return [];
      }
    }
  },

  Mutation: {
    // Note mutations
    createNote: async (_: any, { title, body }: { title: string, body: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      return db.note.create({
        data: {
          title,
          body,
          user: { connect: { id: userId } }
        }
      });
    },
    updateNote: async (_: any, { id, title, body }: { id: string, title?: string, body?: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const note = await db.note.findUnique({ where: { id } });
      if (!note || note.userId !== userId) throw new Error("Note not found or access denied");

      return db.note.update({
        where: { id },
        data: {
          ...(title !== undefined ? { title } : {}),
          ...(body !== undefined ? { body } : {})
        }
      });
    },
    deleteNote: async (_: any, { id }: { id: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const note = await db.note.findUnique({ where: { id } });
      if (!note || note.userId !== userId) throw new Error("Note not found or access denied");

      await db.note.delete({ where: { id } });
      return true;
    },

    // Customer mutations
    createCustomer: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { name, email, phone, address, city, postalCode, country, notes } = args;

      return db.customer.create({
        data: {
          name,
          email,
          phone,
          address,
          city,
          postalCode,
          country,
          notes,
          user: { connect: { id: userId } }
        }
      });
    },
    updateCustomer: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { id, ...data } = args;

      const customer = await db.customer.findUnique({ where: { id } });
      if (!customer || customer.userId !== userId) throw new Error("Customer not found or access denied");

      return db.customer.update({
        where: { id },
        data: Object.fromEntries(
          Object.entries(data).filter(([_, value]) => value !== undefined)
        )
      });
    },
    deleteCustomer: async (_: any, { id }: { id: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const customer = await db.customer.findUnique({ where: { id } });
      if (!customer || customer.userId !== userId) throw new Error("Customer not found or access denied");

      await db.customer.delete({ where: { id } });
      return true;
    },

    // Device mutations
    createDevice: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { customerId, name, model, serialNumber, manufacturer, installationDate, warrantyExpiryDate, notes } = args;

      // Verify customer belongs to user
      const customer = await db.customer.findUnique({ where: { id: customerId } });
      if (!customer || customer.userId !== userId) throw new Error("Customer not found or access denied");

      return db.device.create({
        data: {
          name,
          model,
          serialNumber,
          manufacturer,
          installationDate: installationDate ? new Date(installationDate) : undefined,
          warrantyExpiryDate: warrantyExpiryDate ? new Date(warrantyExpiryDate) : undefined,
          notes,
          customer: { connect: { id: customerId } },
          user: { connect: { id: userId } }
        }
      });
    },
    updateDevice: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { id, customerId, ...data } = args;

      const device = await db.device.findUnique({ where: { id } });
      if (!device || device.userId !== userId) throw new Error("Device not found or access denied");

      // If changing customer, verify new customer belongs to user
      if (customerId) {
        const customer = await db.customer.findUnique({ where: { id: customerId } });
        if (!customer || customer.userId !== userId) throw new Error("Customer not found or access denied");
      }

      // Process date fields
      const processedData = { ...data };
      if (data.installationDate) processedData.installationDate = new Date(data.installationDate);
      if (data.warrantyExpiryDate) processedData.warrantyExpiryDate = new Date(data.warrantyExpiryDate);

      return db.device.update({
        where: { id },
        data: {
          ...Object.fromEntries(
            Object.entries(processedData).filter(([_, value]) => value !== undefined)
          ),
          ...(customerId ? { customer: { connect: { id: customerId } } } : {})
        }
      });
    },
    deleteDevice: async (_: any, { id }: { id: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const device = await db.device.findUnique({ where: { id } });
      if (!device || device.userId !== userId) throw new Error("Device not found or access denied");

      await db.device.delete({ where: { id } });
      return true;
    },

    // ServiceOrder mutations
    createServiceOrder: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { customerId, deviceId, title, description, status, priority, scheduledDate, notes } = args;

      // Verify customer belongs to user
      const customer = await db.customer.findUnique({ where: { id: customerId } });
      if (!customer || customer.userId !== userId) throw new Error("Customer not found or access denied");

      // If device provided, verify it belongs to user and customer
      if (deviceId) {
        const device = await db.device.findUnique({ where: { id: deviceId } });
        if (!device || device.userId !== userId || device.customerId !== customerId) {
          throw new Error("Device not found or access denied");
        }
      }

      return db.serviceOrder.create({
        data: {
          title,
          description,
          status: status || "PENDING",
          priority: priority || "MEDIUM",
          scheduledDate: scheduledDate ? new Date(scheduledDate) : undefined,
          notes,
          customer: { connect: { id: customerId } },
          ...(deviceId ? { device: { connect: { id: deviceId } } } : {}),
          user: { connect: { id: userId } }
        }
      });
    },
    updateServiceOrder: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { id, customerId, deviceId, ...data } = args;

      const serviceOrder = await db.serviceOrder.findUnique({ where: { id } });
      if (!serviceOrder || serviceOrder.userId !== userId) {
        throw new Error("Service order not found or access denied");
      }

      // If changing customer, verify new customer belongs to user
      if (customerId) {
        const customer = await db.customer.findUnique({ where: { id: customerId } });
        if (!customer || customer.userId !== userId) {
          throw new Error("Customer not found or access denied");
        }
      }

      // If changing device, verify it belongs to user and customer
      if (deviceId) {
        const device = await db.device.findUnique({ where: { id: deviceId } });
        if (!device || device.userId !== userId || (customerId && device.customerId !== customerId)) {
          throw new Error("Device not found or access denied");
        }
      }

      // Process date fields
      const processedData = { ...data };
      if (data.scheduledDate) processedData.scheduledDate = new Date(data.scheduledDate);
      if (data.completedDate) processedData.completedDate = new Date(data.completedDate);

      return db.serviceOrder.update({
        where: { id },
        data: {
          ...Object.fromEntries(
            Object.entries(processedData).filter(([_, value]) => value !== undefined)
          ),
          ...(customerId ? { customer: { connect: { id: customerId } } } : {}),
          ...(deviceId ? { device: { connect: { id: deviceId } } } : {})
        }
      });
    },
    deleteServiceOrder: async (_: any, { id }: { id: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const serviceOrder = await db.serviceOrder.findUnique({ where: { id } });
      if (!serviceOrder || serviceOrder.userId !== userId) {
        throw new Error("Service order not found or access denied");
      }

      await db.serviceOrder.delete({ where: { id } });
      return true;
    },

    // CalendarEntry mutations
    createCalendarEntry: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { title, startTime, endTime, description, location, isAllDay } = args;

      return db.calendarEntry.create({
        data: {
          title,
          startTime: new Date(startTime),
          endTime: new Date(endTime),
          description,
          location,
          isAllDay: isAllDay || false,
          user: { connect: { id: userId } }
        }
      });
    },
    updateCalendarEntry: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { id, ...data } = args;

      const calendarEntry = await db.calendarEntry.findUnique({ where: { id } });
      if (!calendarEntry || calendarEntry.userId !== userId) {
        throw new Error("Calendar entry not found or access denied");
      }

      // Process date fields
      const processedData = { ...data };
      if (data.startTime) processedData.startTime = new Date(data.startTime);
      if (data.endTime) processedData.endTime = new Date(data.endTime);

      return db.calendarEntry.update({
        where: { id },
        data: Object.fromEntries(
          Object.entries(processedData).filter(([_, value]) => value !== undefined)
        )
      });
    },
    deleteCalendarEntry: async (_: any, { id }: { id: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const calendarEntry = await db.calendarEntry.findUnique({ where: { id } });
      if (!calendarEntry || calendarEntry.userId !== userId) {
        throw new Error("Calendar entry not found or access denied");
      }

      await db.calendarEntry.delete({ where: { id } });
      return true;
    },

    // Invoice mutations
    createInvoice: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { customerId, serviceOrderId, invoiceNumber, issueDate, dueDate, totalAmount, taxAmount, status, notes } = args;

      // Verify customer belongs to user
      const customer = await db.customer.findUnique({ where: { id: customerId } });
      if (!customer || customer.userId !== userId) throw new Error("Customer not found or access denied");

      // If service order provided, verify it belongs to user and customer
      if (serviceOrderId) {
        const serviceOrder = await db.serviceOrder.findUnique({ where: { id: serviceOrderId } });
        if (!serviceOrder || serviceOrder.userId !== userId || serviceOrder.customerId !== customerId) {
          throw new Error("Service order not found or access denied");
        }
      }

      return db.invoice.create({
        data: {
          invoiceNumber,
          issueDate: issueDate ? new Date(issueDate) : undefined,
          dueDate: dueDate ? new Date(dueDate) : undefined,
          totalAmount,
          taxAmount,
          status: status || "PENDING",
          notes,
          ocrProcessingStatus: "PENDING",
          customer: { connect: { id: customerId } },
          ...(serviceOrderId ? { serviceOrder: { connect: { id: serviceOrderId } } } : {}),
          user: { connect: { id: userId } }
        }
      });
    },
    updateInvoice: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { id, customerId, serviceOrderId, ...data } = args;

      const invoice = await db.invoice.findUnique({ where: { id } });
      if (!invoice) throw new Error("Invoice not found");

      // If changing customer, verify new customer belongs to user
      if (customerId) {
        const customer = await db.customer.findUnique({ where: { id: customerId } });
        if (!customer || customer.userId !== userId) throw new Error("Customer not found or access denied");
      }

      // If changing service order, verify it belongs to user and customer
      if (serviceOrderId) {
        const serviceOrder = await db.serviceOrder.findUnique({ where: { id: serviceOrderId } });
        if (!serviceOrder || serviceOrder.userId !== userId) throw new Error("Service order not found or access denied");

        // If customer is also being changed, verify service order belongs to new customer
        if (customerId && serviceOrder.customerId !== customerId) throw new Error("Service order does not belong to the specified customer");
      }

      // Process date fields
      const processedData = { ...data };
      if (data.issueDate) processedData.issueDate = new Date(data.issueDate);
      if (data.dueDate) processedData.dueDate = new Date(data.dueDate);

      return db.invoice.update({
        where: { id },
        data: {
          ...Object.fromEntries(
            Object.entries(processedData).filter(([_, value]) => value !== undefined)
          ),
          ...(customerId ? { customer: { connect: { id: customerId } } } : {}),
          ...(serviceOrderId ? { serviceOrder: { connect: { id: serviceOrderId } } } : {})
        }
      });
    },
    deleteInvoice: async (_: any, { id }: { id: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const invoice = await db.invoice.findUnique({ where: { id } });
      if (!invoice) throw new Error("Invoice not found");

      // Delete all invoice items first
      await db.invoiceItem.deleteMany({ where: { invoiceId: id } });

      // Then delete the invoice
      await db.invoice.delete({ where: { id } });
      return true;
    },

    // Invoice Item mutations
    createInvoiceItem: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { invoiceId, description, quantity, unitPrice, totalPrice, taxRate } = args;

      // Verify invoice exists
      const invoice = await db.invoice.findUnique({ where: { id: invoiceId } });
      if (!invoice) throw new Error("Invoice not found");

      return db.invoiceItem.create({
        data: {
          description,
          quantity,
          unitPrice,
          totalPrice,
          taxRate,
          invoice: { connect: { id: invoiceId } }
        }
      });
    },
    updateInvoiceItem: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { id, ...data } = args;

      const invoiceItem = await db.invoiceItem.findUnique({
        where: { id },
        include: { invoice: true }
      });
      if (!invoiceItem) throw new Error("Invoice item not found");

      return db.invoiceItem.update({
        where: { id },
        data: Object.fromEntries(
          Object.entries(data).filter(([_, value]) => value !== undefined)
        )
      });
    },
    deleteInvoiceItem: async (_: any, { id }: { id: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const invoiceItem = await db.invoiceItem.findUnique({ where: { id } });
      if (!invoiceItem) throw new Error("Invoice item not found");

      await db.invoiceItem.delete({ where: { id } });
      return true;
    },

    // Service Report mutations
    createServiceReport: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { serviceOrderId, title, description, workPerformed, partsUsed, recommendations } = args;

      // Verify service order belongs to user
      const serviceOrder = await db.serviceOrder.findUnique({ where: { id: serviceOrderId } });
      if (!serviceOrder || serviceOrder.userId !== userId) throw new Error("Service order not found or access denied");

      return db.serviceReport.create({
        data: {
          title,
          description,
          workPerformed,
          partsUsed,
          recommendations,
          ocrProcessingStatus: "PENDING",
          serviceOrder: { connect: { id: serviceOrderId } }
        }
      });
    },
    updateServiceReport: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const { id, ...data } = args;

      const serviceReport = await db.serviceReport.findUnique({
        where: { id },
        include: { serviceOrder: true }
      });
      if (!serviceReport) throw new Error("Service report not found");

      // Verify user has access to the service order
      if (serviceReport.serviceOrder.userId !== userId) throw new Error("Access denied");

      return db.serviceReport.update({
        where: { id },
        data: Object.fromEntries(
          Object.entries(data).filter(([_, value]) => value !== undefined)
        )
      });
    },
    deleteServiceReport: async (_: any, { id }: { id: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");
      const serviceReport = await db.serviceReport.findUnique({
        where: { id },
        include: { serviceOrder: true }
      });
      if (!serviceReport) throw new Error("Service report not found");

      // Verify user has access to the service order
      if (serviceReport.serviceOrder.userId !== userId) throw new Error("Access denied");

      await db.serviceReport.delete({ where: { id } });
      return true;
    },

    // VectorEmbedding mutations
    createVectorEmbedding: async (_: any, { objectType, objectId, content }: { objectType: string, objectId: string, content: string }, { db }: GraphQLContext) => {
      return db.vectorEmbedding.create({
        data: {
          objectType,
          objectId,
          content
        }
      });
    },
    deleteVectorEmbedding: async (_: any, { id }: { id: string }, { db }: GraphQLContext) => {
      await db.vectorEmbedding.delete({ where: { id } });
      return true;
    },

    // OCR related mutations
    processInvoiceWithOCR: async (_: any, { invoiceId, documentUrl }: { invoiceId: string, documentUrl: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");

      // Verify invoice belongs to user
      const invoice = await db.invoice.findUnique({ where: { id: invoiceId } });
      if (!invoice) throw new Error("Invoice not found");

      try {
        // Update invoice with document URL
        await db.invoice.update({
          where: { id: invoiceId },
          data: {
            originalDocumentUrl: documentUrl,
          },
        });

        // Process invoice with OCR
        const updatedInvoice = await ocrService.processInvoice(invoiceId, documentUrl);

        return {
          success: updatedInvoice.ocrProcessingStatus === "COMPLETED",
          message: updatedInvoice.ocrErrorMessage || "Invoice processed successfully",
          confidence: updatedInvoice.ocrConfidenceScore,
          extractedData: JSON.stringify({
            invoiceNumber: updatedInvoice.invoiceNumber,
            issueDate: updatedInvoice.issueDate,
            dueDate: updatedInvoice.dueDate,
            totalAmount: updatedInvoice.totalAmount,
            taxAmount: updatedInvoice.taxAmount,
          }),
        };
      } catch (error) {
        console.error('Error processing invoice with OCR:', error);
        return {
          success: false,
          message: `Failed to process invoice: ${(error as Error).message}`,
          confidence: 0,
          extractedData: null,
        };
      }
    },

    processServiceReportWithOCR: async (_: any, { reportId, documentUrl }: { reportId: string, documentUrl: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");

      // Verify service report belongs to user
      const report = await db.serviceReport.findUnique({ where: { id: reportId } });
      if (!report) throw new Error("Service report not found");

      try {
        // Process service report with OCR
        const updatedReport = await ocrService.processServiceReport(reportId, documentUrl);

        return {
          success: updatedReport.ocrProcessingStatus === "COMPLETED",
          message: updatedReport.ocrProcessingStatus === "COMPLETED" ? "Service report processed successfully" : "Failed to process service report",
          confidence: 0.95, // Mock confidence score
          extractedData: JSON.stringify({
            title: updatedReport.title,
            workPerformed: updatedReport.workPerformed,
            partsUsed: updatedReport.partsUsed,
            recommendations: updatedReport.recommendations,
          }),
        };
      } catch (error) {
        console.error('Error processing service report with OCR:', error);
        return {
          success: false,
          message: `Failed to process service report: ${(error as Error).message}`,
          confidence: 0,
          extractedData: null,
        };
      }
    },

    recordDeviceTelemetry: async (_: any, args: any, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");

      const { deviceId, timestamp, ...telemetryData } = args;

      // Verify device belongs to user
      const device = await db.device.findUnique({ where: { id: deviceId, userId } });
      if (!device) throw new Error("Device not found or access denied");

      // Create telemetry record
      const telemetry = await db.deviceTelemetry.create({
        data: {
          timestamp: timestamp ? new Date(timestamp) : new Date(),
          ...telemetryData,
          device: { connect: { id: deviceId } },
        },
      });

      // After recording telemetry, analyze it for potential issues
      try {
        // Import the predictive maintenance service
        const { analyzeTelemetryAndPredict } = await import('../services/predictive-maintenance.server');
        await analyzeTelemetryAndPredict(deviceId, userId);
      } catch (analyzeError) {
        console.error('Error analyzing telemetry:', analyzeError);
        // Don't fail the operation if analysis fails
      }

      return telemetry;
    },

    markMaintenancePerformed: async (_: any, { predictionId, maintenanceDate, maintenanceNotes }: { predictionId: string, maintenanceDate?: string, maintenanceNotes?: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");

      // Get the prediction to verify access
      const prediction = await db.maintenancePrediction.findUnique({
        where: { id: predictionId },
        include: { device: true },
      });

      if (!prediction) throw new Error("Prediction not found");

      // Verify the user has access to the device
      if (prediction.device.userId !== userId) throw new Error("Access denied");

      // Update the prediction to mark maintenance as performed
      return db.maintenancePrediction.update({
        where: { id: predictionId },
        data: {
          maintenancePerformed: true,
          maintenanceDate: maintenanceDate ? new Date(maintenanceDate) : new Date(),
          maintenanceNotes: maintenanceNotes || undefined,
        },
      });
    },

    // Bielik LLM related mutations
    indexCustomerInQdrant: async (_: any, { customerId }: { customerId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");

      // Verify customer belongs to user
      const customer = await db.customer.findUnique({ where: { id: customerId } });
      if (!customer || customer.userId !== userId) throw new Error("Customer not found or access denied");

      try {
        const result = await qdrantService.indexCustomer(db, customerId);
        return result;
      } catch (error) {
        console.error('Error indexing customer in Qdrant:', error);
        throw new Error(`Failed to index customer: ${(error as Error).message}`);
      }
    },

    indexDeviceInQdrant: async (_: any, { deviceId }: { deviceId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");

      // Verify device belongs to user
      const device = await db.device.findUnique({ where: { id: deviceId } });
      if (!device || device.userId !== userId) throw new Error("Device not found or access denied");

      try {
        const result = await qdrantService.indexDevice(db, deviceId);
        return result;
      } catch (error) {
        console.error('Error indexing device in Qdrant:', error);
        throw new Error(`Failed to index device: ${(error as Error).message}`);
      }
    },

    indexServiceOrderInQdrant: async (_: any, { serviceOrderId }: { serviceOrderId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");

      // Verify service order belongs to user
      const serviceOrder = await db.serviceOrder.findUnique({ where: { id: serviceOrderId } });
      if (!serviceOrder || serviceOrder.userId !== userId) throw new Error("Service order not found or access denied");

      try {
        const result = await qdrantService.indexServiceOrder(db, serviceOrderId);
        return result;
      } catch (error) {
        console.error('Error indexing service order in Qdrant:', error);
        throw new Error(`Failed to index service order: ${(error as Error).message}`);
      }
    },

    indexNoteInQdrant: async (_: any, { noteId }: { noteId: string }, { db, userId }: GraphQLContext) => {
      if (!userId) throw new Error("Not authenticated");

      // Verify note belongs to user
      const note = await db.note.findUnique({ where: { id: noteId } });
      if (!note || note.userId !== userId) throw new Error("Note not found or access denied");

      try {
        const result = await qdrantService.indexNote(db, noteId);
        return result;
      } catch (error) {
        console.error('Error indexing note in Qdrant:', error);
        throw new Error(`Failed to index note: ${(error as Error).message}`);
      }
    },

    generateEmbedding: async (_: any, { text }: { text: string }) => {
      try {
        const embedding = await bielikService.generateEmbedding(text);
        return {
          embedding,
          created: new Date().toISOString(),
        };
      } catch (error) {
        console.error('Error generating embedding with Bielik LLM:', error);
        throw new Error(`Failed to generate embedding: ${(error as Error).message}`);
      }
    }
  },

  // Field resolvers
  User: {
    notes: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.note.findMany({ where: { userId: parent.id } });
    },
    customers: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.customer.findMany({ where: { userId: parent.id } });
    },
    devices: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.device.findMany({ where: { userId: parent.id } });
    },
    serviceOrders: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.serviceOrder.findMany({ where: { userId: parent.id } });
    },
    calendarEntries: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.calendarEntry.findMany({ where: { userId: parent.id } });
    },
    invoices: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.invoice.findMany({ where: { userId: parent.id } });
    },
    serviceReports: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.serviceReport.findMany({ where: { userId: parent.id } });
    }
  },

  Note: {
    user: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.user.findUnique({ where: { id: parent.userId } });
    }
  },

  Customer: {
    devices: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.device.findMany({ where: { customerId: parent.id } });
    },
    serviceOrders: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.serviceOrder.findMany({ where: { customerId: parent.id } });
    },
    invoices: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.invoice.findMany({ where: { customerId: parent.id } });
    },
    user: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.user.findUnique({ where: { id: parent.userId } });
    }
  },

  Device: {
    customer: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.customer.findUnique({ where: { id: parent.customerId } });
    },
    serviceOrders: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.serviceOrder.findMany({ where: { deviceId: parent.id } });
    },
    telemetry: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.deviceTelemetry.findMany({
        where: { deviceId: parent.id },
        orderBy: { timestamp: 'desc' },
        take: 10
      });
    },
    predictions: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.maintenancePrediction.findMany({
        where: { deviceId: parent.id },
        orderBy: { predictionDate: 'desc' },
        take: 5
      });
    },
    user: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.user.findUnique({ where: { id: parent.userId } });
    }
  },

  ServiceOrder: {
    customer: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.customer.findUnique({ where: { id: parent.customerId } });
    },
    device: async (parent: any, _: any, { db }: GraphQLContext) => {
      if (!parent.deviceId) return null;
      return db.device.findUnique({ where: { id: parent.deviceId } });
    },
    user: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.user.findUnique({ where: { id: parent.userId } });
    },
    serviceReports: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.serviceReport.findMany({ where: { serviceOrderId: parent.id } });
    },
    invoices: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.invoice.findMany({ where: { serviceOrderId: parent.id } });
    }
  },

  CalendarEntry: {
    user: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.user.findUnique({ where: { id: parent.userId } });
    }
  },

  Invoice: {
    customer: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.customer.findUnique({ where: { id: parent.customerId } });
    },
    serviceOrder: async (parent: any, _: any, { db }: GraphQLContext) => {
      if (!parent.serviceOrderId) return null;
      return db.serviceOrder.findUnique({ where: { id: parent.serviceOrderId } });
    },
    items: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.invoiceItem.findMany({ where: { invoiceId: parent.id } });
    }
  },

  InvoiceItem: {
    invoice: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.invoice.findUnique({ where: { id: parent.invoiceId } });
    }
  },

  ServiceReport: {
    serviceOrder: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.serviceOrder.findUnique({ where: { id: parent.serviceOrderId } });
    }
  },

  DeviceTelemetry: {
    device: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.device.findUnique({ where: { id: parent.deviceId } });
    }
  },

  MaintenancePrediction: {
    device: async (parent: any, _: any, { db }: GraphQLContext) => {
      return db.device.findUnique({ where: { id: parent.deviceId } });
    }
  }
};

export const schema = makeExecutableSchema({
  typeDefs,
  resolvers,
});