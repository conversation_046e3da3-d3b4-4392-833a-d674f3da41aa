import type { User } from "@prisma/client";
import { prisma } from "~/db.server";

export type { OfferTemplate } from "@prisma/client";

export async function getOfferTemplates() {
  return prisma.offerTemplate.findMany({
    orderBy: { name: "asc" },
  });
}

export async function getOfferTemplate(id: string) {
  return prisma.offerTemplate.findUnique({
    where: { id },
  });
}

export async function createOfferTemplate({
  name,
  description,
  content,
  isDefault,
}: {
  name: string;
  description?: string;
  content: string;
  isDefault?: boolean;
}) {
  // If this template is set as default, unset any existing default templates
  if (isDefault) {
    await prisma.offerTemplate.updateMany({
      where: { isDefault: true },
      data: { isDefault: false },
    });
  }

  return prisma.offerTemplate.create({
    data: {
      name,
      description,
      content,
      isDefault: isDefault ?? false,
    },
  });
}

export async function updateOfferTemplate({
  id,
  name,
  description,
  content,
  isDefault,
}: {
  id: string;
  name?: string;
  description?: string | null;
  content?: string;
  isDefault?: boolean;
}) {
  // If this template is set as default, unset any existing default templates
  if (isDefault) {
    await prisma.offerTemplate.updateMany({
      where: { isDefault: true, id: { not: id } },
      data: { isDefault: false },
    });
  }

  return prisma.offerTemplate.update({
    where: { id },
    data: {
      name,
      description,
      content,
      isDefault,
    },
  });
}

export async function deleteOfferTemplate(id: string) {
  return prisma.offerTemplate.delete({
    where: { id },
  });
}

export async function getDefaultOfferTemplate() {
  return prisma.offerTemplate.findFirst({
    where: { isDefault: true },
  });
}

export async function applyTemplateToOffer({
  offerId,
  templateId,
  userId,
}: {
  offerId: string;
  templateId: string;
  userId: User["id"];
}) {
  // First check if the offer exists and belongs to the user
  const offer = await prisma.offer.findFirst({
    where: { id: offerId, userId },
  });
  
  if (!offer) {
    throw new Error("Offer not found");
  }
  
  // Get the template
  const template = await prisma.offerTemplate.findUnique({
    where: { id: templateId },
  });
  
  if (!template) {
    throw new Error("Template not found");
  }
  
  // Parse the template content
  const templateData = JSON.parse(template.content);
  
  // Update the offer with template data
  return prisma.offer.update({
    where: { id: offerId },
    data: {
      templateId,
      // Apply any template-specific fields here
      // For example, if the template has a description field:
      ...(templateData.description ? { description: templateData.description } : {}),
    },
  });
}