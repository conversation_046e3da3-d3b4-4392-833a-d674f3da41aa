import type { AutomationRule, AutomationExecution } from "@prisma/client";
import { prisma } from "~/db.server";

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface FilterParams {
  search?: string;
  status?: string;
  category?: string;
  triggerType?: string;
  actionType?: string;
}

export interface PaginatedResponse<T> extends ServiceResponse<T[]> {
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Get all automation rules with pagination, filtering, and sorting
 */
export async function getAutomationRules(
  userId: string,
  pagination: PaginationParams = {},
  filters: FilterParams = {}
): Promise<PaginatedResponse<AutomationRule>> {
  try {
    const {
      page = 1,
      pageSize = 10,
      orderBy = 'createdAt',
      orderDirection = 'desc'
    } = pagination;

    const { search, status, category, triggerType, actionType } = filters;

    // Build where clause
    const where: any = { userId };

    // Add search filter if provided
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Add status filter if provided
    if (status) {
      where.isActive = status === 'ACTIVE';
    }

    // Add category filter if provided
    if (category) {
      where.category = category;
    }

    // Add triggerType filter if provided
    if (triggerType) {
      where.triggerType = triggerType;
    }

    // Add actionType filter if provided
    if (actionType) {
      where.actionType = actionType;
    }

    // Get total count for pagination
    const totalCount = await prisma.automationRule.count({ where });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get automation rules with pagination
    const automationRules = await prisma.automationRule.findMany({
      where,
      orderBy: { [orderBy]: orderDirection },
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        executions: {
          take: 1,
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    return {
      data: automationRules,
      error: null,
      success: true,
      totalCount,
      totalPages,
      currentPage: page,
    };
  } catch (error) {
    console.error('Failed to fetch automation rules:', error);
    return {
      data: null,
      error: 'Failed to fetch automation rules',
      success: false,
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
    };
  }
}/**
 * Get an automation rule by ID
 */
export async function getAutomationRuleById(id: string, userId: string): Promise<ServiceResponse<AutomationRule>> {
  try {
    const automationRule = await prisma.automationRule.findUnique({
      where: { id, userId },
      include: {
        executions: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });

    if (!automationRule) {
      return {
        data: null,
        error: 'Automation rule not found',
        success: false
      };
    }

    return {
      data: automationRule,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to fetch automation rule:', error);
    return {
      data: null,
      error: 'Failed to fetch automation rule',
      success: false
    };
  }
}

/**
 * Create a new automation rule
 */
export async function createAutomationRule(
  data: {
    name: string;
    description?: string;
    triggerType: string;
    triggerConfig: string;
    actionType: string;
    actionConfig: string;
    isActive?: boolean;
    category?: string;
    userId: string;
  }
): Promise<ServiceResponse<AutomationRule>> {
  try {
    // Create automation rule in database
    const automationRule = await prisma.automationRule.create({
      data: {
        name: data.name,
        description: data.description || '',
        triggerType: data.triggerType,
        triggerConfig: data.triggerConfig,
        actionType: data.actionType,
        actionConfig: data.actionConfig,
        isActive: data.isActive !== undefined ? data.isActive : true,
        category: data.category,
        userId: data.userId,
        runCount: 0,
      },
    });

    return {
      data: automationRule,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to create automation rule:', error);
    return {
      data: null,
      error: 'Failed to create automation rule',
      success: false
    };
  }
}/**
 * Update an existing automation rule
 */
export async function updateAutomationRule(
  id: string,
  data: Partial<{
    name: string;
    description: string;
    triggerType: string;
    triggerConfig: string;
    actionType: string;
    actionConfig: string;
    isActive: boolean;
    category: string;
  }>,
  userId: string
): Promise<ServiceResponse<AutomationRule>> {
  try {
    // Check if automation rule exists and belongs to user
    const existingRule = await prisma.automationRule.findUnique({
      where: { id, userId },
    });

    if (!existingRule) {
      return {
        data: null,
        error: 'Automation rule not found or access denied',
        success: false
      };
    }

    // Update automation rule
    const updatedRule = await prisma.automationRule.update({
      where: { id },
      data,
    });

    return {
      data: updatedRule,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to update automation rule:', error);
    return {
      data: null,
      error: 'Failed to update automation rule',
      success: false
    };
  }
}

/**
 * Delete an automation rule
 */
export async function deleteAutomationRule(id: string, userId: string): Promise<ServiceResponse<boolean>> {
  try {
    // Check if automation rule exists and belongs to user
    const existingRule = await prisma.automationRule.findUnique({
      where: { id, userId },
    });

    if (!existingRule) {
      return {
        data: null,
        error: 'Automation rule not found or access denied',
        success: false
      };
    }

    // Delete automation rule
    await prisma.automationRule.delete({
      where: { id },
    });

    return {
      data: true,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to delete automation rule:', error);
    return {
      data: null,
      error: 'Failed to delete automation rule',
      success: false
    };
  }
}/**
 * Toggle the active status of an automation rule
 */
export async function toggleAutomationRuleStatus(id: string, userId: string): Promise<ServiceResponse<AutomationRule>> {
  try {
    // Check if automation rule exists and belongs to user
    const existingRule = await prisma.automationRule.findUnique({
      where: { id, userId },
    });

    if (!existingRule) {
      return {
        data: null,
        error: 'Automation rule not found or access denied',
        success: false
      };
    }

    // Toggle isActive status
    const updatedRule = await prisma.automationRule.update({
      where: { id },
      data: {
        isActive: !existingRule.isActive,
      },
    });

    return {
      data: updatedRule,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to toggle automation rule status:', error);
    return {
      data: null,
      error: 'Failed to toggle automation rule status',
      success: false
    };
  }
}

/**
 * Get automation executions for a rule
 */
export async function getAutomationExecutions(
  ruleId: string,
  userId: string,
  pagination: PaginationParams = {}
): Promise<PaginatedResponse<AutomationExecution>> {
  try {
    const {
      page = 1,
      pageSize = 10,
      orderBy = 'createdAt',
      orderDirection = 'desc'
    } = pagination;

    // Check if automation rule exists and belongs to user
    const existingRule = await prisma.automationRule.findUnique({
      where: { id: ruleId, userId },
    });

    if (!existingRule) {
      return {
        data: null,
        error: 'Automation rule not found or access denied',
        success: false,
        totalCount: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }

    // Get total count for pagination
    const totalCount = await prisma.automationExecution.count({
      where: { automationRuleId: ruleId },
    });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get executions with pagination
    const executions = await prisma.automationExecution.findMany({
      where: { automationRuleId: ruleId },
      orderBy: { [orderBy]: orderDirection },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    return {
      data: executions,
      error: null,
      success: true,
      totalCount,
      totalPages,
      currentPage: page,
    };
  } catch (error) {
    console.error('Failed to fetch automation executions:', error);
    return {
      data: null,
      error: 'Failed to fetch automation executions',
      success: false,
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
    };
  }
}/**
 * Record an automation execution
 */
export async function recordAutomationExecution(
  data: {
    automationRuleId: string;
    status: string;
    details?: string;
    affectedEntities?: number;
    errorMessage?: string;
    executionTime?: number;
  }
): Promise<ServiceResponse<AutomationExecution>> {
  try {
    // Create execution record
    const execution = await prisma.automationExecution.create({
      data: {
        automationRuleId: data.automationRuleId,
        status: data.status,
        details: data.details || '',
        affectedEntities: data.affectedEntities || 0,
        errorMessage: data.errorMessage,
        executionTime: data.executionTime,
      },
    });

    // Update rule's lastRun timestamp and increment runCount
    await prisma.automationRule.update({
      where: { id: data.automationRuleId },
      data: {
        lastRun: new Date(),
        runCount: {
          increment: 1,
        },
      },
    });

    return {
      data: execution,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to record automation execution:', error);
    return {
      data: null,
      error: 'Failed to record automation execution',
      success: false
    };
  }
}

/**
 * Get automation statistics
 */
export async function getAutomationStats(userId: string): Promise<ServiceResponse<any>> {
  try {
    // Get total rules count
    const totalRules = await prisma.automationRule.count({
      where: { userId },
    });

    // Get active rules count
    const activeRules = await prisma.automationRule.count({
      where: { userId, isActive: true },
    });

    // Get total executions count
    const totalExecutions = await prisma.automationRule.aggregate({
      where: { userId },
      _sum: {
        runCount: true,
      },
    });

    // Get success rate
    const successExecutions = await prisma.automationExecution.count({
      where: {
        automationRule: { userId },
        status: 'SUCCESS',
      },
    });

    const totalExecutionsCount = await prisma.automationExecution.count({
      where: {
        automationRule: { userId },
      },
    });

    const successRate = totalExecutionsCount > 0
      ? Math.round((successExecutions / totalExecutionsCount) * 100)
      : 100;

    return {
      data: {
        totalRules,
        activeRules,
        totalExecutions: totalExecutions._sum.runCount || 0,
        successRate,
      },
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to get automation stats:', error);
    return {
      data: null,
      error: 'Failed to get automation stats',
      success: false
    };
  }
}