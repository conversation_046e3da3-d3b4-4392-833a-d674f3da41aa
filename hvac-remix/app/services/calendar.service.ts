import type { CalendarEntry as PrismaCalendarEntry } from "@prisma/client";
import { prisma } from "~/db.server";

export interface CalendarEntry extends PrismaCalendarEntry {}

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface FilterParams {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  isAllDay?: boolean;
}

export interface PaginatedResponse<T> extends ServiceResponse<T[]> {
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Get all calendar entries with pagination, filtering, and sorting
 */
export async function getCalendarEntries(
  userId: string,
  pagination: PaginationParams = {},
  filters: FilterParams = {}
): Promise<PaginatedResponse<CalendarEntry>> {
  try {
    const { 
      page = 1, 
      pageSize = 10, 
      orderBy = 'startTime', 
      orderDirection = 'asc' 
    } = pagination;
    
    const { search, dateFrom, dateTo, isAllDay } = filters;
    
    // Build where clause
    const where: any = { userId };
    
    // Add search filter if provided
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { location: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    // Add date range filters if provided
    if (dateFrom) {
      where.startTime = {
        ...where.startTime,
        gte: new Date(dateFrom),
      };
    }
    
    if (dateTo) {
      where.endTime = {
        ...where.endTime,
        lte: new Date(dateTo),
      };
    }
    
    // Add isAllDay filter if provided
    if (isAllDay !== undefined) {
      where.isAllDay = isAllDay;
    }
    
    // Get total count for pagination
    const totalCount = await prisma.calendarEntry.count({ where });
    
    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);
    
    // Get calendar entries with pagination
    const calendarEntries = await prisma.calendarEntry.findMany({
      where,
      orderBy: { [orderBy]: orderDirection },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });
    
    return {
      data: calendarEntries,
      error: null,
      success: true,
      totalCount,
      totalPages,
      currentPage: page,
    };
  } catch (error) {
    console.error('Failed to fetch calendar entries:', error);
    return {
      data: null,
      error: 'Failed to fetch calendar entries',
      success: false,
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
    };
  }
}

/**
 * Get calendar entries by date range
 */
export async function getCalendarEntriesByDateRange(
  userId: string,
  startDate: string,
  endDate: string
): Promise<ServiceResponse<CalendarEntry[]>> {
  try {
    const calendarEntries = await prisma.calendarEntry.findMany({
      where: {
        userId,
        startTime: { gte: new Date(startDate) },
        endTime: { lte: new Date(endDate) },
      },
      orderBy: { startTime: 'asc' },
    });
    
    return {
      data: calendarEntries,
      error: null,
      success: true,
    };
  } catch (error) {
    console.error('Failed to fetch calendar entries by date range:', error);
    return {
      data: null,
      error: 'Failed to fetch calendar entries by date range',
      success: false,
    };
  }
}

/**
 * Get a calendar entry by ID
 */
export async function getCalendarEntryById(id: string, userId: string): Promise<ServiceResponse<CalendarEntry>> {
  try {
    const calendarEntry = await prisma.calendarEntry.findUnique({
      where: { id, userId },
    });
    
    if (!calendarEntry) {
      return {
        data: null,
        error: 'Calendar entry not found',
        success: false
      };
    }
    
    return {
      data: calendarEntry,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to fetch calendar entry:', error);
    return {
      data: null,
      error: 'Failed to fetch calendar entry',
      success: false
    };
  }
}

/**
 * Create a new calendar entry
 */
export async function createCalendarEntry(
  data: Omit<CalendarEntry, 'id' | 'createdAt' | 'updatedAt'>, 
  userId: string
): Promise<ServiceResponse<CalendarEntry>> {
  try {
    // Add userId to data
    const calendarEntryData = {
      ...data,
      userId,
    };
    
    // Create calendar entry in database
    const calendarEntry = await prisma.calendarEntry.create({
      data: calendarEntryData,
    });
    
    return {
      data: calendarEntry,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to create calendar entry:', error);
    return {
      data: null,
      error: 'Failed to create calendar entry',
      success: false
    };
  }
}

/**
 * Update an existing calendar entry
 */
export async function updateCalendarEntry(
  id: string,
  data: Partial<Omit<CalendarEntry, 'id' | 'createdAt' | 'updatedAt'>>,
  userId: string
): Promise<ServiceResponse<CalendarEntry>> {
  try {
    // Check if calendar entry exists and belongs to user
    const existingCalendarEntry = await prisma.calendarEntry.findUnique({
      where: { id, userId },
    });
    
    if (!existingCalendarEntry) {
      return {
        data: null,
        error: 'Calendar entry not found or access denied',
        success: false
      };
    }
    
    // Update calendar entry
    const updatedCalendarEntry = await prisma.calendarEntry.update({
      where: { id },
      data,
    });
    
    return {
      data: updatedCalendarEntry,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to update calendar entry:', error);
    return {
      data: null,
      error: 'Failed to update calendar entry',
      success: false
    };
  }
}

/**
 * Delete a calendar entry
 */
export async function deleteCalendarEntry(id: string, userId: string): Promise<ServiceResponse<boolean>> {
  try {
    // Check if calendar entry exists and belongs to user
    const existingCalendarEntry = await prisma.calendarEntry.findUnique({
      where: { id, userId },
    });
    
    if (!existingCalendarEntry) {
      return {
        data: null,
        error: 'Calendar entry not found or access denied',
        success: false
      };
    }
    
    // Delete calendar entry from database
    await prisma.calendarEntry.delete({
      where: { id },
    });
    
    return {
      data: true,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to delete calendar entry:', error);
    return {
      data: null,
      error: 'Failed to delete calendar entry',
      success: false
    };
  }
}
