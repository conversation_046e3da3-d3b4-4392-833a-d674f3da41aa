import type { User } from "@prisma/client";
import { db } from "~/db.server";

export type { OfferVariant, OfferVariantItem } from "@prisma/client";

export async function getOfferVariants({
  offerId,
  userId,
}: {
  offerId: string;
  userId: User["id"];
}) {
  // First check if the offer exists and belongs to the user
  const offer = await db.offer.findFirst({
    where: { id: offerId, userId },
  });
  
  if (!offer) {
    throw new Error("Offer not found");
  }
  
  return db.offerVariant.findMany({
    where: { offerId },
    include: {
      items: true,
    },
    orderBy: { createdAt: "asc" },
  });
}

export async function getOfferVariant({
  id,
  userId,
}: {
  id: string;
  userId: User["id"];
}) {
  const variant = await db.offerVariant.findUnique({
    where: { id },
    include: {
      items: true,
      offer: true,
    },
  });
  
  if (!variant || variant.offer.userId !== userId) {
    throw new Error("Variant not found");
  }
  
  return variant;
}

export async function createOfferVariant({
  name,
  description,
  totalAmount,
  taxAmount,
  isSelected,
  offerId,
  userId,
  items,
}: {
  name: string;
  description?: string;
  totalAmount: number;
  taxAmount?: number;
  isSelected?: boolean;
  offerId: string;
  userId: User["id"];
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    taxRate?: number;
  }>;
}) {
  // First check if the offer exists and belongs to the user
  const offer = await db.offer.findFirst({
    where: { id: offerId, userId },
  });
  
  if (!offer) {
    throw new Error("Offer not found");
  }
  
  // If this variant is set as selected, unset any existing selected variants
  if (isSelected) {
    await db.offerVariant.updateMany({
      where: { offerId, isSelected: true },
      data: { isSelected: false },
    });
  }
  
  return db.offerVariant.create({
    data: {
      name,
      description,
      totalAmount,
      taxAmount,
      isSelected: isSelected ?? false,
      offerId,
      items: {
        create: items,
      },
    },
  });
}

export async function updateOfferVariant({
  id,
  name,
  description,
  totalAmount,
  taxAmount,
  isSelected,
  userId,
}: {
  id: string;
  name?: string;
  description?: string | null;
  totalAmount?: number;
  taxAmount?: number | null;
  isSelected?: boolean;
  userId: User["id"];
}) {
  // First check if the variant exists and belongs to the user
  const variant = await db.offerVariant.findUnique({
    where: { id },
    include: { offer: true },
  });
  
  if (!variant || variant.offer.userId !== userId) {
    throw new Error("Variant not found");
  }
  
  // If this variant is set as selected, unset any existing selected variants
  if (isSelected) {
    await db.offerVariant.updateMany({
      where: { offerId: variant.offerId, isSelected: true, id: { not: id } },
      data: { isSelected: false },
    });
  }
  
  return db.offerVariant.update({
    where: { id },
    data: {
      name,
      description,
      totalAmount,
      taxAmount,
      isSelected,
    },
  });
}

export async function deleteOfferVariant({
  id,
  userId,
}: {
  id: string;
  userId: User["id"];
}) {
  // First check if the variant exists and belongs to the user
  const variant = await db.offerVariant.findUnique({
    where: { id },
    include: { offer: true },
  });
  
  if (!variant || variant.offer.userId !== userId) {
    throw new Error("Variant not found");
  }
  
  return db.offerVariant.delete({
    where: { id },
  });
}

export async function addOfferVariantItem({
  variantId,
  description,
  quantity,
  unitPrice,
  totalPrice,
  taxRate,
  userId,
}: {
  variantId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate?: number;
  userId: User["id"];
}) {
  // First check if the variant exists and belongs to the user
  const variant = await db.offerVariant.findUnique({
    where: { id: variantId },
    include: { offer: true },
  });
  
  if (!variant || variant.offer.userId !== userId) {
    throw new Error("Variant not found");
  }
  
  return db.offerVariantItem.create({
    data: {
      description,
      quantity,
      unitPrice,
      totalPrice,
      taxRate,
      variantId,
    },
  });
}

export async function updateOfferVariantItem({
  id,
  description,
  quantity,
  unitPrice,
  totalPrice,
  taxRate,
  userId,
}: {
  id: string;
  description?: string;
  quantity?: number;
  unitPrice?: number;
  totalPrice?: number;
  taxRate?: number | null;
  userId: User["id"];
}) {
  // First check if the variant item exists and belongs to the user
  const variantItem = await db.offerVariantItem.findUnique({
    where: { id },
    include: { variant: { include: { offer: true } } },
  });
  
  if (!variantItem || variantItem.variant.offer.userId !== userId) {
    throw new Error("Variant item not found");
  }
  
  return db.offerVariantItem.update({
    where: { id },
    data: {
      description,
      quantity,
      unitPrice,
      totalPrice,
      taxRate,
    },
  });
}

export async function deleteOfferVariantItem({
  id,
  userId,
}: {
  id: string;
  userId: User["id"];
}) {
  // First check if the variant item exists and belongs to the user
  const variantItem = await db.offerVariantItem.findUnique({
    where: { id },
    include: { variant: { include: { offer: true } } },
  });
  
  if (!variantItem || variantItem.variant.offer.userId !== userId) {
    throw new Error("Variant item not found");
  }
  
  return db.offerVariantItem.delete({
    where: { id },
  });
}

export async function recalculateOfferVariantTotal({
  variantId,
  userId,
}: {
  variantId: string;
  userId: User["id"];
}) {
  // First check if the variant exists and belongs to the user
  const variant = await db.offerVariant.findUnique({
    where: { id: variantId },
    include: { 
      items: true,
      offer: true,
    },
  });
  
  if (!variant || variant.offer.userId !== userId) {
    throw new Error("Variant not found");
  }
  
  const totalAmount = variant.items.reduce((sum, item) => sum + item.totalPrice, 0);
  const taxAmount = variant.items.reduce((sum, item) => {
    if (item.taxRate) {
      return sum + (item.totalPrice * item.taxRate / 100);
    }
    return sum;
  }, 0);
  
  return db.offerVariant.update({
    where: { id: variantId },
    data: {
      totalAmount,
      taxAmount,
    },
  });
}

export async function selectOfferVariant({
  variantId,
  userId,
}: {
  variantId: string;
  userId: User["id"];
}) {
  // First check if the variant exists and belongs to the user
  const variant = await db.offerVariant.findUnique({
    where: { id: variantId },
    include: { offer: true },
  });
  
  if (!variant || variant.offer.userId !== userId) {
    throw new Error("Variant not found");
  }
  
  // Unset any existing selected variants
  await db.offerVariant.updateMany({
    where: { offerId: variant.offerId, isSelected: true },
    data: { isSelected: false },
  });
  
  // Set this variant as selected
  return db.offerVariant.update({
    where: { id: variantId },
    data: { isSelected: true },
  });
}