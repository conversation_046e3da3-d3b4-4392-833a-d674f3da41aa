import { prisma } from "~/db.server";

export interface RouteStop {
  id: string;
  serviceOrderId: string;
  customerId: string;
  customerName: string;
  address: string;
  latitude?: number;
  longitude?: number;
  estimatedDuration: number; // in minutes
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  timeWindow?: {
    start: string; // HH:MM format
    end: string;   // HH:MM format
  };
  serviceType: string;
  notes?: string;
}

export interface OptimizedRoute {
  technicianId: string;
  technicianName: string;
  date: string;
  stops: RouteStop[];
  totalDistance: number; // in kilometers
  totalDuration: number; // in minutes
  totalTravelTime: number; // in minutes
  efficiency: number; // percentage
  startLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
}

export interface RouteOptimizationOptions {
  date: Date;
  technicianIds?: string[];
  maxStopsPerRoute?: number;
  considerTraffic?: boolean;
  prioritizeUrgent?: boolean;
  respectTimeWindows?: boolean;
}

/**
 * Get service orders that need route optimization for a given date
 */
export async function getServiceOrdersForRouting(
  userId: string,
  date: Date,
  technicianIds?: string[]
): Promise<RouteStop[]> {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);

  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);

  const whereClause: any = {
    userId,
    scheduledDate: {
      gte: startOfDay,
      lte: endOfDay,
    },
    status: {
      in: ['PENDING', 'SCHEDULED'],
    },
  };

  if (technicianIds && technicianIds.length > 0) {
    whereClause.assignedTechnicianId = {
      in: technicianIds,
    };
  }

  const serviceOrders = await prisma.serviceOrder.findMany({
    where: whereClause,
    include: {
      customer: {
        select: {
          id: true,
          name: true,
          address: true,
          latitude: true,
          longitude: true,
        },
      },
    },
    orderBy: {
      priority: 'desc',
    },
  });

  return serviceOrders.map(order => ({
    id: order.id,
    serviceOrderId: order.id,
    customerId: order.customer.id,
    customerName: order.customer.name,
    address: order.customer.address || '',
    latitude: order.customer.latitude || undefined,
    longitude: order.customer.longitude || undefined,
    estimatedDuration: order.estimatedDuration || 60, // Default 1 hour
    priority: (order.priority as 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT') || 'MEDIUM',
    timeWindow: order.preferredTimeSlot ? {
      start: order.preferredTimeSlot.split('-')[0] || '09:00',
      end: order.preferredTimeSlot.split('-')[1] || '17:00',
    } : undefined,
    serviceType: order.type,
    notes: order.description || undefined,
  }));
}

/**
 * Calculate distance between two points using Haversine formula
 */
function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Estimate travel time between two points (simplified)
 */
function estimateTravelTime(distance: number): number {
  // Assume average speed of 40 km/h in urban areas
  const averageSpeed = 40;
  return Math.round((distance / averageSpeed) * 60); // Convert to minutes
}

/**
 * Simple nearest neighbor algorithm for route optimization
 */
function optimizeRouteOrder(
  stops: RouteStop[],
  startLat: number,
  startLon: number
): RouteStop[] {
  if (stops.length === 0) return [];

  const optimizedRoute: RouteStop[] = [];
  const remainingStops = [...stops];

  // Sort by priority first (urgent stops first)
  remainingStops.sort((a, b) => {
    const priorityOrder = { 'URGENT': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });

  let currentLat = startLat;
  let currentLon = startLon;

  // Handle urgent stops first
  const urgentStops = remainingStops.filter(stop => stop.priority === 'URGENT');
  urgentStops.forEach(stop => {
    optimizedRoute.push(stop);
    const index = remainingStops.indexOf(stop);
    remainingStops.splice(index, 1);
    if (stop.latitude && stop.longitude) {
      currentLat = stop.latitude;
      currentLon = stop.longitude;
    }
  });

  // Use nearest neighbor for remaining stops
  while (remainingStops.length > 0) {
    let nearestStop = remainingStops[0];
    let nearestDistance = Infinity;

    remainingStops.forEach(stop => {
      if (stop.latitude && stop.longitude) {
        const distance = calculateDistance(currentLat, currentLon, stop.latitude, stop.longitude);
        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearestStop = stop;
        }
      }
    });

    optimizedRoute.push(nearestStop);
    const index = remainingStops.indexOf(nearestStop);
    remainingStops.splice(index, 1);

    if (nearestStop.latitude && nearestStop.longitude) {
      currentLat = nearestStop.latitude;
      currentLon = nearestStop.longitude;
    }
  }

  return optimizedRoute;
}

/**
 * Calculate route metrics
 */
function calculateRouteMetrics(
  stops: RouteStop[],
  startLat: number,
  startLon: number
): {
  totalDistance: number;
  totalDuration: number;
  totalTravelTime: number;
  efficiency: number;
} {
  let totalDistance = 0;
  let totalTravelTime = 0;
  const totalDuration = stops.reduce((sum, stop) => sum + stop.estimatedDuration, 0);

  let currentLat = startLat;
  let currentLon = startLon;

  stops.forEach(stop => {
    if (stop.latitude && stop.longitude) {
      const distance = calculateDistance(currentLat, currentLon, stop.latitude, stop.longitude);
      const travelTime = estimateTravelTime(distance);

      totalDistance += distance;
      totalTravelTime += travelTime;

      currentLat = stop.latitude;
      currentLon = stop.longitude;
    }
  });

  // Calculate efficiency (service time vs total time)
  const totalTime = totalDuration + totalTravelTime;
  const efficiency = totalTime > 0 ? (totalDuration / totalTime) * 100 : 0;

  return {
    totalDistance: Math.round(totalDistance * 100) / 100,
    totalDuration,
    totalTravelTime,
    efficiency: Math.round(efficiency * 100) / 100,
  };
}

/**
 * Optimize routes for multiple technicians
 */
export async function optimizeRoutes(
  userId: string,
  options: RouteOptimizationOptions
): Promise<OptimizedRoute[]> {
  const stops = await getServiceOrdersForRouting(userId, options.date, options.technicianIds);

  if (stops.length === 0) {
    return [];
  }

  // Get technician information
  const technicians = await prisma.user.findMany({
    where: {
      id: options.technicianIds ? { in: options.technicianIds } : undefined,
      role: 'TECHNICIAN',
    },
    select: {
      id: true,
      name: true,
      settings: true,
    },
  });

  if (technicians.length === 0) {
    return [];
  }

  const optimizedRoutes: OptimizedRoute[] = [];

  // Simple distribution: divide stops among technicians
  const stopsPerTechnician = Math.ceil(stops.length / technicians.length);

  technicians.forEach((technician: { id: string; name: string; settings: any }, index: number) => {
    const technicianStops = stops.slice(
      index * stopsPerTechnician,
      (index + 1) * stopsPerTechnician
    );

    if (technicianStops.length === 0) return;

    // Default start location (could be from technician settings)
    const startLocation = {
      latitude: 52.2297, // Warsaw coordinates as default
      longitude: 21.0122,
      address: "Warsaw, Poland",
    };

    // Optimize the order of stops
    const optimizedStops = optimizeRouteOrder(
      technicianStops,
      startLocation.latitude,
      startLocation.longitude
    );

    // Calculate metrics
    const metrics = calculateRouteMetrics(
      optimizedStops,
      startLocation.latitude,
      startLocation.longitude
    );

    optimizedRoutes.push({
      technicianId: technician.id,
      technicianName: technician.name,
      date: options.date.toISOString().split('T')[0],
      stops: optimizedStops,
      startLocation,
      ...metrics,
    });
  });

  return optimizedRoutes;
}

/**
 * Get route optimization suggestions
 */
export async function getRouteOptimizationSuggestions(
  userId: string,
  date: Date
): Promise<{
  totalStops: number;
  unassignedStops: number;
  suggestions: string[];
  potentialSavings: {
    timeMinutes: number;
    distanceKm: number;
  };
}> {
  const stops = await getServiceOrdersForRouting(userId, date);
  const unassignedStops = stops.filter(stop => !stop.serviceOrderId);

  const suggestions: string[] = [];

  if (stops.length > 10) {
    suggestions.push("Consider splitting routes among multiple technicians for better efficiency");
  }

  if (unassignedStops.length > 0) {
    suggestions.push(`${unassignedStops.length} service orders need technician assignment`);
  }

  const urgentStops = stops.filter(stop => stop.priority === 'URGENT');
  if (urgentStops.length > 0) {
    suggestions.push(`${urgentStops.length} urgent stops should be prioritized`);
  }

  // Calculate potential savings (simplified)
  const potentialSavings = {
    timeMinutes: Math.round(stops.length * 5), // Assume 5 minutes saved per stop
    distanceKm: Math.round(stops.length * 2), // Assume 2 km saved per stop
  };

  return {
    totalStops: stops.length,
    unassignedStops: unassignedStops.length,
    suggestions,
    potentialSavings,
  };
}
