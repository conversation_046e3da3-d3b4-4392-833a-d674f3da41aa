import type { User } from "@prisma/client";
import { prisma } from "~/db.server";

/**
 * Get service order flow data for visualization
 */
export async function getServiceOrderFlowData({
  userId,
  startDate,
  endDate,
}: {
  userId: User["id"];
  startDate?: Date;
  endDate?: Date;
}) {
  const where = {
    userId,
    ...(startDate && endDate
      ? {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        }
      : {}),
  };

  // Get service orders with their status changes
  const serviceOrders = await prisma.serviceOrder.findMany({
    where,
    select: {
      id: true,
      title: true,
      status: true,
      priority: true,
      createdAt: true,
      updatedAt: true,
      customer: {
        select: {
          id: true,
          name: true,
        },
      },
    },
    orderBy: { createdAt: "asc" },
  });

  // Count service orders by status
  const statusCounts = await prisma.serviceOrder.groupBy({
    by: ["status"],
    where,
    _count: {
      id: true,
    },
  });

  // Count service orders by priority
  const priorityCounts = await prisma.serviceOrder.groupBy({
    by: ["priority"],
    where,
    _count: {
      id: true,
    },
  });

  // Get average time to complete service orders
  const completedOrders = await prisma.serviceOrder.findMany({
    where: {
      ...where,
      status: "COMPLETED",
      completedDate: { not: null },
    },
    select: {
      createdAt: true,
      completedDate: true,
    },
  });

  const averageCompletionTime = completedOrders.length
    ? completedOrders.reduce((sum, order) => {
        const createdAt = new Date(order.createdAt).getTime();
        const completedDate = new Date(order.completedDate!).getTime();
        return sum + (completedDate - createdAt);
      }, 0) / completedOrders.length
    : 0;

  return {
    serviceOrders,
    statusCounts,
    priorityCounts,
    averageCompletionTime,
    completedOrdersCount: completedOrders.length,
  };
}

/**
 * Get data for interactive data exploration
 */
export async function getDataExplorationData({
  userId,
  startDate,
  endDate,
}: {
  userId: User["id"];
  startDate?: Date;
  endDate?: Date;
}) {
  const where = {
    userId,
    ...(startDate && endDate
      ? {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        }
      : {}),
  };

  // Get service orders by month
  const serviceOrdersByMonth = await prisma.$queryRaw`
    SELECT 
      strftime('%Y-%m', "createdAt") as month,
      COUNT(*) as count
    FROM "ServiceOrder"
    WHERE "userId" = ${userId}
    GROUP BY month
    ORDER BY month
  `;

  // Get customers with most service orders
  const topCustomers = await prisma.customer.findMany({
    where: { userId },
    select: {
      id: true,
      name: true,
      _count: {
        select: {
          serviceOrders: true,
        },
      },
    },
    orderBy: {
      serviceOrders: {
        _count: "desc",
      },
    },
    take: 5,
  });

  // Get devices with most service orders
  const topDevices = await prisma.device.findMany({
    where: { userId },
    select: {
      id: true,
      name: true,
      model: true,
      _count: {
        select: {
          serviceOrders: true,
        },
      },
    },
    orderBy: {
      serviceOrders: {
        _count: "desc",
      },
    },
    take: 5,
  });

  // Get service order types distribution
  const serviceOrderTypes = await prisma.serviceOrder.groupBy({
    by: ["type"],
    where,
    _count: {
      id: true,
    },
  });

  return {
    serviceOrdersByMonth,
    topCustomers,
    topDevices,
    serviceOrderTypes,
  };
}