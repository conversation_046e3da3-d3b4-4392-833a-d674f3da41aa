/**
 * Scheduled Report Generation Service
 * 
 * Automated report scheduling and email delivery system
 * Integrates with GoBackend-Kratos and data export services
 */

import { dataExportService } from './data-export.server';
import { goBackendBridge } from './gobackend-bridge.server';

interface ScheduledReport {
  id: string;
  name: string;
  description: string;
  reportConfig: any;
  schedule: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
    dayOfWeek?: number; // 0-6 for weekly
    dayOfMonth?: number; // 1-31 for monthly
    time: string; // HH:MM format
    timezone: string;
  };
  recipients: Array<{
    email: string;
    name: string;
    role: 'admin' | 'manager' | 'customer';
  }>;
  format: 'pdf' | 'excel' | 'csv';
  isActive: boolean;
  lastRun?: Date;
  nextRun: Date;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

interface EmailTemplate {
  subject: string;
  body: string;
  attachmentName: string;
}

export class ScheduledReportsService {
  private static instance: ScheduledReportsService;
  private scheduledReports: Map<string, ScheduledReport> = new Map();
  private runningJobs: Set<string> = new Set();

  static getInstance(): ScheduledReportsService {
    if (!ScheduledReportsService.instance) {
      ScheduledReportsService.instance = new ScheduledReportsService();
    }
    return ScheduledReportsService.instance;
  }

  constructor() {
    // Start the scheduler
    this.startScheduler();
  }

  /**
   * Create a new scheduled report
   */
  async createScheduledReport(reportData: Omit<ScheduledReport, 'id' | 'createdAt' | 'updatedAt' | 'nextRun'>): Promise<string> {
    const id = `scheduled_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const scheduledReport: ScheduledReport = {
      ...reportData,
      id,
      createdAt: new Date(),
      updatedAt: new Date(),
      nextRun: this.calculateNextRun(reportData.schedule),
    };

    this.scheduledReports.set(id, scheduledReport);
    
    // Save to database through GoBackend
    await this.saveScheduledReportToDatabase(scheduledReport);
    
    console.log(`Created scheduled report: ${scheduledReport.name} (${id})`);
    return id;
  }

  /**
   * Update an existing scheduled report
   */
  async updateScheduledReport(id: string, updates: Partial<ScheduledReport>): Promise<boolean> {
    const existingReport = this.scheduledReports.get(id);
    if (!existingReport) {
      return false;
    }

    const updatedReport: ScheduledReport = {
      ...existingReport,
      ...updates,
      id,
      updatedAt: new Date(),
      nextRun: updates.schedule ? this.calculateNextRun(updates.schedule) : existingReport.nextRun,
    };

    this.scheduledReports.set(id, updatedReport);
    await this.saveScheduledReportToDatabase(updatedReport);
    
    console.log(`Updated scheduled report: ${updatedReport.name} (${id})`);
    return true;
  }

  /**
   * Delete a scheduled report
   */
  async deleteScheduledReport(id: string): Promise<boolean> {
    const report = this.scheduledReports.get(id);
    if (!report) {
      return false;
    }

    this.scheduledReports.delete(id);
    await this.deleteScheduledReportFromDatabase(id);
    
    console.log(`Deleted scheduled report: ${report.name} (${id})`);
    return true;
  }

  /**
   * Get all scheduled reports
   */
  getScheduledReports(): ScheduledReport[] {
    return Array.from(this.scheduledReports.values());
  }

  /**
   * Get a specific scheduled report
   */
  getScheduledReport(id: string): ScheduledReport | undefined {
    return this.scheduledReports.get(id);
  }

  /**
   * Manually run a scheduled report
   */
  async runScheduledReport(id: string): Promise<boolean> {
    const report = this.scheduledReports.get(id);
    if (!report || this.runningJobs.has(id)) {
      return false;
    }

    try {
      this.runningJobs.add(id);
      await this.executeReport(report);
      
      // Update last run time and calculate next run
      report.lastRun = new Date();
      report.nextRun = this.calculateNextRun(report.schedule);
      this.scheduledReports.set(id, report);
      await this.saveScheduledReportToDatabase(report);
      
      return true;
    } catch (error) {
      console.error(`Failed to run scheduled report ${id}:`, error);
      return false;
    } finally {
      this.runningJobs.delete(id);
    }
  }

  /**
   * Start the scheduler that checks for reports to run
   */
  private startScheduler(): void {
    // Check every minute for reports to run
    setInterval(() => {
      this.checkAndRunDueReports();
    }, 60000);

    console.log('Scheduled reports service started');
  }

  /**
   * Check for reports that are due to run
   */
  private async checkAndRunDueReports(): Promise<void> {
    const now = new Date();
    
    for (const report of this.scheduledReports.values()) {
      if (
        report.isActive &&
        report.nextRun <= now &&
        !this.runningJobs.has(report.id)
      ) {
        console.log(`Running scheduled report: ${report.name} (${report.id})`);
        this.runScheduledReport(report.id);
      }
    }
  }

  /**
   * Execute a scheduled report
   */
  private async executeReport(report: ScheduledReport): Promise<void> {
    try {
      // Generate the report data
      const reportData = await this.generateReportData(report.reportConfig);
      
      // Create export configuration
      const exportConfig = {
        title: report.name,
        description: report.description,
        data: reportData,
        columns: report.reportConfig.selectedFields?.map((field: any) => ({
          key: field.name,
          label: field.label,
          type: field.type,
        })) || [],
        format: report.format,
        metadata: {
          generatedAt: new Date(),
          generatedBy: 'Scheduled Report System',
          totalRows: reportData.length,
          reportId: report.id,
        },
      };

      // Generate the report file
      const reportBuffer = await dataExportService.exportData(exportConfig);
      
      // Send email to recipients
      await this.sendReportEmail(report, reportBuffer);
      
      console.log(`Successfully executed scheduled report: ${report.name}`);
    } catch (error) {
      console.error(`Failed to execute scheduled report ${report.id}:`, error);
      
      // Send error notification to admin
      await this.sendErrorNotification(report, error);
    }
  }

  /**
   * Generate report data based on configuration
   */
  private async generateReportData(reportConfig: any): Promise<any[]> {
    // Use the same logic as the preview API but without limits
    const query = this.buildReportQuery(reportConfig);
    
    const result = await goBackendBridge.executeCustomQuery(query, {
      skipCache: true, // Always get fresh data for scheduled reports
    });

    if (!result.success) {
      throw new Error(`Failed to generate report data: ${result.error?.message}`);
    }

    return result.data || [];
  }

  /**
   * Build SQL query for report
   */
  private buildReportQuery(config: any): string {
    // Simplified query builder - in production, use the same logic as preview API
    const fields = config.selectedFields?.map((f: any) => f.name).join(', ') || '*';
    const table = config.selectedFields?.[0]?.table || 'customers';
    
    let query = `SELECT ${fields} FROM ${table}`;
    
    // Add filters if any
    if (config.filters && config.filters.length > 0) {
      const conditions = config.filters.map((filter: any) => 
        `${filter.field} ${this.getOperatorSQL(filter.operator)} '${filter.value}'`
      );
      query += ` WHERE ${conditions.join(' AND ')}`;
    }
    
    return query;
  }

  /**
   * Convert filter operator to SQL
   */
  private getOperatorSQL(operator: string): string {
    switch (operator) {
      case 'equals': return '=';
      case 'contains': return 'ILIKE';
      case 'greater_than': return '>';
      case 'less_than': return '<';
      default: return '=';
    }
  }

  /**
   * Send report via email
   */
  private async sendReportEmail(report: ScheduledReport, reportBuffer: Buffer): Promise<void> {
    const template = this.getEmailTemplate(report);
    
    for (const recipient of report.recipients) {
      try {
        await this.sendEmail({
          to: recipient.email,
          subject: template.subject,
          body: template.body.replace('{{recipientName}}', recipient.name),
          attachments: [{
            filename: template.attachmentName,
            content: reportBuffer,
            contentType: this.getContentType(report.format),
          }],
        });
        
        console.log(`Sent report to ${recipient.email}`);
      } catch (error) {
        console.error(`Failed to send report to ${recipient.email}:`, error);
      }
    }
  }

  /**
   * Get email template for report
   */
  private getEmailTemplate(report: ScheduledReport): EmailTemplate {
    const fileExtension = report.format === 'excel' ? 'xlsx' : report.format;
    
    return {
      subject: `${report.name} - ${new Date().toLocaleDateString()}`,
      body: `
        Dear {{recipientName}},

        Please find attached your scheduled report: ${report.name}

        Report Details:
        - Generated: ${new Date().toLocaleString()}
        - Format: ${report.format.toUpperCase()}
        - Frequency: ${report.schedule.frequency}

        ${report.description ? `Description: ${report.description}` : ''}

        This is an automated report from your HVAC CRM system.

        Best regards,
        HVAC CRM Team
      `,
      attachmentName: `${report.name.toLowerCase().replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.${fileExtension}`,
    };
  }

  /**
   * Send error notification
   */
  private async sendErrorNotification(report: ScheduledReport, error: any): Promise<void> {
    // Find admin recipients
    const adminRecipients = report.recipients.filter(r => r.role === 'admin');
    
    for (const admin of adminRecipients) {
      try {
        await this.sendEmail({
          to: admin.email,
          subject: `Scheduled Report Error: ${report.name}`,
          body: `
            Dear ${admin.name},

            There was an error generating the scheduled report "${report.name}".

            Error Details:
            ${error instanceof Error ? error.message : String(error)}

            Report ID: ${report.id}
            Scheduled Time: ${report.nextRun.toLocaleString()}

            Please check the system logs for more details.

            Best regards,
            HVAC CRM System
          `,
        });
      } catch (emailError) {
        console.error(`Failed to send error notification to ${admin.email}:`, emailError);
      }
    }
  }

  /**
   * Calculate next run time based on schedule
   */
  private calculateNextRun(schedule: ScheduledReport['schedule']): Date {
    const now = new Date();
    const [hours, minutes] = schedule.time.split(':').map(Number);
    
    const nextRun = new Date();
    nextRun.setHours(hours, minutes, 0, 0);
    
    // If the time has already passed today, move to next occurrence
    if (nextRun <= now) {
      switch (schedule.frequency) {
        case 'daily':
          nextRun.setDate(nextRun.getDate() + 1);
          break;
        case 'weekly':
          nextRun.setDate(nextRun.getDate() + 7);
          break;
        case 'monthly':
          nextRun.setMonth(nextRun.getMonth() + 1);
          if (schedule.dayOfMonth) {
            nextRun.setDate(schedule.dayOfMonth);
          }
          break;
        case 'quarterly':
          nextRun.setMonth(nextRun.getMonth() + 3);
          break;
      }
    }
    
    return nextRun;
  }

  /**
   * Get content type for file format
   */
  private getContentType(format: string): string {
    switch (format) {
      case 'pdf':
        return 'application/pdf';
      case 'excel':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'csv':
        return 'text/csv';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * Send email (placeholder - integrate with actual email service)
   */
  private async sendEmail(emailData: {
    to: string;
    subject: string;
    body: string;
    attachments?: Array<{
      filename: string;
      content: Buffer;
      contentType: string;
    }>;
  }): Promise<void> {
    // TODO: Integrate with actual email service (SendGrid, AWS SES, etc.)
    console.log(`Email sent to ${emailData.to}: ${emailData.subject}`);
  }

  /**
   * Database operations (placeholder - integrate with actual database)
   */
  private async saveScheduledReportToDatabase(report: ScheduledReport): Promise<void> {
    // TODO: Save to database through GoBackend
    console.log(`Saved scheduled report to database: ${report.id}`);
  }

  private async deleteScheduledReportFromDatabase(id: string): Promise<void> {
    // TODO: Delete from database through GoBackend
    console.log(`Deleted scheduled report from database: ${id}`);
  }

  /**
   * Load scheduled reports from database on startup
   */
  async loadScheduledReportsFromDatabase(): Promise<void> {
    // TODO: Load from database through GoBackend
    console.log('Loaded scheduled reports from database');
  }
}

export const scheduledReportsService = ScheduledReportsService.getInstance();
