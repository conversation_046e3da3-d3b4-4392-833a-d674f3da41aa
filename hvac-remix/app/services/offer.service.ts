import type { User } from "@prisma/client";
import { prisma } from "~/db.server";

export type { Offer, OfferItem, OfferTemplate, OfferVariant, OfferVariantItem } from "@prisma/client";

export type OfferWithRelations = Awaited<ReturnType<typeof getOffer>>;

export async function getOffers({
  userId,
  status,
  customerId,
  page = 1,
  limit = 20,
}: {
  userId: User["id"];
  status?: string;
  customerId?: string;
  page?: number;
  limit?: number;
}) {
  const skip = (page - 1) * limit;
  
  const where = {
    userId,
    ...(status ? { status } : {}),
    ...(customerId ? { customerId } : {}),
  };

  const offers = await prisma.offer.findMany({
    where,
    select: {
      id: true,
      title: true,
      status: true,
      totalAmount: true,
      validUntil: true,
      createdAt: true,
      updatedAt: true,
      customer: {
        select: {
          id: true,
          name: true,
        },
      },
    },
    orderBy: { updatedAt: "desc" },
    skip,
    take: limit,
  });

  const count = await prisma.offer.count({ where });

  return {
    offers,
    count,
    pages: Math.ceil(count / limit),
    currentPage: page,
  };
}

export async function getOffer({
  id,
  userId,
}: {
  id: string;
  userId: User["id"];
}) {
  return prisma.offer.findFirst({
    where: { id, userId },
    include: {
      customer: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          address: true,
          city: true,
          postalCode: true,
          country: true,
        },
      },
      items: true,
      variants: {
        include: {
          items: true,
        },
      },
      template: true,
      serviceOrders: {
        select: {
          id: true,
          title: true,
          status: true,
          createdAt: true,
        },
      },
    },
  });
}

export async function createOffer({
  title,
  description,
  validUntil,
  totalAmount,
  taxAmount,
  discountAmount,
  notes,
  customerId,
  userId,
  templateId,
  items,
}: {
  title: string;
  description?: string;
  validUntil?: Date;
  totalAmount: number;
  taxAmount?: number;
  discountAmount?: number;
  notes?: string;
  customerId: string;
  userId: User["id"];
  templateId?: string;
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    taxRate?: number;
  }>;
}) {
  return prisma.offer.create({
    data: {
      title,
      description,
      validUntil,
      totalAmount,
      taxAmount,
      discountAmount,
      notes,
      customerId,
      userId,
      templateId,
      items: {
        create: items,
      },
    },
  });
}

export async function updateOffer({
  id,
  title,
  description,
  status,
  validUntil,
  totalAmount,
  taxAmount,
  discountAmount,
  notes,
  followUpDate,
  followUpNotes,
  userId,
}: {
  id: string;
  title?: string;
  description?: string | null;
  status?: string;
  validUntil?: Date | null;
  totalAmount?: number;
  taxAmount?: number | null;
  discountAmount?: number | null;
  notes?: string | null;
  followUpDate?: Date | null;
  followUpNotes?: string | null;
  userId: User["id"];
}) {
  return prisma.offer.update({
    where: { id },
    data: {
      title,
      description,
      status,
      validUntil,
      totalAmount,
      taxAmount,
      discountAmount,
      notes,
      followUpDate,
      followUpNotes,
    },
  });
}

export async function deleteOffer({
  id,
  userId,
}: {
  id: string;
  userId: User["id"];
}) {
  return prisma.offer.deleteMany({
    where: { id, userId },
  });
}

export async function markOfferAsSent({
  id,
  userId,
}: {
  id: string;
  userId: User["id"];
}) {
  return prisma.offer.update({
    where: { id },
    data: {
      status: "SENT",
      sentAt: new Date(),
    },
  });
}

export async function markOfferAsViewed({
  id,
}: {
  id: string;
}) {
  return prisma.offer.update({
    where: { id },
    data: {
      viewedAt: new Date(),
    },
  });
}

export async function respondToOffer({
  id,
  status,
  userId,
}: {
  id: string;
  status: "ACCEPTED" | "REJECTED";
  userId: User["id"];
}) {
  return prisma.offer.update({
    where: { id },
    data: {
      status,
      respondedAt: new Date(),
    },
  });
}

export async function createServiceOrderFromOffer({
  offerId,
  userId,
}: {
  offerId: string;
  userId: User["id"];
}) {
  const offer = await getOffer({ id: offerId, userId });
  
  if (!offer) {
    throw new Error("Offer not found");
  }
  
  return prisma.serviceOrder.create({
    data: {
      title: `Service Order from Offer: ${offer.title}`,
      description: offer.description,
      customerId: offer.customerId,
      userId,
      offerId,
      status: "PENDING",
      priority: "MEDIUM",
      type: "SERVICE",
    },
  });
}

export async function addOfferItem({
  offerId,
  description,
  quantity,
  unitPrice,
  totalPrice,
  taxRate,
  userId,
}: {
  offerId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate?: number;
  userId: User["id"];
}) {
  // First check if the offer exists and belongs to the user
  const offer = await prisma.offer.findFirst({
    where: { id: offerId, userId },
  });
  
  if (!offer) {
    throw new Error("Offer not found");
  }
  
  return prisma.offerItem.create({
    data: {
      description,
      quantity,
      unitPrice,
      totalPrice,
      taxRate,
      offerId,
    },
  });
}

export async function updateOfferItem({
  id,
  description,
  quantity,
  unitPrice,
  totalPrice,
  taxRate,
  userId,
}: {
  id: string;
  description?: string;
  quantity?: number;
  unitPrice?: number;
  totalPrice?: number;
  taxRate?: number | null;
  userId: User["id"];
}) {
  // First check if the offer item exists and belongs to the user
  const offerItem = await prisma.offerItem.findFirst({
    where: { id },
    include: { offer: true },
  });
  
  if (!offerItem || offerItem.offer.userId !== userId) {
    throw new Error("Offer item not found");
  }
  
  return prisma.offerItem.update({
    where: { id },
    data: {
      description,
      quantity,
      unitPrice,
      totalPrice,
      taxRate,
    },
  });
}

export async function deleteOfferItem({
  id,
  userId,
}: {
  id: string;
  userId: User["id"];
}) {
  // First check if the offer item exists and belongs to the user
  const offerItem = await prisma.offerItem.findFirst({
    where: { id },
    include: { offer: true },
  });
  
  if (!offerItem || offerItem.offer.userId !== userId) {
    throw new Error("Offer item not found");
  }
  
  return prisma.offerItem.delete({
    where: { id },
  });
}

export async function recalculateOfferTotal({
  offerId,
  userId,
}: {
  offerId: string;
  userId: User["id"];
}) {
  // First check if the offer exists and belongs to the user
  const offer = await prisma.offer.findFirst({
    where: { id: offerId, userId },
    include: { items: true },
  });
  
  if (!offer) {
    throw new Error("Offer not found");
  }
  
  const totalAmount = offer.items.reduce((sum, item) => sum + item.totalPrice, 0);
  const taxAmount = offer.items.reduce((sum, item) => {
    if (item.taxRate) {
      return sum + (item.totalPrice * item.taxRate / 100);
    }
    return sum;
  }, 0);
  
  return prisma.offer.update({
    where: { id: offerId },
    data: {
      totalAmount,
      taxAmount,
    },
  });
}