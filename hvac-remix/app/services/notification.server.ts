/**
 * Notification service for sending notifications through multiple channels
 *
 * This service provides a unified interface for sending notifications through
 * different channels (in-app, email, SMS, push) based on user preferences.
 */

import { prisma } from "~/db.server";
import {
  createNotification,
  getNotificationPreferences
} from "~/models/notification.server";
import { getUserById } from "~/models/user.server";
import {
  sendSMS,
  sendServiceOrderNotificationSMS,
  sendCalendarReminderSMS,
  sendDeviceAlertSMS,
  shouldSendSMS
} from "~/services/sms.server";
import type {
  NotificationType,
  NotificationPriority,
  NotificationChannel,
  ExtendedNotification
} from "~/types/shared";
import {
  sendEmail,
  sendServiceOrderNotificationEmail,
  sendCalendarReminderEmail,
  sendDeviceAlertEmail,
  shouldSendEmail
} from "~/utils/email.server";

/**
 * Send a notification through multiple channels
 *
 * @param params Notification parameters
 * @returns Promise that resolves to the created notification
 */
export async function sendMultiChannelNotification({
  type,
  title,
  message,
  userId,
  priority = 'medium',
  link,
  // Additional data for specific notification types
  serviceOrderId,
  deviceId,
  deviceName,
  eventId,
  eventDate,
  location,
}: {
  type: NotificationType;
  title: string;
  message: string;
  userId: string;
  priority?: NotificationPriority;
  link?: string;
  serviceOrderId?: string;
  deviceId?: string;
  deviceName?: string;
  eventId?: string;
  eventDate?: Date;
  location?: string;
}): Promise<ExtendedNotification | null> {
  try {
    // Get user
    const user = await getUserById(userId);
    if (!user) {
      console.error(`User not found: ${userId}`);
      return null;
    }

    // Get user notification preferences
    const preferences = await getNotificationPreferences(userId);

    // Determine which channels to use
    const channels: NotificationChannel[] = [];

    // Always add in-app notification if enabled
    if (preferences.inAppNotifications) {
      channels.push('IN_APP');
    }

    // Check if email notifications are enabled for this type
    const emailCheck = await shouldSendEmail(userId, type);
    if (emailCheck.shouldSend && emailCheck.emailAddress) {
      channels.push('EMAIL');
    }

    // Check if SMS notifications are enabled for this type
    const smsCheck = await shouldSendSMS(userId, type);
    if (smsCheck.shouldSend && smsCheck.phoneNumber) {
      channels.push('SMS');
    }

    // Check if push notifications are enabled for this type
    if (preferences.pushNotifications) {
      channels.push('PUSH');
    }

    // If no channels are enabled, don't create a notification
    if (channels.length === 0) {
      console.log(`No notification channels enabled for user ${userId}`);
      return null;
    }

    // Create notification in database
    const notification = await prisma.notification.create({
      data: {
        type,
        title,
        message,
        userId,
        priority,
        link,
        status: 'unread',
        channels: JSON.stringify(channels), // Store channels as JSON string
        // Initialize delivery status
        emailSent: channels.includes('EMAIL') ? false : null,
        smsSent: channels.includes('SMS') ? false : null,
        pushSent: channels.includes('PUSH') ? false : null,
        // Store contact info if needed
        phoneNumber: smsCheck.phoneNumber,
        emailAddress: emailCheck.emailAddress,
      }
    });

    // Send notifications through each channel
    const sendPromises: Promise<any>[] = [];

    // Send email notification
    if (channels.includes('EMAIL') && emailCheck.emailAddress) {
      let emailPromise;

      switch (type) {
        case 'SERVICE_ORDER_CREATED':
        case 'SERVICE_ORDER_UPDATED':
        case 'SERVICE_ORDER_COMPLETED':
          if (serviceOrderId) {
            emailPromise = sendServiceOrderNotificationEmail(
              emailCheck.emailAddress,
              serviceOrderId,
              title,
              type === 'SERVICE_ORDER_COMPLETED' ? 'COMPLETED' :
                type === 'SERVICE_ORDER_CREATED' ? 'PENDING' : 'IN_PROGRESS'
            );
          } else {
            emailPromise = sendEmail({
              to: emailCheck.emailAddress,
              subject: title,
              text: message,
            });
          }
          break;

        case 'CALENDAR_REMINDER':
          if (eventDate) {
            emailPromise = sendCalendarReminderEmail(
              emailCheck.emailAddress,
              title,
              eventDate,
              location,
              eventId
            );
          } else {
            emailPromise = sendEmail({
              to: emailCheck.emailAddress,
              subject: title,
              text: message,
            });
          }
          break;

        case 'DEVICE_ALERT':
          if (deviceId && deviceName) {
            emailPromise = sendDeviceAlertEmail(
              emailCheck.emailAddress,
              deviceId,
              deviceName,
              message,
              priority as 'low' | 'medium' | 'high'
            );
          } else {
            emailPromise = sendEmail({
              to: emailCheck.emailAddress,
              subject: title,
              text: message,
            });
          }
          break;

        default:
          emailPromise = sendEmail({
            to: emailCheck.emailAddress,
            subject: title,
            text: message,
          });
      }

      // Update email sent status when done
      emailPromise.then(success => {
        if (success) {
          prisma.notification.update({
            where: { id: notification.id },
            data: { emailSent: true }
          }).catch(err => console.error(`Error updating email sent status: ${err}`));
        }
      });

      sendPromises.push(emailPromise);
    }

    // Send SMS notification
    if (channels.includes('SMS') && smsCheck.phoneNumber) {
      let smsPromise;

      switch (type) {
        case 'SERVICE_ORDER_CREATED':
        case 'SERVICE_ORDER_UPDATED':
        case 'SERVICE_ORDER_COMPLETED':
          if (serviceOrderId) {
            smsPromise = sendServiceOrderNotificationSMS(
              smsCheck.phoneNumber,
              serviceOrderId,
              title,
              type === 'SERVICE_ORDER_COMPLETED' ? 'COMPLETED' :
                type === 'SERVICE_ORDER_CREATED' ? 'PENDING' : 'IN_PROGRESS'
            );
          } else {
            smsPromise = sendSMS({
              to: smsCheck.phoneNumber,
              message,
            });
          }
          break;

        case 'CALENDAR_REMINDER':
          if (eventDate) {
            smsPromise = sendCalendarReminderSMS(
              smsCheck.phoneNumber,
              title,
              eventDate,
              location
            );
          } else {
            smsPromise = sendSMS({
              to: smsCheck.phoneNumber,
              message,
            });
          }
          break;

        case 'DEVICE_ALERT':
          if (deviceName) {
            smsPromise = sendDeviceAlertSMS(
              smsCheck.phoneNumber,
              deviceName,
              message
            );
          } else {
            smsPromise = sendSMS({
              to: smsCheck.phoneNumber,
              message,
            });
          }
          break;

        default:
          smsPromise = sendSMS({
            to: smsCheck.phoneNumber,
            message,
          });
      }

      // Update SMS sent status when done
      smsPromise.then(success => {
        if (success) {
          prisma.notification.update({
            where: { id: notification.id },
            data: { smsSent: true }
          }).catch(err => console.error(`Error updating SMS sent status: ${err}`));
        }
      });

      sendPromises.push(smsPromise);
    }

    // Send push notification (placeholder for future implementation)
    if (channels.includes('PUSH')) {
      // TODO: Implement push notifications
      console.log(`Push notification would be sent to user ${userId}`);
    }

    // Wait for all notifications to be sent
    await Promise.all(sendPromises);

    // Return the created notification with parsed channels
    return {
      ...notification,
      createdAt: notification.createdAt.toISOString(),
      channels: JSON.parse(notification.channels) as NotificationChannel[],
    } as ExtendedNotification;
  } catch (error) {
    console.error("Error sending multi-channel notification:", error);
    return null;
  }
}
