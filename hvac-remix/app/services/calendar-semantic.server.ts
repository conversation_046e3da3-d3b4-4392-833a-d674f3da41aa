import type { CalendarEntry } from "@prisma/client";
import fetch from "node-fetch";
import { prisma } from "~/db.server";

// Configuration
const SEMANTIC_ANALYSIS_API_URL = process.env.SEMANTIC_ANALYSIS_API_URL || "http://localhost:8052/api/calendar/analyze";

/**
 * Interface for semantic analysis result
 */
export interface SemanticAnalysisResult {
  category: string;
  entities: {
    client: string[];
    client_contact: {
      phone: string | null;
      email: string | null;
      address: string | null;
    };
    technician: string[];
    device: string[];
    device_count: number;
    device_details: {
      model: string | null;
      manufacturer: string | null;
      type: string | null;
    };
    location: string[];
    service_type: string[];
  };
  technical_issues: string[];
  spare_parts: string[];
  costs: {
    amount: number | null;
    currency: string | null;
    description: string | null;
  };
  priority: string;
  status: string;
  keywords: string[];
  confidence: number;
}

/**
 * Analyze a calendar entry using the semantic analysis service
 */
export async function analyzeCalendarEntry(
  calendarEntry: CalendarEntry
): Promise<{
  success: boolean;
  message: string;
  analysis?: SemanticAnalysisResult;
}> {
  try {
    // Prepare the entry data for analysis
    const entryData = {
      id: calendarEntry.id,
      title: calendarEntry.title,
      description: calendarEntry.description || "",
      location: calendarEntry.location || "",
      start_time: calendarEntry.startTime.toISOString(),
      end_time: calendarEntry.endTime.toISOString()
    };
    
    // Send request to semantic analysis service
    const response = await fetch(SEMANTIC_ANALYSIS_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(entryData),
    });
    
    if (!response.ok) {
      return {
        success: false,
        message: `Failed to analyze calendar entry: ${response.statusText}`,
      };
    }
    
    const analysisResult = await response.json() as SemanticAnalysisResult;
    
    return {
      success: true,
      message: "Successfully analyzed calendar entry",
      analysis: analysisResult,
    };
  } catch (error) {
    console.error("Failed to analyze calendar entry:", error);
    return {
      success: false,
      message: "Failed to analyze calendar entry",
    };
  }
}

/**
 * Update a calendar entry with semantic analysis results
 */
export async function updateCalendarEntryWithAnalysis(
  calendarEntryId: string,
  analysis: SemanticAnalysisResult
): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Extract relevant information from analysis
    const updates: any = {};
    
    // Update category if available
    if (analysis.category && analysis.category !== "unknown") {
      updates.type = analysis.category;
    }
    
    // Update status if available
    if (analysis.status && analysis.status !== "unknown") {
      updates.status = analysis.status;
    }
    
    // Update priority if available
    if (analysis.priority && analysis.priority !== "unknown") {
      updates.priority = analysis.priority;
    }
    
    // Extract client if available
    if (analysis.entities.client && analysis.entities.client.length > 0) {
      updates.customer = analysis.entities.client[0];
    }
    
    // Extract technician if available
    if (analysis.entities.technician && analysis.entities.technician.length > 0) {
      updates.technician = analysis.entities.technician[0];
    }
    
    // Extract device if available
    if (analysis.entities.device && analysis.entities.device.length > 0) {
      updates.device = analysis.entities.device[0];
    }
    
    // Store the full analysis as JSON
    updates.semanticAnalysis = analysis;
    updates.analysisTimestamp = new Date();
    
    // Update the calendar entry
    await prisma.calendarEntry.update({
      where: { id: calendarEntryId },
      data: updates,
    });
    
    return {
      success: true,
      message: "Successfully updated calendar entry with analysis",
    };
  } catch (error) {
    console.error("Failed to update calendar entry with analysis:", error);
    return {
      success: false,
      message: "Failed to update calendar entry with analysis",
    };
  }
}

/**
 * Analyze and update a calendar entry
 */
export async function analyzeAndUpdateCalendarEntry(
  calendarEntryId: string
): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Get the calendar entry
    const calendarEntry = await prisma.calendarEntry.findUnique({
      where: { id: calendarEntryId },
    });
    
    if (!calendarEntry) {
      return {
        success: false,
        message: "Calendar entry not found",
      };
    }
    
    // Analyze the calendar entry
    const analysisResult = await analyzeCalendarEntry(calendarEntry);
    
    if (!analysisResult.success || !analysisResult.analysis) {
      return {
        success: false,
        message: analysisResult.message,
      };
    }
    
    // Update the calendar entry with the analysis
    const updateResult = await updateCalendarEntryWithAnalysis(
      calendarEntryId,
      analysisResult.analysis
    );
    
    return updateResult;
  } catch (error) {
    console.error("Failed to analyze and update calendar entry:", error);
    return {
      success: false,
      message: "Failed to analyze and update calendar entry",
    };
  }
}