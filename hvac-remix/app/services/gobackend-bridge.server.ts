/**
 * GoBackend-Kratos Bridge Service for HVAC-Remix
 *
 * Server-side service for secure communication with GoBackend-Kratos
 * Handles authentication, error handling, and data transformation
 */

import { gobackendClient, handleGoBackendError, type GoBackendError } from '~/lib/gobackend-client';
import type {
  Customer,
  Job,
  AIAnalysisRequest,
  AIAnalysisResponse,
  Email,
  RealTimeMetrics,
  SystemHealth
} from '~/types/gobackend-api';
import GoBackendCache from './gobackend-cache';
import GoBackendConnectionPool from './gobackend-connection-pool';

/**
 * Enhanced Configuration for GoBackend bridge with connection pooling and caching
 */
const BRIDGE_CONFIG = {
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
  healthCheckInterval: 60000, // 1 minute
  // New connection pooling settings
  connectionPool: {
    maxConnections: 10,
    minConnections: 2,
    acquireTimeoutMillis: 30000,
    idleTimeoutMillis: 300000, // 5 minutes
  },
  // New caching settings
  cache: {
    defaultTTL: 300000, // 5 minutes
    maxSize: 1000,
    enableCompression: true,
  },
  // Enhanced monitoring
  monitoring: {
    enableMetrics: true,
    metricsInterval: 30000, // 30 seconds
    alertThresholds: {
      errorRate: 0.05, // 5%
      latency: 5000, // 5 seconds
      healthCheckFailures: 3,
    },
  },
};

/**
 * Authentication context for server-side requests
 */
interface AuthContext {
  userId?: string;
  token?: string;
  roles?: string[];
}

/**
 * Connection pool interface for managing GoBackend connections
 */
interface ConnectionPool {
  acquire(): Promise<Connection>;
  release(connection: Connection): void;
  destroy(): Promise<void>;
  getStats(): PoolStats;
}

interface Connection {
  id: string;
  isActive: boolean;
  lastUsed: Date;
  execute<T>(operation: () => Promise<T>): Promise<T>;
}

interface PoolStats {
  total: number;
  active: number;
  idle: number;
  waiting: number;
}

/**
 * Cache interface for storing API responses
 */
interface CacheEntry<T> {
  data: T;
  timestamp: Date;
  ttl: number;
  compressed?: boolean;
}

interface Cache {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  getStats(): CacheStats;
}

interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: number;
}

/**
 * Monitoring and metrics interface
 */
interface Metrics {
  requestCount: number;
  errorCount: number;
  totalLatency: number;
  averageLatency: number;
  errorRate: number;
  lastError?: Date;
  healthCheckFailures: number;
}

/**
 * Bridge response wrapper
 */
interface BridgeResponse<T> {
  success: boolean;
  data?: T;
  error?: GoBackendError;
  metadata?: {
    requestId: string;
    timestamp: Date;
    latency: number;
  };
}

/**
 * Main GoBackend Bridge Service
 */
export class GoBackendBridge {
  private static instance: GoBackendBridge;
  private healthStatus: 'healthy' | 'unhealthy' | 'unknown' = 'unknown';
  private lastHealthCheck: Date = new Date();

  // Enhanced features
  private connectionPool: GoBackendConnectionPool;
  private cache: GoBackendCache;
  private metrics: Metrics;
  private monitoringInterval: NodeJS.Timeout | null = null;

  private constructor() {
    // Initialize enhanced features
    this.connectionPool = new GoBackendConnectionPool(BRIDGE_CONFIG.connectionPool);
    this.cache = new GoBackendCache(BRIDGE_CONFIG.cache);
    this.metrics = {
      requestCount: 0,
      errorCount: 0,
      totalLatency: 0,
      averageLatency: 0,
      errorRate: 0,
      healthCheckFailures: 0,
    };

    // Start health monitoring and metrics collection
    this.startHealthMonitoring();
    this.startMetricsCollection();
  }

  static getInstance(): GoBackendBridge {
    if (!GoBackendBridge.instance) {
      GoBackendBridge.instance = new GoBackendBridge();
    }
    return GoBackendBridge.instance;
  }

  /**
   * Health monitoring
   */
  private async startHealthMonitoring() {
    setInterval(async () => {
      try {
        await this.checkHealth();
        this.metrics.healthCheckFailures = 0;
      } catch (error) {
        console.error('Health check failed:', error);
        this.healthStatus = 'unhealthy';
        this.metrics.healthCheckFailures++;

        // Alert if health check failures exceed threshold
        if (this.metrics.healthCheckFailures >= BRIDGE_CONFIG.monitoring.alertThresholds.healthCheckFailures) {
          this.alertHealthCheckFailures();
        }
      }
    }, BRIDGE_CONFIG.healthCheckInterval);
  }

  /**
   * Metrics collection and monitoring
   */
  private startMetricsCollection() {
    if (!BRIDGE_CONFIG.monitoring.enableMetrics) return;

    this.monitoringInterval = setInterval(() => {
      this.updateMetrics();
      this.checkAlertThresholds();
    }, BRIDGE_CONFIG.monitoring.metricsInterval);
  }

  /**
   * Update metrics calculations
   */
  private updateMetrics() {
    if (this.metrics.requestCount > 0) {
      this.metrics.averageLatency = this.metrics.totalLatency / this.metrics.requestCount;
      this.metrics.errorRate = this.metrics.errorCount / this.metrics.requestCount;
    }
  }

  /**
   * Check alert thresholds and trigger alerts
   */
  private checkAlertThresholds() {
    const thresholds = BRIDGE_CONFIG.monitoring.alertThresholds;

    if (this.metrics.errorRate > thresholds.errorRate) {
      this.alertHighErrorRate();
    }

    if (this.metrics.averageLatency > thresholds.latency) {
      this.alertHighLatency();
    }
  }

  /**
   * Alert handlers
   */
  private alertHealthCheckFailures() {
    console.error(`GoBackend health check failures exceeded threshold: ${this.metrics.healthCheckFailures}`);
    // TODO: Implement actual alerting (email, Slack, etc.)
  }

  private alertHighErrorRate() {
    console.warn(`GoBackend error rate is high: ${(this.metrics.errorRate * 100).toFixed(2)}%`);
    // TODO: Implement actual alerting
  }

  private alertHighLatency() {
    console.warn(`GoBackend average latency is high: ${this.metrics.averageLatency.toFixed(2)}ms`);
    // TODO: Implement actual alerting
  }

  async checkHealth(): Promise<SystemHealth> {
    const startTime = Date.now();

    try {
      const health = await gobackendClient.system.health.query();
      this.healthStatus = health.status === 'healthy' ? 'healthy' : 'unhealthy';
      this.lastHealthCheck = new Date();

      return health;
    } catch (error) {
      this.healthStatus = 'unhealthy';
      throw handleGoBackendError(error);
    }
  }

  /**
   * Enhanced request wrapper with caching, connection pooling, and retry logic
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    context?: AuthContext,
    options?: {
      cacheKey?: string;
      cacheTTL?: number;
      skipCache?: boolean;
    }
  ): Promise<BridgeResponse<T>> {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    // Try cache first if enabled
    if (options?.cacheKey && !options.skipCache) {
      try {
        const cachedData = await this.cache.get<T>(options.cacheKey);
        if (cachedData !== null) {
          return {
            success: true,
            data: cachedData,
            metadata: {
              requestId,
              timestamp: new Date(),
              latency: Date.now() - startTime,
              fromCache: true,
            },
          };
        }
      } catch (cacheError) {
        console.warn('Cache retrieval error:', cacheError);
      }
    }

    // Acquire connection from pool
    const connection = await this.connectionPool.acquire();

    try {
      for (let attempt = 1; attempt <= BRIDGE_CONFIG.retries; attempt++) {
        try {
          // Execute operation through connection
          const data = await connection.execute(operation);

          // Update metrics
          this.metrics.requestCount++;
          const latency = Date.now() - startTime;
          this.metrics.totalLatency += latency;

          // Cache successful response if enabled
          if (options?.cacheKey && !options.skipCache) {
            try {
              await this.cache.set(options.cacheKey, data, options.cacheTTL);
            } catch (cacheError) {
              console.warn('Cache storage error:', cacheError);
            }
          }

          return {
            success: true,
            data,
            metadata: {
              requestId,
              timestamp: new Date(),
              latency,
              attempt,
              fromCache: false,
            },
          };
        } catch (error) {
          const goBackendError = handleGoBackendError(error);

          // Update error metrics
          this.metrics.errorCount++;
          this.metrics.lastError = new Date();

          // Don't retry on authentication or validation errors
          if (goBackendError.statusCode === 401 || goBackendError.statusCode === 400) {
            return {
              success: false,
              error: goBackendError,
              metadata: {
                requestId,
                timestamp: new Date(),
                latency: Date.now() - startTime,
                attempt,
              },
            };
          }

          // Last attempt - return error
          if (attempt === BRIDGE_CONFIG.retries) {
            return {
              success: false,
              error: goBackendError,
              metadata: {
                requestId,
                timestamp: new Date(),
                latency: Date.now() - startTime,
                attempt,
              },
            };
          }

          // Wait before retry with exponential backoff
          const delay = BRIDGE_CONFIG.retryDelay * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    } finally {
      // Always release connection back to pool
      this.connectionPool.release(connection);
    }

    // This should never be reached, but TypeScript requires it
    throw new Error('Unexpected error in executeWithRetry');
  }

  /**
   * Customer Service Bridge Methods
   */
  async getCustomers(filters?: {
    search?: string;
    status?: 'active' | 'inactive';
    region?: string;
    limit?: number;
    offset?: number;
  }, context?: AuthContext) {
    const cacheKey = `customers:${JSON.stringify(filters || {})}`;
    return this.executeWithRetry(
      () => gobackendClient.customer.list.query(filters),
      context,
      { cacheKey, cacheTTL: 300000 } // 5 minutes cache
    );
  }

  async getCustomer(id: string, context?: AuthContext) {
    const cacheKey = `customer:${id}`;
    return this.executeWithRetry(
      () => gobackendClient.customer.get.query({ id }),
      context,
      { cacheKey, cacheTTL: 600000 } // 10 minutes cache for individual customers
    );
  }

  async createCustomer(data: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.customer.create.mutate(data),
      context
    );
  }

  async updateCustomer(id: string, data: Partial<Customer>, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.customer.update.mutate({ id, ...data }),
      context
    );
  }

  /**
   * Job Service Bridge Methods
   */
  async getJobs(filters?: {
    status?: Job['status'];
    customerId?: string;
    technicianId?: string;
    priority?: Job['priority'];
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    offset?: number;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.job.list.query(filters),
      context
    );
  }

  async getJob(id: string, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.job.get.query({ id }),
      context
    );
  }

  async createJob(data: Omit<Job, 'id' | 'createdAt' | 'updatedAt' | 'customer' | 'technician'>, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.job.create.mutate(data),
      context
    );
  }

  async updateJobStatus(id: string, status: Job['status'], context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.job.updateStatus.mutate({ id, status }),
      context
    );
  }

  async assignTechnician(jobId: string, technicianId: string, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.job.assignTechnician.mutate({ jobId, technicianId }),
      context
    );
  }

  /**
   * AI Service Bridge Methods
   */
  async analyzeCustomerIssue(data: AIAnalysisRequest, context?: AuthContext): Promise<BridgeResponse<AIAnalysisResponse>> {
    return this.executeWithRetry(
      () => gobackendClient.ai.analyzeIssue.mutate(data),
      context
    );
  }

  async chatWithAI(data: {
    message: string;
    context?: any;
    conversationId?: string;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.ai.chat.mutate(data),
      context
    );
  }

  async generateResponse(data: {
    customerMessage: string;
    context: any;
    tone?: 'professional' | 'friendly' | 'technical';
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.ai.generateResponse.mutate(data),
      context
    );
  }

  async predictMaintenance(data: {
    deviceId: string;
    deviceType: string;
    lastMaintenanceDate?: Date;
    usageData?: any;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.ai.predictMaintenance.mutate(data),
      context
    );
  }

  async optimizeRoute(data: {
    jobs: Array<{ id: string; location: any; priority: number }>;
    technicianLocation: any;
    constraints?: any;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.ai.optimizeRoute.mutate(data),
      context
    );
  }

  /**
   * Email Service Bridge Methods
   */
  async getEmails(filters?: {
    status?: Email['status'];
    category?: string;
    priority?: Email['priority'];
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    offset?: number;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.email.list.query(filters),
      context
    );
  }

  async analyzeEmail(emailId: string, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.email.analyze.mutate({ emailId }),
      context
    );
  }

  async sendEmail(data: {
    to: string[];
    subject: string;
    body: string;
    template?: string;
    attachments?: string[];
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.email.send.mutate(data),
      context
    );
  }

  /**
   * Analytics Service Bridge Methods
   */
  async getRealTimeMetrics(context?: AuthContext): Promise<BridgeResponse<RealTimeMetrics>> {
    return this.executeWithRetry(
      () => gobackendClient.analytics.getRealTimeMetrics.query(),
      context
    );
  }

  async getJobMetrics(filters?: {
    dateFrom?: Date;
    dateTo?: Date;
    groupBy?: 'day' | 'week' | 'month';
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.analytics.getJobMetrics.query(filters),
      context
    );
  }

  async getCustomerSatisfaction(filters?: {
    dateFrom?: Date;
    dateTo?: Date;
    customerId?: string;
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.analytics.getCustomerSatisfaction.query(filters),
      context
    );
  }

  async getRevenue(filters?: {
    dateFrom?: Date;
    dateTo?: Date;
    groupBy?: 'day' | 'week' | 'month';
  }, context?: AuthContext) {
    return this.executeWithRetry(
      () => gobackendClient.analytics.getRevenue.query(filters),
      context
    );
  }

  /**
   * Utility methods
   */
  getHealthStatus(): {
    status: 'healthy' | 'unhealthy' | 'unknown';
    lastCheck: Date;
  } {
    return {
      status: this.healthStatus,
      lastCheck: this.lastHealthCheck,
    };
  }

  async testConnection(): Promise<boolean> {
    try {
      const health = await this.checkHealth();
      return health.status === 'healthy';
    } catch {
      return false;
    }
  }

  /**
   * Enhanced bridge management methods
   */
  getMetrics(): Metrics {
    return { ...this.metrics };
  }

  getCacheStats() {
    return this.cache.getStats();
  }

  getConnectionPoolStats() {
    return this.connectionPool.getStats();
  }

  async clearCache(): Promise<void> {
    await this.cache.clear();
  }

  async invalidateCachePattern(pattern: string): Promise<number> {
    return this.cache.invalidatePattern(pattern);
  }

  /**
   * Cache invalidation helpers for data mutations
   */
  private async invalidateCustomerCache(customerId?: string): Promise<void> {
    if (customerId) {
      await this.cache.delete(`customer:${customerId}`);
    }
    await this.invalidateCachePattern('customers:.*');
  }

  private async invalidateJobCache(jobId?: string): Promise<void> {
    if (jobId) {
      await this.cache.delete(`job:${jobId}`);
    }
    await this.invalidateCachePattern('jobs:.*');
  }

  /**
   * Enhanced customer methods with cache invalidation
   */
  async createCustomerEnhanced(data: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>, context?: AuthContext) {
    const result = await this.executeWithRetry(
      () => gobackendClient.customer.create.mutate(data),
      context,
      { skipCache: true }
    );

    if (result.success) {
      await this.invalidateCustomerCache();
    }

    return result;
  }

  async updateCustomerEnhanced(id: string, data: Partial<Customer>, context?: AuthContext) {
    const result = await this.executeWithRetry(
      () => gobackendClient.customer.update.mutate({ id, ...data }),
      context,
      { skipCache: true }
    );

    if (result.success) {
      await this.invalidateCustomerCache(id);
    }

    return result;
  }

  /**
   * Cleanup and destroy
   */
  async destroy(): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    await this.connectionPool.destroy();
    this.cache.destroy();
  }
}

/**
 * Singleton instance export
 */
export const goBackendBridge = GoBackendBridge.getInstance();

/**
 * Convenience functions for common operations
 */
export async function analyzeHVACIssue(
  description: string,
  customerHistory?: any,
  context?: AuthContext
): Promise<AIAnalysisResponse | null> {
  const response = await goBackendBridge.analyzeCustomerIssue({
    description,
    customerHistory,
    urgencyLevel: 'medium',
  }, context);

  return response.success ? response.data! : null;
}

export async function getSystemHealth(): Promise<SystemHealth | null> {
  try {
    return await goBackendBridge.checkHealth();
  } catch {
    return null;
  }
}

export async function isGoBackendHealthy(): Promise<boolean> {
  const health = goBackendBridge.getHealthStatus();
  return health.status === 'healthy';
}

/**
 * Error handling utilities
 */
export function isBridgeError(error: any): error is GoBackendError {
  return error instanceof Error && error.name === 'GoBackendError';
}

export function formatBridgeError(error: GoBackendError): string {
  return `GoBackend Error (${error.code}): ${error.message}`;
}

export default goBackendBridge;
