import type { CompanySettings } from "@prisma/client";
import { prisma } from "~/db.server";

/**
 * Get company settings
 * If no settings exist, create default settings
 */
export async function getCompanySettings(): Promise<CompanySettings> {
  // Get the first company settings record
  let settings = await prisma.companySettings.findFirst();

  // If no settings exist, create default settings
  if (!settings) {
    settings = await prisma.companySettings.create({
      data: {
        name: "HVAC Service Company",
        address: "123 Main Street",
        city: "Warsaw",
        postalCode: "00-001",
        country: "Poland",
        phone: "+48 ***********",
        email: "<EMAIL>",
        website: "www.hvacservice.com",
        taxId: "PL1234567890",
        primaryColor: "#3b82f6", // Blue
        secondaryColor: "#10b981", // Green
        bankName: "Example Bank",
        bankAccountNumber: "PL12 1234 5678 9012 3456 7890 1234",
        bankSwift: "EXAMPLEBANK",
        invoiceFooter: "Thank you for your business!",
        reportFooter: "We appreciate your trust in our services.",
        termsAndConditions: "Payment due within 14 days of invoice date. Late payments subject to 2% monthly interest.",
      },
    });
  }

  return settings;
}

/**
 * Update company settings
 */
export async function updateCompanySettings(
  data: Partial<CompanySettings>
): Promise<CompanySettings> {
  // Get existing settings
  const existingSettings = await getCompanySettings();

  // Update settings
  return prisma.companySettings.update({
    where: { id: existingSettings.id },
    data,
  });
}

/**
 * Get company settings for printable documents
 * Returns formatted company info for use in printable documents
 */
export async function getCompanyInfoForPrintableDocuments() {
  const settings = await getCompanySettings();

  // Format address
  let formattedAddress = settings.address || "";
  if (settings.city || settings.postalCode) {
    formattedAddress += formattedAddress ? ", " : "";
    formattedAddress += `${settings.city || ""} ${settings.postalCode || ""}`.trim();
  }
  if (settings.country) {
    formattedAddress += formattedAddress ? ", " : "";
    formattedAddress += settings.country;
  }

  return {
    name: settings.name,
    address: formattedAddress,
    phone: settings.phone || "",
    email: settings.email || "",
    website: settings.website || "",
    taxId: settings.taxId || "",
    logo: settings.logoUrl || "",
    primaryColor: settings.primaryColor || "#3b82f6",
    secondaryColor: settings.secondaryColor || "#10b981",
    bankName: settings.bankName || "",
    bankAccountNumber: settings.bankAccountNumber || "",
    bankSwift: settings.bankSwift || "",
    invoiceFooter: settings.invoiceFooter || "",
    reportFooter: settings.reportFooter || "",
    termsAndConditions: settings.termsAndConditions || "",
  };
}