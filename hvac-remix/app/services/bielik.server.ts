/**
 * Bielik AI Service - Divine Quality Estate Intelligence
 * Provides comprehensive AI capabilities for HVAC CRM system
 */

import { prisma } from '~/db.server';

export interface BielikRequest {
  prompt: string;
  context?: string;
  temperature?: number;
  maxTokens?: number;
  model?: 'bielik-v3' | 'gemma-3-4b';
}

export interface BielikResponse {
  content: string;
  model: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  timestamp: string;
}

export interface AIAnalysisRequest {
  type: 'customer_analysis' | 'service_prediction' | 'equipment_diagnosis' | 'maintenance_planning';
  data: Record<string, any>;
  customerId?: string;
  jobId?: string;
}

export interface AIAnalysisResponse {
  analysis: string;
  recommendations: string[];
  confidence: number;
  insights: Record<string, any>;
  timestamp: string;
}

/**
 * Main Bielik AI service class
 */
class BielikService {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = process.env.BIELIK_API_URL || 'https://api.bielik.ai/v1';
    this.apiKey = process.env.BIELIK_API_KEY || '';
    
    if (!this.apiKey) {
      console.warn('BIELIK_API_KEY not configured - AI features will be limited');
    }
  }

  /**
   * Generate AI response using Bielik models
   */
  async generateResponse(request: BielikRequest): Promise<BielikResponse> {
    try {
      if (!this.apiKey) {
        return this.getMockResponse(request);
      }

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: request.model || 'bielik-v3',
          messages: [
            {
              role: 'system',
              content: 'You are an expert HVAC technician and business analyst. Provide helpful, accurate, and actionable insights.'
            },
            {
              role: 'user',
              content: request.context ? `Context: ${request.context}\n\nQuery: ${request.prompt}` : request.prompt
            }
          ],
          temperature: request.temperature || 0.7,
          max_tokens: request.maxTokens || 1000,
        }),
      });

      if (!response.ok) {
        throw new Error(`Bielik API error: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        content: data.choices[0].message.content,
        model: data.model,
        usage: {
          promptTokens: data.usage.prompt_tokens,
          completionTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Bielik API error:', error);
      return this.getMockResponse(request);
    }
  }

  /**
   * Analyze customer data using AI
   */
  async analyzeCustomer(request: AIAnalysisRequest): Promise<AIAnalysisResponse> {
    try {
      const prompt = this.buildCustomerAnalysisPrompt(request);
      const response = await this.generateResponse({
        prompt,
        model: 'bielik-v3',
        temperature: 0.3,
      });

      return {
        analysis: response.content,
        recommendations: this.extractRecommendations(response.content),
        confidence: 0.85,
        insights: {
          model: response.model,
          usage: response.usage,
        },
        timestamp: response.timestamp,
      };
    } catch (error) {
      console.error('Customer analysis error:', error);
      throw new Error('Failed to analyze customer data');
    }
  }

  /**
   * Predict service needs using AI
   */
  async predictServiceNeeds(customerId: string): Promise<AIAnalysisResponse> {
    try {
      // Get customer data
      const customer = await prisma.customer.findUnique({
        where: { id: customerId },
        include: {
          jobs: {
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
          devices: true,
        },
      });

      if (!customer) {
        throw new Error('Customer not found');
      }

      const prompt = this.buildServicePredictionPrompt(customer);
      const response = await this.generateResponse({
        prompt,
        model: 'bielik-v3',
        temperature: 0.4,
      });

      return {
        analysis: response.content,
        recommendations: this.extractRecommendations(response.content),
        confidence: 0.78,
        insights: {
          customerData: {
            totalJobs: customer.jobs.length,
            deviceCount: customer.devices.length,
            lastServiceDate: customer.jobs[0]?.createdAt,
          },
          model: response.model,
        },
        timestamp: response.timestamp,
      };
    } catch (error) {
      console.error('Service prediction error:', error);
      throw new Error('Failed to predict service needs');
    }
  }

  /**
   * Diagnose equipment issues using AI
   */
  async diagnoseEquipment(equipmentId: string, symptoms: string[]): Promise<AIAnalysisResponse> {
    try {
      const equipment = await prisma.device.findUnique({
        where: { id: equipmentId },
        include: {
          maintenanceRecords: {
            orderBy: { date: 'desc' },
            take: 5,
          },
        },
      });

      if (!equipment) {
        throw new Error('Equipment not found');
      }

      const prompt = this.buildEquipmentDiagnosisPrompt(equipment, symptoms);
      const response = await this.generateResponse({
        prompt,
        model: 'bielik-v3',
        temperature: 0.2,
      });

      return {
        analysis: response.content,
        recommendations: this.extractRecommendations(response.content),
        confidence: 0.82,
        insights: {
          equipmentType: equipment.type,
          symptoms,
          maintenanceHistory: equipment.maintenanceRecords.length,
        },
        timestamp: response.timestamp,
      };
    } catch (error) {
      console.error('Equipment diagnosis error:', error);
      throw new Error('Failed to diagnose equipment');
    }
  }

  /**
   * Build customer analysis prompt
   */
  private buildCustomerAnalysisPrompt(request: AIAnalysisRequest): string {
    return `
Analyze the following customer data and provide insights:

Customer Information:
${JSON.stringify(request.data, null, 2)}

Please provide:
1. Customer behavior analysis
2. Service history patterns
3. Potential upselling opportunities
4. Risk factors and concerns
5. Recommended next actions

Format your response with clear sections and actionable recommendations.
    `.trim();
  }

  /**
   * Build service prediction prompt
   */
  private buildServicePredictionPrompt(customer: any): string {
    return `
Analyze this HVAC customer's data to predict future service needs:

Customer: ${customer.name}
Location: ${customer.address}
Service History: ${customer.jobs.length} jobs
Equipment: ${customer.devices.length} devices

Recent Jobs:
${customer.jobs.slice(0, 3).map((job: any) => 
  `- ${job.title} (${job.status}) - ${job.createdAt}`
).join('\n')}

Equipment:
${customer.devices.map((device: any) => 
  `- ${device.type}: ${device.model} (${device.status})`
).join('\n')}

Predict:
1. Likely service needs in next 3-6 months
2. Preventive maintenance recommendations
3. Potential equipment replacement needs
4. Seasonal service requirements
5. Budget estimates for recommended services
    `.trim();
  }

  /**
   * Build equipment diagnosis prompt
   */
  private buildEquipmentDiagnosisPrompt(equipment: any, symptoms: string[]): string {
    return `
Diagnose HVAC equipment issue:

Equipment: ${equipment.type} - ${equipment.model}
Age: ${equipment.installationDate ? 
  Math.floor((Date.now() - new Date(equipment.installationDate).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) 
  : 'Unknown'} years
Status: ${equipment.status}

Reported Symptoms:
${symptoms.map(symptom => `- ${symptom}`).join('\n')}

Recent Maintenance:
${equipment.maintenanceRecords.slice(0, 3).map((record: any) => 
  `- ${record.type}: ${record.description} (${record.date})`
).join('\n')}

Provide:
1. Most likely diagnosis
2. Potential causes
3. Recommended repair steps
4. Parts that may be needed
5. Estimated repair time and cost
6. Prevention recommendations
    `.trim();
  }

  /**
   * Extract recommendations from AI response
   */
  private extractRecommendations(content: string): string[] {
    const lines = content.split('\n');
    const recommendations: string[] = [];
    
    for (const line of lines) {
      if (line.match(/^\d+\.|^-|^•/) && line.length > 10) {
        recommendations.push(line.replace(/^\d+\.|^-|^•/, '').trim());
      }
    }
    
    return recommendations.slice(0, 5); // Limit to top 5 recommendations
  }

  /**
   * Mock response for development/testing
   */
  private getMockResponse(request: BielikRequest): BielikResponse {
    return {
      content: `Mock AI response for: ${request.prompt.substring(0, 50)}...`,
      model: request.model || 'bielik-v3-mock',
      usage: {
        promptTokens: 100,
        completionTokens: 150,
        totalTokens: 250,
      },
      timestamp: new Date().toISOString(),
    };
  }
}

// Export singleton instance
export const bielikService = new BielikService();

// Export convenience functions
export const generateAIResponse = (request: BielikRequest) => bielikService.generateResponse(request);
export const analyzeCustomer = (request: AIAnalysisRequest) => bielikService.analyzeCustomer(request);
export const predictServiceNeeds = (customerId: string) => bielikService.predictServiceNeeds(customerId);
export const diagnoseEquipment = (equipmentId: string, symptoms: string[]) => 
  bielikService.diagnoseEquipment(equipmentId, symptoms);
