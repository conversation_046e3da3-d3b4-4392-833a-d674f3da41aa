import type { User, Customer, CustomerPortalAccess } from "@prisma/client";
import { prisma } from "~/db.server";
import { sendEmail } from "~/utils/email.server";
import { generateRandomPassword } from "~/utils/password";

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

/**
 * Create a customer portal account for a customer
 */
export async function createCustomerPortalAccount(
  customerId: string,
  adminUserId: string
): Promise<ServiceResponse<User>> {
  try {
    // Check if customer exists
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
    });

    if (!customer) {
      return {
        data: null,
        error: "Customer not found",
        success: false,
      };
    }

    // Check if customer already has portal access
    if (customer.hasPortalAccess) {
      return {
        data: null,
        error: "Customer already has portal access",
        success: false,
      };
    }

    // Check if customer has an email
    if (!customer.email) {
      return {
        data: null,
        error: "Customer must have an email to create a portal account",
        success: false,
      };
    }

    // Check if email is already in use
    const existingUser = await prisma.user.findUnique({
      where: { email: customer.email },
    });

    if (existingUser) {
      return {
        data: null,
        error: "Email is already in use",
        success: false,
      };
    }

    // Generate a random password
    const password = generateRandomPassword();

    // Create user with CUSTOMER role
    const user = await prisma.user.create({
      data: {
        email: customer.email,
        name: customer.name,
        role: "CUSTOMER",
        customerId: customer.id,
        password: {
          create: {
            hash: await import("bcryptjs").then((mod) => mod.hash(password, 10)),
          },
        },
        customerPortalAccess: {
          create: {
            canViewDevices: true,
            canViewServiceHistory: true,
            canRequestService: true,
            canViewInvoices: true,
            emailNotifications: true,
            phoneNumber: customer.phone,
          },
        },
      },
    });

    // Update customer
    await prisma.customer.update({
      where: { id: customerId },
      data: {
        hasPortalAccess: true,
        portalAccessInviteSent: new Date(),
      },
    });

    // Send invitation email with temporary password
    try {
      await sendEmail({
        to: customer.email,
        subject: "Your HVAC CRM Customer Portal Account",
        text: `
          Hello ${customer.name},
          
          Your HVAC CRM Customer Portal account has been created.
          
          Email: ${customer.email}
          Temporary Password: ${password}
          
          Please log in at https://your-hvac-crm.com/customer-portal and change your password.
          
          Thank you,
          HVAC CRM Team
        `,
      });
    } catch (emailError) {
      console.error("Failed to send invitation email:", emailError);
      // Don't fail the operation if email sending fails
    }

    return {
      data: user,
      error: null,
      success: true,
    };
  } catch (error) {
    console.error("Failed to create customer portal account:", error);
    return {
      data: null,
      error: "Failed to create customer portal account",
      success: false,
    };
  }
}

/**
 * Get customer portal access settings
 */
export async function getCustomerPortalAccess(
  userId: string
): Promise<ServiceResponse<CustomerPortalAccess>> {
  try {
    const access = await prisma.customerPortalAccess.findUnique({
      where: { userId },
    });

    if (!access) {
      return {
        data: null,
        error: "Customer portal access not found",
        success: false,
      };
    }

    return {
      data: access,
      error: null,
      success: true,
    };
  } catch (error) {
    console.error("Failed to get customer portal access:", error);
    return {
      data: null,
      error: "Failed to get customer portal access",
      success: false,
    };
  }
}

/**
 * Update customer portal access settings
 */
export async function updateCustomerPortalAccess(
  userId: string,
  data: Partial<Omit<CustomerPortalAccess, "id" | "userId" | "createdAt" | "updatedAt">>
): Promise<ServiceResponse<CustomerPortalAccess>> {
  try {
    const access = await prisma.customerPortalAccess.update({
      where: { userId },
      data,
    });

    return {
      data: access,
      error: null,
      success: true,
    };
  } catch (error) {
    console.error("Failed to update customer portal access:", error);
    return {
      data: null,
      error: "Failed to update customer portal access",
      success: false,
    };
  }
}

/**
 * Get customer data for the portal
 */
export async function getCustomerPortalData(
  userId: string
): Promise<ServiceResponse<Customer>> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== "CUSTOMER" || !user.customerId) {
      return {
        data: null,
        error: "User is not a customer or has no associated customer",
        success: false,
      };
    }

    const customer = await prisma.customer.findUnique({
      where: { id: user.customerId },
      include: {
        devices: true,
        serviceOrders: {
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        invoices: {
          orderBy: { createdAt: "desc" },
          take: 10,
        },
      },
    });

    if (!customer) {
      return {
        data: null,
        error: "Customer not found",
        success: false,
      };
    }

    return {
      data: customer,
      error: null,
      success: true,
    };
  } catch (error) {
    console.error("Failed to get customer portal data:", error);
    return {
      data: null,
      error: "Failed to get customer portal data",
      success: false,
    };
  }
}

/**
 * Disable customer portal access
 */
export async function disableCustomerPortalAccess(
  customerId: string,
  adminUserId: string
): Promise<ServiceResponse<boolean>> {
  try {
    // Check if customer exists
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
    });

    if (!customer) {
      return {
        data: null,
        error: "Customer not found",
        success: false,
      };
    }

    // Check if customer has portal access
    if (!customer.hasPortalAccess) {
      return {
        data: null,
        error: "Customer does not have portal access",
        success: false,
      };
    }

    // Find the user with CUSTOMER role and matching customerId
    const user = await prisma.user.findFirst({
      where: {
        role: "CUSTOMER",
        customerId,
      },
    });

    if (!user) {
      return {
        data: null,
        error: "Customer portal user not found",
        success: false,
      };
    }

    // Disable the user account
    await prisma.user.update({
      where: { id: user.id },
      data: {
        isActive: false,
      },
    });

    // Update customer
    await prisma.customer.update({
      where: { id: customerId },
      data: {
        hasPortalAccess: false,
      },
    });

    return {
      data: true,
      error: null,
      success: true,
    };
  } catch (error) {
    console.error("Failed to disable customer portal access:", error);
    return {
      data: null,
      error: "Failed to disable customer portal access",
      success: false,
    };
  }
}