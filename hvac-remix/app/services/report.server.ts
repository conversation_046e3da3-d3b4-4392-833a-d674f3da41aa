import type { User } from "@prisma/client";
import { prisma } from "~/db.server";

export interface ReportData {
  id: string;
  name: string;
  description?: string;
  type: string;
  data: any;
  generatedAt: Date;
  totalRecords: number;
}

export interface OperationalReportData {
  serviceOrderSummary: {
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    cancelled: number;
    completionRate: number;
  };
  technicianPerformance: Array<{
    technicianId: string;
    technicianName: string;
    totalOrders: number;
    completedOrders: number;
    averageCompletionTime: number;
    customerRating: number;
    efficiency: number;
  }>;
  serviceTypeDistribution: Array<{
    type: string;
    count: number;
    percentage: number;
    averageValue: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    totalOrders: number;
    completedOrders: number;
    revenue: number;
  }>;
}

export interface FinancialReportData {
  revenueByService: Array<{
    serviceType: string;
    revenue: number;
    count: number;
    averageValue: number;
    percentage: number;
  }>;
  customerProfitability: Array<{
    customerId: string;
    customerName: string;
    totalRevenue: number;
    totalOrders: number;
    averageOrderValue: number;
    lastServiceDate: Date;
  }>;
  costAnalysis: {
    totalRevenue: number;
    totalCosts: number;
    grossProfit: number;
    profitMargin: number;
    partsCosts: number;
    laborCosts: number;
  };
  monthlyFinancials: Array<{
    month: string;
    revenue: number;
    costs: number;
    profit: number;
    margin: number;
  }>;
}

/**
 * Generate operational report data
 */
export async function generateOperationalReport(
  userId: string,
  startDate: Date,
  endDate: Date
): Promise<OperationalReportData> {
  // Service Order Summary
  const serviceOrders = await prisma.serviceOrder.findMany({
    where: {
      userId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    include: {
      customer: true,
    },
  });

  const serviceOrderSummary = {
    total: serviceOrders.length,
    pending: serviceOrders.filter(so => so.status === 'PENDING').length,
    inProgress: serviceOrders.filter(so => so.status === 'IN_PROGRESS').length,
    completed: serviceOrders.filter(so => so.status === 'COMPLETED').length,
    cancelled: serviceOrders.filter(so => so.status === 'CANCELLED').length,
    completionRate: serviceOrders.length > 0 
      ? (serviceOrders.filter(so => so.status === 'COMPLETED').length / serviceOrders.length) * 100 
      : 0,
  };

  // Technician Performance
  const technicianStats = await prisma.serviceOrder.groupBy({
    by: ['assignedTechnicianId'],
    where: {
      userId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      assignedTechnicianId: {
        not: null,
      },
    },
    _count: {
      id: true,
    },
    _avg: {
      customerRating: true,
    },
  });

  const technicianPerformance = await Promise.all(
    technicianStats.map(async (stat) => {
      const completedOrders = await prisma.serviceOrder.count({
        where: {
          userId,
          assignedTechnicianId: stat.assignedTechnicianId,
          status: 'COMPLETED',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      // Calculate average completion time
      const completedOrdersWithDates = await prisma.serviceOrder.findMany({
        where: {
          userId,
          assignedTechnicianId: stat.assignedTechnicianId,
          status: 'COMPLETED',
          completedDate: { not: null },
          scheduledDate: { not: null },
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          scheduledDate: true,
          completedDate: true,
        },
      });

      const avgCompletionTime = completedOrdersWithDates.length > 0
        ? completedOrdersWithDates.reduce((sum, order) => {
            const scheduled = new Date(order.scheduledDate!);
            const completed = new Date(order.completedDate!);
            return sum + (completed.getTime() - scheduled.getTime());
          }, 0) / completedOrdersWithDates.length / (1000 * 60 * 60 * 24) // Convert to days
        : 0;

      return {
        technicianId: stat.assignedTechnicianId || '',
        technicianName: `Technician ${stat.assignedTechnicianId}`, // TODO: Get actual name
        totalOrders: stat._count.id,
        completedOrders,
        averageCompletionTime: Math.round(avgCompletionTime * 100) / 100,
        customerRating: Math.round((stat._avg.customerRating || 0) * 100) / 100,
        efficiency: stat._count.id > 0 ? (completedOrders / stat._count.id) * 100 : 0,
      };
    })
  );

  // Service Type Distribution
  const serviceTypeStats = await prisma.serviceOrder.groupBy({
    by: ['type'],
    where: {
      userId,
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    _count: {
      id: true,
    },
  });

  const serviceTypeDistribution = serviceTypeStats.map(stat => ({
    type: stat.type,
    count: stat._count.id,
    percentage: (stat._count.id / serviceOrders.length) * 100,
    averageValue: 0, // TODO: Calculate from invoices
  }));

  // Monthly Trends (simplified for now)
  const monthlyTrends = []; // TODO: Implement monthly aggregation

  return {
    serviceOrderSummary,
    technicianPerformance,
    serviceTypeDistribution,
    monthlyTrends,
  };
}

/**
 * Generate financial report data
 */
export async function generateFinancialReport(
  userId: string,
  startDate: Date,
  endDate: Date
): Promise<FinancialReportData> {
  // Get invoices for the period
  const invoices = await prisma.invoice.findMany({
    where: {
      customer: {
        userId,
      },
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    include: {
      customer: true,
      serviceOrder: true,
      items: true,
    },
  });

  // Revenue by Service Type
  const serviceTypeRevenue = new Map<string, { revenue: number; count: number }>();
  
  invoices.forEach(invoice => {
    const serviceType = invoice.serviceOrder?.type || 'Unknown';
    const current = serviceTypeRevenue.get(serviceType) || { revenue: 0, count: 0 };
    serviceTypeRevenue.set(serviceType, {
      revenue: current.revenue + (invoice.totalAmount || 0),
      count: current.count + 1,
    });
  });

  const totalRevenue = invoices.reduce((sum, inv) => sum + (inv.totalAmount || 0), 0);
  
  const revenueByService = Array.from(serviceTypeRevenue.entries()).map(([type, data]) => ({
    serviceType: type,
    revenue: data.revenue,
    count: data.count,
    averageValue: data.count > 0 ? data.revenue / data.count : 0,
    percentage: totalRevenue > 0 ? (data.revenue / totalRevenue) * 100 : 0,
  }));

  // Customer Profitability
  const customerRevenue = new Map<string, { revenue: number; count: number; lastDate: Date; name: string }>();
  
  invoices.forEach(invoice => {
    const customerId = invoice.customerId;
    const current = customerRevenue.get(customerId) || { 
      revenue: 0, 
      count: 0, 
      lastDate: new Date(0),
      name: invoice.customer.name 
    };
    customerRevenue.set(customerId, {
      revenue: current.revenue + (invoice.totalAmount || 0),
      count: current.count + 1,
      lastDate: invoice.createdAt > current.lastDate ? invoice.createdAt : current.lastDate,
      name: invoice.customer.name,
    });
  });

  const customerProfitability = Array.from(customerRevenue.entries()).map(([customerId, data]) => ({
    customerId,
    customerName: data.name,
    totalRevenue: data.revenue,
    totalOrders: data.count,
    averageOrderValue: data.count > 0 ? data.revenue / data.count : 0,
    lastServiceDate: data.lastDate,
  }));

  // Cost Analysis (simplified)
  const costAnalysis = {
    totalRevenue,
    totalCosts: totalRevenue * 0.6, // Estimated 60% costs
    grossProfit: totalRevenue * 0.4,
    profitMargin: 40,
    partsCosts: totalRevenue * 0.3,
    laborCosts: totalRevenue * 0.3,
  };

  // Monthly Financials (simplified for now)
  const monthlyFinancials = []; // TODO: Implement monthly aggregation

  return {
    revenueByService,
    customerProfitability,
    costAnalysis,
    monthlyFinancials,
  };
}

/**
 * Create a new report record
 */
export async function createReportRecord(
  userId: string,
  name: string,
  description: string,
  type: string,
  configuration: any
): Promise<string> {
  const report = await prisma.report.create({
    data: {
      name,
      description,
      type,
      configuration: JSON.stringify(configuration),
      userId,
      status: 'PENDING',
    },
  });

  return report.id;
}

/**
 * Update report with generated data
 */
export async function updateReportWithData(
  reportId: string,
  fileUrl: string,
  status: string = 'COMPLETED'
): Promise<void> {
  await prisma.report.update({
    where: { id: reportId },
    data: {
      fileUrl,
      status,
      generatedAt: new Date(),
    },
  });
}
