/**
 * 🌌 COSMIC DESIGN TOKENS
 * Transcendent design system beyond earthly limitations
 * Colors, typography, spacing that resonate with cosmic harmony
 */

// 🌈 COSMIC COLOR SPECTRUM - 16.7 Million Shades of Transcendence
export const cosmicColors = {
  // Primary Cosmic Spectrum
  cosmic: {
    50: '#f0f4ff',
    100: '#e0e9ff', 
    200: '#c7d6fe',
    300: '#a5b8fc',
    400: '#8b93f8',
    500: '#7c6df2', // Core cosmic frequency
    600: '#6d4de8',
    700: '#5d3bd4',
    800: '#4c2fb0',
    900: '#3f268f',
    950: '#271757'
  },

  // Stellar Radiance
  stellar: {
    50: '#fefce8',
    100: '#fef9c3',
    200: '#fef08a',
    300: '#fde047',
    400: '#facc15',
    500: '#eab308', // Golden star core
    600: '#ca8a04',
    700: '#a16207',
    800: '#854d0e',
    900: '#713f12',
    950: '#422006'
  },

  // Galactic Depths
  galactic: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b', // Deep space core
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
    950: '#020617'
  },

  // Quantum Energy
  quantum: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981', // Quantum resonance
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b',
    950: '#022c22'
  },

  // Transcendent Aura
  transcendent: {
    50: '#fdf4ff',
    100: '#fae8ff',
    200: '#f5d0fe',
    300: '#f0abfc',
    400: '#e879f9',
    500: '#d946ef', // Transcendent core
    600: '#c026d3',
    700: '#a21caf',
    800: '#86198f',
    900: '#701a75',
    950: '#4a044e'
  },

  // Cosmic Gradients
  gradients: {
    cosmic: 'linear-gradient(135deg, #7c6df2 0%, #d946ef 100%)',
    stellar: 'linear-gradient(135deg, #eab308 0%, #f97316 100%)',
    galactic: 'linear-gradient(135deg, #64748b 0%, #1e293b 100%)',
    quantum: 'linear-gradient(135deg, #10b981 0%, #3b82f6 100%)',
    transcendent: 'linear-gradient(135deg, #d946ef 0%, #7c6df2 50%, #10b981 100%)',
    aurora: 'linear-gradient(135deg, #7c6df2 0%, #10b981 25%, #eab308 50%, #f97316 75%, #d946ef 100%)'
  }
};

// 🔤 COSMIC TYPOGRAPHY - Fonts that Sing with Universal Frequencies
export const cosmicTypography = {
  fonts: {
    cosmic: {
      primary: '"Inter Cosmic", "Inter", system-ui, sans-serif',
      secondary: '"Cosmic Sans", "Helvetica Neue", Arial, sans-serif',
      mono: '"Cosmic Code", "Fira Code", "Monaco", monospace',
      display: '"Cosmic Display", "Playfair Display", serif'
    }
  },

  // Font Sizes with Golden Ratio Progression
  sizes: {
    xs: '0.618rem',    // 9.888px
    sm: '0.875rem',    // 14px
    base: '1rem',      // 16px
    lg: '1.125rem',    // 18px
    xl: '1.25rem',     // 20px
    '2xl': '1.5rem',   // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
    '5xl': '3rem',     // 48px
    '6xl': '3.75rem',  // 60px
    '7xl': '4.5rem',   // 72px
    '8xl': '6rem',     // 96px
    '9xl': '8rem',     // 128px
    cosmic: '12rem',   // 192px - For cosmic headers
    transcendent: '16rem' // 256px - For transcendent displays
  },

  // Line Heights with Cosmic Harmony
  lineHeights: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
    cosmic: '1.618', // Golden ratio
    transcendent: '2.618' // Golden ratio squared
  },

  // Font Weights with Cosmic Progression
  weights: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
    cosmic: '950', // Ultra cosmic weight
    transcendent: '999' // Maximum transcendence
  }
};

// 📐 COSMIC SPACING - Proportions of the Golden Ratio in Cosmic Scale
export const cosmicSpacing = {
  // Base spacing unit (1rem = 16px)
  base: '1rem',

  // Cosmic spacing scale with golden ratio progression
  scale: {
    0: '0',
    px: '1px',
    0.5: '0.125rem',   // 2px
    1: '0.25rem',      // 4px
    1.5: '0.375rem',   // 6px
    2: '0.5rem',       // 8px
    2.5: '0.625rem',   // 10px
    3: '0.75rem',      // 12px
    3.5: '0.875rem',   // 14px
    4: '1rem',         // 16px
    5: '1.25rem',      // 20px
    6: '1.5rem',       // 24px
    7: '1.75rem',      // 28px
    8: '2rem',         // 32px
    9: '2.25rem',      // 36px
    10: '2.5rem',      // 40px
    11: '2.75rem',     // 44px
    12: '3rem',        // 48px
    14: '3.5rem',      // 56px
    16: '4rem',        // 64px
    20: '5rem',        // 80px
    24: '6rem',        // 96px
    28: '7rem',        // 112px
    32: '8rem',        // 128px
    36: '9rem',        // 144px
    40: '10rem',       // 160px
    44: '11rem',       // 176px
    48: '12rem',       // 192px
    52: '13rem',       // 208px
    56: '14rem',       // 224px
    60: '15rem',       // 240px
    64: '16rem',       // 256px
    72: '18rem',       // 288px
    80: '20rem',       // 320px
    96: '24rem',       // 384px
    cosmic: '30rem',   // 480px - Cosmic spacing
    transcendent: '40rem' // 640px - Transcendent spacing
  }
};

// 🌊 COSMIC ANIMATIONS - Easing Functions Based on Quantum Physics
export const cosmicAnimations = {
  // Duration with cosmic timing
  duration: {
    instant: '0ms',
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
    slower: '750ms',
    cosmic: '1000ms',
    transcendent: '1618ms', // Golden ratio in milliseconds
    eternal: '2618ms'       // Golden ratio squared
  },

  // Easing functions inspired by cosmic phenomena
  easing: {
    // Standard easing
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',

    // Cosmic easing functions
    cosmic: 'cubic-bezier(0.4, 0, 0.2, 1)',
    stellar: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    galactic: 'cubic-bezier(0.165, 0.84, 0.44, 1)',
    quantum: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    transcendent: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',

    // Physics-based easing
    gravity: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',
    antigravity: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
    wormhole: 'cubic-bezier(0.645, 0.045, 0.355, 1)',
    blackhole: 'cubic-bezier(0.95, 0.05, 0.795, 0.035)',
    lightspeed: 'cubic-bezier(0.23, 1, 0.32, 1)'
  },

  // Keyframe animations
  keyframes: {
    cosmic: {
      '0%': { transform: 'scale(1) rotate(0deg)', opacity: '1' },
      '50%': { transform: 'scale(1.05) rotate(180deg)', opacity: '0.8' },
      '100%': { transform: 'scale(1) rotate(360deg)', opacity: '1' }
    },
    stellar: {
      '0%': { transform: 'translateY(0px)', boxShadow: '0 0 0 rgba(124, 109, 242, 0)' },
      '50%': { transform: 'translateY(-10px)', boxShadow: '0 10px 25px rgba(124, 109, 242, 0.3)' },
      '100%': { transform: 'translateY(0px)', boxShadow: '0 0 0 rgba(124, 109, 242, 0)' }
    },
    quantum: {
      '0%': { opacity: '1', transform: 'scale(1)' },
      '25%': { opacity: '0.7', transform: 'scale(0.95)' },
      '50%': { opacity: '1', transform: 'scale(1.05)' },
      '75%': { opacity: '0.7', transform: 'scale(0.95)' },
      '100%': { opacity: '1', transform: 'scale(1)' }
    },
    transcendent: {
      '0%': { 
        transform: 'scale(1) rotate(0deg)', 
        background: 'linear-gradient(135deg, #7c6df2 0%, #d946ef 100%)' 
      },
      '33%': { 
        transform: 'scale(1.1) rotate(120deg)', 
        background: 'linear-gradient(135deg, #10b981 0%, #7c6df2 100%)' 
      },
      '66%': { 
        transform: 'scale(1.1) rotate(240deg)', 
        background: 'linear-gradient(135deg, #eab308 0%, #10b981 100%)' 
      },
      '100%': { 
        transform: 'scale(1) rotate(360deg)', 
        background: 'linear-gradient(135deg, #7c6df2 0%, #d946ef 100%)' 
      }
    }
  }
};

// 🔮 COSMIC SHADOWS - Multi-dimensional Shadow System
export const cosmicShadows = {
  // Standard shadows with cosmic enhancement
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',

  // Cosmic shadows with color and glow
  cosmic: '0 0 20px rgba(124, 109, 242, 0.3), 0 0 40px rgba(124, 109, 242, 0.1)',
  stellar: '0 0 30px rgba(234, 179, 8, 0.4), 0 0 60px rgba(234, 179, 8, 0.1)',
  quantum: '0 0 25px rgba(16, 185, 129, 0.3), 0 0 50px rgba(16, 185, 129, 0.1)',
  transcendent: '0 0 40px rgba(217, 70, 239, 0.4), 0 0 80px rgba(217, 70, 239, 0.2)',

  // Multi-dimensional shadows
  portal: '0 0 0 1px rgba(124, 109, 242, 0.1), 0 0 20px rgba(124, 109, 242, 0.3), inset 0 0 20px rgba(124, 109, 242, 0.1)',
  wormhole: '0 0 50px rgba(0, 0, 0, 0.5), inset 0 0 50px rgba(124, 109, 242, 0.2)',
  blackhole: '0 0 100px rgba(0, 0, 0, 0.8), inset 0 0 100px rgba(0, 0, 0, 0.5)'
};

// 🌟 COSMIC BORDER RADIUS - Organic Curves of the Universe
export const cosmicBorderRadius = {
  none: '0',
  sm: '0.125rem',
  base: '0.25rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  '3xl': '1.5rem',
  cosmic: '2rem',
  transcendent: '3rem',
  infinite: '9999px',
  organic: '30% 70% 70% 30% / 30% 30% 70% 70%', // Organic blob shape
  portal: '50% 20% 80% 40%', // Portal-like shape
  quantum: '40% 60% 60% 40% / 60% 30% 70% 40%' // Quantum fluctuation shape
};

// 🎭 COSMIC Z-INDEX - Dimensional Layering System
export const cosmicZIndex = {
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1020,
  banner: 1030,
  overlay: 1040,
  modal: 1050,
  popover: 1060,
  skipLink: 1070,
  toast: 1080,
  tooltip: 1090,
  cosmic: 2000,
  transcendent: 3000,
  omnipresent: 9999
};

// 🌌 Export complete cosmic design system
export const cosmicDesignSystem = {
  colors: cosmicColors,
  typography: cosmicTypography,
  spacing: cosmicSpacing,
  animations: cosmicAnimations,
  shadows: cosmicShadows,
  borderRadius: cosmicBorderRadius,
  zIndex: cosmicZIndex
};

export default cosmicDesignSystem;
