# App
NODE_ENV=development
PORT=3000

# Database
DATABASE_URL=postgres://postgres.tiblohqkwompuvgpgfwp:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1
DIRECT_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Authentication
SESSION_SECRET=your-super-secret-key-change-me-in-production

# Supabase
SUPABASE_URL=https://tiblohqkwompuvgpgfwp.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRpYmxvaHFrd29tcHV2Z3BnZndwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4OTE1NzIsImV4cCI6MjA2MzQ2NzU3Mn0.EN8hrLC8nQHzcIp37bt8XwPo2KC7wqIIg2ttBPmkhag
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRpYmxvaHFrd29tcHV2Z3BnZndwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Nzg5MTU3MiwiZXhwIjoyMDYzNDY3NTcyfQ.EN8hrLC8nQHzcIp37bt8XwPo2KC7wqIIg2ttBPmkhag

# GoBackend-Kratos Integration
GOBACKEND_URL=http://localhost:8080
GOBACKEND_WS_URL=ws://localhost:8080

# Qdrant Vector Database
QDRANT_URL=http://localhost:6333

# Redis Cache
REDIS_URL=redis://localhost:6379

# GraphQL
GRAPHQL_PATH=/graphql

# Azure Vision API
AZURE_VISION_KEY=
AZURE_VISION_ENDPOINT=

# ===== AGENT PROTOCOL INTEGRATION =====

# Agent Protocol API
AGENT_PROTOCOL_URL=http://localhost:8001
AGENT_PROTOCOL_API_KEY=hvac-agent-protocol-key-2024

# LLM Endpoints
BIELIK_V3_URL=http://localhost:8877
GEMMA4_URL=http://localhost:8878
GEMMA3_HF_URL=http://localhost:8879

# Agent Integration Settings
ENABLE_AGENT_INTEGRATION=true
AGENT_RESPONSE_TIMEOUT=30000
AGENT_MAX_RETRIES=3
AGENT_POLLING_INTERVAL=2000

# LLM Configuration
BIELIK_V3_MAX_TOKENS=2000
BIELIK_V3_TEMPERATURE=0.7
BIELIK_V3_CONTEXT_WINDOW=32000

GEMMA4_MAX_TOKENS=2000
GEMMA4_TEMPERATURE=0.5
GEMMA4_CONTEXT_WINDOW=32000

GEMMA3_HF_MAX_TOKENS=4000
GEMMA3_HF_TEMPERATURE=0.3
GEMMA3_HF_CONTEXT_WINDOW=131072
GEMMA3_HF_MULTIMODAL=true

# Database Sync Settings
ENABLE_REAL_TIME_SYNC=true
SYNC_BATCH_SIZE=100
SYNC_CONCURRENCY_LIMIT=5
SYNC_RETRY_ATTEMPTS=3

# Performance Settings
AGENT_CACHE_TTL=300
VECTOR_SEARCH_LIMIT=10
EMBEDDING_BATCH_SIZE=50

# Monitoring and Logging
ENABLE_AGENT_MONITORING=true
LOG_LEVEL=info
METRICS_COLLECTION=true