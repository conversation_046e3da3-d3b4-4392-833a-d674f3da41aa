import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Define performance SLAs
const PERFORMANCE_SLAS = {
  maxResponseTime: 200, // milliseconds
  percentile: 95, // 95th percentile
  maxErrorRate: 1, // 1%
  concurrentUsers: [50, 100, 200], // Test with different user loads
};

// Define critical workflows to test
const CRITICAL_WORKFLOWS = [
  {
    name: 'Customer Creation',
    endpoint: '/api/customers',
    method: 'POST',
    payload: {
      name: 'Test Customer',
      email: '<EMAIL>',
      phone: '************',
      address: '123 Test St',
      city: 'Test City',
      postalCode: '12345',
      country: 'Test Country',
    },
  },
  {
    name: 'Job Scheduling',
    endpoint: '/api/service-orders',
    method: 'POST',
    payload: {
      title: 'Test Service Order',
      description: 'Test service order description',
      customerId: '{{CUSTOMER_ID}}', // Will be replaced with actual ID
      scheduledDate: '{{FUTURE_DATE}}', // Will be replaced with future date
    },
  },
  {
    name: 'Invoice Generation',
    endpoint: '/api/invoices',
    method: 'POST',
    payload: {
      customerId: '{{CUSTOMER_ID}}', // Will be replaced with actual ID
      serviceOrderId: '{{SERVICE_ORDER_ID}}', // Will be replaced with actual ID
      issueDate: '{{CURRENT_DATE}}', // Will be replaced with current date
      dueDate: '{{FUTURE_DATE}}', // Will be replaced with future date
      items: [
        {
          description: 'Service fee',
          quantity: 1,
          unitPrice: 100,
          totalPrice: 100,
        },
      ],
    },
  },
  {
    name: 'Customer Search',
    endpoint: '/api/customers?search=test',
    method: 'GET',
  },
  {
    name: 'Dashboard Load',
    endpoint: '/dashboard',
    method: 'GET',
  },
];

/**
 * Generate k6 load testing script
 */
function generateK6Script(workflow, concurrentUsers) {
  const script = `
import http from 'k6/http';
import { check, sleep } from 'k6';

export const options = {
  stages: [
    { duration: '30s', target: ${concurrentUsers} }, // Ramp up
    { duration: '1m', target: ${concurrentUsers} },  // Stay at target
    { duration: '30s', target: 0 },                 // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<${PERFORMANCE_SLAS.maxResponseTime}'], // 95% of requests must complete below threshold
    http_req_failed: ['rate<${PERFORMANCE_SLAS.maxErrorRate / 100}'],  // Error rate must be below threshold
  },
};

export default function() {
  const payload = ${JSON.stringify(workflow.payload || {})};
  
  // Replace placeholders with actual values
  if (payload.customerId === '{{CUSTOMER_ID}}') {
    payload.customerId = __ENV.CUSTOMER_ID;
  }
  
  if (payload.serviceOrderId === '{{SERVICE_ORDER_ID}}') {
    payload.serviceOrderId = __ENV.SERVICE_ORDER_ID;
  }
  
  if (payload.issueDate === '{{CURRENT_DATE}}') {
    payload.issueDate = new Date().toISOString().split('T')[0];
  }
  
  if (payload.dueDate === '{{FUTURE_DATE}}' || payload.scheduledDate === '{{FUTURE_DATE}}') {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 14);
    const futureDateStr = futureDate.toISOString().split('T')[0];
    
    if (payload.dueDate === '{{FUTURE_DATE}}') {
      payload.dueDate = futureDateStr;
    }
    
    if (payload.scheduledDate === '{{FUTURE_DATE}}') {
      payload.scheduledDate = futureDateStr;
    }
  }
  
  const params = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + __ENV.API_TOKEN,
    },
  };
  
  let response;
  
  if ('${workflow.method}' === 'GET') {
    response = http.get('${process.env.APP_URL}${workflow.endpoint}', params);
  } else {
    response = http.${workflow.method.toLowerCase()}('${process.env.APP_URL}${workflow.endpoint}', JSON.stringify(payload), params);
  }
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < ${PERFORMANCE_SLAS.maxResponseTime}ms': (r) => r.timings.duration < ${PERFORMANCE_SLAS.maxResponseTime},
  });
  
  sleep(1);
}
`;

  return script;
}

/**
 * Run load tests for all critical workflows
 */
async function runLoadTests() {
  console.log('Starting load testing...');
  
  // Create directory for test scripts and results
  const testDir = path.join(process.cwd(), 'load-test-results');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir);
  }
  
  // Run tests for each workflow and user load
  for (const workflow of CRITICAL_WORKFLOWS) {
    console.log(`Testing workflow: ${workflow.name}`);
    
    for (const userCount of PERFORMANCE_SLAS.concurrentUsers) {
      console.log(`  With ${userCount} concurrent users`);
      
      // Generate k6 script
      const scriptPath = path.join(testDir, `${workflow.name.replace(/\s+/g, '-').toLowerCase()}-${userCount}.js`);
      fs.writeFileSync(scriptPath, generateK6Script(workflow, userCount));
      
      try {
        // Run k6 test (requires k6 to be installed)
        const { stdout, stderr } = await execAsync(`k6 run --env API_TOKEN=${process.env.TEST_API_TOKEN} --env CUSTOMER_ID=${process.env.TEST_CUSTOMER_ID} --env SERVICE_ORDER_ID=${process.env.TEST_SERVICE_ORDER_ID} ${scriptPath}`);
        
        // Save results
        const resultsPath = path.join(testDir, `${workflow.name.replace(/\s+/g, '-').toLowerCase()}-${userCount}-results.txt`);
        fs.writeFileSync(resultsPath, stdout);
        
        console.log(`  Results saved to ${resultsPath}`);
        
        // Check if test passed
        if (stderr) {
          console.error(`  Test failed: ${stderr}`);
        } else if (stdout.includes('✓ status is 200') && stdout.includes(`✓ response time < ${PERFORMANCE_SLAS.maxResponseTime}ms`)) {
          console.log(`  Test passed: Response time within SLA`);
        } else {
          console.warn(`  Test warning: Performance may not meet SLA`);
        }
      } catch (error) {
        console.error(`  Error running test: ${error.message}`);
      }
    }
  }
  
  console.log('Load testing completed');
}

/**
 * Identify and resolve performance bottlenecks
 */
async function identifyBottlenecks() {
  console.log('Identifying performance bottlenecks...');
  
  // Analyze test results
  const testDir = path.join(process.cwd(), 'load-test-results');
  if (!fs.existsSync(testDir)) {
    console.log('No test results found');
    return;
  }
  
  const results = fs.readdirSync(testDir)
    .filter(file => file.endsWith('-results.txt'))
    .map(file => {
      const content = fs.readFileSync(path.join(testDir, file), 'utf8');
      const match = content.match(/http_req_duration\..............: avg=([0-9.]+)ms min=([0-9.]+)ms med=([0-9.]+)ms max=([0-9.]+)ms p\(90\)=([0-9.]+)ms p\(95\)=([0-9.]+)ms/);
      
      if (!match) return null;
      
      return {
        file,
        avg: parseFloat(match[1]),
        min: parseFloat(match[2]),
        med: parseFloat(match[3]),
        max: parseFloat(match[4]),
        p90: parseFloat(match[5]),
        p95: parseFloat(match[6]),
      };
    })
    .filter(Boolean);
  
  // Sort by p95 response time (descending)
  results.sort((a, b) => b.p95 - a.p95);
  
  console.log('Performance bottlenecks (sorted by p95 response time):');
  console.table(results);
  
  // Provide recommendations for the slowest endpoints
  if (results.length > 0) {
    console.log('\nRecommendations for improving performance:');
    
    for (let i = 0; i < Math.min(3, results.length); i++) {
      const result = results[i];
      const workflowName = result.file.split('-')[0];
      
      console.log(`\n${i + 1}. Optimize ${workflowName} workflow (p95: ${result.p95}ms):`);
      console.log('   - Add database indexes for frequently queried fields');
      console.log('   - Implement Redis caching for read-heavy operations');
      console.log('   - Consider optimizing Remix loader/action functions');
      console.log('   - Review database query patterns for N+1 query issues');
    }
  }
}

/**
 * Main function to run load testing and performance benchmarking
 */
async function runPerformanceBenchmarking() {
  console.log('Starting performance benchmarking...');
  
  await runLoadTests();
  await identifyBottlenecks();
  
  console.log('Performance benchmarking completed');
}

// Run the benchmarking
runPerformanceBenchmarking().catch(console.error);