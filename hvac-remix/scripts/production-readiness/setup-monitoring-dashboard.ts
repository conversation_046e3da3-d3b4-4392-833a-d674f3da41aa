import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

/**
 * Set up real-time dashboard for key metrics
 */
async function setupMonitoringDashboard() {
  console.log('Setting up monitoring dashboard...');
  
  try {
    // Create a dashboard component
    const dashboardComponent = `import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert';
import { Line<PERSON>hart, Line, BarChart, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { getRedisClient } from '~/utils/redis.server';
import { useLoaderData } from '@remix-run/react';
import { json } from '@remix-run/node';
import { AlertLevel } from '~/utils/monitoring.server';

export async function loader() {
  // Get metrics from Redis
  const client = await getRedisClient();
  
  // Get recent metrics
  const metrics = await client.xRange(
    'metrics',
    Date.now() - 24 * 60 * 60 * 1000, // 24 hours ago
    '+'
  );
  
  // Get recent alerts
  const alerts = await client.xRange(
    'alerts',
    Date.now() - 24 * 60 * 60 * 1000, // 24 hours ago
    '+'
  );
  
  // Process metrics
  const processedMetrics = metrics.map(entry => {
    const data = JSON.parse(entry.data);
    return {
      id: entry.id,
      ...data
    };
  });
  
  // Process alerts
  const processedAlerts = alerts.map(entry => {
    const data = JSON.parse(entry.data);
    return {
      id: entry.id,
      ...data
    };
  });
  
  // Group metrics by name
  const metricsByName = processedMetrics.reduce((acc, metric) => {
    if (!acc[metric.name]) {
      acc[metric.name] = [];
    }
    acc[metric.name].push(metric);
    return acc;
  }, {});
  
  return json({
    metrics: metricsByName,
    alerts: processedAlerts
  });
}

export default function MonitoringDashboard() {
  const { metrics, alerts } = useLoaderData<typeof loader>();
  const [activeTab, setActiveTab] = useState('overview');
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">System Monitoring Dashboard</h1>
      
      <Tabs defaultValue="overview" onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="errors">Errors</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <MetricCard
              title="API Response Time"
              description="Average response time in milliseconds"
              metricName="api_response_time"
              metrics={metrics}
              format={(value) => \`\${value.toFixed(2)} ms\`}
            />
            
            <MetricCard
              title="Error Rate"
              description="Percentage of requests resulting in errors"
              metricName="error_rate"
              metrics={metrics}
              format={(value) => \`\${value.toFixed(2)}%\`}
            />
            
            <MetricCard
              title="Active Users"
              description="Number of active users in the last 15 minutes"
              metricName="active_users"
              metrics={metrics}
              format={(value) => value.toFixed(0)}
            />
            
            <MetricCard
              title="Database Load"
              description="Current database load percentage"
              metricName="db_load"
              metrics={metrics}
              format={(value) => \`\${value.toFixed(2)}%\`}
            />
            
            <MetricCard
              title="Memory Usage"
              description="Server memory usage percentage"
              metricName="memory_usage"
              metrics={metrics}
              format={(value) => \`\${value.toFixed(2)}%\`}
            />
            
            <MetricCard
              title="CPU Usage"
              description="Server CPU usage percentage"
              metricName="cpu_usage"
              metrics={metrics}
              format={(value) => \`\${value.toFixed(2)}%\`}
            />
          </div>
        </TabsContent>
        
        <TabsContent value="performance">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>API Response Times</CardTitle>
                <CardDescription>Average response time by endpoint</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={metrics.api_response_time_by_endpoint || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" tickFormatter={(timestamp) => new Date(timestamp).toLocaleTimeString()} />
                    <YAxis />
                    <Tooltip formatter={(value) => \`\${value} ms\`} />
                    <Legend />
                    <Line type="monotone" dataKey="value" stroke="#8884d8" activeDot={{ r: 8 }} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Request Volume</CardTitle>
                <CardDescription>Number of requests per minute</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={metrics.request_volume || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timestamp" tickFormatter={(timestamp) => new Date(timestamp).toLocaleTimeString()} />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="value" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="alerts">
          <div className="space-y-4">
            {alerts.length === 0 ? (
              <p className="text-center text-gray-500">No alerts in the last 24 hours</p>
            ) : (
              alerts.map((alert) => (
                <Alert key={alert.id} variant={getAlertVariant(alert.level)}>
                  <AlertTitle>{alert.name}</AlertTitle>
                  <AlertDescription>
                    {alert.message}
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(alert.timestamp).toLocaleString()}
                    </div>
                  </AlertDescription>
                </Alert>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

function MetricCard({ title, description, metricName, metrics, format }) {
  const metricData = metrics[metricName] || [];
  const latestMetric = metricData.length > 0 ? metricData[metricData.length - 1] : null;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-4xl font-bold">
          {latestMetric ? format(latestMetric.value) : 'N/A'}
        </div>
        
        <div className="h-24 mt-4">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={metricData}>
              <Line type="monotone" dataKey="value" stroke="#8884d8" dot={false} />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

function getAlertVariant(level: AlertLevel) {
  switch (level) {
    case AlertLevel.INFO:
      return 'default';
    case AlertLevel.WARNING:
      return 'warning';
    case AlertLevel.ERROR:
      return 'destructive';
    case AlertLevel.CRITICAL:
      return 'destructive';
    default:
      return 'default';
  }
}`;
    
    // Write the dashboard component
    fs.writeFileSync(
      path.join(process.cwd(), 'app', 'routes', 'admin.monitoring.tsx'),
      dashboardComponent
    );
    
    console.log('Monitoring dashboard component created at app/routes/admin.monitoring.tsx');
  } catch (error) {
    console.error('Error setting up monitoring dashboard:', error);
  }
}

/**
 * Configure alerts for critical thresholds
 */
async function configureAlerts() {
  console.log('Configuring alerts for critical thresholds...');
  
  try {
    // Create an alerts configuration file
    const alertsConfig = `import { AlertLevel, Comparisons, checkMetricThreshold } from '~/utils/monitoring.server';

// Define alert thresholds
export const ALERT_THRESHOLDS = {
  // Performance alerts
  API_RESPONSE_TIME: {
    WARNING: 200, // ms
    CRITICAL: 500, // ms
  },
  ERROR_RATE: {
    WARNING: 1, // %
    CRITICAL: 5, // %
  },
  
  // Resource alerts
  CPU_USAGE: {
    WARNING: 70, // %
    CRITICAL: 90, // %
  },
  MEMORY_USAGE: {
    WARNING: 70, // %
    CRITICAL: 90, // %
  },
  DISK_USAGE: {
    WARNING: 70, // %
    CRITICAL: 90, // %
  },
  
  // Database alerts
  DB_CONNECTIONS: {
    WARNING: 80, // % of max connections
    CRITICAL: 95, // % of max connections
  },
  DB_QUERY_TIME: {
    WARNING: 500, // ms
    CRITICAL: 2000, // ms
  },
  
  // Business alerts
  FAILED_PAYMENTS: {
    WARNING: 5, // count in last hour
    CRITICAL: 20, // count in last hour
  },
  SERVICE_ORDER_CREATION_FAILURES: {
    WARNING: 3, // count in last hour
    CRITICAL: 10, // count in last hour
  },
};

/**
 * Check all alert thresholds and trigger alerts if needed
 */
export async function checkAllAlertThresholds() {
  // Performance alerts
  await checkMetricThreshold(
    'api_response_time',
    ALERT_THRESHOLDS.API_RESPONSE_TIME.WARNING,
    Comparisons.greaterThan,
    'High API Response Time',
    'API response time is above warning threshold',
    AlertLevel.WARNING
  );
  
  await checkMetricThreshold(
    'api_response_time',
    ALERT_THRESHOLDS.API_RESPONSE_TIME.CRITICAL,
    Comparisons.greaterThan,
    'Critical API Response Time',
    'API response time is above critical threshold',
    AlertLevel.CRITICAL
  );
  
  await checkMetricThreshold(
    'error_rate',
    ALERT_THRESHOLDS.ERROR_RATE.WARNING,
    Comparisons.greaterThan,
    'High Error Rate',
    'Error rate is above warning threshold',
    AlertLevel.WARNING
  );
  
  await checkMetricThreshold(
    'error_rate',
    ALERT_THRESHOLDS.ERROR_RATE.CRITICAL,
    Comparisons.greaterThan,
    'Critical Error Rate',
    'Error rate is above critical threshold',
    AlertLevel.CRITICAL
  );
  
  // Resource alerts
  await checkMetricThreshold(
    'cpu_usage',
    ALERT_THRESHOLDS.CPU_USAGE.WARNING,
    Comparisons.greaterThan,
    'High CPU Usage',
    'CPU usage is above warning threshold',
    AlertLevel.WARNING
  );
  
  await checkMetricThreshold(
    'cpu_usage',
    ALERT_THRESHOLDS.CPU_USAGE.CRITICAL,
    Comparisons.greaterThan,
    'Critical CPU Usage',
    'CPU usage is above critical threshold',
    AlertLevel.CRITICAL
  );
  
  // Database alerts
  await checkMetricThreshold(
    'db_connections_percent',
    ALERT_THRESHOLDS.DB_CONNECTIONS.WARNING,
    Comparisons.greaterThan,
    'High Database Connections',
    'Database connection usage is above warning threshold',
    AlertLevel.WARNING
  );
  
  await checkMetricThreshold(
    'db_connections_percent',
    ALERT_THRESHOLDS.DB_CONNECTIONS.CRITICAL,
    Comparisons.greaterThan,
    'Critical Database Connections',
    'Database connection usage is above critical threshold',
    AlertLevel.CRITICAL
  );
  
  // Business alerts
  await checkMetricThreshold(
    'failed_payments_hourly',
    ALERT_THRESHOLDS.FAILED_PAYMENTS.WARNING,
    Comparisons.greaterThan,
    'High Failed Payments',
    'Number of failed payments is above warning threshold',
    AlertLevel.WARNING
  );
  
  await checkMetricThreshold(
    'failed_payments_hourly',
    ALERT_THRESHOLDS.FAILED_PAYMENTS.CRITICAL,
    Comparisons.greaterThan,
    'Critical Failed Payments',
    'Number of failed payments is above critical threshold',
    AlertLevel.CRITICAL
  );
}`;
    
    // Write the alerts configuration file
    fs.writeFileSync(
      path.join(process.cwd(), 'app', 'utils', 'alerts.server.ts'),
      alertsConfig
    );
    
    console.log('Alerts configuration created at app/utils/alerts.server.ts');
  } catch (error) {
    console.error('Error configuring alerts:', error);
  }
}

/**
 * Implement structured logging
 */
async function implementStructuredLogging() {
  console.log('Implementing structured logging...');
  
  try {
    // Create a structured logging utility
    const structuredLogging = `import { captureMessage } from './monitoring.server';

// Log levels
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

// Log categories
export enum LogCategory {
  API = 'api',
  DATABASE = 'database',
  AUTH = 'auth',
  PAYMENT = 'payment',
  SYSTEM = 'system',
  BUSINESS = 'business'
}

/**
 * Create a structured log entry
 * @param level Log level
 * @param category Log category
 * @param message Log message
 * @param data Additional data
 */
export function log(
  level: LogLevel,
  category: LogCategory,
  message: string,
  data?: Record<string, any>
) {
  const timestamp = new Date().toISOString();
  const requestId = getRequestId();
  
  const logEntry = {
    timestamp,
    level,
    category,
    message,
    requestId,
    ...data
  };
  
  // In production, send to centralized logging
  if (process.env.NODE_ENV === 'production') {
    // Send to Sentry for error and warning levels
    if (level === LogLevel.ERROR || level === LogLevel.WARN) {
      captureMessage(\`[\${category}] \${message}\`, level as any);
    }
    
    // TODO: Send to centralized logging service
    // This could be implemented with a service like Datadog, Logstash, etc.
  }
  
  // Always log to console
  const logFn = getConsoleMethod(level);
  logFn(JSON.stringify(logEntry));
}

/**
 * Get the appropriate console method for the log level
 */
function getConsoleMethod(level: LogLevel): (message: string) => void {
  switch (level) {
    case LogLevel.DEBUG:
      return console.debug;
    case LogLevel.INFO:
      return console.info;
    case LogLevel.WARN:
      return console.warn;
    case LogLevel.ERROR:
      return console.error;
    default:
      return console.log;
  }
}

/**
 * Get the current request ID from the context
 */
function getRequestId(): string {
  // This would be implemented with a request context library
  // For now, return a random ID
  return Math.random().toString(36).substring(2, 15);
}

// Convenience methods
export const logDebug = (category: LogCategory, message: string, data?: Record<string, any>) =>
  log(LogLevel.DEBUG, category, message, data);

export const logInfo = (category: LogCategory, message: string, data?: Record<string, any>) =>
  log(LogLevel.INFO, category, message, data);

export const logWarn = (category: LogCategory, message: string, data?: Record<string, any>) =>
  log(LogLevel.WARN, category, message, data);

export const logError = (category: LogCategory, message: string, data?: Record<string, any>) =>
  log(LogLevel.ERROR, category, message, data);`;
    
    // Write the structured logging utility
    fs.writeFileSync(
      path.join(process.cwd(), 'app', 'utils', 'logging.server.ts'),
      structuredLogging
    );
    
    console.log('Structured logging utility created at app/utils/logging.server.ts');
  } catch (error) {
    console.error('Error implementing structured logging:', error);
  }
}

/**
 * Create custom metrics for HVAC-specific business processes
 */
async function createCustomMetrics() {
  console.log('Creating custom metrics for HVAC-specific business processes...');
  
  try {
    // Create a custom metrics utility
    const customMetrics = `import { MetricType, recordMetric } from './monitoring.server';

/**
 * Record service order metrics
 */
export async function recordServiceOrderMetrics(serviceOrder: any) {
  // Record service order creation
  await recordMetric(
    'service_orders_created',
    1,
    MetricType.COUNTER,
    {
      type: serviceOrder.type,
      priority: serviceOrder.priority,
      status: serviceOrder.status
    }
  );
  
  // Record time to completion if completed
  if (serviceOrder.status === 'COMPLETED' && serviceOrder.completedDate && serviceOrder.createdAt) {
    const createdAt = new Date(serviceOrder.createdAt);
    const completedAt = new Date(serviceOrder.completedDate);
    const timeToCompletionHours = (completedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
    
    await recordMetric(
      'service_order_completion_time',
      timeToCompletionHours,
      MetricType.HISTOGRAM,
      {
        type: serviceOrder.type,
        priority: serviceOrder.priority
      }
    );
  }
}

/**
 * Record customer metrics
 */
export async function recordCustomerMetrics(customer: any) {
  // Record customer creation
  await recordMetric(
    'customers_created',
    1,
    MetricType.COUNTER
  );
  
  // Record customers with portal access
  if (customer.hasPortalAccess) {
    await recordMetric(
      'customers_with_portal_access',
      1,
      MetricType.COUNTER
    );
  }
}

/**
 * Record device metrics
 */
export async function recordDeviceMetrics(device: any) {
  // Record device creation
  await recordMetric(
    'devices_created',
    1,
    MetricType.COUNTER,
    {
      type: device.type || 'unknown'
    }
  );
  
  // Record devices by manufacturer
  if (device.manufacturer) {
    await recordMetric(
      'devices_by_manufacturer',
      1,
      MetricType.COUNTER,
      {
        manufacturer: device.manufacturer
      }
    );
  }
}

/**
 * Record maintenance prediction metrics
 */
export async function recordMaintenancePredictionMetrics(prediction: any) {
  // Record prediction creation
  await recordMetric(
    'maintenance_predictions_created',
    1,
    MetricType.COUNTER,
    {
      predictedComponent: prediction.predictedComponent || 'unknown'
    }
  );
  
  // Record prediction accuracy if maintenance was performed
  if (prediction.maintenancePerformed) {
    await recordMetric(
      'maintenance_prediction_accuracy',
      prediction.confidence,
      MetricType.HISTOGRAM
    );
  }
}

/**
 * Record invoice metrics
 */
export async function recordInvoiceMetrics(invoice: any) {
  // Record invoice creation
  await recordMetric(
    'invoices_created',
    1,
    MetricType.COUNTER,
    {
      status: invoice.status,
      paymentStatus: invoice.paymentStatus
    }
  );
  
  // Record invoice amount
  if (invoice.totalAmount) {
    await recordMetric(
      'invoice_amount',
      invoice.totalAmount,
      MetricType.HISTOGRAM
    );
  }
  
  // Record time to payment if paid
  if (invoice.paymentStatus === 'PAID' && invoice.createdAt) {
    const createdAt = new Date(invoice.createdAt);
    const now = new Date();
    const timeToPaidDays = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
    
    await recordMetric(
      'invoice_time_to_payment',
      timeToPaidDays,
      MetricType.HISTOGRAM
    );
  }
}

/**
 * Record sales opportunity metrics
 */
export async function recordSalesOpportunityMetrics(opportunity: any) {
  // Record opportunity creation
  await recordMetric(
    'sales_opportunities_created',
    1,
    MetricType.COUNTER,
    {
      status: opportunity.status,
      source: opportunity.source || 'unknown'
    }
  );
  
  // Record opportunity value
  if (opportunity.estimatedValue) {
    await recordMetric(
      'sales_opportunity_value',
      opportunity.estimatedValue,
      MetricType.HISTOGRAM,
      {
        status: opportunity.status
      }
    );
  }
  
  // Record conversion rate if won
  if (opportunity.status === 'WON') {
    await recordMetric(
      'sales_opportunity_won',
      1,
      MetricType.COUNTER,
      {
        source: opportunity.source || 'unknown'
      }
    );
  }
}`;
    
    // Write the custom metrics utility
    fs.writeFileSync(
      path.join(process.cwd(), 'app', 'utils', 'business-metrics.server.ts'),
      customMetrics
    );
    
    console.log('Custom metrics utility created at app/utils/business-metrics.server.ts');
  } catch (error) {
    console.error('Error creating custom metrics:', error);
  }
}

/**
 * Main function to set up monitoring and alerting
 */
async function setupMonitoringAndAlerting() {
  console.log('Setting up monitoring and alerting infrastructure...');
  
  await setupMonitoringDashboard();
  await configureAlerts();
  await implementStructuredLogging();
  await createCustomMetrics();
  
  console.log('Monitoring and alerting infrastructure setup completed');
}

// Run the setup
setupMonitoringAndAlerting().catch(console.error);