# HVAC-Remix CRM Production Dockerfile
# Enhanced multi-stage build for optimal performance and security

FROM node:18-alpine AS base

# Install system dependencies for native modules and PDF generation
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    dumb-init \
    curl

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies with optimizations
RUN npm ci --legacy-peer-deps --silent && \
    npm cache clean --force

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Set environment variables
ENV NODE_ENV=production

# Build the application and patch the build files
RUN npm run build

# Production image, copy all the files and run the app
FROM base AS runner
WORKDIR /app

# Set production environment
ENV NODE_ENV=production
ENV PORT=3000
ENV HOST=0.0.0.0
ENV NODE_PATH=./build:./app:.

# Create non-root user for security
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 remix

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/tmp /app/uploads && \
    chown -R remix:nodejs /app

# Copy built application and necessary files
COPY --from=builder --chown=remix:nodejs /app/public ./public
COPY --from=builder --chown=remix:nodejs /app/build ./build
COPY --from=builder --chown=remix:nodejs /app/prisma ./prisma
COPY --from=builder --chown=remix:nodejs /app/package.json ./package.json
COPY --from=builder --chown=remix:nodejs /app/package-lock.json ./package-lock.json

# Copy configuration files
COPY --chown=remix:nodejs remix.config.js ./
COPY --chown=remix:nodejs tailwind.config.js ./
COPY --chown=remix:nodejs tsconfig.json ./

# Copy entrypoint script if it exists
COPY --chown=remix:nodejs docker-entrypoint.sh* ./

# Install production-only dependencies
RUN npm ci --omit=dev --legacy-peer-deps --silent && \
    npm cache clean --force

# Enhanced health check for HVAC-Remix CRM
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Switch to non-root user
USER remix

# Expose application port
EXPOSE 3000

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Start command with fallback
CMD ["sh", "-c", "if [ -f ./docker-entrypoint.sh ]; then ./docker-entrypoint.sh; else npm run start; fi"]

# Metadata labels
LABEL maintainer="HVAC CRM Team"
LABEL version="2.0"
LABEL description="HVAC-Remix CRM Production Container with Enhanced Features"
LABEL org.opencontainers.image.source="https://github.com/hvac-crm/hvac-remix"