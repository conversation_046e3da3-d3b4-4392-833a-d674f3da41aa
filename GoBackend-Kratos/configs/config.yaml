server:
  http:
    addr: 0.0.0.0:8080
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s

data:
  database:
    driver: postgres
    source: *************************************************/hvacdb?sslmode=disable
  redis:
    addr: localhost:6379
    read_timeout: 0.2s
    write_timeout: 0.2s

ai:
  models:
    gemma:
      endpoint: "http://************:1234"
      model_name: "gemma-3-4b-it-qat"
      max_tokens: 100000
    bielik:
      endpoint: "http://************:1234"
      model_name: "gemma-3-12b-it-qat"
      max_tokens: 100000

  # 🚀 Enhanced AI Configuration
  langchain:
    enabled: true
    default_model: "gemma-3-4b-it-qat"
    temperature: 0.7
    max_tokens: 100000
    timeout: "120s"
    retry_attempts: 3
    cache_enabled: true
    cache_ttl: "1h"

  vector_db:
    type: "chromem"
    enabled: true
    persistence: true
    storage_path: "/data/vectordb"
    embedding_model: "nomic-embed-text"
    collections:
      - name: "hvac-knowledge"
        description: "HVAC technical knowledge and troubleshooting guides"
        embedding_model: "nomic-embed-text"
        max_documents: 10000
      - name: "customer-emails"
        description: "Customer email history for similarity matching"
        embedding_model: "nomic-embed-text"
        max_documents: 50000
      - name: "knowledge-base"
        description: "General company knowledge base"
        embedding_model: "nomic-embed-text"
        max_documents: 25000
      - name: "customer-data"
        description: "Customer interaction history and preferences"
        embedding_model: "nomic-embed-text"
        max_documents: 100000

  lm_studio:
    enabled: true
    load_balancing: "round_robin"
    health_check_interval: "30s"
    max_retries: 3
    timeout: "120s"
    nodes:
      - endpoint: "http://************:1234"
        models: ["gemma-3-4b-it-qat", "gemma-3-12b-it-qat", "text-embedding-nomic-embed-text-v1.5"]
        weight: 1
        max_concurrent: 10
        token_window: 100000

  performance:
    json_iterator:
      enabled: true
      config: "compatible"
      fast_mode: false

    caching:
      enabled: true
      max_size: 1000
      ttl: "1h"
      cleanup_interval: "10m"

    metrics:
      enabled: true
      collection_interval: "30s"
      retention_period: "24h"

email:
  # 📧 Primary HVAC Email Account
  primary:
    smtp:
      host: "serwer2440139.home.pl"
      port: 587
      username: "<EMAIL>"
      password: "Blaeritipol1"
      from: "<EMAIL>"
      sender_name: "HVAC CRM"
      use_tls: true
      secure: false
    imap:
      host: "serwer2440139.home.pl"
      port: 993
      username: "<EMAIL>"
      password: "Blaeritipol1"
      use_ssl: true
      reject_unauthorized: true

  # 🎵 Audio Email Account (for call recordings)
  audio:
    imap:
      host: "serwer2440139.home.pl"
      port: 993
      username: "<EMAIL>"
      password: "Blaeritipol1"
      use_ssl: true
      folders: ["INBOX"]
      batch_size: 10
      polling_interval: "15s"
  templates:
    service_reminder: "service_reminder"
    quote_follow_up: "quote_follow_up"
    invoice_notification: "invoice_notification"
    appointment_confirmation: "appointment_confirmation"

mcp:
  server:
    addr: 0.0.0.0:8081
    transport: "stdio"
  tools:
    enabled: true
    hvac_tools: true
    email_tools: true

logging:
  level: "info"
  format: "json"
  output: "stdout"

tracing:
  endpoint: "http://localhost:14268/api/traces"
  sampler: 1.0

metrics:
  addr: 0.0.0.0:9090
  path: "/metrics"