// 🐙 Morphic Octopus Interface - Main JavaScript
class OctopusInterface {
    constructor() {
        this.config = window.OCTOPUS_CONFIG || {};
        this.isRealtimeEnabled = true;
        this.refreshInterval = null;

        this.init();
    }

    init() {
        console.log('🐙 Initializing Morphic Octopus Interface...');
        this.setupEventListeners();
        this.startPeriodicUpdates();
        this.loadInitialData();
    }

    setupEventListeners() {
        // Quick action buttons
        this.setupQuickActions();

        // Real-time toggle
        const realtimeToggle = document.getElementById('realtime-toggle');
        if (realtimeToggle) {
            realtimeToggle.addEventListener('click', () => {
                this.toggleRealtime();
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Page visibility change
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }

    setupQuickActions() {
        const quickActionButtons = document.querySelectorAll('[class*="bg-"][class*="hover:bg-"]');
        quickActionButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.handleQuickAction(e.target);
            });
        });
    }

    handleQuickAction(button) {
        const text = button.textContent.trim();

        if (text.includes('View Analytics')) {
            this.showAnalytics();
        } else if (text.includes('Add Customer')) {
            this.showAddCustomer();
        } else if (text.includes('Check Emails')) {
            this.showEmailIntelligence();
        } else if (text.includes('AI Status')) {
            this.showAIStatus();
        }

        // Visual feedback
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
    }

    showAnalytics() {
        console.log('📊 Opening Analytics Dashboard...');
        // Implementation for analytics view
        this.showNotification('Analytics dashboard opened', 'info');
    }

    showAddCustomer() {
        console.log('👥 Opening Add Customer Dialog...');
        // Implementation for add customer
        this.showNotification('Add customer dialog opened', 'info');
    }

    showEmailIntelligence() {
        console.log('📧 Opening Email Intelligence...');
        // Implementation for email intelligence
        this.showNotification('Email intelligence opened', 'info');
    }

    showAIStatus() {
        console.log('🤖 Opening AI Status...');
        // Implementation for AI status
        this.showNotification('AI status opened', 'info');
    }

    toggleRealtime() {
        this.isRealtimeEnabled = !this.isRealtimeEnabled;
        const toggle = document.getElementById('realtime-toggle');

        if (this.isRealtimeEnabled) {
            toggle.textContent = 'Real-time: ON';
            toggle.className = 'bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm';
            this.startPeriodicUpdates();
            console.log('✅ Real-time updates enabled');
        } else {
            toggle.textContent = 'Real-time: OFF';
            toggle.className = 'bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm';
            this.stopPeriodicUpdates();
            console.log('⏸️ Real-time updates disabled');
        }

        this.showNotification(
            `Real-time updates ${this.isRealtimeEnabled ? 'enabled' : 'disabled'}`,
            'info'
        );
    }

    startPeriodicUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        if (this.isRealtimeEnabled) {
            this.refreshInterval = setInterval(() => {
                this.fetchDashboardData();
            }, this.config.refreshInterval || 5000);
        }
    }

    stopPeriodicUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    async loadInitialData() {
        console.log('📊 Loading initial dashboard data...');
        await this.fetchDashboardData();
    }

    async fetchDashboardData() {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/dashboard/data`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            this.updateDashboard(data);

        } catch (error) {
            console.error('❌ Error fetching dashboard data:', error);
            this.showNotification('Failed to fetch dashboard data', 'error');
        }
    }

    updateDashboard(data) {
        // This method is called when we receive data via HTTP API
        // WebSocket updates are handled separately in websocket.js
        console.log('📊 Updating dashboard with HTTP data:', data);

        if (data.system_status) {
            this.updateSystemStatus(data.system_status);
        }

        if (data.service_health) {
            this.updateServiceHealth(data.service_health);
        }

        if (data.customer_metrics) {
            this.updateCustomerMetrics(data.customer_metrics);
        }

        if (data.transcription_stats) {
            this.updateTranscriptionStats(data.transcription_stats);
        }

        if (data.email_intelligence) {
            this.updateEmailIntelligence(data.email_intelligence);
        }

        if (data.ai_performance) {
            this.updateAIPerformance(data.ai_performance);
        }

        if (data.langchain_metrics) {
            this.updateLangChainMetrics(data.langchain_metrics);
        }

        if (data.workflow_metrics) {
            this.updateWorkflowMetrics(data.workflow_metrics);
        }

        if (data.realtime_alerts) {
            this.updateRealtimeAlerts(data.realtime_alerts);
        }

        // Update timestamp
        this.updateLastUpdated();
    }

    updateSystemStatus(status) {
        // Fallback updates if WebSocket is not available
        if (!window.octopusWS || !window.octopusWS.isConnected) {
            const uptimeElement = document.getElementById('system-uptime');
            if (uptimeElement && status.uptime) {
                uptimeElement.textContent = this.formatUptime(status.uptime);
            }
        }
    }

    updateCustomerMetrics(metrics) {
        // Fallback updates if WebSocket is not available
        if (!window.octopusWS || !window.octopusWS.isConnected) {
            this.updateElement('total-customers', metrics.total_customers);
            this.updateElement('new-customers-today', metrics.new_today);
        }
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    updateLastUpdated() {
        const element = document.getElementById('last-updated');
        if (element) {
            element.textContent = new Date().toLocaleTimeString();
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + R: Toggle real-time
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            this.toggleRealtime();
        }

        // Ctrl/Cmd + D: Refresh dashboard
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            this.fetchDashboardData();
            this.showNotification('Dashboard refreshed', 'info');
        }
    }

    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden, reduce update frequency
            this.stopPeriodicUpdates();
        } else {
            // Page is visible, resume normal updates
            if (this.isRealtimeEnabled) {
                this.startPeriodicUpdates();
                this.fetchDashboardData(); // Immediate update
            }
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${this.getNotificationClasses(type)}`;
        notification.textContent = message;

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 10);

        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    getNotificationClasses(type) {
        const baseClasses = 'transform translate-x-full opacity-0 transition-all duration-300';

        switch (type) {
            case 'success':
                return `${baseClasses} bg-green-600 text-white`;
            case 'error':
                return `${baseClasses} bg-red-600 text-white`;
            case 'warning':
                return `${baseClasses} bg-yellow-600 text-white`;
            default:
                return `${baseClasses} bg-blue-600 text-white`;
        }
    }

    formatUptime(uptimeNs) {
        const seconds = Math.floor(uptimeNs / **********);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m`;
        } else {
            return `${seconds}s`;
        }
    }

    // Public API
    refresh() {
        this.fetchDashboardData();
    }

    // 🏥 Update Service Health Status
    updateServiceHealth(serviceHealth) {
        console.log('🏥 Updating service health:', serviceHealth);

        // Update individual service statuses
        this.updateServiceStatus('email-service-status', serviceHealth.email_service);
        this.updateServiceStatus('transcription-service-status', serviceHealth.transcription_service);
        this.updateServiceStatus('customer-service-status', serviceHealth.customer_service);
        this.updateServiceStatus('ai-service-status', serviceHealth.ai_service);
        this.updateServiceStatus('database-service-status', serviceHealth.database_service);
        this.updateServiceStatus('redis-service-status', serviceHealth.redis_service);
    }

    updateServiceStatus(elementId, serviceStatus) {
        const element = document.getElementById(elementId);
        if (!element || !serviceStatus) return;

        const statusIndicator = element.querySelector('.w-3.h-3');
        const statusText = element.querySelector('.text-xs');

        if (statusIndicator) {
            // Update status color
            statusIndicator.className = `w-3 h-3 rounded-full ${this.getStatusColor(serviceStatus.status)}`;
        }

        if (statusText) {
            statusText.textContent = `${serviceStatus.status} - ${serviceStatus.response_time}ms`;
        }
    }

    getStatusColor(status) {
        switch (status) {
            case 'healthy': return 'bg-green-500';
            case 'degraded': return 'bg-yellow-500';
            case 'unhealthy': return 'bg-red-500';
            default: return 'bg-gray-500';
        }
    }

    // 📞 Update Transcription Statistics
    updateTranscriptionStats(stats) {
        console.log('📞 Updating transcription stats:', stats);

        this.updateElement('total-transcriptions', stats.total_calls);
        this.updateElement('transcriptions-today', stats.calls_today);
        this.updateElement('hvac-relevant-calls', stats.hvac_relevant_calls);
        this.updateElement('emergency-calls', stats.emergency_calls);
        this.updateElement('avg-confidence', `${stats.avg_confidence}%`);
        this.updateElement('processing-backlog', stats.processing_backlog);
    }

    // 📧 Update Email Intelligence
    updateEmailIntelligence(intelligence) {
        console.log('📧 Updating email intelligence:', intelligence);

        this.updateElement('total-emails', intelligence.total_emails);
        this.updateElement('emails-today', intelligence.emails_today);
        this.updateElement('emails-processed', intelligence.hvac_relevant_emails);
        this.updateElement('positive-sentiment', intelligence.positive_sentiment);
        this.updateElement('negative-sentiment', intelligence.negative_sentiment);
    }

    // 🤖 Update AI Performance
    updateAIPerformance(performance) {
        console.log('🤖 Updating AI performance:', performance);

        this.updateElement('ai-requests', performance.total_requests);
        this.updateElement('ai-requests-today', performance.requests_today);
        this.updateElement('ai-success-rate', `${performance.success_rate}%`);
        this.updateElement('ai-response-time', `${performance.avg_response_time}ms`);
        this.updateElement('model-accuracy', `${performance.model_accuracy}%`);
        this.updateElement('tokens-processed', performance.tokens_processed);
        this.updateElement('queue-length', performance.queue_length);
    }

    // 🧠 Update LangChain Metrics
    updateLangChainMetrics(metrics) {
        console.log('🧠 Updating LangChain metrics:', metrics);

        this.updateElement('langchain-workflows', metrics.total_workflows);
        this.updateElement('langchain-workflows-today', metrics.workflows_today);
        this.updateElement('langchain-success-rate', `${metrics.success_rate}%`);
        this.updateElement('langchain-avg-time', `${metrics.avg_workflow_time}ms`);
        this.updateElement('langchain-executions', metrics.chain_executions);
        this.updateElement('langchain-tokens', metrics.tokens_consumed);
        this.updateElement('langchain-cost', `$${metrics.cost_today}`);

        // Update active chains list
        const chainsElement = document.getElementById('langchain-active-chains');
        if (chainsElement && metrics.active_chains) {
            chainsElement.innerHTML = metrics.active_chains.slice(0, 3).map(chain =>
                `<span class="inline-block bg-blue-600 text-xs px-2 py-1 rounded mr-1 mb-1">${chain}</span>`
            ).join('');
        }
    }

    // 🕸️ Update Workflow Metrics
    updateWorkflowMetrics(metrics) {
        console.log('🕸️ Updating Workflow metrics:', metrics);

        this.updateElement('workflow-executions', metrics.total_executions);
        this.updateElement('workflow-executions-today', metrics.executions_today);
        this.updateElement('workflow-running', metrics.running_workflows);
        this.updateElement('workflow-completed', metrics.completed_workflows);
        this.updateElement('workflow-failed', metrics.failed_workflows);
        this.updateElement('workflow-success-rate', `${metrics.success_rate}%`);
        this.updateElement('workflow-avg-time', `${metrics.avg_execution_time}s`);
        this.updateElement('workflow-queued', metrics.queued_workflows);

        // Update workflow types list
        const typesElement = document.getElementById('workflow-types');
        if (typesElement && metrics.workflow_types) {
            typesElement.innerHTML = metrics.workflow_types.slice(0, 3).map(type =>
                `<span class="inline-block bg-green-600 text-xs px-2 py-1 rounded mr-1 mb-1">${type}</span>`
            ).join('');
        }
    }

    // 🚨 Update Realtime Alerts
    updateRealtimeAlerts(alerts) {
        console.log('🚨 Updating realtime alerts:', alerts);

        const alertsContainer = document.getElementById('alerts-container');
        if (!alertsContainer) return;

        // Clear existing alerts
        alertsContainer.innerHTML = '';

        if (!alerts || alerts.length === 0) {
            alertsContainer.innerHTML = '<div class="text-gray-400 text-sm">No alerts at this time</div>';
            return;
        }

        // Add new alerts
        alerts.forEach(alert => {
            const alertElement = document.createElement('div');
            alertElement.className = `p-3 rounded border-l-4 ${this.getAlertClasses(alert.type)}`;
            alertElement.innerHTML = `
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="font-semibold text-sm">${alert.title}</h4>
                        <p class="text-sm mt-1">${alert.message}</p>
                        <p class="text-xs text-gray-400 mt-1">${new Date(alert.timestamp).toLocaleTimeString()}</p>
                    </div>
                    <button class="text-gray-400 hover:text-white" onclick="this.parentElement.parentElement.remove()">
                        ✕
                    </button>
                </div>
            `;
            alertsContainer.appendChild(alertElement);
        });
    }

    getAlertClasses(type) {
        switch (type) {
            case 'critical': return 'bg-red-900 border-red-500 text-red-100';
            case 'warning': return 'bg-yellow-900 border-yellow-500 text-yellow-100';
            case 'info': return 'bg-blue-900 border-blue-500 text-blue-100';
            default: return 'bg-gray-900 border-gray-500 text-gray-100';
        }
    }

    destroy() {
        this.stopPeriodicUpdates();
    }
}

// Initialize interface when page loads
let octopusInterface;
document.addEventListener('DOMContentLoaded', () => {
    octopusInterface = new OctopusInterface();
    console.log('🐙 Morphic Octopus Interface initialized successfully!');
});

// Export for global access
window.OctopusInterface = OctopusInterface;