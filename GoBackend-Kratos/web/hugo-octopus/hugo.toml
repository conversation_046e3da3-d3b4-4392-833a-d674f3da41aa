baseURL = 'http://localhost:8083'
languageCode = 'en-us'
title = '🐙 Morphic Octopus Interface - HVAC CRM Dashboard'
theme = 'octopus-dashboard'

# Hugo Configuration for Octopus Dashboard
[params]
  description = "Advanced HVAC CRM Management Dashboard with Real-time Analytics"
  author = "HVAC GoBackend-Kratos Team"
  version = "2.0.0"
  
  # Dashboard Configuration
  [params.dashboard]
    websocket_url = "ws://localhost:8083/api/dashboard/ws"
    api_base_url = "http://localhost:8083/api"
    refresh_interval = 5000
    theme = "dark"
    
  # Real-time Features
  [params.realtime]
    enabled = true
    auto_refresh = true
    notification_sound = true
    chart_animation = true
    
  # Security
  [params.security]
    auth_enabled = false
    cors_enabled = true

# Build Configuration
[build]
  publishDir = "../octopus/static"
  
[markup]
  [markup.goldmark]
    [markup.goldmark.renderer]
      unsafe = true

# Development Configuration
[server]
  [[server.headers]]
    for = "/**"
    [server.headers.values]
      X-Frame-Options = "DENY"
      X-Content-Type-Options = "nosniff"
      Referrer-Policy = "strict-origin-when-cross-origin"
      Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' ws: wss:;"