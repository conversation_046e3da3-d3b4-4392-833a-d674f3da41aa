version: v1.3.1
dsn: ************************************************************************

log:
  level: debug
  leak_sensitive_values: true
  format: json

courier:
  smtp:
    connection_uri: smtps://grzegorz%40koldbringers.pl:<EMAIL>:465/?skip_ssl_verify=false

serve:
  public:
    base_url: http://localhost:4433
    cors:
      enabled: true
      allowed_origins:
        - http://localhost:4433
        - http://localhost:8080
        - http://localhost:3000
        - http://localhost:8081
        - http://localhost:8083
      allowed_methods:
        - POST
        - GET
        - PUT
        - PATCH
        - DELETE
      allowed_headers:
        - Authorization
        - Cookie
        - Content-Type
      exposed_headers:
        - Content-Type
        - Set-Cookie
  admin:
    base_url: http://localhost:4434

selfservice:
  default_browser_return_url: http://localhost:3000/
  allowed_return_urls:
    - http://localhost:3000
    - http://localhost:8080
    - http://localhost:8081
    - http://localhost:8083

  methods:
    password:
      enabled: true
    totp:
      config:
        issuer: HVAC CRM
      enabled: true
    lookup_secret:
      enabled: true
    link:
      enabled: true
    code:
      enabled: true

  flows:
    error:
      ui_url: https://www.ory.sh/kratos/docs/fallback/error

    settings:
      ui_url: http://localhost:4433/self-service/settings/browser
      privileged_session_max_age: 1h
      required_aal: highest_available
      lifespan: 1h

    recovery:
      enabled: false
      ui_url: https://www.ory.sh/kratos/docs/fallback/recovery
      use: code
      lifespan: 1h
      notify_unknown_recipients: false

    verification:
      enabled: false
      ui_url: https://www.ory.sh/kratos/docs/fallback/verification
      use: code
      lifespan: 1h
      notify_unknown_recipients: false

    logout:
      after:
        default_browser_return_url: http://localhost:3000/login

    login:
      ui_url: http://localhost:4433/self-service/login/browser
      lifespan: 1h
      style: unified

    registration:
      ui_url: http://localhost:4433/self-service/registration/browser
      lifespan: 1h
      login_hints: false
      enabled: true
      enable_legacy_one_step: false

identity:
  default_schema_id: default
  schemas:
    - id: default
      url: file:///etc/config/kratos/identity.schema.json

hashers:
  algorithm: bcrypt
  bcrypt:
    cost: 8

session:
  cookie:
    domain: localhost
    same_site: Lax
    persistent: true
  lifespan: 24h

secrets:
  cookie:
    - PLEASE-CHANGE-ME-I-AM-VERY-INSECURE
  cipher:
    - 32-LONG-SECRET-NOT-SECURE-AT-ALL

ciphers:
  algorithm: xchacha20-poly1305