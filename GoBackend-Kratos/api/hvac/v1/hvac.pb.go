// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/hvac/v1/hvac.proto

package v1

import (
context "context"
grpc "google.golang.org/grpc"
codes "google.golang.org/grpc/codes"
status "google.golang.org/grpc/status"
timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// Customer represents an HVAC customer
type Customer struct {
Id        int64                  `json:"id,omitempty"`
Name      string                 `json:"name,omitempty"`
Email     string                 `json:"email,omitempty"`
Phone     string                 `json:"phone,omitempty"`
Address   string                 `json:"address,omitempty"`
CreatedAt *timestamppb.Timestamp `json:"created_at,omitempty"`
UpdatedAt *timestamppb.Timestamp `json:"updated_at,omitempty"`
}

func (x *Customer) Reset() { *x = Customer{} }
func (x *Customer) String() string { return "Customer{}" }
func (*Customer) ProtoMessage() {}
func (x *Customer) GetId() int64 { if x != nil { return x.Id }; return 0 }
func (x *Customer) GetName() string { if x != nil { return x.Name }; return "" }
func (x *Customer) GetEmail() string { if x != nil { return x.Email }; return "" }
func (x *Customer) GetPhone() string { if x != nil { return x.Phone }; return "" }
func (x *Customer) GetAddress() string { if x != nil { return x.Address }; return "" }
func (x *Customer) GetCreatedAt() *timestamppb.Timestamp { if x != nil { return x.CreatedAt }; return nil }
func (x *Customer) GetUpdatedAt() *timestamppb.Timestamp { if x != nil { return x.UpdatedAt }; return nil }

// Job represents an HVAC job
type Job struct {
Id          int64                  `json:"id,omitempty"`
CustomerId  int64                  `json:"customer_id,omitempty"`
Title       string                 `json:"title,omitempty"`
Description string                 `json:"description,omitempty"`
Status      string                 `json:"status,omitempty"`
Priority    string                 `json:"priority,omitempty"`
ScheduledAt *timestamppb.Timestamp `json:"scheduled_at,omitempty"`
CreatedAt   *timestamppb.Timestamp `json:"created_at,omitempty"`
UpdatedAt   *timestamppb.Timestamp `json:"updated_at,omitempty"`
}

func (x *Job) Reset() { *x = Job{} }
func (x *Job) String() string { return "Job{}" }
func (*Job) ProtoMessage() {}
func (x *Job) GetId() int64 { if x != nil { return x.Id }; return 0 }
func (x *Job) GetCustomerId() int64 { if x != nil { return x.CustomerId }; return 0 }
func (x *Job) GetTitle() string { if x != nil { return x.Title }; return "" }
func (x *Job) GetDescription() string { if x != nil { return x.Description }; return "" }
func (x *Job) GetStatus() string { if x != nil { return x.Status }; return "" }
func (x *Job) GetPriority() string { if x != nil { return x.Priority }; return "" }
func (x *Job) GetScheduledAt() *timestamppb.Timestamp { if x != nil { return x.ScheduledAt }; return nil }
func (x *Job) GetCreatedAt() *timestamppb.Timestamp { if x != nil { return x.CreatedAt }; return nil }
func (x *Job) GetUpdatedAt() *timestamppb.Timestamp { if x != nil { return x.UpdatedAt }; return nil }

// Request/Response types
type CreateCustomerRequest struct {
Name    string `json:"name,omitempty"`
Email   string `json:"email,omitempty"`
Phone   string `json:"phone,omitempty"`
Address string `json:"address,omitempty"`
}

func (x *CreateCustomerRequest) Reset() { *x = CreateCustomerRequest{} }
func (x *CreateCustomerRequest) String() string { return "CreateCustomerRequest{}" }
func (*CreateCustomerRequest) ProtoMessage() {}
func (x *CreateCustomerRequest) GetName() string { if x != nil { return x.Name }; return "" }
func (x *CreateCustomerRequest) GetEmail() string { if x != nil { return x.Email }; return "" }
func (x *CreateCustomerRequest) GetPhone() string { if x != nil { return x.Phone }; return "" }
func (x *CreateCustomerRequest) GetAddress() string { if x != nil { return x.Address }; return "" }

type CreateCustomerResponse struct {
Customer *Customer `json:"customer,omitempty"`
}

func (x *CreateCustomerResponse) Reset() { *x = CreateCustomerResponse{} }
func (x *CreateCustomerResponse) String() string { return "CreateCustomerResponse{}" }
func (*CreateCustomerResponse) ProtoMessage() {}
func (x *CreateCustomerResponse) GetCustomer() *Customer { if x != nil { return x.Customer }; return nil }

type GetCustomerRequest struct {
Id int64 `json:"id,omitempty"`
}

func (x *GetCustomerRequest) Reset() { *x = GetCustomerRequest{} }
func (x *GetCustomerRequest) String() string { return "GetCustomerRequest{}" }
func (*GetCustomerRequest) ProtoMessage() {}
func (x *GetCustomerRequest) GetId() int64 { if x != nil { return x.Id }; return 0 }

type GetCustomerResponse struct {
Customer *Customer `json:"customer,omitempty"`
}

func (x *GetCustomerResponse) Reset() { *x = GetCustomerResponse{} }
func (x *GetCustomerResponse) String() string { return "GetCustomerResponse{}" }
func (*GetCustomerResponse) ProtoMessage() {}
func (x *GetCustomerResponse) GetCustomer() *Customer { if x != nil { return x.Customer }; return nil }

type ListCustomersRequest struct {
Page     int32 `json:"page,omitempty"`
PageSize int32 `json:"page_size,omitempty"`
}

func (x *ListCustomersRequest) Reset() { *x = ListCustomersRequest{} }
func (x *ListCustomersRequest) String() string { return "ListCustomersRequest{}" }
func (*ListCustomersRequest) ProtoMessage() {}
func (x *ListCustomersRequest) GetPage() int32 { if x != nil { return x.Page }; return 0 }
func (x *ListCustomersRequest) GetPageSize() int32 { if x != nil { return x.PageSize }; return 0 }

type ListCustomersResponse struct {
Customers []*Customer `json:"customers,omitempty"`
Total     int32       `json:"total,omitempty"`
}

func (x *ListCustomersResponse) Reset() { *x = ListCustomersResponse{} }
func (x *ListCustomersResponse) String() string { return "ListCustomersResponse{}" }
func (*ListCustomersResponse) ProtoMessage() {}
func (x *ListCustomersResponse) GetCustomers() []*Customer { if x != nil { return x.Customers }; return nil }
func (x *ListCustomersResponse) GetTotal() int32 { if x != nil { return x.Total }; return 0 }

type CreateJobRequest struct {
CustomerId  int64  `json:"customer_id,omitempty"`
Title       string `json:"title,omitempty"`
Description string `json:"description,omitempty"`
Priority    string `json:"priority,omitempty"`
}

func (x *CreateJobRequest) Reset() { *x = CreateJobRequest{} }
func (x *CreateJobRequest) String() string { return "CreateJobRequest{}" }
func (*CreateJobRequest) ProtoMessage() {}
func (x *CreateJobRequest) GetCustomerId() int64 { if x != nil { return x.CustomerId }; return 0 }
func (x *CreateJobRequest) GetTitle() string { if x != nil { return x.Title }; return "" }
func (x *CreateJobRequest) GetDescription() string { if x != nil { return x.Description }; return "" }
func (x *CreateJobRequest) GetPriority() string { if x != nil { return x.Priority }; return "" }

type CreateJobResponse struct {
Job *Job `json:"job,omitempty"`
}

func (x *CreateJobResponse) Reset() { *x = CreateJobResponse{} }
func (x *CreateJobResponse) String() string { return "CreateJobResponse{}" }
func (*CreateJobResponse) ProtoMessage() {}
func (x *CreateJobResponse) GetJob() *Job { if x != nil { return x.Job }; return nil }

type GetJobRequest struct {
Id int64 `json:"id,omitempty"`
}

func (x *GetJobRequest) Reset() { *x = GetJobRequest{} }
func (x *GetJobRequest) String() string { return "GetJobRequest{}" }
func (*GetJobRequest) ProtoMessage() {}
func (x *GetJobRequest) GetId() int64 { if x != nil { return x.Id }; return 0 }

type GetJobResponse struct {
Job *Job `json:"job,omitempty"`
}

func (x *GetJobResponse) Reset() { *x = GetJobResponse{} }
func (x *GetJobResponse) String() string { return "GetJobResponse{}" }
func (*GetJobResponse) ProtoMessage() {}
func (x *GetJobResponse) GetJob() *Job { if x != nil { return x.Job }; return nil }

type ListJobsRequest struct {
Page     int32 `json:"page,omitempty"`
PageSize int32 `json:"page_size,omitempty"`
}

func (x *ListJobsRequest) Reset() { *x = ListJobsRequest{} }
func (x *ListJobsRequest) String() string { return "ListJobsRequest{}" }
func (*ListJobsRequest) ProtoMessage() {}
func (x *ListJobsRequest) GetPage() int32 { if x != nil { return x.Page }; return 0 }
func (x *ListJobsRequest) GetPageSize() int32 { if x != nil { return x.PageSize }; return 0 }

type ListJobsResponse struct {
Jobs  []*Job `json:"jobs,omitempty"`
Total int32  `json:"total,omitempty"`
}

func (x *ListJobsResponse) Reset() { *x = ListJobsResponse{} }
func (x *ListJobsResponse) String() string { return "ListJobsResponse{}" }
func (*ListJobsResponse) ProtoMessage() {}
func (x *ListJobsResponse) GetJobs() []*Job { if x != nil { return x.Jobs }; return nil }
func (x *ListJobsResponse) GetTotal() int32 { if x != nil { return x.Total }; return 0 }

// HVACServiceServer is the server API for HVACService service.
type HVACServiceServer interface {
CreateCustomer(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error)
GetCustomer(context.Context, *GetCustomerRequest) (*GetCustomerResponse, error)
ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error)
CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error)
GetJob(context.Context, *GetJobRequest) (*GetJobResponse, error)
ListJobs(context.Context, *ListJobsRequest) (*ListJobsResponse, error)
}

// UnimplementedHVACServiceServer can be embedded to have forward compatible implementations.
type UnimplementedHVACServiceServer struct{}

func (*UnimplementedHVACServiceServer) CreateCustomer(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error) {
return nil, status.Errorf(codes.Unimplemented, "method CreateCustomer not implemented")
}
func (*UnimplementedHVACServiceServer) GetCustomer(context.Context, *GetCustomerRequest) (*GetCustomerResponse, error) {
return nil, status.Errorf(codes.Unimplemented, "method GetCustomer not implemented")
}
func (*UnimplementedHVACServiceServer) ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error) {
return nil, status.Errorf(codes.Unimplemented, "method ListCustomers not implemented")
}
func (*UnimplementedHVACServiceServer) CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error) {
return nil, status.Errorf(codes.Unimplemented, "method CreateJob not implemented")
}
func (*UnimplementedHVACServiceServer) GetJob(context.Context, *GetJobRequest) (*GetJobResponse, error) {
return nil, status.Errorf(codes.Unimplemented, "method GetJob not implemented")
}
func (*UnimplementedHVACServiceServer) ListJobs(context.Context, *ListJobsRequest) (*ListJobsResponse, error) {
return nil, status.Errorf(codes.Unimplemented, "method ListJobs not implemented")
}

func RegisterHVACServiceServer(s *grpc.Server, srv HVACServiceServer) {
s.RegisterService(&_HVACService_serviceDesc, srv)
}

var _HVACService_serviceDesc = grpc.ServiceDesc{
ServiceName: "api.hvac.v1.HVACService",
HandlerType: (*HVACServiceServer)(nil),
Methods: []grpc.MethodDesc{
{MethodName: "CreateCustomer", Handler: _HVACService_CreateCustomer_Handler},
{MethodName: "GetCustomer", Handler: _HVACService_GetCustomer_Handler},
{MethodName: "ListCustomers", Handler: _HVACService_ListCustomers_Handler},
{MethodName: "CreateJob", Handler: _HVACService_CreateJob_Handler},
{MethodName: "GetJob", Handler: _HVACService_GetJob_Handler},
{MethodName: "ListJobs", Handler: _HVACService_ListJobs_Handler},
},
Streams:  []grpc.StreamDesc{},
Metadata: "api/hvac/v1/hvac.proto",
}

func _HVACService_CreateCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
in := new(CreateCustomerRequest)
if err := dec(in); err != nil { return nil, err }
if interceptor == nil { return srv.(HVACServiceServer).CreateCustomer(ctx, in) }
info := &grpc.UnaryServerInfo{Server: srv, FullMethod: "/api.hvac.v1.HVACService/CreateCustomer"}
handler := func(ctx context.Context, req interface{}) (interface{}, error) {
return srv.(HVACServiceServer).CreateCustomer(ctx, req.(*CreateCustomerRequest))
}
return interceptor(ctx, in, info, handler)
}

func _HVACService_GetCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
in := new(GetCustomerRequest)
if err := dec(in); err != nil { return nil, err }
if interceptor == nil { return srv.(HVACServiceServer).GetCustomer(ctx, in) }
info := &grpc.UnaryServerInfo{Server: srv, FullMethod: "/api.hvac.v1.HVACService/GetCustomer"}
handler := func(ctx context.Context, req interface{}) (interface{}, error) {
return srv.(HVACServiceServer).GetCustomer(ctx, req.(*GetCustomerRequest))
}
return interceptor(ctx, in, info, handler)
}

func _HVACService_ListCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
in := new(ListCustomersRequest)
if err := dec(in); err != nil { return nil, err }
if interceptor == nil { return srv.(HVACServiceServer).ListCustomers(ctx, in) }
info := &grpc.UnaryServerInfo{Server: srv, FullMethod: "/api.hvac.v1.HVACService/ListCustomers"}
handler := func(ctx context.Context, req interface{}) (interface{}, error) {
return srv.(HVACServiceServer).ListCustomers(ctx, req.(*ListCustomersRequest))
}
return interceptor(ctx, in, info, handler)
}

func _HVACService_CreateJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
in := new(CreateJobRequest)
if err := dec(in); err != nil { return nil, err }
if interceptor == nil { return srv.(HVACServiceServer).CreateJob(ctx, in) }
info := &grpc.UnaryServerInfo{Server: srv, FullMethod: "/api.hvac.v1.HVACService/CreateJob"}
handler := func(ctx context.Context, req interface{}) (interface{}, error) {
return srv.(HVACServiceServer).CreateJob(ctx, req.(*CreateJobRequest))
}
return interceptor(ctx, in, info, handler)
}

func _HVACService_GetJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
in := new(GetJobRequest)
if err := dec(in); err != nil { return nil, err }
if interceptor == nil { return srv.(HVACServiceServer).GetJob(ctx, in) }
info := &grpc.UnaryServerInfo{Server: srv, FullMethod: "/api.hvac.v1.HVACService/GetJob"}
handler := func(ctx context.Context, req interface{}) (interface{}, error) {
return srv.(HVACServiceServer).GetJob(ctx, req.(*GetJobRequest))
}
return interceptor(ctx, in, info, handler)
}

func _HVACService_ListJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
in := new(ListJobsRequest)
if err := dec(in); err != nil { return nil, err }
if interceptor == nil { return srv.(HVACServiceServer).ListJobs(ctx, in) }
info := &grpc.UnaryServerInfo{Server: srv, FullMethod: "/api.hvac.v1.HVACService/ListJobs"}
handler := func(ctx context.Context, req interface{}) (interface{}, error) {
return srv.(HVACServiceServer).ListJobs(ctx, req.(*ListJobsRequest))
}
return interceptor(ctx, in, info, handler)
}
