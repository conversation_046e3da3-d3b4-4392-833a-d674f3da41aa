// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.21.12
// source: workflow/v1/workflow.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationWorkflowServiceCreateWorkflowFromTemplate = "/api.workflow.v1.WorkflowService/CreateWorkflowFromTemplate"
const OperationWorkflowServiceCreateWorkflowRule = "/api.workflow.v1.WorkflowService/CreateWorkflowRule"
const OperationWorkflowServiceDeleteWorkflowRule = "/api.workflow.v1.WorkflowService/DeleteWorkflowRule"
const OperationWorkflowServiceExecuteWorkflowsForTrigger = "/api.workflow.v1.WorkflowService/ExecuteWorkflowsForTrigger"
const OperationWorkflowServiceGetWorkflowExecutions = "/api.workflow.v1.WorkflowService/GetWorkflowExecutions"
const OperationWorkflowServiceGetWorkflowRules = "/api.workflow.v1.WorkflowService/GetWorkflowRules"
const OperationWorkflowServiceGetWorkflowTemplates = "/api.workflow.v1.WorkflowService/GetWorkflowTemplates"
const OperationWorkflowServiceHealthCheck = "/api.workflow.v1.WorkflowService/HealthCheck"
const OperationWorkflowServiceUpdateWorkflowRule = "/api.workflow.v1.WorkflowService/UpdateWorkflowRule"

type WorkflowServiceHTTPServer interface {
	// CreateWorkflowFromTemplate Create workflow from template
	CreateWorkflowFromTemplate(context.Context, *CreateWorkflowFromTemplateRequest) (*CreateWorkflowFromTemplateResponse, error)
	// CreateWorkflowRule Create workflow rule
	CreateWorkflowRule(context.Context, *CreateWorkflowRuleRequest) (*CreateWorkflowRuleResponse, error)
	// DeleteWorkflowRule Delete workflow rule
	DeleteWorkflowRule(context.Context, *DeleteWorkflowRuleRequest) (*DeleteWorkflowRuleResponse, error)
	// ExecuteWorkflowsForTrigger Execute workflows for trigger
	ExecuteWorkflowsForTrigger(context.Context, *ExecuteWorkflowsForTriggerRequest) (*ExecuteWorkflowsForTriggerResponse, error)
	// GetWorkflowExecutions Get workflow executions
	GetWorkflowExecutions(context.Context, *GetWorkflowExecutionsRequest) (*GetWorkflowExecutionsResponse, error)
	// GetWorkflowRules Get workflow rules
	GetWorkflowRules(context.Context, *GetWorkflowRulesRequest) (*GetWorkflowRulesResponse, error)
	// GetWorkflowTemplates Get workflow templates
	GetWorkflowTemplates(context.Context, *GetWorkflowTemplatesRequest) (*GetWorkflowTemplatesResponse, error)
	// HealthCheck Health check
	HealthCheck(context.Context, *HealthCheckRequest) (*HealthCheckResponse, error)
	// UpdateWorkflowRule Update workflow rule
	UpdateWorkflowRule(context.Context, *UpdateWorkflowRuleRequest) (*UpdateWorkflowRuleResponse, error)
}

func RegisterWorkflowServiceHTTPServer(s *http.Server, srv WorkflowServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/workflow/rules", _WorkflowService_CreateWorkflowRule0_HTTP_Handler(srv))
	r.GET("/api/v1/workflow/rules", _WorkflowService_GetWorkflowRules0_HTTP_Handler(srv))
	r.POST("/api/v1/workflow/execute", _WorkflowService_ExecuteWorkflowsForTrigger0_HTTP_Handler(srv))
	r.GET("/api/v1/workflow/executions", _WorkflowService_GetWorkflowExecutions0_HTTP_Handler(srv))
	r.GET("/api/v1/workflow/templates", _WorkflowService_GetWorkflowTemplates0_HTTP_Handler(srv))
	r.POST("/api/v1/workflow/templates/{template_id}/create", _WorkflowService_CreateWorkflowFromTemplate0_HTTP_Handler(srv))
	r.PUT("/api/v1/workflow/rules/{rule_id}", _WorkflowService_UpdateWorkflowRule0_HTTP_Handler(srv))
	r.DELETE("/api/v1/workflow/rules/{rule_id}", _WorkflowService_DeleteWorkflowRule0_HTTP_Handler(srv))
	r.GET("/api/v1/workflow/health", _WorkflowService_HealthCheck1_HTTP_Handler(srv))
}

func _WorkflowService_CreateWorkflowRule0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateWorkflowRuleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceCreateWorkflowRule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateWorkflowRule(ctx, req.(*CreateWorkflowRuleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateWorkflowRuleResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_GetWorkflowRules0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetWorkflowRulesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceGetWorkflowRules)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWorkflowRules(ctx, req.(*GetWorkflowRulesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetWorkflowRulesResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_ExecuteWorkflowsForTrigger0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExecuteWorkflowsForTriggerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceExecuteWorkflowsForTrigger)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExecuteWorkflowsForTrigger(ctx, req.(*ExecuteWorkflowsForTriggerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExecuteWorkflowsForTriggerResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_GetWorkflowExecutions0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetWorkflowExecutionsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceGetWorkflowExecutions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWorkflowExecutions(ctx, req.(*GetWorkflowExecutionsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetWorkflowExecutionsResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_GetWorkflowTemplates0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetWorkflowTemplatesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceGetWorkflowTemplates)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWorkflowTemplates(ctx, req.(*GetWorkflowTemplatesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetWorkflowTemplatesResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_CreateWorkflowFromTemplate0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateWorkflowFromTemplateRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceCreateWorkflowFromTemplate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateWorkflowFromTemplate(ctx, req.(*CreateWorkflowFromTemplateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateWorkflowFromTemplateResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_UpdateWorkflowRule0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateWorkflowRuleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceUpdateWorkflowRule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateWorkflowRule(ctx, req.(*UpdateWorkflowRuleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateWorkflowRuleResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_DeleteWorkflowRule0_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteWorkflowRuleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceDeleteWorkflowRule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteWorkflowRule(ctx, req.(*DeleteWorkflowRuleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteWorkflowRuleResponse)
		return ctx.Result(200, reply)
	}
}

func _WorkflowService_HealthCheck1_HTTP_Handler(srv WorkflowServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HealthCheckRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorkflowServiceHealthCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.HealthCheck(ctx, req.(*HealthCheckRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HealthCheckResponse)
		return ctx.Result(200, reply)
	}
}

type WorkflowServiceHTTPClient interface {
	CreateWorkflowFromTemplate(ctx context.Context, req *CreateWorkflowFromTemplateRequest, opts ...http.CallOption) (rsp *CreateWorkflowFromTemplateResponse, err error)
	CreateWorkflowRule(ctx context.Context, req *CreateWorkflowRuleRequest, opts ...http.CallOption) (rsp *CreateWorkflowRuleResponse, err error)
	DeleteWorkflowRule(ctx context.Context, req *DeleteWorkflowRuleRequest, opts ...http.CallOption) (rsp *DeleteWorkflowRuleResponse, err error)
	ExecuteWorkflowsForTrigger(ctx context.Context, req *ExecuteWorkflowsForTriggerRequest, opts ...http.CallOption) (rsp *ExecuteWorkflowsForTriggerResponse, err error)
	GetWorkflowExecutions(ctx context.Context, req *GetWorkflowExecutionsRequest, opts ...http.CallOption) (rsp *GetWorkflowExecutionsResponse, err error)
	GetWorkflowRules(ctx context.Context, req *GetWorkflowRulesRequest, opts ...http.CallOption) (rsp *GetWorkflowRulesResponse, err error)
	GetWorkflowTemplates(ctx context.Context, req *GetWorkflowTemplatesRequest, opts ...http.CallOption) (rsp *GetWorkflowTemplatesResponse, err error)
	HealthCheck(ctx context.Context, req *HealthCheckRequest, opts ...http.CallOption) (rsp *HealthCheckResponse, err error)
	UpdateWorkflowRule(ctx context.Context, req *UpdateWorkflowRuleRequest, opts ...http.CallOption) (rsp *UpdateWorkflowRuleResponse, err error)
}

type WorkflowServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewWorkflowServiceHTTPClient(client *http.Client) WorkflowServiceHTTPClient {
	return &WorkflowServiceHTTPClientImpl{client}
}

func (c *WorkflowServiceHTTPClientImpl) CreateWorkflowFromTemplate(ctx context.Context, in *CreateWorkflowFromTemplateRequest, opts ...http.CallOption) (*CreateWorkflowFromTemplateResponse, error) {
	var out CreateWorkflowFromTemplateResponse
	pattern := "/api/v1/workflow/templates/{template_id}/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkflowServiceCreateWorkflowFromTemplate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) CreateWorkflowRule(ctx context.Context, in *CreateWorkflowRuleRequest, opts ...http.CallOption) (*CreateWorkflowRuleResponse, error) {
	var out CreateWorkflowRuleResponse
	pattern := "/api/v1/workflow/rules"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkflowServiceCreateWorkflowRule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) DeleteWorkflowRule(ctx context.Context, in *DeleteWorkflowRuleRequest, opts ...http.CallOption) (*DeleteWorkflowRuleResponse, error) {
	var out DeleteWorkflowRuleResponse
	pattern := "/api/v1/workflow/rules/{rule_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkflowServiceDeleteWorkflowRule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) ExecuteWorkflowsForTrigger(ctx context.Context, in *ExecuteWorkflowsForTriggerRequest, opts ...http.CallOption) (*ExecuteWorkflowsForTriggerResponse, error) {
	var out ExecuteWorkflowsForTriggerResponse
	pattern := "/api/v1/workflow/execute"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkflowServiceExecuteWorkflowsForTrigger))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) GetWorkflowExecutions(ctx context.Context, in *GetWorkflowExecutionsRequest, opts ...http.CallOption) (*GetWorkflowExecutionsResponse, error) {
	var out GetWorkflowExecutionsResponse
	pattern := "/api/v1/workflow/executions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkflowServiceGetWorkflowExecutions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) GetWorkflowRules(ctx context.Context, in *GetWorkflowRulesRequest, opts ...http.CallOption) (*GetWorkflowRulesResponse, error) {
	var out GetWorkflowRulesResponse
	pattern := "/api/v1/workflow/rules"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkflowServiceGetWorkflowRules))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) GetWorkflowTemplates(ctx context.Context, in *GetWorkflowTemplatesRequest, opts ...http.CallOption) (*GetWorkflowTemplatesResponse, error) {
	var out GetWorkflowTemplatesResponse
	pattern := "/api/v1/workflow/templates"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkflowServiceGetWorkflowTemplates))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) HealthCheck(ctx context.Context, in *HealthCheckRequest, opts ...http.CallOption) (*HealthCheckResponse, error) {
	var out HealthCheckResponse
	pattern := "/api/v1/workflow/health"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWorkflowServiceHealthCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WorkflowServiceHTTPClientImpl) UpdateWorkflowRule(ctx context.Context, in *UpdateWorkflowRuleRequest, opts ...http.CallOption) (*UpdateWorkflowRuleResponse, error) {
	var out UpdateWorkflowRuleResponse
	pattern := "/api/v1/workflow/rules/{rule_id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorkflowServiceUpdateWorkflowRule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
