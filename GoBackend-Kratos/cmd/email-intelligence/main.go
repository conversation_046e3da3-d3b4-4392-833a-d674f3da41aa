package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	kratoslog "github.com/go-kratos/kratos/v2/log"

	"gobackend-hvac-kratos/internal/email"
)

// 🚀 Email Intelligence Service - Standalone Application
// Integrates with GoBackend-Kratos HVAC CRM for comprehensive email analysis

var (
	// Name is the name of the compiled software.
	Name = "email-intelligence-service"
	// Version is the version of the compiled software.
	Version = "v1.0.0"
	// flagconf is the config flag.
	flagconf = flag.String("conf", "../../configs/email-intelligence.yaml", "config path, eg: -conf config.yaml")
)

// 📧 Email Intelligence Configuration
type EmailIntelligenceConfig struct {
	Analysis              *email.EmailAnalysisConfig      `yaml:"analysis"`
	Mailboxes            []*email.MailboxConfig          `yaml:"mailboxes"`
	HTTPPort             int                             `yaml:"http_port"`
	EnableCORS           bool                            `yaml:"enable_cors"`
	LogLevel             string                          `yaml:"log_level"`
	OllamaURL            string                          `yaml:"ollama_url"`
	VectorDBPath         string                          `yaml:"vector_db_path"`
	MaxConcurrentAnalysis int                            `yaml:"max_concurrent_analysis"`
	ProcessingTimeout    time.Duration                   `yaml:"processing_timeout"`
	RetryAttempts        int                             `yaml:"retry_attempts"`
}

func main() {
	flag.Parse()

	// Initialize logger
	logger := kratoslog.With(kratoslog.NewStdLogger(os.Stdout),
		"ts", kratoslog.DefaultTimestamp,
		"caller", kratoslog.DefaultCaller,
		"service.name", Name,
		"service.version", Version,
	)

	// Load configuration
	c := config.New(
		config.WithSource(
			file.NewSource(*flagconf),
		),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	var cfg EmailIntelligenceConfig
	if err := c.Scan(&cfg); err != nil {
		log.Fatalf("Failed to scan config: %v", err)
	}

	// Convert to service config
	serviceConfig := &email.EmailIntelligenceConfig{
		Analysis:              cfg.Analysis,
		Mailboxes:            cfg.Mailboxes,
		HTTPPort:             cfg.HTTPPort,
		EnableCORS:           cfg.EnableCORS,
		LogLevel:             cfg.LogLevel,
		OllamaURL:            cfg.OllamaURL,
		VectorDBPath:         cfg.VectorDBPath,
		MaxConcurrentAnalysis: cfg.MaxConcurrentAnalysis,
		ProcessingTimeout:    cfg.ProcessingTimeout,
		RetryAttempts:        cfg.RetryAttempts,
	}

	// Apply environment variable overrides
	applyEnvironmentOverrides(serviceConfig)

	// Initialize Email Intelligence Service
	emailService, err := email.NewEmailIntelligenceService(serviceConfig, logger)
	if err != nil {
		log.Fatalf("Failed to initialize email intelligence service: %v", err)
	}

	// Create Kratos app
	app := kratos.New(
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{
			"description": "HVAC Email Intelligence & Analysis Service",
			"author":      "GoBackend-Kratos Team",
		}),
		kratos.Logger(logger),
		kratos.Server(
			// We'll use the email service's built-in HTTP server
		),
	)

	// Start the application
	ctx := context.Background()

	// Start email intelligence service
	if err := emailService.Start(ctx); err != nil {
		log.Fatalf("Failed to start email intelligence service: %v", err)
	}

	// Print startup information
	printStartupInfo(serviceConfig)

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	kratoslog.NewHelper(logger).Info("Email Intelligence Service started successfully")
	kratoslog.NewHelper(logger).Infof("HTTP server listening on port %d", serviceConfig.HTTPPort)
	kratoslog.NewHelper(logger).Info("Press Ctrl+C to shutdown...")

	// Wait for shutdown signal
	<-quit

	kratoslog.NewHelper(logger).Info("Shutting down Email Intelligence Service...")

	// Graceful shutdown
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := emailService.Stop(shutdownCtx); err != nil {
		kratoslog.NewHelper(logger).Errorf("Email service shutdown error: %v", err)
	}

	if err := app.Stop(); err != nil {
		kratoslog.NewHelper(logger).Errorf("App shutdown error: %v", err)
	}

	kratoslog.NewHelper(logger).Info("Email Intelligence Service stopped")
}

// 🌍 Apply environment variable overrides
func applyEnvironmentOverrides(config *email.EmailIntelligenceConfig) {
	// Gmail configuration
	if username := os.Getenv("GMAIL_USERNAME"); username != "" {
		for _, mailbox := range config.Mailboxes {
			if mailbox.Name == "HVAC_Gmail_Primary" {
				mailbox.Username = username
			}
		}
	}

	if password := os.Getenv("GMAIL_PASSWORD"); password != "" {
		for _, mailbox := range config.Mailboxes {
			if mailbox.Name == "HVAC_Gmail_Primary" {
				mailbox.Password = password
				mailbox.Enabled = true // Auto-enable if credentials provided
			}
		}
	}

	// Outlook configuration
	if username := os.Getenv("OUTLOOK_USERNAME"); username != "" {
		for _, mailbox := range config.Mailboxes {
			if mailbox.Name == "HVAC_Outlook_Support" {
				mailbox.Username = username
			}
		}
	}

	if password := os.Getenv("OUTLOOK_PASSWORD"); password != "" {
		for _, mailbox := range config.Mailboxes {
			if mailbox.Name == "HVAC_Outlook_Support" {
				mailbox.Password = password
				mailbox.Enabled = true // Auto-enable if credentials provided
			}
		}
	}

	// Business email configuration
	if username := os.Getenv("BUSINESS_USERNAME"); username != "" {
		for _, mailbox := range config.Mailboxes {
			if mailbox.Name == "HVAC_Business_Main" {
				mailbox.Username = username
			}
		}
	}

	if password := os.Getenv("BUSINESS_PASSWORD"); password != "" {
		for _, mailbox := range config.Mailboxes {
			if mailbox.Name == "HVAC_Business_Main" {
				mailbox.Password = password
				mailbox.Enabled = true // Auto-enable if credentials provided
			}
		}
	}

	// Ollama URL override
	if ollamaURL := os.Getenv("OLLAMA_URL"); ollamaURL != "" {
		config.OllamaURL = ollamaURL
		if config.Analysis != nil {
			config.Analysis.OllamaURL = ollamaURL
		}
	}

	// Vector DB path override
	if vectorDBPath := os.Getenv("VECTOR_DB_PATH"); vectorDBPath != "" {
		config.VectorDBPath = vectorDBPath
		if config.Analysis != nil {
			config.Analysis.VectorDBPath = vectorDBPath
		}
	}

	// HTTP port override
	if httpPort := os.Getenv("HTTP_PORT"); httpPort != "" {
		if port := parseInt(httpPort); port > 0 {
			config.HTTPPort = port
		}
	}
}

// 📊 Print startup information
func printStartupInfo(config *email.EmailIntelligenceConfig) {
	fmt.Println("\n🚀 ===============================================")
	fmt.Println("📧 HVAC Email Intelligence Service")
	fmt.Println("🔧 GoBackend-Kratos Integration")
	fmt.Println("===============================================")
	fmt.Printf("🌐 HTTP Server: http://localhost:%d\n", config.HTTPPort)
	fmt.Printf("🤖 Ollama URL: %s\n", config.OllamaURL)
	fmt.Printf("🗄️ Vector DB: %s\n", config.VectorDBPath)
	fmt.Printf("⚡ Max Concurrent: %d\n", config.MaxConcurrentAnalysis)
	fmt.Println("\n📮 Configured Mailboxes:")

	enabledCount := 0
	for _, mailbox := range config.Mailboxes {
		status := "❌ Disabled"
		if mailbox.Enabled {
			status = "✅ Enabled"
			enabledCount++
		}
		fmt.Printf("  • %s (%s:%d) - %s\n", mailbox.Name, mailbox.Host, mailbox.Port, status)
	}

	fmt.Printf("\n📊 Active Mailboxes: %d/%d\n", enabledCount, len(config.Mailboxes))

	fmt.Println("\n🔗 API Endpoints:")
	fmt.Printf("  • Dashboard Stats: http://localhost:%d/api/v1/email-analysis/dashboard/stats\n", config.HTTPPort)
	fmt.Printf("  • Search Emails: http://localhost:%d/api/v1/email-analysis/search\n", config.HTTPPort)
	fmt.Printf("  • Retrieval Status: http://localhost:%d/api/v1/retrieval/status\n", config.HTTPPort)
	fmt.Printf("  • Health Check: http://localhost:%d/health\n", config.HTTPPort)
	fmt.Printf("  • Service Status: http://localhost:%d/status\n", config.HTTPPort)

	fmt.Println("\n🔧 Quick Commands:")
	fmt.Printf("  # Start email retrieval\n")
	fmt.Printf("  curl -X POST http://localhost:%d/api/v1/retrieval/start\n\n", config.HTTPPort)
	fmt.Printf("  # View dashboard stats\n")
	fmt.Printf("  curl http://localhost:%d/api/v1/email-analysis/dashboard/stats\n\n", config.HTTPPort)
	fmt.Printf("  # Search HVAC emails\n")
	fmt.Printf("  curl -X POST http://localhost:%d/api/v1/email-analysis/search \\\n", config.HTTPPort)
	fmt.Printf("    -H \"Content-Type: application/json\" \\\n")
	fmt.Printf("    -d '{\"query\":\"HVAC repair\",\"limit\":10}'\n\n")

	if enabledCount == 0 {
		fmt.Println("⚠️  WARNING: No mailboxes are enabled!")
		fmt.Println("   Set environment variables and restart to enable email retrieval:")
		fmt.Println("   export GMAIL_USERNAME=<EMAIL>")
		fmt.Println("   export GMAIL_PASSWORD=your-app-password")
	}

	fmt.Println("===============================================")
}

// 🔧 Helper function to parse integer
func parseInt(s string) int {
	var result int
	fmt.Sscanf(s, "%d", &result)
	return result
}

// 🎯 Integration Notes:
//
// This Email Intelligence Service is designed to work alongside the main
// GoBackend-Kratos HVAC CRM system. It provides:
//
// 1. 📧 Multi-mailbox email retrieval (Gmail, Outlook, Business)
// 2. 🤖 AI-powered email analysis using Ollama/Gemma models
// 3. 📊 Real-time dashboard for email analytics
// 4. 🔍 Vector-based semantic email search
// 5. 📎 Attachment processing (Excel, Word, PDF)
// 6. 🏷️ HVAC-specific categorization and priority detection
// 7. 🌐 RESTful API for CRM integration
//
// To integrate with the main CRM:
// - Use the API endpoints to fetch analyzed emails
// - Subscribe to webhooks for real-time notifications
// - Query the vector database for similar emails
// - Access dashboard data for business intelligence
//
// The service runs independently but can be deployed alongside
// the main GoBackend-Kratos application for seamless integration.
