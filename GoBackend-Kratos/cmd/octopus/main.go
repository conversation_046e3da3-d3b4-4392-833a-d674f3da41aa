package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/customer"
	"gobackend-hvac-kratos/internal/email"
	"gobackend-hvac-kratos/internal/octopus"
	"gobackend-hvac-kratos/internal/transcription"
)

var (
	// Name is the name of the compiled software.
	Name = "octopus-interface"
	// Version is the version of the compiled software.
	Version = "v1.0.0"

	flagconf = flag.String("conf", "../../configs/octopus.yaml", "config path, eg: -conf config.yaml")
)

func main() {
	flag.Parse()

	// Initialize logger with enhanced formatting
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", Name,
		"service.name", Name,
		"service.version", Version,
		"component", "octopus-interface",
	)

	log := log.NewHelper(logger)

	log.Infof("🐙 Starting Morphic Octopus Interface %s", Version)
	log.Info("🔧 Initializing LangFuse-like AI Flow Tracking System...")
	log.Info("📊 Loading advanced monitoring and analytics...")

	// Load configuration
	c := config.New(
		config.WithSource(
			file.NewSource(*flagconf),
		),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize database connection
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Initialize services
	services, err := initServices(db, logger)
	if err != nil {
		log.Fatalf("Failed to initialize services: %v", err)
	}

	// Initialize Octopus configuration
	octopusConfig := &octopus.OctopusConfig{
		HTTPPort:         8083,
		WebSocketEnabled: true,
		DashboardPath:    "/dashboard",
		AuthEnabled:      false,
		AdminUsers:       []string{"<EMAIL>"},
		RefreshInterval:  5 * time.Second,
		MaxConnections:   100,
	}

	// Create Morphic Octopus Interface
	octopusInterface := octopus.NewMorphicOctopusInterface(
		db,
		services.EmailService,
		services.TranscriptionService,
		services.CustomerService,
		services.AIService,
		octopusConfig,
		logger,
	)

	// Start the interface
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := octopusInterface.Start(ctx); err != nil {
		log.Fatalf("Failed to start Octopus Interface: %v", err)
	}

	log.Info("🐙 Morphic Octopus Interface started successfully!")
	log.Infof("🌐 Dashboard available at: http://localhost:%d/dashboard", octopusConfig.HTTPPort)
	log.Infof("🔌 WebSocket endpoint: ws://localhost:%d/api/dashboard/ws", octopusConfig.HTTPPort)

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("🐙 Shutting down Morphic Octopus Interface...")

	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	if err := octopusInterface.Stop(shutdownCtx); err != nil {
		log.Errorf("Failed to shutdown Octopus Interface: %v", err)
	}

	log.Info("🐙 Morphic Octopus Interface shutdown complete")
}

// initDatabase initializes the database connection
func initDatabase() (*gorm.DB, error) {
	// Get database URL from environment
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		databaseURL = "*************************************************/hvacdb?sslmode=disable"
	}

	// Connect to PostgreSQL
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return db, nil
}

// Services represents all initialized services
type Services struct {
	EmailService         *email.EmailIntelligenceService
	TranscriptionService *transcription.TranscriptionParser
	CustomerService      *customer.CustomerIntelligenceService
	AIService            *ai.Gemma3Service
}

// initServices initializes all required services
func initServices(db *gorm.DB, logger log.Logger) (*Services, error) {
	// Initialize email service (mock for now)
	emailService := &email.EmailIntelligenceService{}

	// Initialize transcription service (mock for now)
	transcriptionService := &transcription.TranscriptionParser{}

	// Initialize customer service (mock for now)
	customerService := &customer.CustomerIntelligenceService{}

	// Initialize AI service (mock for now)
	aiService := &ai.Gemma3Service{}

	return &Services{
		EmailService:         emailService,
		TranscriptionService: transcriptionService,
		CustomerService:      customerService,
		AIService:            aiService,
	}, nil
}