version: '3.8'

services:
  # Ollama AI Model Server
  ollama:
    image: ollama/ollama:latest
    container_name: hvac-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
      - ./models:/models
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_ORIGINS=*
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Ollama Web UI (Optional)
  ollama-webui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: hvac-ollama-webui
    ports:
      - "3000:8080"
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
      - WEBUI_SECRET_KEY=hvac-secret-key-2024
    volumes:
      - ollama_webui_data:/app/backend/data
    depends_on:
      ollama:
        condition: service_healthy
    restart: unless-stopped

  # Model Downloader (Init Container)
  model-downloader:
    image: curlimages/curl:latest
    container_name: hvac-model-downloader
    volumes:
      - ./scripts:/scripts
    command: >
      sh -c "
        echo '🚀 Downloading Gemma-3-4b-it-qat-q4_0-gguf model...'
        curl -L -o /scripts/download-gemma.sh https://raw.githubusercontent.com/ollama/ollama/main/scripts/install.sh
        chmod +x /scripts/download-gemma.sh
        echo '✅ Model download script ready!'
      "
    restart: "no"

volumes:
  ollama_data:
  ollama_webui_data:

networks:
  default:
    name: hvac-ai-network