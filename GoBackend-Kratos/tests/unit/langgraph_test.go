package unit

import (
	"database/sql"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/go-kratos/kratos/v2/log"

	"gobackend-hvac-kratos/internal/langgraph"
)

// 🧪 LangGraph Workflow Service Unit Tests

func TestWorkflowService_MapToJSON(t *testing.T) {
	// Mock test - in real implementation, create actual service
	t.Skip("Skipping test - requires actual WorkflowService implementation")

	t.Run("Valid_Map_Conversion", func(t *testing.T) {
		testData := map[string]interface{}{
			"customer_id": "123",
			"priority": "high",
			"service_type": "emergency",
		}

		result := service.MapToJSON(testData)
		assert.NotEmpty(t, result)
		assert.Contains(t, result, "customer_id")
		assert.Contains(t, result, "123")
	})

	t.Run("Empty_Map_Conversion", func(t *testing.T) {
		testData := make(map[string]interface{})
		result := service.MapToJSON(testData)
		assert.Equal(t, "{}", result)
	})

	t.Run("Nil_Map_Conversion", func(t *testing.T) {
		result := service.MapToJSON(nil)
		assert.Equal(t, "{}", result)
	})
}

func TestWorkflowService_JSONToMap(t *testing.T) {
	logger := log.NewStdLogger(log.NewStdLogger(nil))
	service := &langgraph.WorkflowService{}
	service.SetLogger(log.NewHelper(logger))

	t.Run("Valid_JSON_Conversion", func(t *testing.T) {
		jsonStr := `{"customer_id":"123","priority":"high","service_type":"emergency"}`

		result, err := service.JSONToMap(jsonStr)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "123", result["customer_id"])
		assert.Equal(t, "high", result["priority"])
		assert.Equal(t, "emergency", result["service_type"])
	})

	t.Run("Empty_JSON_Conversion", func(t *testing.T) {
		result, err := service.JSONToMap("")
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Empty(t, result)
	})

	t.Run("Empty_Object_JSON_Conversion", func(t *testing.T) {
		result, err := service.JSONToMap("{}")
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Empty(t, result)
	})

	t.Run("Invalid_JSON_Conversion", func(t *testing.T) {
		_, err := service.JSONToMap("invalid json")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to unmarshal JSON")
	})
}

func TestWorkflowService_ExecuteWorkflow(t *testing.T) {
	// Mock database for testing
	db := setupMockDB(t)
	defer db.Close()

	logger := log.NewStdLogger(log.NewStdLogger(nil))
	config := &langgraph.LangGraphConfig{
		MaxExecutionTime: 30 * time.Second,
		MaxRetries:       3,
		EnableLogging:    true,
		CacheResults:     false,
	}

	service := langgraph.NewWorkflowService(db, config, logger)

	t.Run("Valid_Workflow_Execution", func(t *testing.T) {
		ctx := context.Background()
		input := map[string]interface{}{
			"customer_data": map[string]interface{}{
				"name": "John Doe",
				"email": "<EMAIL>",
				"service_type": "heating",
			},
		}

		execution, err := service.ExecuteWorkflow(ctx, "customer_onboarding", input)
		require.NoError(t, err)
		assert.NotNil(t, execution)
		assert.NotEmpty(t, execution.ID)
		assert.Equal(t, "customer_onboarding", execution.WorkflowType)
		assert.Equal(t, "running", execution.Status)
		assert.NotEmpty(t, execution.InputData)
	})

	t.Run("Invalid_Workflow_ID", func(t *testing.T) {
		ctx := context.Background()
		input := map[string]interface{}{"test": "data"}

		_, err := service.ExecuteWorkflow(ctx, "nonexistent_workflow", input)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "workflow nonexistent_workflow not found")
	})

	t.Run("Empty_Input_Data", func(t *testing.T) {
		ctx := context.Background()

		execution, err := service.ExecuteWorkflow(ctx, "customer_onboarding", nil)
		require.NoError(t, err)
		assert.NotNil(t, execution)
		assert.Equal(t, "{}", execution.InputData)
	})
}

func TestWorkflowService_GetWorkflowStatus(t *testing.T) {
	db := setupMockDB(t)
	defer db.Close()

	// Insert test data
	insertTestWorkflowExecution(t, db)

	logger := log.NewStdLogger(log.NewStdLogger(nil))
	config := &langgraph.LangGraphConfig{}
	service := langgraph.NewWorkflowService(db, config, logger)

	t.Run("Existing_Workflow_Status", func(t *testing.T) {
		ctx := context.Background()

		execution, err := service.GetWorkflowStatus(ctx, "test-execution-1")
		require.NoError(t, err)
		assert.NotNil(t, execution)
		assert.Equal(t, "test-execution-1", execution.ID)
		assert.Equal(t, "customer_onboarding", execution.WorkflowType)
		assert.Equal(t, "completed", execution.Status)
	})

	t.Run("Nonexistent_Workflow_Status", func(t *testing.T) {
		ctx := context.Background()

		_, err := service.GetWorkflowStatus(ctx, "nonexistent-execution")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "workflow execution nonexistent-execution not found")
	})
}

func TestWorkflowService_ListWorkflows(t *testing.T) {
	logger := log.NewStdLogger(log.NewStdLogger(nil))
	config := &langgraph.LangGraphConfig{}
	service := langgraph.NewWorkflowService(nil, config, logger)

	workflows := service.ListWorkflows()
	assert.NotNil(t, workflows)
	assert.NotEmpty(t, workflows)

	// Check for expected workflows
	assert.Contains(t, workflows, "customer_onboarding")
	assert.Contains(t, workflows, "emergency_response")

	// Validate workflow structure
	customerOnboarding := workflows["customer_onboarding"]
	assert.NotNil(t, customerOnboarding)
	assert.Equal(t, "customer_onboarding", customerOnboarding.ID)
	assert.Equal(t, "Customer Onboarding Process", customerOnboarding.Name)
	assert.NotEmpty(t, customerOnboarding.Nodes)
	assert.NotEmpty(t, customerOnboarding.Edges)
	assert.NotEmpty(t, customerOnboarding.StartNode)
	assert.NotEmpty(t, customerOnboarding.EndNodes)
}

// 🔧 Test Helper Functions

func setupMockDB(t *testing.T) *sql.DB {
	// In a real test, you would use a test database
	// For this example, we'll use an in-memory SQLite database
	db, err := sql.Open("sqlite3", ":memory:")
	require.NoError(t, err)

	// Create test tables
	createTestTables(t, db)

	return db
}

func createTestTables(t *testing.T, db *sql.DB) {
	createTableSQL := `
	CREATE TABLE workflow_executions (
		execution_id TEXT PRIMARY KEY,
		workflow_type TEXT NOT NULL,
		status TEXT NOT NULL,
		input_data TEXT,
		output_data TEXT,
		started_at DATETIME,
		completed_at DATETIME,
		error_message TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	_, err := db.Exec(createTableSQL)
	require.NoError(t, err)
}

func insertTestWorkflowExecution(t *testing.T, db *sql.DB) {
	insertSQL := `
	INSERT INTO workflow_executions
	(execution_id, workflow_type, status, input_data, output_data, started_at, completed_at)
	VALUES
	(?, ?, ?, ?, ?, ?, ?)`

	now := time.Now()
	_, err := db.Exec(insertSQL,
		"test-execution-1",
		"customer_onboarding",
		"completed",
		`{"customer_data":{"name":"John Doe","email":"<EMAIL>"}}`,
		`{"account_id":"CUST_12345","account_created":true}`,
		now.Add(-1*time.Hour),
		now.Add(-30*time.Minute),
	)
	require.NoError(t, err)
}

// 🧪 Workflow Node Handler Tests

func TestWorkflowNodeHandlers(t *testing.T) {
	logger := log.NewStdLogger(log.NewStdLogger(nil))
	config := &langgraph.LangGraphConfig{}
	service := langgraph.NewWorkflowService(nil, config, logger)

	ctx := context.Background()

	t.Run("Customer_Input_Handler", func(t *testing.T) {
		input := map[string]interface{}{
			"name": "John Doe",
			"email": "<EMAIL>",
			"service_type": "heating",
		}

		result, err := service.HandleCustomerInput(ctx, input)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "data_valid", result["condition"])
		assert.Equal(t, input, result["customer_data"])
	})

	t.Run("Data_Validation_Handler", func(t *testing.T) {
		input := map[string]interface{}{
			"customer_data": map[string]interface{}{
				"name": "John Doe",
				"email": "<EMAIL>",
			},
		}

		result, err := service.HandleDataValidation(ctx, input)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "data_valid", result["condition"])
		assert.Equal(t, "passed", result["validation_result"])
	})

	t.Run("Credit_Check_Handler", func(t *testing.T) {
		input := map[string]interface{}{
			"customer_data": map[string]interface{}{
				"name": "John Doe",
				"ssn": "***********",
			},
		}

		result, err := service.HandleCreditCheck(ctx, input)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Contains(t, result, "credit_score")
		assert.Contains(t, result, "credit_status")

		creditScore, ok := result["credit_score"].(int)
		assert.True(t, ok)
		assert.Greater(t, creditScore, 0)
	})

	t.Run("Emergency_Intake_Handler", func(t *testing.T) {
		input := map[string]interface{}{
			"emergency_type": "heating_failure",
			"customer_location": "123 Main St",
		}

		result, err := service.HandleEmergencyIntake(ctx, input)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Contains(t, result, "emergency_type")
		assert.Contains(t, result, "severity")
		assert.Equal(t, "heating_failure", result["emergency_type"])
		assert.Equal(t, "high", result["severity"])
	})
}

// 🔄 Workflow Edge and Flow Tests

func TestWorkflowFlow(t *testing.T) {
	logger := log.NewStdLogger(log.NewStdLogger(nil))
	config := &langgraph.LangGraphConfig{}
	service := langgraph.NewWorkflowService(nil, config, logger)

	workflows := service.ListWorkflows()
	customerOnboarding := workflows["customer_onboarding"]

	t.Run("Workflow_Structure_Validation", func(t *testing.T) {
		assert.NotNil(t, customerOnboarding)
		assert.NotEmpty(t, customerOnboarding.StartNode)
		assert.NotEmpty(t, customerOnboarding.EndNodes)
		assert.NotEmpty(t, customerOnboarding.Nodes)
		assert.NotEmpty(t, customerOnboarding.Edges)

		// Validate start node exists in nodes
		_, exists := customerOnboarding.Nodes[customerOnboarding.StartNode]
		assert.True(t, exists, "Start node should exist in nodes map")

		// Validate end nodes exist in nodes
		for _, endNode := range customerOnboarding.EndNodes {
			_, exists := customerOnboarding.Nodes[endNode]
			assert.True(t, exists, "End node %s should exist in nodes map", endNode)
		}
	})

	t.Run("Node_Handler_Validation", func(t *testing.T) {
		for nodeID, node := range customerOnboarding.Nodes {
			assert.NotNil(t, node.Handler, "Node %s should have a handler", nodeID)
			assert.NotEmpty(t, node.Name, "Node %s should have a name", nodeID)
			assert.NotEmpty(t, node.Description, "Node %s should have a description", nodeID)
			assert.Greater(t, node.Timeout, time.Duration(0), "Node %s should have a positive timeout", nodeID)
		}
	})

	t.Run("Edge_Connectivity_Validation", func(t *testing.T) {
		for _, edge := range customerOnboarding.Edges {
			// Validate from node exists
			_, exists := customerOnboarding.Nodes[edge.From]
			assert.True(t, exists, "Edge from node %s should exist", edge.From)

			// Validate to node exists
			_, exists = customerOnboarding.Nodes[edge.To]
			assert.True(t, exists, "Edge to node %s should exist", edge.To)
		}
	})
}
