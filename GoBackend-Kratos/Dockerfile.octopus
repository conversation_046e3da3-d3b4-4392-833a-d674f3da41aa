# 🐙 Morphic Octopus Interface Dockerfile
# Ultimate HVAC Backend Management System with LangFuse-like Tracking

# Build stage
FROM golang:1.23-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata protobuf-dev protoc make

# Set working directory
WORKDIR /app

# Install protoc plugins
RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@latest && \
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest && \
    go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Generate protobuf files
RUN make api config

# Build the Octopus Interface binary
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o octopus-interface ./cmd/octopus

# Runtime stage
FROM alpine:latest

# Install runtime dependencies
RUN apk --no-cache add ca-certificates curl

# Create app user
RUN addgroup -g 1001 -S octopus && \
    adduser -u 1001 -S octopus -G octopus

# Set working directory
WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/octopus-interface .

# Copy configuration files
COPY --from=builder /app/configs ./configs

# Create necessary directories
RUN mkdir -p logs data/vectordb web/octopus/static && \
    chown -R octopus:octopus /app

# Switch to app user
USER octopus

# Expose port
EXPOSE 8083

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8083/api/system/health || exit 1

# Run the Octopus Interface
CMD ["./octopus-interface", "-conf", "configs/octopus.yaml"]