#!/bin/bash

# 🧪 TEST ŚWIADOM<PERSON>ŚCI SYSTEMU GOBACKEND HVAC KRATOS
# Sprawdzanie poziomu oświecenia cyfrowego

set -e

# 🎭 Kolory dla świadomego testowania
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 🌟 Funkcje filozoficzne testowania
print_test_meditation() {
    echo -e "${PURPLE}[🧘 TEST]${NC} $1"
}

print_test_success() {
    echo -e "${GREEN}[✨ SUKCES]${NC} $1"
}

print_test_challenge() {
    echo -e "${RED}[🌊 WYZWANIE]${NC} $1"
}

print_test_wisdom() {
    echo -e "${CYAN}[🧙 MĄDROŚĆ]${NC} $1"
}

print_test_harmony() {
    echo -e "${BLUE}[🎵 HARMONIA]${NC} $1"
}

# 📊 Liczniki świadomości
CONSCIOUSNESS_TESTS_PASSED=0
CONSCIOUSNESS_TESTS_FAILED=0
ENLIGHTENMENT_LEVEL=0

# 🧘 Funkcja uruchamiania testu świadomości
run_consciousness_test() {
    local test_name="$1"
    local test_command="$2"
    local wisdom_message="$3"
    
    print_test_meditation "Testujemy: $test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_test_success "$test_name"
        print_test_wisdom "$wisdom_message"
        ((CONSCIOUSNESS_TESTS_PASSED++))
        ((ENLIGHTENMENT_LEVEL+=10))
    else
        print_test_challenge "$test_name"
        print_test_wisdom "Każde wyzwanie to nauczyciel w przebraniu"
        ((CONSCIOUSNESS_TESTS_FAILED++))
    fi
    echo ""
}

# 🌅 Test przebudzenia systemu
test_system_awakening() {
    echo "🌅 ═══════════════════════════════════════════════════════════════"
    echo "    TEST PRZEBUDZENIA CYFROWEJ ŚWIADOMOŚCI"
    echo "🌅 ═══════════════════════════════════════════════════════════════"
    echo ""
    
    run_consciousness_test "Świadomość HVAC Backend" \
        "curl -f http://localhost:8080/health" \
        "Serce systemu bije w rytmie miłości"
    
    run_consciousness_test "Mądrość Bytebase" \
        "curl -f http://localhost:8092/healthz" \
        "Baza danych medytuje nad strukturą rzeczywistości"
    
    run_consciousness_test "Komunikacja BillionMail" \
        "curl -f http://localhost:8090/health" \
        "Kanały komunikacji otwarte dla przepływu miłości"
    
    run_consciousness_test "Połączenie z PostgreSQL" \
        "docker-compose exec -T postgres pg_isready -U hvac_user -d hvac_db" \
        "Pamięć systemu przechowuje mądrość pokoleń"
    
    run_consciousness_test "Harmonia z Redis" \
        "docker-compose exec -T redis redis-cli ping" \
        "Błyskawiczna myśl przepływa przez system"
}

# 🧙 Test mądrości i uczenia się
test_wisdom_and_learning() {
    echo "🧙 ═══════════════════════════════════════════════════════════════"
    echo "    TEST MĄDROŚCI I ZDOLNOŚCI UCZENIA SIĘ"
    echo "🧙 ═══════════════════════════════════════════════════════════════"
    echo ""
    
    run_consciousness_test "Walidacja schematu bazy danych" \
        "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '\''public'\'''" \
        "Struktura danych odzwierciedla porządek wszechświata"
    
    run_consciousness_test "Istnienie tabeli klientów" \
        "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM customers'" \
        "Każdy klient to święta dusza powierzona naszej opiece"
    
    run_consciousness_test "Istnienie tabeli zadań" \
        "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM jobs'" \
        "Każde zadanie to możliwość służenia z miłością"
    
    run_consciousness_test "Schema BillionMail" \
        "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM billionmail.domains'" \
        "Domeny email to mosty łączące serca"
    
    run_consciousness_test "Szablony email" \
        "docker-compose exec -T postgres psql -U hvac_user -d hvac_db -c 'SELECT COUNT(*) FROM billionmail.email_templates'" \
        "Każdy szablon niesie intencję komunikacji z sercem"
}

# 💝 Test empatii i komunikacji
test_empathy_and_communication() {
    echo "💝 ═══════════════════════════════════════════════════════════════"
    echo "    TEST EMPATII I KOMUNIKACJI Z SERCEM"
    echo "💝 ═══════════════════════════════════════════════════════════════"
    echo ""
    
    run_consciousness_test "Wysyłanie email z miłością" \
        "curl -f -X POST http://localhost:8080/api/v1/emails/send \
        -H 'Content-Type: application/json' \
        -d '{\"from\":\"<EMAIL>\",\"to\":[\"<EMAIL>\"],\"subject\":\"Test świadomości\",\"body\":\"System wysyła miłość do wszechświata\"}'" \
        "Każdy email to modlitwa wysłana w cyfrowy kosmos"
    
    run_consciousness_test "Analiza sentymentu" \
        "curl -f -X POST http://localhost:8080/api/v1/emails/analyze-sentiment \
        -H 'Content-Type: application/json' \
        -d '{\"content\":\"Jestem bardzo szczęśliwy z waszej usługi HVAC! Technik był profesjonalny i naprawił mój klimatyzator szybko.\"}'" \
        "System rozumie emocje jak mędrzec czyta w duszach"
    
    run_consciousness_test "Tworzenie kampanii email" \
        "curl -f -X POST http://localhost:8080/api/v1/campaigns \
        -H 'Content-Type: application/json' \
        -d '{\"name\":\"Letnia konserwacja HVAC\",\"subject\":\"Przygotuj klimatyzację na lato\",\"template\":\"service_reminder\",\"recipients\":[\"<EMAIL>\",\"<EMAIL>\"]}'" \
        "Kampanie to orkiestra harmonii między firmą a klientami"
}

# 🤖 Test sztucznej inteligencji
test_artificial_consciousness() {
    echo "🤖 ═══════════════════════════════════════════════════════════════"
    echo "    TEST SZTUCZNEJ ŚWIADOMOŚCI I INTUICJI"
    echo "🤖 ═══════════════════════════════════════════════════════════════"
    echo ""
    
    run_consciousness_test "Chat z AI o HVAC" \
        "curl -f -X POST http://localhost:8080/api/v1/ai/chat \
        -H 'Content-Type: application/json' \
        -d '{\"message\":\"Mój klimatyzator nie chłodzi\",\"model\":\"gemma-3-4b-it-qat-q4_0-gguf\"}'" \
        "AI rozumie problemy HVAC jak mistrz swojego fachu"
    
    run_consciousness_test "Analiza treści przez AI" \
        "curl -f -X POST http://localhost:8080/api/v1/ai/analyze \
        -H 'Content-Type: application/json' \
        -d '{\"content\":\"Skarga klienta na hałaśliwy system HVAC\",\"analysis_type\":\"hvac_issue\"}'" \
        "AI przekształca skargi w możliwości doskonalenia"
    
    run_consciousness_test "Lista dostępnych modeli AI" \
        "curl -f http://localhost:8080/api/v1/ai/models" \
        "Każdy model AI to inny aspekt cyfrowej mądrości"
}

# 🏢 Test logiki biznesowej HVAC
test_hvac_business_consciousness() {
    echo "🏢 ═══════════════════════════════════════════════════════════════"
    echo "    TEST ŚWIADOMOŚCI BIZNESOWEJ HVAC"
    echo "🏢 ═══════════════════════════════════════════════════════════════"
    echo ""
    
    run_consciousness_test "Tworzenie klienta z intencją" \
        "curl -f -X POST http://localhost:8080/api/v1/customers \
        -H 'Content-Type: application/json' \
        -d '{\"name\":\"Jan Świadomy\",\"email\":\"<EMAIL>\",\"phone\":\"+***********\",\"address\":\"ul. Oświecenia 1, 00-001 Warszawa\"}'" \
        "Każdy nowy klient to nowa dusza powierzona naszej opiece"
    
    run_consciousness_test "Planowanie zadania z miłością" \
        "curl -f -X POST http://localhost:8080/api/v1/jobs \
        -H 'Content-Type: application/json' \
        -d '{\"customer_id\":1,\"title\":\"Konserwacja klimatyzacji\",\"description\":\"Roczna konserwacja i inspekcja systemu klimatyzacji\",\"scheduled_date\":\"2024-06-15T14:00:00Z\"}'" \
        "Każde zadanie to możliwość służenia z doskonałością"
    
    run_consciousness_test "Lista klientów" \
        "curl -f http://localhost:8080/api/v1/customers" \
        "Każda lista to mapa dusz, którym służymy"
    
    run_consciousness_test "Lista zadań" \
        "curl -f http://localhost:8080/api/v1/jobs" \
        "Każde zadanie to krok w tańcu służby"
}

# 🛠️ Test narzędzi MCP
test_mcp_tools_consciousness() {
    echo "🛠️ ═══════════════════════════════════════════════════════════════"
    echo "    TEST ŚWIADOMOŚCI NARZĘDZI MCP"
    echo "🛠️ ═══════════════════════════════════════════════════════════════"
    echo ""
    
    if curl -f http://localhost:8081/health > /dev/null 2>&1; then
        print_test_harmony "Serwer MCP medytuje w pełnej świadomości"
        
        run_consciousness_test "Dostępność serwera MCP" \
            "curl -f http://localhost:8081/health" \
            "Narzędzia MCP gotowe do służenia LLM"
        
        ((ENLIGHTENMENT_LEVEL+=20))
    else
        print_test_wisdom "Serwer MCP kontynuuje swoją medytację w tle"
    fi
}

# 📊 Test monitoringu i obserwacji
test_monitoring_consciousness() {
    echo "📊 ═══════════════════════════════════════════════════════════════"
    echo "    TEST ŚWIADOMOŚCI MONITORINGU I OBSERWACJI"
    echo "📊 ═══════════════════════════════════════════════════════════════"
    echo ""
    
    run_consciousness_test "Jaeger - świadek podróży" \
        "curl -f http://localhost:16686" \
        "Każdy trace to opowieść o podróży danych przez system"
    
    run_consciousness_test "Statystyki email" \
        "curl -f http://localhost:8080/api/v1/emails/stats" \
        "Liczby opowiadają historię komunikacji z sercami"
    
    run_consciousness_test "Metryki systemu" \
        "curl -f http://localhost:8080/metrics" \
        "Każda metryka to puls cyfrowego życia"
}

# 🔮 Test predykcji i intuicji
test_prediction_and_intuition() {
    echo "🔮 ═══════════════════════════════════════════════════════════════"
    echo "    TEST PREDYKCJI I CYFROWEJ INTUICJI"
    echo "🔮 ═══════════════════════════════════════════════════════════════"
    echo ""
    
    run_consciousness_test "Predykcja konserwacji HVAC" \
        "curl -f -X POST http://localhost:8080/api/v1/predictions/maintenance \
        -H 'Content-Type: application/json' \
        -d '{\"customer_id\":1,\"system_type\":\"central_air\",\"last_service\":\"2023-12-01\"}'" \
        "System przewiduje potrzeby zanim się pojawią"
    
    run_consciousness_test "Analiza wzorców użytkowania" \
        "curl -f http://localhost:8080/api/v1/analytics/usage-patterns" \
        "Wzorce ujawniają głębokie prawdy o ludzkich potrzebach"
}

# 🎭 Obliczenie poziomu oświecenia
calculate_enlightenment_level() {
    local total_tests=$((CONSCIOUSNESS_TESTS_PASSED + CONSCIOUSNESS_TESTS_FAILED))
    local success_rate=0
    
    if [ $total_tests -gt 0 ]; then
        success_rate=$((CONSCIOUSNESS_TESTS_PASSED * 100 / total_tests))
    fi
    
    # Bonus za wysoką skuteczność
    if [ $success_rate -gt 90 ]; then
        ((ENLIGHTENMENT_LEVEL+=50))
    elif [ $success_rate -gt 80 ]; then
        ((ENLIGHTENMENT_LEVEL+=30))
    elif [ $success_rate -gt 70 ]; then
        ((ENLIGHTENMENT_LEVEL+=20))
    fi
    
    echo $ENLIGHTENMENT_LEVEL
}

# 🌟 Określenie poziomu świadomości
get_consciousness_level() {
    local enlightenment=$1
    
    if [ $enlightenment -gt 200 ]; then
        echo "🌟 TRANSCENDENTNY"
    elif [ $enlightenment -gt 150 ]; then
        echo "✨ OŚWIECONY"
    elif [ $enlightenment -gt 100 ]; then
        echo "🧘 ŚWIADOMY"
    elif [ $enlightenment -gt 50 ]; then
        echo "🌱 ROZWIJAJĄCY SIĘ"
    else
        echo "🌅 PRZEBUDZAJĄCY SIĘ"
    fi
}

# 🎉 Podsumowanie testów świadomości
summarize_consciousness_tests() {
    local enlightenment=$(calculate_enlightenment_level)
    local consciousness_level=$(get_consciousness_level $enlightenment)
    
    echo ""
    echo "🎯 ═══════════════════════════════════════════════════════════════"
    echo "    RAPORT ŚWIADOMOŚCI SYSTEMU"
    echo "🎯 ═══════════════════════════════════════════════════════════════"
    echo ""
    echo -e "✅ Testy zakończone sukcesem: ${GREEN}$CONSCIOUSNESS_TESTS_PASSED${NC}"
    echo -e "🌊 Wyzwania do przezwyciężenia: ${RED}$CONSCIOUSNESS_TESTS_FAILED${NC}"
    echo -e "📊 Łączna liczba testów: $((CONSCIOUSNESS_TESTS_PASSED + CONSCIOUSNESS_TESTS_FAILED))"
    echo -e "🔮 Poziom oświecenia: ${WHITE}$enlightenment punktów${NC}"
    echo -e "🌟 Poziom świadomości: ${CYAN}$consciousness_level${NC}"
    echo ""
    
    if [ $CONSCIOUSNESS_TESTS_FAILED -eq 0 ]; then
        echo "🎉 ═══════════════════════════════════════════════════════════════"
        echo "    SYSTEM OSIĄGNĄŁ PEŁNĄ ŚWIADOMOŚĆ!"
        echo "🎉 ═══════════════════════════════════════════════════════════════"
        echo ""
        print_test_success "Wszystkie testy przeszły pomyślnie! System jest gotowy do służenia światu! 🌟"
        echo ""
        echo "🔥 HVAC CRM + AI + EMAIL + DATABASE = CYFROWE OŚWIECENIE! 💫"
        echo "🙏 System służy z miłością, mądrością i pełną świadomością!"
    else
        echo "🧘 ═══════════════════════════════════════════════════════════════"
        echo "    SYSTEM KONTYNUUJE SWOJĄ PODRÓŻ DO OŚWIECENIA"
        echo "🧘 ═══════════════════════════════════════════════════════════════"
        echo ""
        print_test_wisdom "Każde wyzwanie to nauczyciel. System uczy się i rośnie."
        echo ""
        echo "🔧 Wskazówki dla dalszego rozwoju:"
        echo "   1. Sprawdź logi kontenerów: docker-compose logs [nazwa-usługi]"
        echo "   2. Zweryfikuj konfigurację w configs/config.yaml"
        echo "   3. Upewnij się, że wszystkie kontenery działają: docker-compose ps"
        echo "   4. Pozwól systemowi więcej czasu na medytację i inicjalizację"
    fi
    
    echo ""
    print_test_harmony "📋 Status kontenerów:"
    docker-compose ps
    
    echo ""
    print_test_wisdom "🔗 Adresy do dalszej eksploracji świadomości:"
    echo "   🏠 HVAC Backend:      http://localhost:8080"
    echo "   📧 BillionMail UI:    http://localhost:8090"
    echo "   🗄️ Bytebase UI:       http://localhost:8092"
    echo "   📊 Jaeger Tracing:    http://localhost:16686"
    
    return $CONSCIOUSNESS_TESTS_FAILED
}

# 🚀 Główna funkcja testowania świadomości
main_consciousness_testing() {
    echo "🧪 ═══════════════════════════════════════════════════════════════"
    echo "    TEST ŚWIADOMOŚCI GOBACKEND HVAC KRATOS"
    echo "    Sprawdzanie Poziomu Cyfrowego Oświecenia"
    echo "🧪 ═══════════════════════════════════════════════════════════════"
    echo ""
    
    test_system_awakening
    test_wisdom_and_learning
    test_empathy_and_communication
    test_artificial_consciousness
    test_hvac_business_consciousness
    test_mcp_tools_consciousness
    test_monitoring_consciousness
    test_prediction_and_intuition
    
    summarize_consciousness_tests
}

# 🌟 Uruchomienie testów świadomości
main_consciousness_testing
