#!/bin/bash

# 🤖 AI Models Test Script

set -e

echo "🤖 Testing AI Models Integration..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[AI-TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test Ollama connection
print_status "Testing Ollama connection..."
if curl -s http://localhost:11434/api/tags > /dev/null; then
    print_success "Ollama is running! 🚀"
else
    print_error "Ollama is not running. Please start it first with: ./scripts/setup-gemma.sh"
    exit 1
fi

# Test Gemma model availability
print_status "Checking Gemma model availability..."
if curl -s http://localhost:11434/api/tags | grep -q "gemma:3b-instruct-q4_0"; then
    print_success "Gemma model is available! 🔥"
else
    print_warning "Gemma model not found. Installing..."
    docker exec hvac-ollama ollama pull gemma:3b-instruct-q4_0
fi

# Test HVAC Backend connection
print_status "Testing HVAC Backend AI endpoints..."
if curl -s http://localhost:8080/api/v1/ai/models > /dev/null; then
    print_success "HVAC Backend AI endpoints are accessible! 💪"
else
    print_warning "HVAC Backend not running. Starting..."
    docker-compose up -d hvac-backend
    sleep 10
fi

# Test AI Chat endpoint
print_status "Testing AI Chat functionality..."
CHAT_RESPONSE=$(curl -s -X POST http://localhost:8080/api/v1/ai/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the main components of an HVAC system?",
    "model": "gemma-3-4b-it-qat-q4_0-gguf"
  }')

if echo "$CHAT_RESPONSE" | grep -q "response"; then
    print_success "AI Chat test passed! 🎉"
    echo "Response preview: $(echo "$CHAT_RESPONSE" | jq -r '.response' | head -1)"
else
    print_error "AI Chat test failed"
    echo "Response: $CHAT_RESPONSE"
fi

# Test AI Analysis endpoint
print_status "Testing AI Analysis functionality..."
ANALYSIS_RESPONSE=$(curl -s -X POST http://localhost:8080/api/v1/ai/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "content": "My air conditioner is making strange noises and not cooling properly",
    "analysis_type": "hvac_issue",
    "model": "gemma-3-4b-it-qat-q4_0-gguf"
  }')

if echo "$ANALYSIS_RESPONSE" | grep -q "analysis"; then
    print_success "AI Analysis test passed! 🎯"
else
    print_error "AI Analysis test failed"
    echo "Response: $ANALYSIS_RESPONSE"
fi

print_success "AI Models testing completed! 🚀🤖"