#!/bin/bash

# 🐙 Morphic Octopus Interface Deployment Script
# Ultimate HVAC Backend Management System

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis for visual appeal
OCTOPUS="🐙"
ROCKET="🚀"
GEAR="⚙️"
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"

echo -e "${PURPLE}${OCTOPUS} MORPHIC OCTOPUS INTERFACE DEPLOYMENT ${OCTOPUS}${NC}"
echo -e "${CYAN}Ultimate HVAC Backend Management System${NC}"
echo ""

# Function to print colored output
print_status() {
    echo -e "${GREEN}${CHECK} $1${NC}"
}

print_error() {
    echo -e "${RED}${CROSS} $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
}

print_info() {
    echo -e "${BLUE}${INFO} $1${NC}"
}

# Check if Docker is running
check_docker() {
    print_info "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_status "Docker is running"
}

# Check if database is accessible
check_database() {
    print_info "Checking database connection..."
    
    # Try to connect to PostgreSQL
    if docker exec -it gobackend-kratos-postgres-1 pg_isready -U hvac_user -d hvac_db > /dev/null 2>&1; then
        print_status "Database is accessible"
    else
        print_warning "Database not accessible. Starting database services..."
        docker-compose up -d postgres redis
        sleep 10
        
        if docker exec -it gobackend-kratos-postgres-1 pg_isready -U hvac_user -d hvac_db > /dev/null 2>&1; then
            print_status "Database is now accessible"
        else
            print_error "Failed to connect to database"
            exit 1
        fi
    fi
}

# Run database migrations
run_migrations() {
    print_info "Running database migrations..."
    
    # Check if migration files exist
    if [ -f "migrations/003_transcription_intelligence.sql" ]; then
        print_info "Applying transcription intelligence migration..."
        docker exec -i gobackend-kratos-postgres-1 psql -U hvac_user -d hvac_db < migrations/003_transcription_intelligence.sql
        print_status "Transcription intelligence migration applied"
    else
        print_warning "Migration file not found, skipping..."
    fi
}

# Build Octopus Interface
build_octopus() {
    print_info "Building Morphic Octopus Interface..."
    
    # Build the Go binary
    cd cmd/octopus
    go mod tidy
    go build -o ../../bin/octopus-interface .
    cd ../..
    
    print_status "Octopus Interface built successfully"
}

# Create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    
    mkdir -p bin
    mkdir -p logs
    mkdir -p data/vectordb
    mkdir -p web/octopus/static
    
    print_status "Directories created"
}

# Start supporting services
start_services() {
    print_info "Starting supporting services..."
    
    # Start core services
    docker-compose up -d postgres redis
    sleep 5
    
    # Start BillionMail services
    docker-compose up -d billionmail-core billionmail-postfix billionmail-dovecot
    sleep 10
    
    # Start Bytebase
    docker-compose up -d bytebase
    sleep 5
    
    print_status "Supporting services started"
}

# Start Octopus Interface
start_octopus() {
    print_info "Starting Morphic Octopus Interface..."
    
    # Set environment variables
    export DATABASE_URL="postgres://hvac_user:hvac_password@localhost:5432/hvac_db?sslmode=disable"
    export REDIS_URL="redis://localhost:6379"
    export OLLAMA_URL="http://localhost:11434"
    
    # Start Octopus Interface in background
    nohup ./bin/octopus-interface -conf configs/octopus.yaml > logs/octopus.log 2>&1 &
    OCTOPUS_PID=$!
    
    # Save PID for later use
    echo $OCTOPUS_PID > octopus.pid
    
    # Wait a moment for startup
    sleep 3
    
    # Check if process is running
    if kill -0 $OCTOPUS_PID 2>/dev/null; then
        print_status "Octopus Interface started successfully (PID: $OCTOPUS_PID)"
    else
        print_error "Failed to start Octopus Interface"
        exit 1
    fi
}

# Health check
health_check() {
    print_info "Performing health check..."
    
    # Wait for service to be ready
    sleep 5
    
    # Check if Octopus Interface is responding
    if curl -s http://localhost:8083/api/dashboard/data > /dev/null; then
        print_status "Octopus Interface is responding"
    else
        print_warning "Octopus Interface not responding yet, checking logs..."
        tail -n 20 logs/octopus.log
    fi
    
    # Check database connection
    if curl -s http://localhost:8083/api/system/health > /dev/null; then
        print_status "System health check passed"
    else
        print_warning "System health check failed"
    fi
}

# Display access information
show_access_info() {
    echo ""
    echo -e "${PURPLE}${OCTOPUS} MORPHIC OCTOPUS INTERFACE DEPLOYED SUCCESSFULLY! ${OCTOPUS}${NC}"
    echo ""
    echo -e "${CYAN}Access Information:${NC}"
    echo -e "${GREEN}🌐 Dashboard:${NC}        http://localhost:8083/dashboard"
    echo -e "${GREEN}🔌 WebSocket:${NC}        ws://localhost:8083/api/dashboard/ws"
    echo -e "${GREEN}📊 API Endpoint:${NC}     http://localhost:8083/api"
    echo -e "${GREEN}🏥 Health Check:${NC}     http://localhost:8083/api/system/health"
    echo ""
    echo -e "${CYAN}Supporting Services:${NC}"
    echo -e "${GREEN}🗄️  Database:${NC}         http://localhost:5432 (PostgreSQL)"
    echo -e "${GREEN}📧 BillionMail:${NC}       http://localhost:8090 (Web UI)"
    echo -e "${GREEN}🔧 Bytebase:${NC}          http://localhost:8092 (DB Management)"
    echo -e "${GREEN}⚡ Redis:${NC}             localhost:6379"
    echo ""
    echo -e "${CYAN}Management Commands:${NC}"
    echo -e "${GREEN}📝 View Logs:${NC}         tail -f logs/octopus.log"
    echo -e "${GREEN}🛑 Stop Service:${NC}      kill \$(cat octopus.pid)"
    echo -e "${GREEN}🔄 Restart:${NC}           ./scripts/deploy-octopus.sh"
    echo ""
    echo -e "${YELLOW}${WARNING} Note: This is a development deployment. For production, use proper process management.${NC}"
}

# Main deployment process
main() {
    echo -e "${ROCKET} Starting deployment process..."
    echo ""
    
    check_docker
    create_directories
    check_database
    run_migrations
    start_services
    build_octopus
    start_octopus
    health_check
    show_access_info
    
    echo ""
    echo -e "${GREEN}${CHECK} Deployment completed successfully!${NC}"
    echo -e "${PURPLE}${OCTOPUS} Morphic Octopus Interface is ready to manage your HVAC backend! ${OCTOPUS}${NC}"
}

# Handle script interruption
cleanup() {
    echo ""
    print_warning "Deployment interrupted. Cleaning up..."
    
    if [ -f "octopus.pid" ]; then
        PID=$(cat octopus.pid)
        if kill -0 $PID 2>/dev/null; then
            kill $PID
            print_status "Octopus Interface stopped"
        fi
        rm -f octopus.pid
    fi
    
    exit 1
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Run main function
main "$@"