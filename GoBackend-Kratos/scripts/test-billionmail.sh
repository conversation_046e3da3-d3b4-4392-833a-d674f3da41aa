#!/bin/bash

# 🧪 BillionMail Integration Testing Script
# Tests all email functionality in GoBackend HVAC Kratos

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    print_status "Running: $test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_success "$test_name"
        ((TESTS_PASSED++))
    else
        print_error "$test_name"
        ((TESTS_FAILED++))
    fi
}

echo "🧪 =============================================="
echo "🚀 BillionMail Integration Testing Suite"
echo "🧪 =============================================="
echo ""

# Basic connectivity tests
print_status "🔍 Testing basic connectivity..."

run_test "HVAC Backend Health" "curl -f http://localhost:8080/health"
run_test "BillionMail Core Health" "curl -f http://localhost:8090/health"
run_test "PostgreSQL Connection" "docker-compose exec -T postgres pg_isready -U hvac_user -d hvac_db"
run_test "Redis Connection" "docker-compose exec -T redis redis-cli ping"

echo ""
print_status "📧 Testing email functionality..."

# Test email sending
run_test "Send Simple Email" "curl -f -X POST http://localhost:8080/api/v1/emails/send \
    -H 'Content-Type: application/json' \
    -d '{
        \"from\": \"<EMAIL>\",
        \"to\": [\"<EMAIL>\"],
        \"subject\": \"Test Email\",
        \"body\": \"This is a test email from HVAC CRM system.\"
    }'"

# Test email with HTML
run_test "Send HTML Email" "curl -f -X POST http://localhost:8080/api/v1/emails/send \
    -H 'Content-Type: application/json' \
    -d '{
        \"from\": \"<EMAIL>\",
        \"to\": [\"<EMAIL>\"],
        \"subject\": \"HTML Test Email\",
        \"body\": \"Plain text version\",
        \"html_body\": \"<h1>HTML Version</h1><p>This is an <strong>HTML</strong> email.</p>\"
    }'"

# Test HVAC service reminder email
run_test "Send Service Reminder" "curl -f -X POST http://localhost:8080/api/v1/emails/send \
    -H 'Content-Type: application/json' \
    -d '{
        \"from\": \"<EMAIL>\",
        \"to\": [\"<EMAIL>\"],
        \"subject\": \"HVAC Maintenance Reminder\",
        \"body\": \"Dear Customer, your HVAC system is due for maintenance. Please contact us to schedule an appointment.\"
    }'"

# Test email listing
run_test "List Emails" "curl -f http://localhost:8080/api/v1/emails"

echo ""
print_status "🤖 Testing AI integration with emails..."

# Test sentiment analysis
run_test "Email Sentiment Analysis" "curl -f -X POST http://localhost:8080/api/v1/emails/analyze-sentiment \
    -H 'Content-Type: application/json' \
    -d '{
        \"content\": \"I am very happy with your HVAC service! The technician was professional and fixed my AC quickly.\"
    }'"

echo ""
print_status "📊 Testing campaign functionality..."

# Test campaign creation
run_test "Create Email Campaign" "curl -f -X POST http://localhost:8080/api/v1/campaigns \
    -H 'Content-Type: application/json' \
    -d '{
        \"name\": \"Summer HVAC Maintenance\",
        \"subject\": \"Prepare Your AC for Summer\",
        \"template\": \"service_reminder\",
        \"recipients\": [\"<EMAIL>\", \"<EMAIL>\"],
        \"scheduled_at\": \"2024-06-01T10:00:00Z\"
    }'"

echo ""
print_status "🛠️ Testing MCP email tools..."

# Test MCP email tool (if MCP server is running)
if curl -f http://localhost:8081/health > /dev/null 2>&1; then
    print_status "MCP Server is running, testing email tools..."
    
    # Note: MCP tools testing would require specific MCP client
    # For now, we'll just check if the server is responding
    run_test "MCP Server Health" "curl -f http://localhost:8081/health"
else
    print_warning "MCP Server not accessible, skipping MCP tests"
fi

echo ""
print_status "🔍 Testing BillionMail specific features..."

# Test BillionMail API endpoints (if available)
run_test "BillionMail API Status" "curl -f http://localhost:8090/api/v1/status"

# Test webmail interface
run_test "Webmail Interface" "curl -f http://localhost:8090/roundcube"

echo ""
print_status "📈 Testing email statistics and monitoring..."

# Test email statistics
run_test "Email Statistics" "curl -f http://localhost:8080/api/v1/emails/stats"

echo ""
print_status "🧪 Testing HVAC-specific email workflows..."

# Test customer creation with email notification
run_test "Create Customer with Email" "curl -f -X POST http://localhost:8080/api/v1/customers \
    -H 'Content-Type: application/json' \
    -d '{
        \"name\": \"Test Customer\",
        \"email\": \"<EMAIL>\",
        \"phone\": \"+**********\",
        \"address\": \"123 Test St, Test City, TC 12345\",
        \"send_welcome_email\": true
    }'"

# Test job creation with email notification
run_test "Create Job with Email" "curl -f -X POST http://localhost:8080/api/v1/jobs \
    -H 'Content-Type: application/json' \
    -d '{
        \"customer_id\": 1,
        \"title\": \"AC Maintenance\",
        \"description\": \"Annual AC maintenance and inspection\",
        \"scheduled_date\": \"2024-06-15T14:00:00Z\",
        \"send_confirmation_email\": true
    }'"

echo ""
echo "🎯 =============================================="
echo "📊 Test Results Summary"
echo "🎯 =============================================="
echo ""
echo -e "✅ Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "❌ Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "📊 Total Tests:  $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    print_success "🎉 ALL TESTS PASSED! BillionMail integration is working perfectly! 🚀📧"
    echo ""
    echo "🔥 Ready for production email automation!"
    echo "📧 HVAC CRM + BillionMail = EMAIL POWERHOUSE! 💪"
else
    echo ""
    print_warning "⚠️  Some tests failed. Check the logs and configuration."
    echo ""
    echo "🔧 Troubleshooting tips:"
    echo "   1. Ensure all containers are running: docker-compose ps"
    echo "   2. Check container logs: docker-compose logs [service-name]"
    echo "   3. Verify BillionMail configuration in configs/config.yaml"
    echo "   4. Check database connectivity and initialization"
fi

echo ""
print_status "📋 Service Status:"
docker-compose ps

echo ""
print_status "🔗 Useful URLs:"
echo "   🏠 HVAC Backend:      http://localhost:8080"
echo "   📧 BillionMail UI:    http://localhost:8090"
echo "   📊 Jaeger Tracing:    http://localhost:16686"
echo "   📥 Webmail:          http://localhost:8090/roundcube"

exit $TESTS_FAILED
