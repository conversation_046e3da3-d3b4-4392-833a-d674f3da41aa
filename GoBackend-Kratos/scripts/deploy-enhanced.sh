#!/bin/bash

# 🚀 Enhanced GoBackend-Kratos Deployment Script
# Deploys the enhanced HVAC CRM with LangChain Go, Chromem-Go, and JSON-Iterator

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis for better UX
ROCKET="🚀"
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
GEAR="⚙️"
FIRE="🔥"
BRAIN="🧠"
DATABASE="🗄️"
LIGHTNING="⚡"

echo -e "${CYAN}${ROCKET} Enhanced GoBackend-Kratos Deployment${NC}"
echo -e "${BLUE}================================================${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}${CHECK} $1${NC}"
}

print_error() {
    echo -e "${RED}${CROSS} $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
}

print_info() {
    echo -e "${BLUE}${INFO} $1${NC}"
}

print_step() {
    echo -e "${PURPLE}${GEAR} $1${NC}"
}

# Check if Docker is running
check_docker() {
    print_step "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_status "Docker is running"
}

# Check if Ollama is available
check_ollama() {
    print_step "Checking Ollama availability..."
    if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        print_warning "Ollama is not running on localhost:11434"
        print_info "Starting Ollama in Docker..."
        docker run -d --name ollama -p 11434:11434 -v ollama:/root/.ollama ollama/ollama
        sleep 5
    fi
    print_status "Ollama is available"
}

# Pull required Ollama models
setup_ollama_models() {
    print_step "Setting up Ollama models..."
    
    print_info "Pulling Gemma 3B model..."
    docker exec ollama ollama pull gemma:3b-instruct-q4_0 || {
        print_warning "Failed to pull Gemma model, continuing..."
    }
    
    print_info "Pulling Nomic Embed Text model..."
    docker exec ollama ollama pull nomic-embed-text || {
        print_warning "Failed to pull embedding model, continuing..."
    }
    
    print_status "Ollama models setup completed"
}

# Build the enhanced application
build_application() {
    print_step "Building enhanced GoBackend-Kratos application..."
    
    # Update dependencies
    print_info "Updating Go dependencies..."
    go mod tidy
    go mod download
    
    # Build the application
    print_info "Building application binary..."
    go build -o bin/gobackend-hvac-kratos cmd/server/main.go
    
    print_status "Application built successfully"
}

# Build Docker image
build_docker_image() {
    print_step "Building enhanced Docker image..."
    
    docker build -t gobackend-hvac-kratos:enhanced .
    
    print_status "Docker image built successfully"
}

# Create enhanced docker-compose file
create_enhanced_compose() {
    print_step "Creating enhanced docker-compose configuration..."
    
    cat > docker-compose.enhanced.yml << 'EOF'
version: '3.8'

services:
  # Enhanced GoBackend-Kratos Application
  gobackend-enhanced:
    image: gobackend-hvac-kratos:enhanced
    container_name: gobackend-enhanced
    ports:
      - "8080:8080"
      - "9000:9000"
      - "8081:8081"
    environment:
      - DATABASE_URL=************************************************/hvac_db?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - OLLAMA_ENDPOINT=http://ollama:11434
      - VECTOR_DB_PATH=/data/vectordb
      - LANGCHAIN_ENABLED=true
      - JSON_ITERATOR_ENABLED=true
    volumes:
      - vectordb_data:/data/vectordb
      - ./configs:/app/configs
    depends_on:
      - postgres
      - redis
      - ollama
    networks:
      - hvac-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: postgres-enhanced
    environment:
      POSTGRES_DB: hvac_db
      POSTGRES_USER: hvac_user
      POSTGRES_PASSWORD: hvac_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - hvac-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: redis-enhanced
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hvac-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Ollama AI Service
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-enhanced
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - hvac-network
    restart: unless-stopped
    environment:
      - OLLAMA_KEEP_ALIVE=24h
      - OLLAMA_HOST=0.0.0.0

  # Jaeger Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: jaeger-enhanced
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - hvac-network
    restart: unless-stopped

  # Prometheus Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-enhanced
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - hvac-network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-enhanced
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - hvac-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  ollama_data:
  vectordb_data:
  prometheus_data:
  grafana_data:

networks:
  hvac-network:
    driver: bridge
EOF

    print_status "Enhanced docker-compose configuration created"
}

# Create monitoring configuration
setup_monitoring() {
    print_step "Setting up enhanced monitoring..."
    
    mkdir -p monitoring
    
    # Prometheus configuration
    cat > monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'gobackend-enhanced'
    static_configs:
      - targets: ['gobackend-enhanced:9090']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'ollama'
    static_configs:
      - targets: ['ollama:11434']
    metrics_path: '/metrics'
    scrape_interval: 60s
EOF

    # Grafana dashboard
    mkdir -p monitoring/grafana/dashboards
    cat > monitoring/grafana/dashboards/enhanced-ai.json << 'EOF'
{
  "dashboard": {
    "title": "Enhanced AI Metrics",
    "panels": [
      {
        "title": "LangChain Operations",
        "type": "stat",
        "targets": [
          {
            "expr": "langchain_operations_total"
          }
        ]
      },
      {
        "title": "Vector Search Performance",
        "type": "graph",
        "targets": [
          {
            "expr": "vector_search_duration_seconds"
          }
        ]
      },
      {
        "title": "JSON Processing Speed",
        "type": "graph",
        "targets": [
          {
            "expr": "json_processing_duration_seconds"
          }
        ]
      }
    ]
  }
}
EOF

    print_status "Monitoring configuration created"
}

# Deploy the enhanced stack
deploy_stack() {
    print_step "Deploying enhanced HVAC CRM stack..."
    
    # Stop existing containers
    print_info "Stopping existing containers..."
    docker-compose -f docker-compose.enhanced.yml down 2>/dev/null || true
    
    # Start the enhanced stack
    print_info "Starting enhanced stack..."
    docker-compose -f docker-compose.enhanced.yml up -d
    
    print_status "Enhanced stack deployed successfully"
}

# Wait for services to be ready
wait_for_services() {
    print_step "Waiting for services to be ready..."
    
    # Wait for database
    print_info "Waiting for PostgreSQL..."
    until docker exec postgres-enhanced pg_isready -U hvac_user -d hvac_db; do
        sleep 2
    done
    
    # Wait for Redis
    print_info "Waiting for Redis..."
    until docker exec redis-enhanced redis-cli ping; do
        sleep 2
    done
    
    # Wait for Ollama
    print_info "Waiting for Ollama..."
    until curl -s http://localhost:11434/api/tags > /dev/null; do
        sleep 5
    done
    
    # Wait for main application
    print_info "Waiting for GoBackend-Kratos..."
    until curl -s http://localhost:8080/health > /dev/null; do
        sleep 5
    done
    
    print_status "All services are ready!"
}

# Test enhanced features
test_enhanced_features() {
    print_step "Testing enhanced AI features..."
    
    # Test enhanced chat
    print_info "Testing enhanced chat..."
    curl -X POST http://localhost:8080/api/v1/ai/enhanced-chat \
        -H "Content-Type: application/json" \
        -d '{
            "message": "My HVAC system is not cooling properly",
            "use_vector_search": true,
            "enable_caching": true,
            "max_context_docs": 3
        }' > /dev/null && print_status "Enhanced chat working" || print_warning "Enhanced chat test failed"
    
    # Test vector search
    print_info "Testing semantic search..."
    curl -X POST http://localhost:8080/api/v1/ai/knowledge-search \
        -H "Content-Type: application/json" \
        -d '{
            "query": "air conditioning maintenance",
            "collections": ["hvac"],
            "top_k": 5
        }' > /dev/null && print_status "Semantic search working" || print_warning "Semantic search test failed"
    
    # Test metrics
    print_info "Testing enhanced metrics..."
    curl -s http://localhost:8080/api/v1/ai/metrics > /dev/null && \
        print_status "Enhanced metrics working" || print_warning "Enhanced metrics test failed"
    
    print_status "Enhanced features testing completed"
}

# Display deployment summary
show_summary() {
    echo ""
    echo -e "${CYAN}${FIRE} Enhanced Deployment Summary${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    echo -e "${GREEN}${CHECK} Services Deployed:${NC}"
    echo -e "  ${LIGHTNING} GoBackend-Kratos Enhanced: http://localhost:8080"
    echo -e "  ${DATABASE} Vector Database: Chromem-Go (embedded)"
    echo -e "  ${BRAIN} LangChain Go: Integrated"
    echo -e "  ${GEAR} JSON-Iterator: Enabled"
    echo -e "  ${INFO} Ollama AI: http://localhost:11434"
    echo -e "  ${INFO} Jaeger Tracing: http://localhost:16686"
    echo -e "  ${INFO} Prometheus: http://localhost:9090"
    echo -e "  ${INFO} Grafana: http://localhost:3000 (admin/admin)"
    echo ""
    echo -e "${GREEN}${CHECK} Enhanced Features:${NC}"
    echo -e "  ${LIGHTNING} 2-3x faster JSON processing"
    echo -e "  ${BRAIN} Advanced LLM chains and reasoning"
    echo -e "  ${DATABASE} Semantic vector search"
    echo -e "  ${GEAR} Intelligent caching and optimization"
    echo -e "  ${INFO} Comprehensive metrics and monitoring"
    echo ""
    echo -e "${YELLOW}${ROCKET} Ready for enhanced HVAC CRM operations!${NC}"
}

# Main deployment flow
main() {
    echo -e "${FIRE} Starting Enhanced Deployment Process...${NC}"
    echo ""
    
    check_docker
    check_ollama
    setup_ollama_models
    build_application
    build_docker_image
    create_enhanced_compose
    setup_monitoring
    deploy_stack
    wait_for_services
    test_enhanced_features
    show_summary
    
    echo ""
    echo -e "${GREEN}${ROCKET} Enhanced GoBackend-Kratos deployment completed successfully!${NC}"
}

# Run main function
main "$@"
