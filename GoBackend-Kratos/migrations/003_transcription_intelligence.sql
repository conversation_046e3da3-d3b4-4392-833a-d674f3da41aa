-- Migration: 003_transcription_intelligence.sql
-- Description: Advanced Transcription and Customer Intelligence System
-- Author: GoBackend HVAC Kratos - Morphic Octopus Interface
-- Date: 2024-12-19

-- Enable additional extensions for advanced functionality
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- Create transcription schema for call intelligence
CREATE SCHEMA IF NOT EXISTS transcription;

-- 📞 Call Transcription Sources Table
CREATE TABLE IF NOT EXISTS transcription.call_sources (
    id SERIAL PRIMARY KEY,
    source_name VARCHAR(100) NOT NULL UNIQUE,
    provider VARCHAR(50) NOT NULL, -- whisper, google, azure, rev, otter
    api_endpoint VARCHAR(255),
    api_key_encrypted TEXT,
    webhook_url VARCHAR(255),
    email_pattern VARCHAR(255), -- regex pattern for email detection
    phone_regex VARCHAR(255),
    confidence_threshold DECIMAL(3,2) DEFAULT 0.70,
    active B<PERSON><PERSON>EAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 📧 Transcription Emails Table (Raw email data)
CREATE TABLE IF NOT EXISTS transcription.transcription_emails (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_id INTEGER REFERENCES transcription.call_sources(id),
    email_message_id VARCHAR(255) UNIQUE,
    from_address VARCHAR(255) NOT NULL,
    to_address VARCHAR(255) NOT NULL,
    subject TEXT,
    body_text TEXT,
    body_html TEXT,
    headers JSONB,
    attachments JSONB, -- Array of attachment metadata
    received_at TIMESTAMP WITH TIME ZONE,
    processed_at TIMESTAMP WITH TIME ZONE,
    processing_status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, failed
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 🎤 Call Transcriptions Table (Parsed call data)
CREATE TABLE IF NOT EXISTS transcription.call_transcriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transcription_email_id UUID REFERENCES transcription.transcription_emails(id),
    customer_id BIGINT REFERENCES customers(id) ON DELETE SET NULL,
    
    -- Call Metadata
    phone_number VARCHAR(50),
    caller_id VARCHAR(100),
    caller_name VARCHAR(255),
    caller_email VARCHAR(255),
    caller_company VARCHAR(255),
    call_direction VARCHAR(20) CHECK (call_direction IN ('inbound', 'outbound')),
    call_duration INTERVAL,
    call_timestamp TIMESTAMP WITH TIME ZONE,
    call_status VARCHAR(50), -- completed, missed, voicemail, busy
    
    -- Transcription Data
    transcription_text TEXT NOT NULL,
    confidence_score DECIMAL(4,3),
    language_code VARCHAR(10) DEFAULT 'en',
    provider VARCHAR(50),
    provider_metadata JSONB,
    
    -- AI Analysis Results
    call_purpose VARCHAR(100), -- emergency, quote_request, follow_up, complaint, information
    urgency_level VARCHAR(20) CHECK (urgency_level IN ('critical', 'high', 'medium', 'low')),
    customer_mood VARCHAR(50), -- frustrated, satisfied, neutral, angry, confused
    satisfaction_level VARCHAR(50), -- very_satisfied, satisfied, neutral, dissatisfied, very_dissatisfied
    technical_issues TEXT[], -- Array of identified technical issues
    service_requests TEXT[], -- Array of service requests
    action_items TEXT[], -- Array of action items
    follow_up_needed BOOLEAN DEFAULT false,
    follow_up_date TIMESTAMP WITH TIME ZONE,
    
    -- HVAC Specific Analysis
    hvac_relevance BOOLEAN DEFAULT false,
    service_type VARCHAR(100), -- maintenance, repair, installation, emergency
    equipment_mentioned TEXT[], -- Array of equipment types
    estimated_value DECIMAL(10,2),
    business_priority INTEGER CHECK (business_priority >= 1 AND business_priority <= 10),
    
    -- Processing Metadata
    ai_analysis_version VARCHAR(20),
    ai_confidence DECIMAL(4,3),
    ai_processing_time INTERVAL,
    manual_review_needed BOOLEAN DEFAULT false,
    manual_review_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 🧠 Enhanced Customer Intelligence Tables

-- Customer Interaction History (Enhanced)
CREATE TABLE IF NOT EXISTS customer_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    
    -- Interaction Metadata
    interaction_type VARCHAR(50) NOT NULL, -- email, phone_call, service_visit, chat, sms
    direction VARCHAR(20) CHECK (direction IN ('inbound', 'outbound', 'internal')),
    channel VARCHAR(50), -- gmail, outlook, phone_system, website, mobile_app
    subject VARCHAR(500),
    summary TEXT,
    content TEXT,
    
    -- Timing Information
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    duration INTERVAL,
    response_time INTERVAL, -- Time to first response
    resolution_time INTERVAL, -- Time to resolution
    
    -- Classification and Analysis
    category VARCHAR(100), -- support, sales, billing, technical, emergency
    subcategory VARCHAR(100),
    priority_level VARCHAR(20) CHECK (priority_level IN ('critical', 'high', 'medium', 'low')),
    sentiment VARCHAR(20) CHECK (sentiment IN ('very_positive', 'positive', 'neutral', 'negative', 'very_negative')),
    emotion VARCHAR(50), -- happy, frustrated, angry, confused, satisfied, worried
    
    -- HVAC Specific Fields
    hvac_relevance BOOLEAN DEFAULT false,
    service_category VARCHAR(100), -- heating, cooling, ventilation, maintenance, installation
    equipment_type VARCHAR(100),
    issue_severity VARCHAR(20),
    weather_context JSONB, -- Weather conditions during interaction
    
    -- Business Intelligence
    lead_score INTEGER CHECK (lead_score >= 0 AND lead_score <= 100),
    conversion_potential VARCHAR(20), -- high, medium, low, none
    upsell_opportunities TEXT[],
    customer_satisfaction INTEGER CHECK (customer_satisfaction >= 1 AND customer_satisfaction <= 5),
    nps_score INTEGER CHECK (nps_score >= 0 AND nps_score <= 10),
    
    -- AI Analysis
    ai_tags TEXT[], -- AI-generated tags
    ai_insights JSONB, -- Structured AI insights
    ai_confidence DECIMAL(4,3),
    ai_processing_version VARCHAR(20),
    
    -- Staff and Resolution
    assigned_to VARCHAR(255),
    handled_by VARCHAR(255),
    escalated_to VARCHAR(255),
    resolution_status VARCHAR(50), -- open, in_progress, resolved, closed, escalated
    resolution_notes TEXT,
    
    -- References
    parent_interaction_id UUID REFERENCES customer_interactions(id),
    related_job_id BIGINT REFERENCES jobs(id),
    call_transcription_id UUID REFERENCES transcription.call_transcriptions(id),
    email_message_id INTEGER REFERENCES billionmail.email_messages(id),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Customer Analytics (Enhanced)
CREATE TABLE IF NOT EXISTS customer_analytics (
    customer_id BIGINT PRIMARY KEY REFERENCES customers(id) ON DELETE CASCADE,
    
    -- Interaction Metrics
    total_interactions INTEGER DEFAULT 0,
    email_count INTEGER DEFAULT 0,
    phone_call_count INTEGER DEFAULT 0,
    service_visit_count INTEGER DEFAULT 0,
    chat_count INTEGER DEFAULT 0,
    
    -- Timing Analytics
    avg_response_time INTERVAL,
    avg_resolution_time INTERVAL,
    first_contact_date TIMESTAMP WITH TIME ZONE,
    last_contact_date TIMESTAMP WITH TIME ZONE,
    contact_frequency_days DECIMAL(8,2), -- Average days between contacts
    
    -- Satisfaction Metrics
    avg_satisfaction DECIMAL(3,2),
    satisfaction_trend VARCHAR(20), -- improving, declining, stable
    nps_score DECIMAL(3,2),
    complaint_count INTEGER DEFAULT 0,
    compliment_count INTEGER DEFAULT 0,
    
    -- Business Metrics
    total_revenue DECIMAL(12,2) DEFAULT 0,
    avg_job_value DECIMAL(10,2) DEFAULT 0,
    lifetime_value DECIMAL(12,2) DEFAULT 0,
    predicted_ltv DECIMAL(12,2),
    churn_probability DECIMAL(4,3),
    churn_risk_level VARCHAR(20), -- low, medium, high, critical
    
    -- Behavioral Analytics
    preferred_contact_method VARCHAR(50),
    preferred_contact_time VARCHAR(50), -- morning, afternoon, evening
    communication_style VARCHAR(50), -- formal, casual, technical, simple
    technical_knowledge_level VARCHAR(50), -- expert, intermediate, basic, novice
    decision_making_speed VARCHAR(50), -- fast, moderate, slow, very_slow
    price_sensitivity VARCHAR(50), -- low, medium, high
    
    -- Service Analytics
    service_frequency_months DECIMAL(4,1),
    emergency_call_count INTEGER DEFAULT 0,
    preventive_maintenance_adoption BOOLEAN DEFAULT false,
    equipment_age_avg DECIMAL(4,1), -- Average age of customer's equipment
    energy_efficiency_interest BOOLEAN DEFAULT false,
    
    -- Predictive Analytics
    next_service_predicted TIMESTAMP WITH TIME ZONE,
    next_contact_predicted TIMESTAMP WITH TIME ZONE,
    upsell_probability DECIMAL(4,3),
    referral_likelihood DECIMAL(4,3),
    seasonal_patterns JSONB, -- Seasonal interaction patterns
    
    -- AI-Generated Insights
    ai_personality_profile JSONB,
    ai_preferences JSONB,
    ai_recommendations TEXT[],
    ai_risk_factors TEXT[],
    ai_opportunities TEXT[],
    
    -- Calculation Metadata
    last_calculated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    calculation_version VARCHAR(20),
    data_quality_score DECIMAL(4,3), -- Quality of underlying data
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 🎯 Customer Segments Table (AI-Driven Segmentation)
CREATE TABLE IF NOT EXISTS customer_segments (
    id SERIAL PRIMARY KEY,
    segment_name VARCHAR(100) NOT NULL UNIQUE,
    segment_description TEXT,
    segment_criteria JSONB NOT NULL, -- JSON criteria for segment membership
    ai_generated BOOLEAN DEFAULT false,
    active BOOLEAN DEFAULT true,
    color_code VARCHAR(7), -- Hex color for UI
    icon VARCHAR(50), -- Icon identifier for UI
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Customer Segment Membership (Many-to-Many)
CREATE TABLE IF NOT EXISTS customer_segment_memberships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    segment_id INTEGER NOT NULL REFERENCES customer_segments(id) ON DELETE CASCADE,
    membership_score DECIMAL(4,3), -- How well customer fits segment (0-1)
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_by VARCHAR(100), -- 'ai' or user identifier
    UNIQUE(customer_id, segment_id)
);

-- 📊 Business Intelligence Views and Materialized Views

-- Customer Intelligence Summary View
CREATE OR REPLACE VIEW customer_intelligence_summary AS
SELECT 
    c.id,
    c.name,
    c.email,
    c.primary_phone,
    c.company,
    c.customer_type,
    ca.total_interactions,
    ca.avg_satisfaction,
    ca.lifetime_value,
    ca.churn_probability,
    ca.churn_risk_level,
    ca.preferred_contact_method,
    ca.last_contact_date,
    ca.next_contact_predicted,
    COALESCE(recent_calls.call_count, 0) as recent_calls_30d,
    COALESCE(recent_emails.email_count, 0) as recent_emails_30d,
    array_agg(DISTINCT cs.segment_name) FILTER (WHERE cs.segment_name IS NOT NULL) as segments
FROM customers c
LEFT JOIN customer_analytics ca ON c.id = ca.customer_id
LEFT JOIN (
    SELECT customer_id, COUNT(*) as call_count
    FROM customer_interactions 
    WHERE interaction_type = 'phone_call' 
    AND timestamp >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY customer_id
) recent_calls ON c.id = recent_calls.customer_id
LEFT JOIN (
    SELECT customer_id, COUNT(*) as email_count
    FROM customer_interactions 
    WHERE interaction_type = 'email' 
    AND timestamp >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY customer_id
) recent_emails ON c.id = recent_emails.customer_id
LEFT JOIN customer_segment_memberships csm ON c.id = csm.customer_id
LEFT JOIN customer_segments cs ON csm.segment_id = cs.id
GROUP BY c.id, c.name, c.email, c.primary_phone, c.company, c.customer_type,
         ca.total_interactions, ca.avg_satisfaction, ca.lifetime_value, 
         ca.churn_probability, ca.churn_risk_level, ca.preferred_contact_method,
         ca.last_contact_date, ca.next_contact_predicted, 
         recent_calls.call_count, recent_emails.email_count;

-- Transcription Intelligence Summary View
CREATE OR REPLACE VIEW transcription_intelligence_summary AS
SELECT 
    ct.id,
    ct.phone_number,
    ct.caller_name,
    ct.caller_company,
    ct.call_timestamp,
    ct.call_duration,
    ct.call_purpose,
    ct.urgency_level,
    ct.customer_mood,
    ct.hvac_relevance,
    ct.service_type,
    ct.estimated_value,
    ct.business_priority,
    ct.follow_up_needed,
    ct.follow_up_date,
    c.name as customer_name,
    c.email as customer_email,
    c.customer_type,
    ca.churn_probability,
    ca.lifetime_value
FROM transcription.call_transcriptions ct
LEFT JOIN customers c ON ct.customer_id = c.id
LEFT JOIN customer_analytics ca ON c.id = ca.customer_id
WHERE ct.hvac_relevance = true
ORDER BY ct.call_timestamp DESC;

-- Create comprehensive indexes for performance
CREATE INDEX IF NOT EXISTS idx_transcription_emails_status ON transcription.transcription_emails(processing_status);
CREATE INDEX IF NOT EXISTS idx_transcription_emails_received ON transcription.transcription_emails(received_at);
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_customer ON transcription.call_transcriptions(customer_id);
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_phone ON transcription.call_transcriptions(phone_number);
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_timestamp ON transcription.call_transcriptions(call_timestamp);
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_urgency ON transcription.call_transcriptions(urgency_level);
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_hvac ON transcription.call_transcriptions(hvac_relevance);
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_purpose ON transcription.call_transcriptions(call_purpose);

CREATE INDEX IF NOT EXISTS idx_customer_interactions_customer ON customer_interactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_timestamp ON customer_interactions(timestamp);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_type ON customer_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_priority ON customer_interactions(priority_level);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_sentiment ON customer_interactions(sentiment);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_hvac ON customer_interactions(hvac_relevance);
CREATE INDEX IF NOT EXISTS idx_customer_interactions_resolution ON customer_interactions(resolution_status);

-- Full-text search indexes
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_text_search ON transcription.call_transcriptions 
    USING gin(to_tsvector('english', transcription_text));
CREATE INDEX IF NOT EXISTS idx_customer_interactions_content_search ON customer_interactions 
    USING gin(to_tsvector('english', coalesce(subject, '') || ' ' || coalesce(content, '')));

-- Trigram indexes for fuzzy matching
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_caller_name_trgm ON transcription.call_transcriptions 
    USING gin(caller_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_call_transcriptions_phone_trgm ON transcription.call_transcriptions 
    USING gin(phone_number gin_trgm_ops);

-- Create triggers for updated_at columns
CREATE TRIGGER update_transcription_emails_updated_at 
    BEFORE UPDATE ON transcription.transcription_emails 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_call_transcriptions_updated_at 
    BEFORE UPDATE ON transcription.call_transcriptions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_interactions_updated_at 
    BEFORE UPDATE ON customer_interactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_analytics_updated_at 
    BEFORE UPDATE ON customer_analytics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_segments_updated_at 
    BEFORE UPDATE ON customer_segments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default transcription sources
INSERT INTO transcription.call_sources (source_name, provider, email_pattern, phone_regex, confidence_threshold) VALUES
('Whisper AI', 'whisper', '.*whisper.*|.*openai.*', '(?i)phone[:\s]*([+]?[\d\s\-\(\)]+)', 0.85),
('Google Speech-to-Text', 'google', '.*google.*|.*speech.*|.*cloud.*', '(?i)caller[:\s]*([+]?[\d\s\-\(\)]+)', 0.80),
('Azure Speech Services', 'azure', '.*azure.*|.*microsoft.*|.*cognitive.*', '(?i)number[:\s]*([+]?[\d\s\-\(\)]+)', 0.75),
('Rev.ai', 'rev', '.*rev\.ai.*|.*rev.*', '(?i)phone[:\s]*([+]?[\d\s\-\(\)]+)', 0.90),
('Otter.ai', 'otter', '.*otter\.ai.*|.*otter.*', '(?i)caller[:\s]*([+]?[\d\s\-\(\)]+)', 0.85)
ON CONFLICT (source_name) DO NOTHING;

-- Insert default customer segments
INSERT INTO customer_segments (segment_name, segment_description, segment_criteria, ai_generated, color_code, icon) VALUES
('High Value Customers', 'Customers with high lifetime value and low churn risk', 
 '{"lifetime_value": {"min": 5000}, "churn_probability": {"max": 0.3}}', false, '#2E7D32', 'star'),
('At Risk Customers', 'Customers with high churn probability requiring attention', 
 '{"churn_probability": {"min": 0.7}}', false, '#D32F2F', 'warning'),
('New Customers', 'Recently acquired customers in first 90 days', 
 '{"days_since_first_contact": {"max": 90}}', false, '#1976D2', 'new_releases'),
('Emergency Service Users', 'Customers who frequently use emergency services', 
 '{"emergency_call_count": {"min": 3}}', false, '#FF5722', 'emergency'),
('Maintenance Subscribers', 'Customers with preventive maintenance contracts', 
 '{"preventive_maintenance_adoption": true}', false, '#388E3C', 'build'),
('Commercial Clients', 'Business customers with multiple locations', 
 '{"customer_type": "commercial"}', false, '#7B1FA2', 'business'),
('Residential Premium', 'High-value residential customers', 
 '{"customer_type": "residential", "lifetime_value": {"min": 2000}}', false, '#F57C00', 'home'),
('Tech Savvy', 'Customers with high technical knowledge', 
 '{"technical_knowledge_level": "expert"}', true, '#00796B', 'computer')
ON CONFLICT (segment_name) DO NOTHING;

-- Grant permissions
GRANT USAGE ON SCHEMA transcription TO hvac_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA transcription TO hvac_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA transcription TO hvac_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON customer_interactions TO hvac_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON customer_analytics TO hvac_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON customer_segments TO hvac_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON customer_segment_memberships TO hvac_user;
GRANT SELECT ON customer_intelligence_summary TO hvac_user;
GRANT SELECT ON transcription_intelligence_summary TO hvac_user;

-- Add comments for documentation
COMMENT ON SCHEMA transcription IS 'Advanced call transcription and intelligence system';
COMMENT ON TABLE transcription.call_sources IS 'Configuration for different transcription service providers';
COMMENT ON TABLE transcription.transcription_emails IS 'Raw email data from transcription services';
COMMENT ON TABLE transcription.call_transcriptions IS 'Parsed and analyzed call transcription data with AI insights';
COMMENT ON TABLE customer_interactions IS 'Comprehensive customer interaction history with AI analysis';
COMMENT ON TABLE customer_analytics IS 'Advanced customer analytics and behavioral insights';
COMMENT ON TABLE customer_segments IS 'AI-driven customer segmentation definitions';
COMMENT ON TABLE customer_segment_memberships IS 'Customer assignments to segments with scoring';