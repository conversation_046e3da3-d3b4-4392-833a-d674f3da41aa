-- Rollback: 001_rollback_initial_hvac_schema.sql
-- Description: Rollback initial HVAC CRM schema
-- Author: GoBackend HVAC Kratos
-- Date: 2024-12-19

-- Drop triggers
DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
DROP TRIGGER IF EXISTS update_jobs_updated_at ON jobs;

-- Drop trigger function
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop indexes
DROP INDEX IF EXISTS idx_customers_email;
DROP INDEX IF EXISTS idx_customers_created_at;
DROP INDEX IF EXISTS idx_jobs_customer_id;
DROP INDEX IF EXISTS idx_jobs_status;
DROP INDEX IF EXISTS idx_jobs_scheduled_date;
DROP INDEX IF EXISTS idx_jobs_created_at;

-- Drop tables (in reverse order due to foreign key constraints)
DROP TABLE IF EXISTS jobs;
DROP TABLE IF EXISTS customers;

-- Note: We don't drop the UUID extension as it might be used by other parts of the system
