-- Migration: 001_initial_hvac_schema.sql
-- Description: Initial HVAC CRM schema with customers and jobs
-- Author: GoBackend HVAC Kratos
-- Date: 2024-12-19

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create jobs table
CREATE TABLE IF NOT EXISTS jobs (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    scheduled_date TIMESTAMP WITH TIME ZONE,
    completed_date TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER, -- in minutes
    actual_duration INTEGER, -- in minutes
    estimated_cost DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    technician_notes TEXT,
    customer_feedback TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_customers_created_at ON customers(created_at);
CREATE INDEX IF NOT EXISTS idx_jobs_customer_id ON jobs(customer_id);
CREATE INDEX IF NOT EXISTS idx_jobs_status ON jobs(status);
CREATE INDEX IF NOT EXISTS idx_jobs_scheduled_date ON jobs(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs(created_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_customers_updated_at 
    BEFORE UPDATE ON customers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_jobs_updated_at 
    BEFORE UPDATE ON jobs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing
INSERT INTO customers (name, email, phone, address) VALUES
('John Smith', '<EMAIL>', '******-0101', '123 Main St, Anytown, ST 12345'),
('Jane Doe', '<EMAIL>', '******-0102', '456 Oak Ave, Somewhere, ST 67890'),
('Bob Johnson', '<EMAIL>', '******-0103', '789 Pine Rd, Elsewhere, ST 11111')
ON CONFLICT (email) DO NOTHING;

-- Insert sample jobs
INSERT INTO jobs (customer_id, title, description, status, priority, scheduled_date, estimated_duration, estimated_cost) VALUES
(1, 'AC Maintenance', 'Annual air conditioning maintenance and filter replacement', 'scheduled', 'medium', CURRENT_TIMESTAMP + INTERVAL '1 day', 120, 150.00),
(1, 'Heating System Inspection', 'Pre-winter heating system inspection and tune-up', 'pending', 'high', CURRENT_TIMESTAMP + INTERVAL '3 days', 90, 120.00),
(2, 'Duct Cleaning', 'Complete HVAC duct system cleaning and sanitization', 'scheduled', 'medium', CURRENT_TIMESTAMP + INTERVAL '2 days', 180, 300.00),
(3, 'Emergency Repair', 'AC unit not cooling - emergency service call', 'in_progress', 'urgent', CURRENT_TIMESTAMP - INTERVAL '2 hours', 60, 200.00)
ON CONFLICT DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE customers IS 'HVAC customers with contact information and service history';
COMMENT ON TABLE jobs IS 'HVAC service jobs and maintenance tasks';

COMMENT ON COLUMN customers.id IS 'Unique customer identifier';
COMMENT ON COLUMN customers.name IS 'Customer full name';
COMMENT ON COLUMN customers.email IS 'Customer email address (unique)';
COMMENT ON COLUMN customers.phone IS 'Customer phone number';
COMMENT ON COLUMN customers.address IS 'Customer service address';

COMMENT ON COLUMN jobs.id IS 'Unique job identifier';
COMMENT ON COLUMN jobs.customer_id IS 'Reference to customer who owns this job';
COMMENT ON COLUMN jobs.title IS 'Brief job title or summary';
COMMENT ON COLUMN jobs.description IS 'Detailed job description';
COMMENT ON COLUMN jobs.status IS 'Job status: pending, scheduled, in_progress, completed, cancelled';
COMMENT ON COLUMN jobs.priority IS 'Job priority: low, medium, high, urgent';
COMMENT ON COLUMN jobs.scheduled_date IS 'When the job is scheduled to be performed';
COMMENT ON COLUMN jobs.completed_date IS 'When the job was actually completed';
COMMENT ON COLUMN jobs.estimated_duration IS 'Estimated job duration in minutes';
COMMENT ON COLUMN jobs.actual_duration IS 'Actual job duration in minutes';
COMMENT ON COLUMN jobs.estimated_cost IS 'Estimated job cost in dollars';
COMMENT ON COLUMN jobs.actual_cost IS 'Actual job cost in dollars';
COMMENT ON COLUMN jobs.technician_notes IS 'Notes from the technician performing the job';
COMMENT ON COLUMN jobs.customer_feedback IS 'Feedback from the customer after job completion';
COMMENT ON COLUMN jobs.rating IS 'Customer rating for the job (1-5 stars)';

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON customers TO hvac_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON jobs TO hvac_user;
GRANT USAGE, SELECT ON SEQUENCE customers_id_seq TO hvac_user;
GRANT USAGE, SELECT ON SEQUENCE jobs_id_seq TO hvac_user;
