-- ⚡ Workflow Automation - Database Schema
-- GoBackend-<PERSON>ratos HVAC CRM System

-- ============================================================================
-- ⚡ WORKFLOW TABLES
-- ============================================================================

-- 1. ⚡ Workflow Rules
CREATE TABLE IF NOT EXISTS workflow_rules (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL,
    description TEXT,
    trigger_type VARCHAR(50) NOT NULL,
    trigger_conditions JSONB NOT NULL,
    actions JSONB NOT NULL,
    priority INTEGER DEFAULT 5,
    is_active BOOLEAN DEFAULT true,
    execution_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    last_executed TIMESTAMP,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for workflow_rules
CREATE INDEX IF NOT EXISTS idx_workflow_rules_trigger_type ON workflow_rules(trigger_type);
CREATE INDEX IF NOT EXISTS idx_workflow_rules_active ON workflow_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_workflow_rules_priority ON workflow_rules(priority DESC);

-- 2. ⚡ Workflow Executions
CREATE TABLE IF NOT EXISTS workflow_executions (
    id SERIAL PRIMARY KEY,
    workflow_rule_id INTEGER NOT NULL REFERENCES workflow_rules(id),
    trigger_entity_id INTEGER,
    trigger_entity_type VARCHAR(50),
    execution_status VARCHAR(20) DEFAULT 'pending',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    execution_time_ms INTEGER,
    actions_executed JSONB,
    results JSONB,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for workflow_executions
CREATE INDEX IF NOT EXISTS idx_workflow_executions_rule_id ON workflow_executions(workflow_rule_id);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON workflow_executions(execution_status);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_started_at ON workflow_executions(started_at);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_entity ON workflow_executions(trigger_entity_type, trigger_entity_id);

-- 3. ⚡ Workflow Templates
CREATE TABLE IF NOT EXISTS workflow_templates (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT,
    template JSONB NOT NULL,
    is_public BOOLEAN DEFAULT false,
    usage_count INTEGER DEFAULT 0,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for workflow_templates
CREATE INDEX IF NOT EXISTS idx_workflow_templates_category ON workflow_templates(category);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_public ON workflow_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_usage ON workflow_templates(usage_count DESC);

-- ============================================================================
-- ⚡ WORKFLOW VIEWS
-- ============================================================================

-- Workflow Performance View
CREATE OR REPLACE VIEW workflow_performance_summary AS
SELECT 
    wr.id,
    wr.rule_name,
    wr.trigger_type,
    wr.execution_count,
    wr.success_count,
    CASE 
        WHEN wr.execution_count > 0 THEN 
            ROUND((wr.success_count::DECIMAL / wr.execution_count) * 100, 2)
        ELSE 0 
    END as success_rate,
    wr.last_executed,
    COUNT(we.id) as recent_executions,
    AVG(we.execution_time_ms) as avg_execution_time_ms
FROM workflow_rules wr
LEFT JOIN workflow_executions we ON wr.id = we.workflow_rule_id 
    AND we.started_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY wr.id, wr.rule_name, wr.trigger_type, wr.execution_count, wr.success_count, wr.last_executed
ORDER BY wr.priority DESC, wr.success_count DESC;

-- Recent Workflow Executions View
CREATE OR REPLACE VIEW recent_workflow_executions AS
SELECT 
    we.id,
    we.workflow_rule_id,
    wr.rule_name,
    wr.trigger_type,
    we.trigger_entity_type,
    we.trigger_entity_id,
    we.execution_status,
    we.started_at,
    we.completed_at,
    we.execution_time_ms,
    we.error_message
FROM workflow_executions we
JOIN workflow_rules wr ON we.workflow_rule_id = wr.id
WHERE we.started_at >= CURRENT_DATE - INTERVAL '24 hours'
ORDER BY we.started_at DESC;

-- ============================================================================
-- ⚡ WORKFLOW FUNCTIONS
-- ============================================================================

-- Function to update workflow rule statistics
CREATE OR REPLACE FUNCTION update_workflow_rule_stats(
    rule_id_param INTEGER,
    success_param BOOLEAN
)
RETURNS VOID AS $$
BEGIN
    UPDATE workflow_rules 
    SET 
        execution_count = execution_count + 1,
        success_count = CASE WHEN success_param THEN success_count + 1 ELSE success_count END,
        last_executed = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = rule_id_param;
END;
$$ LANGUAGE plpgsql;

-- Function to clean old workflow executions
CREATE OR REPLACE FUNCTION cleanup_old_workflow_executions(days_to_keep INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM workflow_executions 
    WHERE started_at < CURRENT_DATE - INTERVAL '1 day' * days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log cleanup
    INSERT INTO business_metrics (metric_name, metric_category, metric_value, metric_unit, time_period, period_start, period_end)
    VALUES ('Workflow Cleanup', 'system', deleted_count, 'count', 'maintenance', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- ⚡ SAMPLE WORKFLOW TEMPLATES
-- ============================================================================

-- Emergency Response Template
INSERT INTO workflow_templates (template_name, category, description, template, is_public, created_by) VALUES
('Emergency HVAC Response', 'hvac', 'Immediate response workflow for HVAC emergencies', '{
  "trigger_type": "email",
  "conditions": [
    {
      "field": "subject",
      "operator": "contains",
      "value": "emergency",
      "logic_op": "OR"
    },
    {
      "field": "urgency_score",
      "operator": "greater_than",
      "value": 0.8
    }
  ],
  "actions": [
    {
      "type": "escalation",
      "parameters": {
        "level": "urgent",
        "target": "emergency_team"
      }
    },
    {
      "type": "email",
      "parameters": {
        "to": "<EMAIL>",
        "subject": "URGENT: Emergency HVAC Issue - {{.subject}}",
        "body": "Emergency detected. Customer: {{.customer_name}}. Issue: {{.description}}. Immediate attention required."
      }
    },
    {
      "type": "notification",
      "parameters": {
        "message": "Emergency HVAC issue escalated for {{.customer_name}}",
        "recipients": ["<EMAIL>", "<EMAIL>"]
      }
    }
  ]
}', true, 'system')
ON CONFLICT DO NOTHING;

-- Customer Follow-up Template
INSERT INTO workflow_templates (template_name, category, description, template, is_public, created_by) VALUES
('Customer Service Follow-up', 'customer', 'Automated follow-up after service completion', '{
  "trigger_type": "job",
  "conditions": [
    {
      "field": "status",
      "operator": "equals",
      "value": "completed"
    }
  ],
  "actions": [
    {
      "type": "email",
      "delay": "24h",
      "parameters": {
        "to": "{{.customer_email}}",
        "subject": "How was your HVAC service experience?",
        "body": "Dear {{.customer_name}}, we hope you are satisfied with our recent service. Please take a moment to rate your experience and let us know if you need anything else."
      }
    },
    {
      "type": "create_job",
      "delay": "30d",
      "parameters": {
        "job_type": "maintenance_reminder",
        "customer_id": "{{.customer_id}}",
        "description": "Scheduled maintenance reminder for {{.equipment_type}}"
      }
    }
  ]
}', true, 'system')
ON CONFLICT DO NOTHING;

-- Quote Request Template
INSERT INTO workflow_templates (template_name, category, description, template, is_public, created_by) VALUES
('Quote Request Processing', 'sales', 'Automated processing of quote requests', '{
  "trigger_type": "email",
  "conditions": [
    {
      "field": "detected_intent",
      "operator": "contains",
      "value": "quote"
    }
  ],
  "actions": [
    {
      "type": "assignment",
      "parameters": {
        "assignee": "sales_team",
        "entity_type": "lead"
      }
    },
    {
      "type": "email",
      "parameters": {
        "to": "{{.customer_email}}",
        "subject": "Thank you for your HVAC quote request",
        "body": "Dear {{.customer_name}}, we have received your quote request. Our sales team will contact you within 24 hours with a detailed estimate."
      }
    },
    {
      "type": "notification",
      "parameters": {
        "message": "New quote request from {{.customer_name}} - {{.service_type}}",
        "recipients": ["<EMAIL>"]
      }
    }
  ]
}', true, 'system')
ON CONFLICT DO NOTHING;

-- Maintenance Reminder Template
INSERT INTO workflow_templates (template_name, category, description, template, is_public, created_by) VALUES
('Scheduled Maintenance Reminder', 'maintenance', 'Automated maintenance reminders for customers', '{
  "trigger_type": "schedule",
  "schedule": {
    "type": "monthly",
    "day": 1,
    "time": "09:00"
  },
  "conditions": [
    {
      "field": "last_maintenance_date",
      "operator": "less_than",
      "value": "90_days_ago"
    }
  ],
  "actions": [
    {
      "type": "email",
      "parameters": {
        "to": "{{.customer_email}}",
        "subject": "Time for your HVAC maintenance check",
        "body": "Dear {{.customer_name}}, it has been {{.days_since_maintenance}} days since your last maintenance. Schedule your next service to keep your HVAC system running efficiently."
      }
    },
    {
      "type": "create_job",
      "parameters": {
        "job_type": "maintenance",
        "customer_id": "{{.customer_id}}",
        "priority": "normal",
        "description": "Scheduled maintenance for {{.equipment_type}}"
      }
    }
  ]
}', true, 'system')
ON CONFLICT DO NOTHING;

-- ============================================================================
-- ⚡ SAMPLE WORKFLOW RULES
-- ============================================================================

-- Emergency Escalation Rule
INSERT INTO workflow_rules (rule_name, description, trigger_type, trigger_conditions, actions, priority, is_active, created_by) VALUES
('Emergency HVAC Escalation', 'Escalate emergency HVAC issues immediately', 'email', '[
  {
    "field": "subject",
    "operator": "contains",
    "value": "emergency",
    "logic_op": "OR"
  },
  {
    "field": "urgency_score",
    "operator": "greater_than",
    "value": 0.8
  }
]', '[
  {
    "type": "escalation",
    "parameters": {
      "level": "urgent",
      "target": "emergency_team"
    }
  },
  {
    "type": "email",
    "parameters": {
      "to": "<EMAIL>",
      "subject": "URGENT: Emergency HVAC Issue",
      "body": "Emergency detected. Immediate attention required."
    }
  }
]', 10, true, 'system')
ON CONFLICT DO NOTHING;

-- High Priority Customer Rule
INSERT INTO workflow_rules (rule_name, description, trigger_type, trigger_conditions, actions, priority, is_active, created_by) VALUES
('High Priority Customer Service', 'Special handling for VIP customers', 'email', '[
  {
    "field": "customer_tier",
    "operator": "in",
    "value": ["platinum", "gold"]
  }
]', '[
  {
    "type": "assignment",
    "parameters": {
      "assignee": "senior_technician",
      "priority": "high"
    }
  },
  {
    "type": "notification",
    "parameters": {
      "message": "VIP customer request received",
      "recipients": ["<EMAIL>"]
    }
  }
]', 8, true, 'system')
ON CONFLICT DO NOTHING;

-- ============================================================================
-- ⚡ SAMPLE DATA FOR TESTING
-- ============================================================================

-- Sample workflow executions
INSERT INTO workflow_executions (workflow_rule_id, trigger_entity_id, trigger_entity_type, execution_status, execution_time_ms, results) 
SELECT 
    wr.id,
    123,
    'email',
    'completed',
    150,
    '{"actions_executed": 2, "success": true}'
FROM workflow_rules wr 
WHERE wr.rule_name = 'Emergency HVAC Escalation'
LIMIT 1;

-- Add comments
COMMENT ON TABLE workflow_rules IS '⚡ Workflow automation rules with conditions and actions';
COMMENT ON TABLE workflow_executions IS '⚡ Workflow execution history and results';
COMMENT ON TABLE workflow_templates IS '⚡ Reusable workflow templates for common scenarios';

SELECT '⚡ Workflow Automation schema created successfully!' as status;