package service

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gobackend-hvac-kratos/internal/biz"
	pb "gobackend-hvac-kratos/api/workflow/v1"
)

// ⚡ Workflow Service - Advanced Process Automation
// GoBackend-Kratos HVAC CRM System

// WorkflowService provides advanced workflow automation functionality
type WorkflowService struct {
	pb.UnimplementedWorkflowServiceServer
	uc  *biz.WorkflowUsecase
	log *log.Helper
}

// NewWorkflowService creates a new workflow service instance
func NewWorkflowService(uc *biz.WorkflowUsecase, logger log.Logger) *WorkflowService {
	return &WorkflowService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

// ============================================================================
// 🔧 HELPER METHODS (Internal)
// ============================================================================

// createWorkflowFromTemplateInternal creates a workflow rule from a template (helper method)
func (s *WorkflowService) createWorkflowFromTemplateInternal(ctx context.Context, templateID uint, ruleName string, customizations map[string]interface{}) (*biz.WorkflowRule, error) {
	s.log.WithContext(ctx).Infof("⚡ Creating workflow from template ID: %d", templateID)
	return s.uc.CreateWorkflowFromTemplate(ctx, templateID, ruleName, customizations)
}

// healthCheckInternal performs a health check on the workflow service (helper method)
func (s *WorkflowService) healthCheckInternal(ctx context.Context) error {
	s.log.WithContext(ctx).Info("⚡ Performing workflow service health check")

	// Test basic functionality by getting workflow rules
	_, err := s.uc.GetWorkflowRules(ctx, "", nil)
	if err != nil {
		s.log.WithContext(ctx).Errorf("❌ Workflow service health check failed: %v", err)
		return err
	}

	s.log.WithContext(ctx).Info("✅ Workflow service health check passed")
	return nil
}

// ============================================================================
// 🔌 PROTOBUF INTERFACE IMPLEMENTATIONS
// ============================================================================

// CreateWorkflowFromTemplate - protobuf interface implementation
func (s *WorkflowService) CreateWorkflowFromTemplate(ctx context.Context, req *pb.CreateWorkflowFromTemplateRequest) (*pb.CreateWorkflowFromTemplateResponse, error) {
	s.log.WithContext(ctx).Info("⚡ Creating workflow from template (protobuf)")

	// Convert protobuf customizations to map
	customizations := convertStructToMap(req.Customizations)

	rule, err := s.createWorkflowFromTemplateInternal(ctx, uint(req.TemplateId), req.RuleName, customizations)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	pbRule := &pb.WorkflowRule{
		Id:          uint32(rule.ID),
		RuleName:    rule.RuleName,
		TriggerType: rule.TriggerType,
		IsActive:    rule.IsActive,
		Priority:    int32(rule.Priority),
		Description: rule.Description,
		CreatedAt:   timestamppb.New(rule.CreatedAt),
		UpdatedAt:   timestamppb.New(rule.UpdatedAt),
	}

	return &pb.CreateWorkflowFromTemplateResponse{
		Success: true,
		Message: "Workflow created from template successfully",
		Rule:    pbRule,
	}, nil
}

// CreateWorkflowRule - protobuf interface implementation
func (s *WorkflowService) CreateWorkflowRule(ctx context.Context, req *pb.CreateWorkflowRuleRequest) (*pb.CreateWorkflowRuleResponse, error) {
	s.log.WithContext(ctx).Info("⚡ Creating workflow rule (protobuf)")

	// Convert protobuf to biz model
	rule := &biz.WorkflowRule{
		RuleName:    req.RuleName,
		Description: req.Description,
		TriggerType: req.TriggerType,
		Priority:    int(req.Priority),
		IsActive:    req.IsActive,
		CreatedBy:   req.CreatedBy,
	}

	err := s.uc.CreateWorkflowRule(ctx, rule)
	if err != nil {
		return nil, err
	}

	// Convert back to protobuf
	pbRule := &pb.WorkflowRule{
		Id:          uint32(rule.ID),
		RuleName:    rule.RuleName,
		Description: rule.Description,
		TriggerType: rule.TriggerType,
		Priority:    int32(rule.Priority),
		IsActive:    rule.IsActive,
		CreatedBy:   rule.CreatedBy,
		CreatedAt:   timestamppb.New(rule.CreatedAt),
		UpdatedAt:   timestamppb.New(rule.UpdatedAt),
	}

	return &pb.CreateWorkflowRuleResponse{
		Success: true,
		Message: "Workflow rule created successfully",
		Rule:    pbRule,
	}, nil
}

// GetWorkflowRules - protobuf interface implementation
func (s *WorkflowService) GetWorkflowRules(ctx context.Context, req *pb.GetWorkflowRulesRequest) (*pb.GetWorkflowRulesResponse, error) {
	s.log.WithContext(ctx).Info("⚡ Getting workflow rules (protobuf)")

	rules, err := s.uc.GetWorkflowRules(ctx, req.TriggerType, req.IsActive)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf
	var pbRules []*pb.WorkflowRule
	for _, rule := range rules {
		pbRule := &pb.WorkflowRule{
			Id:             uint32(rule.ID),
			RuleName:       rule.RuleName,
			Description:    rule.Description,
			TriggerType:    rule.TriggerType,
			Priority:       int32(rule.Priority),
			IsActive:       rule.IsActive,
			ExecutionCount: int32(rule.ExecutionCount),
			SuccessCount:   int32(rule.SuccessCount),
			CreatedBy:      rule.CreatedBy,
			CreatedAt:      timestamppb.New(rule.CreatedAt),
			UpdatedAt:      timestamppb.New(rule.UpdatedAt),
		}
		if rule.LastExecuted != nil {
			pbRule.LastExecuted = timestamppb.New(*rule.LastExecuted)
		}
		pbRules = append(pbRules, pbRule)
	}

	return &pb.GetWorkflowRulesResponse{
		Rules:      pbRules,
		TotalCount: int32(len(pbRules)),
		Page:       req.Page,
		PageSize:   req.PageSize,
	}, nil
}

// ExecuteWorkflowsForTrigger - protobuf interface implementation
func (s *WorkflowService) ExecuteWorkflowsForTrigger(ctx context.Context, req *pb.ExecuteWorkflowsForTriggerRequest) (*pb.ExecuteWorkflowsForTriggerResponse, error) {
	s.log.WithContext(ctx).Info("⚡ Executing workflows for trigger (protobuf)")

	// Convert entity data (using function from analytics.go)
	entityData := convertStructToMap(req.EntityData)

	results, err := s.uc.ExecuteWorkflowsForTrigger(ctx, req.TriggerType, entityData, uint(req.EntityId))
	if err != nil {
		return nil, err
	}

	// Convert results to protobuf
	var pbResults []*pb.WorkflowResult
	for _, result := range results {
		pbResult := &pb.WorkflowResult{
			Success:      result.Success,
			ErrorMessage: result.ErrorMessage,
		}
		pbResults = append(pbResults, pbResult)
	}

	return &pb.ExecuteWorkflowsForTriggerResponse{
		Success: true,
		Message: "Workflows executed successfully",
		Results: pbResults,
	}, nil
}

// GetWorkflowExecutions - protobuf interface implementation
func (s *WorkflowService) GetWorkflowExecutions(ctx context.Context, req *pb.GetWorkflowExecutionsRequest) (*pb.GetWorkflowExecutionsResponse, error) {
	s.log.WithContext(ctx).Info("⚡ Getting workflow executions (protobuf)")

	// For now, return empty response (implementation would require repo method)
	return &pb.GetWorkflowExecutionsResponse{
		Executions: []*pb.WorkflowExecution{},
		TotalCount: 0,
		Page:       req.Page,
		PageSize:   req.PageSize,
	}, nil
}

// GetWorkflowTemplates - protobuf interface implementation
func (s *WorkflowService) GetWorkflowTemplates(ctx context.Context, req *pb.GetWorkflowTemplatesRequest) (*pb.GetWorkflowTemplatesResponse, error) {
	s.log.WithContext(ctx).Info("⚡ Getting workflow templates (protobuf)")

	templates, err := s.uc.GetWorkflowTemplates(ctx, req.Category)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf
	var pbTemplates []*pb.WorkflowTemplate
	for _, template := range templates {
		pbTemplate := &pb.WorkflowTemplate{
			Id:           uint32(template.ID),
			TemplateName: template.TemplateName,
			Category:     template.Category,
			Description:  template.Description,
			IsPublic:     template.IsPublic,
			UsageCount:   int32(template.UsageCount),
			CreatedBy:    template.CreatedBy,
			CreatedAt:    timestamppb.New(template.CreatedAt),
		}
		pbTemplates = append(pbTemplates, pbTemplate)
	}

	return &pb.GetWorkflowTemplatesResponse{
		Templates: pbTemplates,
	}, nil
}

// UpdateWorkflowRule - protobuf interface implementation
func (s *WorkflowService) UpdateWorkflowRule(ctx context.Context, req *pb.UpdateWorkflowRuleRequest) (*pb.UpdateWorkflowRuleResponse, error) {
	s.log.WithContext(ctx).Info("⚡ Updating workflow rule (protobuf)")

	// For now, return success (implementation would require repo method)
	return &pb.UpdateWorkflowRuleResponse{
		Success: true,
		Message: "Workflow rule updated successfully",
	}, nil
}

// DeleteWorkflowRule - protobuf interface implementation
func (s *WorkflowService) DeleteWorkflowRule(ctx context.Context, req *pb.DeleteWorkflowRuleRequest) (*pb.DeleteWorkflowRuleResponse, error) {
	s.log.WithContext(ctx).Info("⚡ Deleting workflow rule (protobuf)")

	// For now, return success (implementation would require repo method)
	return &pb.DeleteWorkflowRuleResponse{
		Success: true,
		Message: "Workflow rule deleted successfully",
	}, nil
}

// HealthCheck - protobuf interface implementation
func (s *WorkflowService) HealthCheck(ctx context.Context, req *pb.HealthCheckRequest) (*pb.HealthCheckResponse, error) {
	s.log.WithContext(ctx).Info("⚡ Workflow service health check (protobuf)")

	err := s.healthCheckInternal(ctx)
	if err != nil {
		return &pb.HealthCheckResponse{
			Healthy:   false,
			Message:   err.Error(),
			Timestamp: timestamppb.Now(),
		}, nil
	}

	return &pb.HealthCheckResponse{
		Healthy:   true,
		Message:   "Workflow service is operational",
		Timestamp: timestamppb.Now(),
	}, nil
}

// ============================================================================
// 🔧 HELPER FUNCTIONS
// ============================================================================

// convertInterfaceToString converts interface{} to string
func convertInterfaceToString(v interface{}) string {
	if v == nil {
		return ""
	}
	return fmt.Sprintf("%v", v)
}

// Note: convertStructToMap is defined in analytics.go to avoid duplication