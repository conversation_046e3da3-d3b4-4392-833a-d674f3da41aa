package service

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gobackend-hvac-kratos/internal/biz"
	pb "gobackend-hvac-kratos/api/analytics/v1"
)

// 📊 Analytics Service - Advanced Dashboard Analytics
// GoBackend-Kratos HVAC CRM System

// AnalyticsService provides advanced analytics and dashboard functionality
type AnalyticsService struct {
	pb.UnimplementedAnalyticsServiceServer
	uc  *biz.AnalyticsUsecase
	log *log.Helper
}

// NewAnalyticsService creates a new analytics service instance
func NewAnalyticsService(uc *biz.AnalyticsUsecase, logger log.Logger) *AnalyticsService {
	return &AnalyticsService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

// ============================================================================
// 📊 SERVICE METHODS (Non-protobuf helpers)
// ============================================================================

// CalculateCustomerAnalytics calculates and updates customer analytics
func (s *AnalyticsService) CalculateCustomerAnalytics(ctx context.Context, customerID uint) error {
	s.log.WithContext(ctx).Infof("📊 Calculating customer analytics for ID: %d", customerID)
	return s.uc.CalculateCustomerAnalytics(ctx, customerID)
}

// UpdateRevenueAnalytics updates revenue analytics
func (s *AnalyticsService) UpdateRevenueAnalytics(ctx context.Context, date time.Time, category string, revenue float64, jobsCount int) error {
	s.log.WithContext(ctx).Infof("📊 Updating revenue analytics: %.2f for %s", revenue, date.Format("2006-01-02"))
	return s.uc.UpdateRevenueAnalytics(ctx, date, category, revenue, jobsCount)
}

// UpdateOperationalAnalytics updates operational analytics
func (s *AnalyticsService) UpdateOperationalAnalytics(ctx context.Context, date time.Time, data *biz.OperationalAnalytics) error {
	s.log.WithContext(ctx).Infof("📊 Updating operational analytics for %s", date.Format("2006-01-02"))
	return s.uc.UpdateOperationalAnalytics(ctx, date, data)
}

// healthCheckInternal performs internal health check (helper method)
func (s *AnalyticsService) healthCheckInternal(ctx context.Context) error {
	s.log.WithContext(ctx).Info("📊 Performing analytics service health check")

	// Test basic functionality by getting real-time metrics
	_, err := s.uc.GetRealTimeMetrics(ctx)
	if err != nil {
		s.log.WithContext(ctx).Errorf("❌ Analytics service health check failed: %v", err)
		return err
	}

	s.log.WithContext(ctx).Info("✅ Analytics service health check passed")
	return nil
}

// ============================================================================
// 🔌 PROTOBUF INTERFACE IMPLEMENTATIONS
// ============================================================================

// GetExecutiveDashboard - protobuf interface implementation
func (s *AnalyticsService) GetExecutiveDashboard(ctx context.Context, req *pb.GetExecutiveDashboardRequest) (*pb.GetExecutiveDashboardResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting executive dashboard (protobuf)")

	data, err := s.uc.GetExecutiveDashboard(ctx)
	if err != nil {
		return nil, err
	}

	// Convert biz model to protobuf
	pbData := &pb.ExecutiveDashboardSummary{
		Period:            data.Period,
		TodayRevenue:      data.TodayRevenue,
		TodayJobs:         int32(data.TodayJobs),
		TodaySatisfaction: data.TodaySatisfaction,
		TodayEfficiency:   data.TodayEfficiency,
		WeekRevenue:       data.WeekRevenue,
		WeekJobs:          int32(data.WeekJobs),
		MonthRevenue:      data.MonthRevenue,
		MonthJobs:         int32(data.MonthJobs),
	}

	return &pb.GetExecutiveDashboardResponse{
		DashboardType: "executive",
		LastUpdated:   timestamppb.Now(),
		Data:          pbData,
		Widgets:       []*pb.DashboardWidget{}, // TODO: Convert widgets
	}, nil
}

// GetCustomerInsightsDashboard - protobuf interface implementation
func (s *AnalyticsService) GetCustomerInsightsDashboard(ctx context.Context, req *pb.GetCustomerInsightsDashboardRequest) (*pb.GetCustomerInsightsDashboardResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting customer insights dashboard (protobuf)")

	insights, err := s.uc.GetCustomerInsights(ctx)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	var pbData []*pb.CustomerInsightsDashboard
	for _, insight := range insights {
		pbData = append(pbData, &pb.CustomerInsightsDashboard{
			LoyaltyTier:      insight.LoyaltyTier,
			CustomerCount:    int32(insight.CustomerCount),
			AvgLifetimeValue: insight.AvgLifetimeValue,
			AvgSatisfaction:  insight.AvgSatisfaction,
			AvgChurnRisk:     insight.AvgChurnRisk,
			TierTotalRevenue: insight.TierTotalRevenue,
		})
	}

	return &pb.GetCustomerInsightsDashboardResponse{
		DashboardType: "customer_insights",
		LastUpdated:   timestamppb.Now(),
		Data:          pbData,
		Widgets:       []*pb.DashboardWidget{}, // TODO: Convert widgets
	}, nil
}

// GetOperationalDashboard - protobuf interface implementation
func (s *AnalyticsService) GetOperationalDashboard(ctx context.Context, req *pb.GetOperationalDashboardRequest) (*pb.GetOperationalDashboardResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting operational dashboard (protobuf)")

	data, err := s.uc.GetOperationalDashboard(ctx)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	pbData := &pb.OperationalAnalytics{
		Id:                         uint32(data.ID),
		AnalysisDate:              timestamppb.New(data.AnalysisDate),
		TotalActiveJobs:           int32(data.TotalActiveJobs),
		CompletedJobs:             int32(data.CompletedJobs),
		CancelledJobs:             int32(data.CancelledJobs),
		EmergencyJobs:             int32(data.EmergencyJobs),
		AverageResponseTimeMs:     convertDurationToMs(data.AverageResponseTime),
		AverageCompletionTimeMs:   convertDurationToMs(data.AverageCompletionTime),
		TechnicianEfficiency:      convertFloatPtr(data.TechnicianEfficiency),
		EquipmentUtilization:      convertFloatPtr(data.EquipmentUtilization),
		CustomerSatisfaction:      convertFloatPtr(data.CustomerSatisfaction),
		FirstTimeFixRate:          convertFloatPtr(data.FirstTimeFixRate),
		CallbackRate:              convertFloatPtr(data.CallbackRate),
		PartsAvailability:         convertFloatPtr(data.PartsAvailability),
		FuelCosts:                 data.FuelCosts,
		OvertimeHours:             data.OvertimeHours,
	}

	return &pb.GetOperationalDashboardResponse{
		DashboardType: "operational",
		LastUpdated:   timestamppb.Now(),
		Data:          pbData,
		Widgets:       []*pb.DashboardWidget{}, // TODO: Convert widgets
	}, nil
}

// GetPerformanceTrends - protobuf interface implementation
func (s *AnalyticsService) GetPerformanceTrends(ctx context.Context, req *pb.GetPerformanceTrendsRequest) (*pb.GetPerformanceTrendsResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting performance trends (protobuf)")

	trends, err := s.uc.GetPerformanceTrends(ctx, int(req.Weeks))
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	var pbTrends []*pb.PerformanceTrendsDashboard
	for _, trend := range trends {
		pbTrends = append(pbTrends, &pb.PerformanceTrendsDashboard{
			WeekStart:         timestamppb.New(trend.WeekStart),
			AvgEfficiency:     trend.AvgEfficiency,
			AvgSatisfaction:   trend.AvgSatisfaction,
			AvgFirstTimeFix:   trend.AvgFirstTimeFix,
			TotalJobs:         int32(trend.TotalJobs),
			AvgResponseHours:  trend.AvgResponseHours,
		})
	}

	return &pb.GetPerformanceTrendsResponse{
		Trends: pbTrends,
	}, nil
}

// GetKPIs - protobuf interface implementation
func (s *AnalyticsService) GetKPIs(ctx context.Context, req *pb.GetKPIsRequest) (*pb.GetKPIsResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting KPIs (protobuf)")

	kpis, err := s.uc.GetKPIs(ctx, req.Category)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	var pbKPIs []*pb.KPITracking
	for _, kpi := range kpis {
		pbKPI := &pb.KPITracking{
			Id:                  uint32(kpi.ID),
			KpiName:             kpi.KPIName,
			KpiCategory:         kpi.KPICategory,
			CurrentValue:        kpi.CurrentValue,
			TrendDirection:      kpi.TrendDirection,
			MeasurementDate:     timestamppb.New(kpi.MeasurementDate),
			MeasurementTime:     timestamppb.New(kpi.MeasurementTime),
			IsAlertTriggered:    kpi.IsAlertTriggered,
			Notes:               kpi.Notes,
		}
		if kpi.TargetValue != nil {
			pbKPI.TargetValue = kpi.TargetValue
		}
		if kpi.PreviousValue != nil {
			pbKPI.PreviousValue = kpi.PreviousValue
		}
		if kpi.TrendPercentage != nil {
			pbKPI.TrendPercentage = kpi.TrendPercentage
		}
		if kpi.AlertThresholdMin != nil {
			pbKPI.AlertThresholdMin = kpi.AlertThresholdMin
		}
		if kpi.AlertThresholdMax != nil {
			pbKPI.AlertThresholdMax = kpi.AlertThresholdMax
		}
		pbKPIs = append(pbKPIs, pbKPI)
	}

	return &pb.GetKPIsResponse{
		Kpis: pbKPIs,
	}, nil
}

// UpdateKPI - protobuf interface implementation
func (s *AnalyticsService) UpdateKPI(ctx context.Context, req *pb.UpdateKPIRequest) (*pb.UpdateKPIResponse, error) {
	s.log.WithContext(ctx).Info("📊 Updating KPI (protobuf)")

	var target *float64
	if req.Target != nil {
		target = req.Target
	}

	err := s.uc.UpdateKPI(ctx, req.KpiName, req.Category, req.Value, target)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateKPIResponse{
		Success: true,
		Message: "KPI updated successfully",
	}, nil
}

// GetRealTimeMetrics - protobuf interface implementation
func (s *AnalyticsService) GetRealTimeMetrics(ctx context.Context, req *pb.GetRealTimeMetricsRequest) (*pb.GetRealTimeMetricsResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting real-time metrics (protobuf)")

	metrics, err := s.uc.GetRealTimeMetrics(ctx)
	if err != nil {
		return nil, err
	}

	// Convert map to protobuf struct
	pbMetrics, err := convertMapToStruct(metrics)
	if err != nil {
		return nil, err
	}

	return &pb.GetRealTimeMetricsResponse{
		Metrics: pbMetrics,
	}, nil
}

// GetDashboardWidgets - protobuf interface implementation
func (s *AnalyticsService) GetDashboardWidgets(ctx context.Context, req *pb.GetDashboardWidgetsRequest) (*pb.GetDashboardWidgetsResponse, error) {
	s.log.WithContext(ctx).Info("📊 Getting dashboard widgets (protobuf)")

	widgets, err := s.uc.GetDashboardWidgets(ctx, req.DashboardCategory)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf format
	var pbWidgets []*pb.DashboardWidget
	for _, widget := range widgets {
		config, _ := convertMapToStruct(widget.WidgetConfig)
		pbWidgets = append(pbWidgets, &pb.DashboardWidget{
			Id:                uint32(widget.ID),
			WidgetName:        widget.WidgetName,
			WidgetType:        widget.WidgetType,
			DashboardCategory: widget.DashboardCategory,
			DataSource:        widget.DataSource,
			RefreshInterval:   int32(widget.RefreshInterval),
			WidgetConfig:      config,
			IsActive:          widget.IsActive,
			CreatedAt:         timestamppb.New(widget.CreatedAt),
			UpdatedAt:         timestamppb.New(widget.UpdatedAt),
		})
	}

	return &pb.GetDashboardWidgetsResponse{
		Widgets: pbWidgets,
	}, nil
}

// CreateDashboardWidget - protobuf interface implementation
func (s *AnalyticsService) CreateDashboardWidget(ctx context.Context, req *pb.CreateDashboardWidgetRequest) (*pb.CreateDashboardWidgetResponse, error) {
	s.log.WithContext(ctx).Info("📊 Creating dashboard widget (protobuf)")

	config := convertStructToMap(req.WidgetConfig)
	widget := &biz.DashboardWidget{
		WidgetName:        req.WidgetName,
		WidgetType:        req.WidgetType,
		DashboardCategory: req.DashboardCategory,
		DataSource:        req.DataSource,
		RefreshInterval:   int(req.RefreshInterval),
		WidgetConfig:      config,
		IsActive:          req.IsActive,
	}

	err := s.uc.CreateDashboardWidget(ctx, widget)
	if err != nil {
		return nil, err
	}

	pbConfig, _ := convertMapToStruct(widget.WidgetConfig)
	return &pb.CreateDashboardWidgetResponse{
		Success: true,
		Message: "Dashboard widget created successfully",
		Widget: &pb.DashboardWidget{
			Id:                uint32(widget.ID),
			WidgetName:        widget.WidgetName,
			WidgetType:        widget.WidgetType,
			DashboardCategory: widget.DashboardCategory,
			DataSource:        widget.DataSource,
			RefreshInterval:   int32(widget.RefreshInterval),
			WidgetConfig:      pbConfig,
			IsActive:          widget.IsActive,
			CreatedAt:         timestamppb.New(widget.CreatedAt),
			UpdatedAt:         timestamppb.New(widget.UpdatedAt),
		},
	}, nil
}

// HealthCheck - protobuf interface implementation
func (s *AnalyticsService) HealthCheck(ctx context.Context, req *pb.HealthCheckRequest) (*pb.HealthCheckResponse, error) {
	s.log.WithContext(ctx).Info("📊 Analytics service health check (protobuf)")

	err := s.healthCheckInternal(ctx)
	if err != nil {
		return &pb.HealthCheckResponse{
			Healthy:   false,
			Message:   err.Error(),
			Timestamp: timestamppb.Now(),
		}, nil
	}

	return &pb.HealthCheckResponse{
		Healthy:   true,
		Message:   "Analytics service is operational",
		Timestamp: timestamppb.Now(),
	}, nil
}

// ============================================================================
// 🔧 HELPER FUNCTIONS
// ============================================================================

// convertDurationToFloat converts *time.Duration to float64 (in hours)
func convertDurationToFloat(d *time.Duration) float64 {
	if d == nil {
		return 0.0
	}
	return d.Hours()
}

// convertFloatPtr converts *float64 to float64
func convertFloatPtr(f *float64) float64 {
	if f == nil {
		return 0.0
	}
	return *f
}

// convertDurationToMs converts *time.Duration to int64 (in milliseconds)
func convertDurationToMs(d *time.Duration) int64 {
	if d == nil {
		return 0
	}
	return d.Milliseconds()
}

// convertMapToStruct converts map[string]interface{} to *structpb.Struct
func convertMapToStruct(m map[string]interface{}) (*structpb.Struct, error) {
	if m == nil {
		return nil, nil
	}
	return structpb.NewStruct(m)
}

// convertStructToMap converts *structpb.Struct to map[string]interface{}
func convertStructToMap(s *structpb.Struct) map[string]interface{} {
	if s == nil {
		return nil
	}
	return s.AsMap()
}