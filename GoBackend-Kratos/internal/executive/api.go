package executive

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/mux"

	"gobackend-hvac-kratos/internal/data"
)

// 🌐 ExecutiveAPI - HTTP API for Executive AI Assistant
type ExecutiveAPI struct {
	log     *log.Helper
	service *Service
}

// 📊 API Response structures
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Meta    *APIMeta    `json:"meta,omitempty"`
}

type APIMeta struct {
	ProcessingTime string `json:"processing_time"`
	Timestamp      string `json:"timestamp"`
	Version        string `json:"version"`
}

// 📧 Email processing request/response
type ProcessEmailRequest struct {
	EmailID           int64                  `json:"email_id"`
	ForceReprocessing bool                   `json:"force_reprocessing"`
	SkipWorkflows     bool                   `json:"skip_workflows"`
	Context           map[string]interface{} `json:"context"`
}

type ProcessEmailResponse struct {
	EmailID         int64                      `json:"email_id"`
	ProcessingTime  string                     `json:"processing_time"`
	TriageResult    *TriageResult             `json:"triage_result"`
	DraftResult     *DraftResult              `json:"draft_result"`
	WorkflowResults []*WorkflowExecutionResult `json:"workflow_results"`
	MemoryUpdates   []*MemoryUpdate           `json:"memory_updates"`
	CalendarEvents  []*CalendarEventResult    `json:"calendar_events"`
	Success         bool                      `json:"success"`
	ErrorMessage    string                    `json:"error_message,omitempty"`
}

// 📝 Draft management requests
type ApproveDraftRequest struct {
	DraftID    int64  `json:"draft_id"`
	ApprovedBy string `json:"approved_by"`
}

type RejectDraftRequest struct {
	DraftID      int64  `json:"draft_id"`
	RejectedBy   string `json:"rejected_by"`
	RejectReason string `json:"reject_reason"`
}

// 🔍 Search requests
type SearchMemoryRequest struct {
	Query      string `json:"query"`
	EntityType string `json:"entity_type,omitempty"`
	Limit      int    `json:"limit,omitempty"`
}

type TriageHistoryRequest struct {
	Limit   int                    `json:"limit,omitempty"`
	Filters map[string]interface{} `json:"filters,omitempty"`
}

// NewExecutiveAPI creates a new Executive AI API handler
func NewExecutiveAPI(service *Service, logger log.Logger) *ExecutiveAPI {
	return &ExecutiveAPI{
		log:     log.NewHelper(logger),
		service: service,
	}
}

// 🚀 RegisterRoutes registers all API routes
func (api *ExecutiveAPI) RegisterRoutes(router *mux.Router) {
	// Email processing endpoints
	router.HandleFunc("/api/v1/executive/process-email", api.handleProcessEmail).Methods("POST")
	router.HandleFunc("/api/v1/executive/triage-history", api.handleGetTriageHistory).Methods("GET")

	// Draft management endpoints
	router.HandleFunc("/api/v1/executive/drafts", api.handleGetDrafts).Methods("GET")
	router.HandleFunc("/api/v1/executive/drafts/{id}/approve", api.handleApproveDraft).Methods("POST")
	router.HandleFunc("/api/v1/executive/drafts/{id}/reject", api.handleRejectDraft).Methods("POST")

	// Memory bank endpoints
	router.HandleFunc("/api/v1/executive/memory/search", api.handleSearchMemory).Methods("POST")
	router.HandleFunc("/api/v1/executive/memory/insights", api.handleGetMemoryInsights).Methods("GET")

	// Analytics endpoints
	router.HandleFunc("/api/v1/executive/metrics", api.handleGetMetrics).Methods("GET")
	router.HandleFunc("/api/v1/executive/performance", api.handleGetPerformance).Methods("GET")

	// Workflow endpoints
	router.HandleFunc("/api/v1/executive/workflows/execute", api.handleExecuteWorkflow).Methods("POST")
	router.HandleFunc("/api/v1/executive/workflows/rules", api.handleGetWorkflowRules).Methods("GET")

	// Calendar endpoints
	router.HandleFunc("/api/v1/executive/calendar/schedule", api.handleScheduleMeeting).Methods("POST")
	router.HandleFunc("/api/v1/executive/calendar/events", api.handleGetCalendarEvents).Methods("GET")

	// Health check
	router.HandleFunc("/api/v1/executive/health", api.handleHealthCheck).Methods("GET")
}

// ==========================================
// EMAIL PROCESSING ENDPOINTS
// ==========================================

// 📧 Process incoming email
func (api *ExecutiveAPI) handleProcessEmail(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	var req ProcessEmailRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate request
	if req.EmailID <= 0 {
		api.writeErrorResponse(w, "Invalid email ID", http.StatusBadRequest)
		return
	}

	// Process email
	serviceReq := &EmailProcessingRequest{
		EmailID:           req.EmailID,
		ForceReprocessing: req.ForceReprocessing,
		SkipWorkflows:     req.SkipWorkflows,
		Context:           req.Context,
	}

	result, err := api.service.ProcessIncomingEmail(ctx, serviceReq)
	if err != nil {
		api.log.WithContext(ctx).Errorf("Email processing failed: %v", err)
		api.writeErrorResponse(w, "Email processing failed", http.StatusInternalServerError)
		return
	}

	// Convert to API response
	response := &ProcessEmailResponse{
		EmailID:         result.EmailID,
		ProcessingTime:  result.ProcessingTime.String(),
		TriageResult:    result.TriageResult,
		DraftResult:     result.DraftResult,
		WorkflowResults: result.WorkflowResults,
		MemoryUpdates:   result.MemoryUpdates,
		CalendarEvents:  result.CalendarEvents,
		Success:         result.Success,
		ErrorMessage:    result.ErrorMessage,
	}

	api.writeSuccessResponse(w, response, startTime)
}

// 📊 Get triage history
func (api *ExecutiveAPI) handleGetTriageHistory(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	// Parse query parameters
	limitStr := r.URL.Query().Get("limit")
	limit := 50 // default
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	// Parse filters
	filters := make(map[string]interface{})
	if category := r.URL.Query().Get("category"); category != "" {
		filters["email_category"] = category
	}
	if priority := r.URL.Query().Get("priority"); priority != "" {
		filters["priority_level"] = priority
	}
	if action := r.URL.Query().Get("action"); action != "" {
		filters["triage_action"] = action
	}
	if fromDate := r.URL.Query().Get("from_date"); fromDate != "" {
		if date, err := time.Parse("2006-01-02", fromDate); err == nil {
			filters["from_date"] = date
		}
	}
	if toDate := r.URL.Query().Get("to_date"); toDate != "" {
		if date, err := time.Parse("2006-01-02", toDate); err == nil {
			filters["to_date"] = date
		}
	}

	// Get triage history
	triages, err := api.service.GetTriageHistory(ctx, limit, filters)
	if err != nil {
		api.log.WithContext(ctx).Errorf("Failed to get triage history: %v", err)
		api.writeErrorResponse(w, "Failed to retrieve triage history", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, triages, startTime)
}

// ==========================================
// DRAFT MANAGEMENT ENDPOINTS
// ==========================================

// 📝 Get response drafts
func (api *ExecutiveAPI) handleGetDrafts(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	// Parse status filter
	statusStr := r.URL.Query().Get("status")
	status := data.DraftStatusPendingReview // default
	if statusStr != "" {
		status = data.DraftStatus(statusStr)
	}

	// Parse limit
	limitStr := r.URL.Query().Get("limit")
	limit := 20 // default
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	// Get drafts
	drafts, err := api.service.GetResponseDrafts(ctx, status, limit)
	if err != nil {
		api.log.WithContext(ctx).Errorf("Failed to get drafts: %v", err)
		api.writeErrorResponse(w, "Failed to retrieve drafts", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, drafts, startTime)
}

// ✅ Approve draft
func (api *ExecutiveAPI) handleApproveDraft(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	// Get draft ID from URL
	vars := mux.Vars(r)
	draftIDStr := vars["id"]
	draftID, err := strconv.ParseInt(draftIDStr, 10, 64)
	if err != nil {
		api.writeErrorResponse(w, "Invalid draft ID", http.StatusBadRequest)
		return
	}

	var req ApproveDraftRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate
	if req.ApprovedBy == "" {
		api.writeErrorResponse(w, "Approved by field is required", http.StatusBadRequest)
		return
	}

	// Approve draft
	err = api.service.ApproveResponseDraft(ctx, draftID, req.ApprovedBy)
	if err != nil {
		api.log.WithContext(ctx).Errorf("Failed to approve draft: %v", err)
		api.writeErrorResponse(w, "Failed to approve draft", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, map[string]interface{}{
		"draft_id":    draftID,
		"approved_by": req.ApprovedBy,
		"status":      "approved",
	}, startTime)
}

// ❌ Reject draft
func (api *ExecutiveAPI) handleRejectDraft(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	// Get draft ID from URL
	vars := mux.Vars(r)
	draftIDStr := vars["id"]
	draftID, err := strconv.ParseInt(draftIDStr, 10, 64)
	if err != nil {
		api.writeErrorResponse(w, "Invalid draft ID", http.StatusBadRequest)
		return
	}

	var req RejectDraftRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate
	if req.RejectedBy == "" {
		api.writeErrorResponse(w, "Rejected by field is required", http.StatusBadRequest)
		return
	}
	if req.RejectReason == "" {
		api.writeErrorResponse(w, "Reject reason is required", http.StatusBadRequest)
		return
	}

	// Reject draft
	err = api.service.RejectResponseDraft(ctx, draftID, req.RejectedBy, req.RejectReason)
	if err != nil {
		api.log.WithContext(ctx).Errorf("Failed to reject draft: %v", err)
		api.writeErrorResponse(w, "Failed to reject draft", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, map[string]interface{}{
		"draft_id":      draftID,
		"rejected_by":   req.RejectedBy,
		"reject_reason": req.RejectReason,
		"status":        "rejected",
	}, startTime)
}

// ==========================================
// MEMORY BANK ENDPOINTS
// ==========================================

// 🔍 Search memory bank
func (api *ExecutiveAPI) handleSearchMemory(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	var req SearchMemoryRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate
	if req.Query == "" {
		api.writeErrorResponse(w, "Search query is required", http.StatusBadRequest)
		return
	}

	// Set default limit
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// Search memory bank
	memories, err := api.service.SearchMemoryBank(ctx, req.Query, req.EntityType, req.Limit)
	if err != nil {
		api.log.WithContext(ctx).Errorf("Memory search failed: %v", err)
		api.writeErrorResponse(w, "Memory search failed", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, memories, startTime)
}

// 📊 Get memory insights
func (api *ExecutiveAPI) handleGetMemoryInsights(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	// Parse time range
	timeRangeStr := r.URL.Query().Get("time_range")
	timeRange := 24 * time.Hour * 30 // default: 30 days
	if timeRangeStr != "" {
		if tr, err := time.ParseDuration(timeRangeStr); err == nil {
			timeRange = tr
		}
	}

	// Parse entity type filter
	entityType := r.URL.Query().Get("entity_type")

	// Get insights
	insights, err := api.service.memoryBank.GetMemoryInsights(ctx, entityType, timeRange)
	if err != nil {
		api.log.WithContext(ctx).Errorf("Failed to get memory insights: %v", err)
		api.writeErrorResponse(w, "Failed to retrieve memory insights", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, insights, startTime)
}

// ==========================================
// ANALYTICS ENDPOINTS
// ==========================================

// 📈 Get performance metrics
func (api *ExecutiveAPI) handleGetMetrics(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	// Parse parameters
	category := r.URL.Query().Get("category")

	fromDateStr := r.URL.Query().Get("from_date")
	fromDate := time.Now().AddDate(0, 0, -7) // default: 7 days ago
	if fromDateStr != "" {
		if fd, err := time.Parse("2006-01-02", fromDateStr); err == nil {
			fromDate = fd
		}
	}

	toDateStr := r.URL.Query().Get("to_date")
	toDate := time.Now() // default: now
	if toDateStr != "" {
		if td, err := time.Parse("2006-01-02", toDateStr); err == nil {
			toDate = td
		}
	}

	// Get metrics
	metrics, err := api.service.GetPerformanceMetrics(ctx, category, fromDate, toDate)
	if err != nil {
		api.log.WithContext(ctx).Errorf("Failed to get metrics: %v", err)
		api.writeErrorResponse(w, "Failed to retrieve metrics", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, metrics, startTime)
}

// 📊 Get performance summary
func (api *ExecutiveAPI) handleGetPerformance(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	// Get performance summary from multiple sources
	summary := map[string]interface{}{
		"triage_stats":    api.getTriageStats(ctx),
		"draft_stats":     api.getDraftStats(ctx),
		"memory_stats":    api.getMemoryStats(ctx),
		"workflow_stats":  api.getWorkflowStats(ctx),
		"calendar_stats":  api.getCalendarStats(ctx),
	}

	api.writeSuccessResponse(w, summary, startTime)
}

// ==========================================
// WORKFLOW ENDPOINTS
// ==========================================

// 🔄 Execute workflow rule
func (api *ExecutiveAPI) handleExecuteWorkflow(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	var req struct {
		RuleID  int64 `json:"rule_id"`
		EmailID int64 `json:"email_id"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate
	if req.RuleID <= 0 || req.EmailID <= 0 {
		api.writeErrorResponse(w, "Invalid rule ID or email ID", http.StatusBadRequest)
		return
	}

	// Execute workflow
	result, err := api.service.ExecuteWorkflowRule(ctx, req.RuleID, req.EmailID)
	if err != nil {
		api.log.WithContext(ctx).Errorf("Workflow execution failed: %v", err)
		api.writeErrorResponse(w, "Workflow execution failed", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, result, startTime)
}

// 📋 Get workflow rules
func (api *ExecutiveAPI) handleGetWorkflowRules(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	// Get workflow rules from database
	var rules []*data.EmailWorkflowRule
	err := api.service.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("priority ASC, execution_order ASC").
		Find(&rules).Error

	if err != nil {
		api.log.WithContext(ctx).Errorf("Failed to get workflow rules: %v", err)
		api.writeErrorResponse(w, "Failed to retrieve workflow rules", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, rules, startTime)
}

// ==========================================
// CALENDAR ENDPOINTS
// ==========================================

// 📅 Schedule meeting
func (api *ExecutiveAPI) handleScheduleMeeting(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	var req struct {
		EmailID        int64              `json:"email_id"`
		MeetingRequest *MeetingRequest    `json:"meeting_request"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeErrorResponse(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate
	if req.EmailID <= 0 {
		api.writeErrorResponse(w, "Invalid email ID", http.StatusBadRequest)
		return
	}

	// Schedule meeting
	result, err := api.service.ScheduleMeeting(ctx, req.EmailID, req.MeetingRequest)
	if err != nil {
		api.log.WithContext(ctx).Errorf("Meeting scheduling failed: %v", err)
		api.writeErrorResponse(w, "Meeting scheduling failed", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, result, startTime)
}

// 📅 Get calendar events
func (api *ExecutiveAPI) handleGetCalendarEvents(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	ctx := r.Context()

	// Parse date range
	fromDateStr := r.URL.Query().Get("from_date")
	fromDate := time.Now().AddDate(0, 0, -7) // default: 7 days ago
	if fromDateStr != "" {
		if fd, err := time.Parse("2006-01-02", fromDateStr); err == nil {
			fromDate = fd
		}
	}

	toDateStr := r.URL.Query().Get("to_date")
	toDate := time.Now().AddDate(0, 0, 7) // default: 7 days from now
	if toDateStr != "" {
		if td, err := time.Parse("2006-01-02", toDateStr); err == nil {
			toDate = td
		}
	}

	// Get calendar events
	var events []*data.CalendarEvent
	err := api.service.db.WithContext(ctx).
		Where("start_time >= ? AND end_time <= ?", fromDate, toDate).
		Order("start_time ASC").
		Find(&events).Error

	if err != nil {
		api.log.WithContext(ctx).Errorf("Failed to get calendar events: %v", err)
		api.writeErrorResponse(w, "Failed to retrieve calendar events", http.StatusInternalServerError)
		return
	}

	api.writeSuccessResponse(w, events, startTime)
}

// ==========================================
// UTILITY ENDPOINTS
// ==========================================

// 🏥 Health check
func (api *ExecutiveAPI) handleHealthCheck(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	health := map[string]interface{}{
		"status":    "healthy",
		"service":   "executive-ai-assistant",
		"version":   "1.0.0",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"uptime":    time.Since(startTime).String(),
	}

	api.writeSuccessResponse(w, health, startTime)
}

// ==========================================
// HELPER METHODS
// ==========================================

func (api *ExecutiveAPI) writeSuccessResponse(w http.ResponseWriter, data interface{}, startTime time.Time) {
	response := APIResponse{
		Success: true,
		Data:    data,
		Meta: &APIMeta{
			ProcessingTime: time.Since(startTime).String(),
			Timestamp:      time.Now().UTC().Format(time.RFC3339),
			Version:        "1.0.0",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

func (api *ExecutiveAPI) writeErrorResponse(w http.ResponseWriter, message string, statusCode int) {
	response := APIResponse{
		Success: false,
		Error:   message,
		Meta: &APIMeta{
			Timestamp: time.Now().UTC().Format(time.RFC3339),
			Version:   "1.0.0",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// ==========================================
// STATS HELPER METHODS
// ==========================================

func (api *ExecutiveAPI) getTriageStats(ctx context.Context) map[string]interface{} {
	// Get triage statistics from database
	// This is a simplified version - in production, implement proper aggregation

	return map[string]interface{}{
		"total_triaged":     0,
		"auto_responded":    0,
		"escalated":         0,
		"average_confidence": 0.0,
	}
}

func (api *ExecutiveAPI) getDraftStats(ctx context.Context) map[string]interface{} {
	return map[string]interface{}{
		"total_drafts":      0,
		"approved_drafts":   0,
		"rejected_drafts":   0,
		"pending_drafts":    0,
		"average_quality":   0.0,
	}
}

func (api *ExecutiveAPI) getMemoryStats(ctx context.Context) map[string]interface{} {
	return map[string]interface{}{
		"total_memories":    0,
		"active_memories":   0,
		"memory_types":      map[string]int{},
		"average_confidence": 0.0,
	}
}

func (api *ExecutiveAPI) getWorkflowStats(ctx context.Context) map[string]interface{} {
	return map[string]interface{}{
		"total_executions":  0,
		"successful_executions": 0,
		"failed_executions": 0,
		"active_rules":      0,
	}
}

func (api *ExecutiveAPI) getCalendarStats(ctx context.Context) map[string]interface{} {
	return map[string]interface{}{
		"total_events":      0,
		"ai_created_events": 0,
		"upcoming_events":   0,
	}
}

// ==========================================
// SUPPORTING TYPES (referenced in other files)
// ==========================================

type WorkflowExecutionResult struct {
	ExecutionID      int64                  `json:"execution_id"`
	RuleID           int64                  `json:"rule_id"`
	RuleName         string                 `json:"rule_name"`
	Status           string                 `json:"status"`
	ActionsExecuted  []string               `json:"actions_executed"`
	ActionsFailed    []string               `json:"actions_failed"`
	ExecutionTime    time.Duration          `json:"execution_time"`
	Result           map[string]interface{} `json:"result"`
	Success          bool                   `json:"success"`
	ErrorMessage     string                 `json:"error_message,omitempty"`
}

type CalendarEventResult struct {
	EventID      int64                  `json:"event_id"`
	Title        string                 `json:"title"`
	StartTime    time.Time              `json:"start_time"`
	EndTime      time.Time              `json:"end_time"`
	Attendees    []string               `json:"attendees"`
	MeetingType  string                 `json:"meeting_type"`
	CreatedByAI  bool                   `json:"created_by_ai"`
	Confidence   float64                `json:"confidence"`
	Success      bool                   `json:"success"`
	ErrorMessage string                 `json:"error_message,omitempty"`
}

type MeetingRequest struct {
	Title         string                 `json:"title"`
	Description   string                 `json:"description"`
	Duration      time.Duration          `json:"duration"`
	Attendees     []string               `json:"attendees"`
	MeetingType   string                 `json:"meeting_type"`
	PreferredTime *time.Time             `json:"preferred_time,omitempty"`
	Context       map[string]interface{} `json:"context"`
}

type ProcessingMetrics struct {
	ProcessingTime    time.Duration `json:"processing_time"`
	TriageConfidence  float64       `json:"triage_confidence"`
	WorkflowsExecuted int           `json:"workflows_executed"`
	MemoriesCreated   int           `json:"memories_created"`
	CalendarEvents    int           `json:"calendar_events"`
	Success           bool          `json:"success"`
}