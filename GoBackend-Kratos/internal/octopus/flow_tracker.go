package octopus

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
)

// 📊 LangFuse-like Flow Tracking Implementation

// NewFlowTracker creates a new flow tracker
func NewFlowTracker(logger log.Logger) *FlowTracker {
	return &FlowTracker{
		flows: make(map[string]*Flow),
		log:   log.NewHelper(logger),
	}
}

// StartFlow creates a new flow and returns its ID
func (ft *FlowTracker) StartFlow(ctx context.Context, name string, userID string, metadata map[string]interface{}) string {
	ft.flowsMutex.Lock()
	defer ft.flowsMutex.Unlock()

	flowID := uuid.New().String()
	flow := &Flow{
		ID:         flowID,
		Name:       name,
		UserID:     userID,
		SessionID:  extractSessionID(ctx),
		StartTime:  time.Now(),
		Status:     "running",
		Traces:     make([]*Trace, 0),
		Metadata:   metadata,
		Tags:       make([]string, 0),
		TokenUsage: &TokenUsage{},
	}

	ft.flows[flowID] = flow
	ft.log.WithContext(ctx).Infof("🚀 Started flow: %s (%s)", name, flowID)

	return flowID
}

// EndFlow completes a flow
func (ft *FlowTracker) EndFlow(ctx context.Context, flowID string, status string) error {
	ft.flowsMutex.Lock()
	defer ft.flowsMutex.Unlock()

	flow, exists := ft.flows[flowID]
	if !exists {
		return fmt.Errorf("flow %s not found", flowID)
	}

	endTime := time.Now()
	duration := endTime.Sub(flow.StartTime)

	flow.EndTime = &endTime
	flow.Duration = &duration
	flow.Status = status

	// Calculate total cost and token usage
	ft.calculateFlowTotals(flow)

	ft.log.WithContext(ctx).Infof("✅ Ended flow: %s (%s) - Duration: %v", flow.Name, flowID, duration)

	return nil
}

// StartTrace creates a new trace within a flow
func (ft *FlowTracker) StartTrace(ctx context.Context, flowID string, name string, traceType string, parentID string) string {
	ft.flowsMutex.Lock()
	defer ft.flowsMutex.Unlock()

	flow, exists := ft.flows[flowID]
	if !exists {
		ft.log.WithContext(ctx).Errorf("Flow %s not found for trace %s", flowID, name)
		return ""
	}

	traceID := uuid.New().String()
	trace := &Trace{
		ID:        traceID,
		FlowID:    flowID,
		ParentID:  parentID,
		Name:      name,
		Type:      traceType,
		StartTime: time.Now(),
		Status:    "running",
		Metadata:  make(map[string]interface{}),
	}

	flow.Traces = append(flow.Traces, trace)
	ft.log.WithContext(ctx).Infof("🔍 Started trace: %s (%s) in flow %s", name, traceID, flowID)

	return traceID
}

// EndTrace completes a trace
func (ft *FlowTracker) EndTrace(ctx context.Context, flowID string, traceID string, status string, output interface{}, tokenUsage *TokenUsage, cost float64, model string) error {
	ft.flowsMutex.Lock()
	defer ft.flowsMutex.Unlock()

	flow, exists := ft.flows[flowID]
	if !exists {
		return fmt.Errorf("flow %s not found", flowID)
	}

	// Find the trace
	var trace *Trace
	for _, t := range flow.Traces {
		if t.ID == traceID {
			trace = t
			break
		}
	}

	if trace == nil {
		return fmt.Errorf("trace %s not found in flow %s", traceID, flowID)
	}

	endTime := time.Now()
	duration := endTime.Sub(trace.StartTime)

	trace.EndTime = &endTime
	trace.Duration = &duration
	trace.Status = status
	trace.Output = output
	trace.TokenUsage = tokenUsage
	trace.Cost = cost
	trace.Model = model

	ft.log.WithContext(ctx).Infof("✅ Ended trace: %s (%s) - Duration: %v, Cost: $%.4f", trace.Name, traceID, duration, cost)

	return nil
}

// SetTraceInput sets the input for a trace
func (ft *FlowTracker) SetTraceInput(flowID string, traceID string, input interface{}) error {
	ft.flowsMutex.Lock()
	defer ft.flowsMutex.Unlock()

	flow, exists := ft.flows[flowID]
	if !exists {
		return fmt.Errorf("flow %s not found", flowID)
	}

	for _, trace := range flow.Traces {
		if trace.ID == traceID {
			trace.Input = input
			return nil
		}
	}

	return fmt.Errorf("trace %s not found in flow %s", traceID, flowID)
}

// SetTraceError sets an error for a trace
func (ft *FlowTracker) SetTraceError(flowID string, traceID string, err error) error {
	ft.flowsMutex.Lock()
	defer ft.flowsMutex.Unlock()

	flow, exists := ft.flows[flowID]
	if !exists {
		return fmt.Errorf("flow %s not found", flowID)
	}

	for _, trace := range flow.Traces {
		if trace.ID == traceID {
			trace.Error = err.Error()
			trace.Status = "failed"
			return nil
		}
	}

	return fmt.Errorf("trace %s not found in flow %s", traceID, flowID)
}

// GetFlow retrieves a flow by ID
func (ft *FlowTracker) GetFlow(flowID string) (*Flow, error) {
	ft.flowsMutex.RLock()
	defer ft.flowsMutex.RUnlock()

	flow, exists := ft.flows[flowID]
	if !exists {
		return nil, fmt.Errorf("flow %s not found", flowID)
	}

	return flow, nil
}

// GetFlows retrieves all flows with optional filtering
func (ft *FlowTracker) GetFlows(limit int, offset int, status string, userID string) ([]*Flow, error) {
	ft.flowsMutex.RLock()
	defer ft.flowsMutex.RUnlock()

	flows := make([]*Flow, 0)
	count := 0

	for _, flow := range ft.flows {
		// Apply filters
		if status != "" && flow.Status != status {
			continue
		}
		if userID != "" && flow.UserID != userID {
			continue
		}

		// Apply pagination
		if count < offset {
			count++
			continue
		}

		if len(flows) >= limit {
			break
		}

		flows = append(flows, flow)
		count++
	}

	return flows, nil
}

// GetFlowStats returns statistics about flows
func (ft *FlowTracker) GetFlowStats() *FlowStats {
	ft.flowsMutex.RLock()
	defer ft.flowsMutex.RUnlock()

	stats := &FlowStats{
		TotalFlows:     len(ft.flows),
		RunningFlows:   0,
		CompletedFlows: 0,
		FailedFlows:    0,
		TotalCost:      0,
		TotalTokens:    0,
		AvgDuration:    0,
		FlowsByType:    make(map[string]int),
	}

	var totalDuration time.Duration
	completedCount := 0

	for _, flow := range ft.flows {
		switch flow.Status {
		case "running":
			stats.RunningFlows++
		case "completed":
			stats.CompletedFlows++
		case "failed":
			stats.FailedFlows++
		}

		stats.TotalCost += flow.TotalCost
		if flow.TokenUsage != nil {
			stats.TotalTokens += flow.TokenUsage.TotalTokens
		}

		if flow.Duration != nil {
			totalDuration += *flow.Duration
			completedCount++
		}

		// Count by type (based on first trace type)
		if len(flow.Traces) > 0 {
			traceType := flow.Traces[0].Type
			stats.FlowsByType[traceType]++
		}
	}

	if completedCount > 0 {
		stats.AvgDuration = totalDuration / time.Duration(completedCount)
	}

	return stats
}

// FlowStats represents flow statistics
type FlowStats struct {
	TotalFlows     int                    `json:"total_flows"`
	RunningFlows   int                    `json:"running_flows"`
	CompletedFlows int                    `json:"completed_flows"`
	FailedFlows    int                    `json:"failed_flows"`
	TotalCost      float64                `json:"total_cost"`
	TotalTokens    int64                  `json:"total_tokens"`
	AvgDuration    time.Duration          `json:"avg_duration"`
	FlowsByType    map[string]int         `json:"flows_by_type"`
}

// calculateFlowTotals calculates total cost and token usage for a flow
func (ft *FlowTracker) calculateFlowTotals(flow *Flow) {
	var totalCost float64
	var totalTokens TokenUsage

	for _, trace := range flow.Traces {
		totalCost += trace.Cost
		if trace.TokenUsage != nil {
			totalTokens.InputTokens += trace.TokenUsage.InputTokens
			totalTokens.OutputTokens += trace.TokenUsage.OutputTokens
			totalTokens.TotalTokens += trace.TokenUsage.TotalTokens
		}
	}

	flow.TotalCost = totalCost
	flow.TokenUsage = &totalTokens
}

// extractSessionID extracts session ID from context
func extractSessionID(ctx context.Context) string {
	if sessionID := ctx.Value("session_id"); sessionID != nil {
		if id, ok := sessionID.(string); ok {
			return id
		}
	}
	return ""
}

// CleanupOldFlows removes flows older than specified duration
func (ft *FlowTracker) CleanupOldFlows(maxAge time.Duration) int {
	ft.flowsMutex.Lock()
	defer ft.flowsMutex.Unlock()

	cutoff := time.Now().Add(-maxAge)
	removed := 0

	for flowID, flow := range ft.flows {
		if flow.StartTime.Before(cutoff) && flow.Status != "running" {
			delete(ft.flows, flowID)
			removed++
		}
	}

	if removed > 0 {
		ft.log.Infof("🧹 Cleaned up %d old flows", removed)
	}

	return removed
}

// NewRequestMetrics creates a new request metrics tracker
func NewRequestMetrics() *RequestMetrics {
	return &RequestMetrics{
		RequestsByModel: make(map[string]int64),
		RequestsByType:  make(map[string]int64),
		LastUpdated:     time.Now(),
	}
}

// NewTokenUsageTracker creates a new token usage tracker
func NewTokenUsageTracker() *TokenUsageTracker {
	return &TokenUsageTracker{
		TokensByModel: make(map[string]*TokenUsage),
		TokensByUser:  make(map[string]*TokenUsage),
		CostByModel:   make(map[string]float64),
		LastUpdated:   time.Now(),
	}
}
