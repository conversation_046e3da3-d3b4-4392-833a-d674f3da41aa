package octopus

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gorilla/mux"
)

// 📧 Email Intelligence Handler - Enhanced with AI Email Service
func (o *MorphicOctopusInterface) handleEmailIntelligence(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get AI email intelligence stats
	if o.aiEmailService != nil {
		stats, err := o.aiEmailService.GetEmailIntelligenceStats(ctx)
		if err != nil {
			o.log.Errorf("Failed to get AI email stats: %v", err)
		} else {
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(stats)
			return
		}
	}

	// Fallback to basic intelligence
	intelligence, err := o.buildEmailIntelligence(ctx)
	if err != nil {
		http.Error(w, "Failed to get email intelligence", http.StatusInternalServerError)
		return
	}

	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(intelligence)
}

// 📧 Email Campaigns Handler
func (o *MorphicOctopusInterface) handleEmailCampaigns(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		var campaigns []map[string]interface{}
		o.db.Table("billionmail.email_campaigns").Find(&campaigns)

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(campaigns)
	} else if r.Method == "POST" {
		var campaign map[string]interface{}
		if err := json.NewDecoder(r.Body).Decode(&campaign); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}

		campaign["created_at"] = time.Now()
		campaign["updated_at"] = time.Now()
		campaign["status"] = "draft"

		if err := o.db.Table("billionmail.email_campaigns").Create(&campaign).Error; err != nil {
			http.Error(w, "Failed to create campaign", http.StatusInternalServerError)
			return
		}

		o.log.Infof("📧 Email campaign created: %v", campaign["name"])

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(campaign)
	}
}

// 📧 Email Templates Handler
func (o *MorphicOctopusInterface) handleEmailTemplates(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		var templates []map[string]interface{}
		o.db.Table("billionmail.email_templates").Find(&templates)

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(templates)
	} else if r.Method == "POST" {
		var template map[string]interface{}
		if err := json.NewDecoder(r.Body).Decode(&template); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}

		template["created_at"] = time.Now()
		template["updated_at"] = time.Now()
		template["active"] = true

		if err := o.db.Table("billionmail.email_templates").Create(&template).Error; err != nil {
			http.Error(w, "Failed to create template", http.StatusInternalServerError)
			return
		}

		o.log.Infof("📧 Email template created: %v", template["name"])

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(template)
	}
}

// 📊 Email Analytics Handler
func (o *MorphicOctopusInterface) handleEmailAnalytics(w http.ResponseWriter, r *http.Request) {
	// Placeholder email analytics
	analytics := map[string]interface{}{
		"total_sent":      1500,
		"delivered":       1450,
		"opened":          725,
		"clicked":         145,
		"bounced":         25,
		"unsubscribed":    15,
		"delivery_rate":   96.7,
		"open_rate":       50.0,
		"click_rate":      10.0,
		"bounce_rate":     1.7,
		"unsubscribe_rate": 1.0,
		"timestamp":       time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(analytics)
}

// 📮 Email Mailboxes Handler
func (o *MorphicOctopusInterface) handleEmailMailboxes(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		var mailboxes []map[string]interface{}
		o.db.Table("billionmail.mailboxes").Select("id, email, name, active, created_at").Find(&mailboxes)

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(mailboxes)
	} else if r.Method == "POST" {
		var mailbox map[string]interface{}
		if err := json.NewDecoder(r.Body).Decode(&mailbox); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}

		mailbox["created_at"] = time.Now()
		mailbox["updated_at"] = time.Now()
		mailbox["active"] = true

		if err := o.db.Table("billionmail.mailboxes").Create(&mailbox).Error; err != nil {
			http.Error(w, "Failed to create mailbox", http.StatusInternalServerError)
			return
		}

		o.log.Infof("📮 Mailbox created: %v", mailbox["email"])

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(mailbox)
	}
}

// 📤 Email Send Handler
func (o *MorphicOctopusInterface) handleEmailSend(w http.ResponseWriter, r *http.Request) {
	var emailRequest struct {
		To       []string `json:"to"`
		Subject  string   `json:"subject"`
		Body     string   `json:"body"`
		Template string   `json:"template,omitempty"`
		Priority string   `json:"priority,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&emailRequest); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	o.log.Infof("📤 Email send requested: %s to %v", emailRequest.Subject, emailRequest.To)

	response := map[string]interface{}{
		"status":     "queued",
		"message_id": "msg_" + time.Now().Format("20060102_150405"),
		"recipients": emailRequest.To,
		"subject":    emailRequest.Subject,
		"timestamp":  time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 🤖 AI Performance Handler
func (o *MorphicOctopusInterface) handleAIPerformance(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	performance, err := o.buildAIPerformance(ctx)
	if err != nil {
		http.Error(w, "Failed to get AI performance", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(performance)
}

// 🤖 AI Models Handler
func (o *MorphicOctopusInterface) handleAIModels(w http.ResponseWriter, r *http.Request) {
	models := []map[string]interface{}{
		{
			"name":        "gemma:3b-instruct-q4_0",
			"type":        "language_model",
			"status":      "active",
			"accuracy":    92.3,
			"last_used":   time.Now().Add(-5 * time.Minute),
			"requests":    1500,
			"avg_latency": "250ms",
		},
		{
			"name":        "bielik-v3",
			"type":        "language_model",
			"status":      "active",
			"accuracy":    89.7,
			"last_used":   time.Now().Add(-2 * time.Minute),
			"requests":    800,
			"avg_latency": "180ms",
		},
		{
			"name":        "nomic-embed-text",
			"type":        "embedding_model",
			"status":      "active",
			"accuracy":    95.1,
			"last_used":   time.Now().Add(-1 * time.Minute),
			"requests":    2200,
			"avg_latency": "50ms",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(models)
}

// 🤖 AI Model Status Handler
func (o *MorphicOctopusInterface) handleAIModelStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	modelName := vars["model"]

	status := map[string]interface{}{
		"model":       modelName,
		"status":      "active",
		"health":      "healthy",
		"uptime":      "2h 15m",
		"memory_usage": 1024.5,
		"gpu_usage":   75.2,
		"requests":    150,
		"errors":      2,
		"last_check":  time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

// 🔄 AI Model Restart Handler
func (o *MorphicOctopusInterface) handleAIModelRestart(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	modelName := vars["model"]

	o.log.Infof("🔄 AI model restart requested: %s", modelName)

	response := map[string]interface{}{
		"status":    "restarting",
		"model":     modelName,
		"message":   "Model restart initiated",
		"timestamp": time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 🧠 AI Analyze Handler
func (o *MorphicOctopusInterface) handleAIAnalyze(w http.ResponseWriter, r *http.Request) {
	var analyzeRequest struct {
		Text     string `json:"text"`
		Type     string `json:"type"` // email, transcription, customer
		Model    string `json:"model,omitempty"`
		Options  map[string]interface{} `json:"options,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&analyzeRequest); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	o.log.Infof("🧠 AI analysis requested: type=%s, model=%s", analyzeRequest.Type, analyzeRequest.Model)

	// Placeholder analysis result
	result := map[string]interface{}{
		"analysis_id": "analysis_" + time.Now().Format("20060102_150405"),
		"type":        analyzeRequest.Type,
		"model":       analyzeRequest.Model,
		"sentiment":   "positive",
		"confidence":  0.87,
		"keywords":    []string{"HVAC", "maintenance", "satisfied"},
		"summary":     "Customer expresses satisfaction with HVAC maintenance service",
		"timestamp":   time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// 📮 Handle Mailbox Status (removed duplicate - using original implementation above)

// 🔄 Handle Sync All Mailboxes
func (o *MorphicOctopusInterface) handleEmailSyncAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	if o.aiEmailService != nil {
		err := o.aiEmailService.SyncAllMailboxes(ctx)
		if err != nil {
			http.Error(w, "Failed to sync mailboxes", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{"status":"success","message":"Mailbox sync initiated"}`))
		return
	}

	http.Error(w, "AI Email Service not available", http.StatusServiceUnavailable)
}

// ⚡ Handle Process Email Queue
func (o *MorphicOctopusInterface) handleEmailProcessQueue(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	if o.aiEmailService != nil {
		err := o.aiEmailService.ProcessEmailQueue(ctx)
		if err != nil {
			http.Error(w, "Failed to process email queue", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{"status":"success","message":"Email queue processing started"}`))
		return
	}

	http.Error(w, "AI Email Service not available", http.StatusServiceUnavailable)
}

// 📊 Handle Export Email Analytics
func (o *MorphicOctopusInterface) handleEmailExportAnalytics(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	if o.aiEmailService != nil {
		data, err := o.aiEmailService.ExportAnalytics(ctx)
		if err != nil {
			http.Error(w, "Failed to export analytics", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "text/csv")
		w.Header().Set("Content-Disposition", "attachment; filename=email-analytics.csv")
		w.Write(data)
		return
	}

	http.Error(w, "AI Email Service not available", http.StatusServiceUnavailable)
}

// 🎓 AI Train Handler
func (o *MorphicOctopusInterface) handleAITrain(w http.ResponseWriter, r *http.Request) {
	var trainRequest struct {
		Model    string                 `json:"model"`
		Dataset  string                 `json:"dataset"`
		Options  map[string]interface{} `json:"options,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&trainRequest); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	o.log.Infof("🎓 AI training requested: model=%s, dataset=%s", trainRequest.Model, trainRequest.Dataset)

	response := map[string]interface{}{
		"status":     "started",
		"training_id": "train_" + time.Now().Format("20060102_150405"),
		"model":      trainRequest.Model,
		"dataset":    trainRequest.Dataset,
		"message":    "Model training initiated",
		"timestamp":  time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 📋 AI Queue Handler
func (o *MorphicOctopusInterface) handleAIQueue(w http.ResponseWriter, r *http.Request) {
	queue := []map[string]interface{}{
		{
			"id":        "req_001",
			"type":      "analysis",
			"model":     "gemma:3b-instruct-q4_0",
			"status":    "processing",
			"priority":  "high",
			"queued_at": time.Now().Add(-2 * time.Minute),
		},
		{
			"id":        "req_002",
			"type":      "embedding",
			"model":     "nomic-embed-text",
			"status":    "pending",
			"priority":  "medium",
			"queued_at": time.Now().Add(-1 * time.Minute),
		},
		{
			"id":        "req_003",
			"type":      "analysis",
			"model":     "bielik-v3",
			"status":    "pending",
			"priority":  "low",
			"queued_at": time.Now().Add(-30 * time.Second),
		},
	}

	response := map[string]interface{}{
		"queue_length": len(queue),
		"queue":        queue,
		"timestamp":    time.Now(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 🧠 Handle AI Model Retraining
func (o *MorphicOctopusInterface) handleAIRetrain(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var retrainRequest struct {
		Model string `json:"model"`
		Type  string `json:"type"`
	}

	if err := json.NewDecoder(r.Body).Decode(&retrainRequest); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if o.aiEmailService != nil && retrainRequest.Type == "email_classification" {
		err := o.aiEmailService.RetrainModel(ctx, retrainRequest.Model)
		if err != nil {
			http.Error(w, "Failed to retrain model", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{"status":"success","message":"Model retraining initiated"}`))
		return
	}

	http.Error(w, "Model retraining not available for this type", http.StatusServiceUnavailable)
}