package langchain

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/prompts"
)

// 🤖 HVAC-Specific Agent Chains - World-Class AI Automation
// Advanced agent chains for intelligent HVAC business automation

type HVACAgentChains struct {
	DiagnosticAgent     *chains.LLMChain  // Equipment troubleshooting & diagnostics
	MaintenanceAgent    *chains.LLMChain  // Predictive maintenance scheduling
	CustomerAgent       *chains.LLMChain  // Intelligent customer communication
	TechnicalAgent      *chains.LLMChain  // Technical documentation assistance
	BusinessAgent       *chains.LLMChain  // Business process optimization
	EmergencyAgent      *chains.LLMChain  // Emergency response coordination
	QuoteAgent          *chains.LLMChain  // Intelligent quote generation
	SchedulingAgent     *chains.LLMChain  // Smart scheduling optimization

	llm    llms.LLM
	log    *log.Helper
}

// NewHVACAgentChains creates a new set of HVAC-specific agent chains
func NewHVACAgentChains(llm llms.LLM, logger log.Logger) (*HVACAgentChains, error) {
	helper := log.NewHelper(logger)
	helper.Info("🤖 Initializing HVAC Agent Chains...")

	agents := &HVACAgentChains{
		llm: llm,
		log: helper,
	}

	if err := agents.initializeAgents(); err != nil {
		return nil, fmt.Errorf("failed to initialize HVAC agents: %w", err)
	}

	helper.Info("✅ HVAC Agent Chains initialized successfully!")
	return agents, nil
}

// initializeAgents sets up all specialized HVAC agent chains
func (h *HVACAgentChains) initializeAgents() error {
	var err error

	// Diagnostic Agent - Equipment troubleshooting
	h.DiagnosticAgent, err = h.createDiagnosticAgent()
	if err != nil {
		return fmt.Errorf("failed to create diagnostic agent: %w", err)
	}

	// Maintenance Agent - Predictive maintenance
	h.MaintenanceAgent, err = h.createMaintenanceAgent()
	if err != nil {
		return fmt.Errorf("failed to create maintenance agent: %w", err)
	}

	// Customer Agent - Customer communication
	h.CustomerAgent, err = h.createCustomerAgent()
	if err != nil {
		return fmt.Errorf("failed to create customer agent: %w", err)
	}

	// Technical Agent - Documentation assistance
	h.TechnicalAgent, err = h.createTechnicalAgent()
	if err != nil {
		return fmt.Errorf("failed to create technical agent: %w", err)
	}

	// Business Agent - Process optimization
	h.BusinessAgent, err = h.createBusinessAgent()
	if err != nil {
		return fmt.Errorf("failed to create business agent: %w", err)
	}

	// Emergency Agent - Emergency response
	h.EmergencyAgent, err = h.createEmergencyAgent()
	if err != nil {
		return fmt.Errorf("failed to create emergency agent: %w", err)
	}

	// Quote Agent - Intelligent quoting
	h.QuoteAgent, err = h.createQuoteAgent()
	if err != nil {
		return fmt.Errorf("failed to create quote agent: %w", err)
	}

	// Scheduling Agent - Smart scheduling
	h.SchedulingAgent, err = h.createSchedulingAgent()
	if err != nil {
		return fmt.Errorf("failed to create scheduling agent: %w", err)
	}

	return nil
}

// 🔧 Diagnostic Agent - Advanced Equipment Troubleshooting
func (h *HVACAgentChains) createDiagnosticAgent() (*chains.LLMChain, error) {
	template := `You are an expert HVAC diagnostic specialist with 20+ years of experience.
Analyze the following equipment issue and provide comprehensive diagnostic guidance.

EQUIPMENT INFORMATION:
- System Type: {system_type}
- Model: {model}
- Age: {age}
- Last Maintenance: {last_maintenance}

CUSTOMER ISSUE:
{customer_issue}

SYMPTOMS:
{symptoms}

ENVIRONMENTAL CONDITIONS:
- Temperature: {temperature}
- Humidity: {humidity}
- Season: {season}

Please provide:
1. IMMEDIATE DIAGNOSIS: Most likely cause(s)
2. DIAGNOSTIC STEPS: Step-by-step troubleshooting procedure
3. SAFETY CONSIDERATIONS: Important safety warnings
4. PARTS NEEDED: Likely replacement parts
5. URGENCY LEVEL: Emergency/High/Medium/Low
6. ESTIMATED REPAIR TIME: Time required for repair
7. COST ESTIMATE: Approximate repair cost range
8. PREVENTIVE MEASURES: How to prevent future issues

Format your response as structured JSON for easy parsing.

DIAGNOSTIC ANALYSIS:`

	prompt := prompts.NewPromptTemplate(template, []string{
		"system_type", "model", "age", "last_maintenance", "customer_issue",
		"symptoms", "temperature", "humidity", "season",
	})

	return chains.NewLLMChain(h.llm, prompt), nil
}

// 🔮 Maintenance Agent - Predictive Maintenance Intelligence
func (h *HVACAgentChains) createMaintenanceAgent() (*chains.LLMChain, error) {
	template := `You are an AI-powered predictive maintenance specialist for HVAC systems.
Analyze equipment data and predict optimal maintenance schedules.

EQUIPMENT DATA:
- System Type: {system_type}
- Installation Date: {install_date}
- Usage Hours: {usage_hours}
- Performance Metrics: {performance_metrics}
- Historical Issues: {historical_issues}
- Environmental Factors: {environmental_factors}

CURRENT STATUS:
- Last Service: {last_service}
- Current Performance: {current_performance}
- Sensor Readings: {sensor_readings}

BUSINESS CONTEXT:
- Customer Type: {customer_type}
- Service Contract: {service_contract}
- Budget Constraints: {budget_constraints}

Provide predictive maintenance recommendations:
1. NEXT SERVICE DATE: Optimal timing for next maintenance
2. MAINTENANCE TYPE: Routine/Preventive/Corrective
3. PRIORITY TASKS: Critical maintenance items
4. PARTS TO ORDER: Recommended parts inventory
5. FAILURE PREDICTIONS: Potential failure points and timeline
6. COST OPTIMIZATION: Ways to reduce maintenance costs
7. PERFORMANCE IMPROVEMENTS: Efficiency enhancement opportunities
8. SEASONAL ADJUSTMENTS: Season-specific maintenance needs

Return structured JSON response with confidence scores.

MAINTENANCE ANALYSIS:`

	prompt := prompts.NewPromptTemplate(template, []string{
		"system_type", "install_date", "usage_hours", "performance_metrics",
		"historical_issues", "environmental_factors", "last_service",
		"current_performance", "sensor_readings", "customer_type",
		"service_contract", "budget_constraints",
	})

	return chains.NewLLMChain(h.llm, prompt), nil
}

// 💬 Customer Agent - Intelligent Customer Communication
func (h *HVACAgentChains) createCustomerAgent() (*chains.LLMChain, error) {
	template := `You are a professional HVAC customer service specialist with excellent communication skills.
Generate appropriate customer communications based on the situation.

CUSTOMER INFORMATION:
- Name: {customer_name}
- Type: {customer_type}
- History: {customer_history}
- Preferences: {communication_preferences}
- Previous Issues: {previous_issues}

COMMUNICATION CONTEXT:
- Purpose: {communication_purpose}
- Urgency: {urgency_level}
- Technical Details: {technical_details}
- Service Status: {service_status}

BUSINESS CONTEXT:
- Company Policy: {company_policy}
- Service Level: {service_level}
- Pricing Information: {pricing_info}

Generate appropriate communication:
1. SUBJECT LINE: Clear, professional subject
2. GREETING: Personalized greeting
3. MAIN MESSAGE: Clear, concise explanation
4. TECHNICAL EXPLANATION: Simplified technical details
5. NEXT STEPS: Clear action items
6. TIMELINE: Realistic timeframes
7. CONTACT INFORMATION: How to reach us
8. CLOSING: Professional closing

Tone should be: Professional, empathetic, clear, and solution-focused.
Avoid technical jargon unless necessary.

CUSTOMER COMMUNICATION:`

	prompt := prompts.NewPromptTemplate(template, []string{
		"customer_name", "customer_type", "customer_history",
		"communication_preferences", "previous_issues", "communication_purpose",
		"urgency_level", "technical_details", "service_status",
		"company_policy", "service_level", "pricing_info",
	})

	return chains.NewLLMChain(h.llm, prompt), nil
}

// 📚 Technical Agent - Documentation & Knowledge Assistant
func (h *HVACAgentChains) createTechnicalAgent() (*chains.LLMChain, error) {
	template := `You are an expert HVAC technical documentation specialist.
Provide comprehensive technical guidance and documentation assistance.

TECHNICAL QUERY:
{technical_query}

EQUIPMENT CONTEXT:
- Manufacturer: {manufacturer}
- Model: {model}
- System Type: {system_type}
- Specifications: {specifications}

KNOWLEDGE BASE CONTEXT:
{knowledge_base_context}

TECHNICIAN LEVEL:
{technician_level}

Provide technical assistance:
1. TECHNICAL EXPLANATION: Detailed technical information
2. STEP-BY-STEP PROCEDURES: Clear procedural guidance
3. SAFETY REQUIREMENTS: Critical safety information
4. TOOLS REQUIRED: Necessary tools and equipment
5. REFERENCE MATERIALS: Relevant manuals and codes
6. TROUBLESHOOTING TIPS: Expert troubleshooting advice
7. BEST PRACTICES: Industry best practices
8. RELATED TOPICS: Additional relevant information

Adjust technical depth based on technician level.
Include relevant code references and standards.

TECHNICAL GUIDANCE:`

	prompt := prompts.NewPromptTemplate(template, []string{
		"technical_query", "manufacturer", "model", "system_type",
		"specifications", "knowledge_base_context", "technician_level",
	})

	return chains.NewLLMChain(h.llm, prompt), nil
}

// 📊 Business Agent - Process Optimization & Analytics
func (h *HVACAgentChains) createBusinessAgent() (*chains.LLMChain, error) {
	template := `You are a business optimization specialist for HVAC companies.
Analyze business data and provide strategic recommendations.

BUSINESS METRICS:
- Revenue Data: {revenue_data}
- Service Metrics: {service_metrics}
- Customer Satisfaction: {customer_satisfaction}
- Technician Performance: {technician_performance}
- Operational Costs: {operational_costs}

MARKET CONTEXT:
- Seasonal Trends: {seasonal_trends}
- Competition: {competition_analysis}
- Market Opportunities: {market_opportunities}

BUSINESS GOALS:
{business_goals}

Provide business optimization recommendations:
1. PERFORMANCE ANALYSIS: Key performance insights
2. EFFICIENCY OPPORTUNITIES: Process improvement areas
3. REVENUE OPTIMIZATION: Revenue enhancement strategies
4. COST REDUCTION: Cost-saving opportunities
5. CUSTOMER RETENTION: Customer loyalty strategies
6. MARKET EXPANSION: Growth opportunities
7. OPERATIONAL IMPROVEMENTS: Workflow optimizations
8. STRATEGIC RECOMMENDATIONS: Long-term strategic advice

Include specific metrics and actionable recommendations.

BUSINESS ANALYSIS:`

	prompt := prompts.NewPromptTemplate(template, []string{
		"revenue_data", "service_metrics", "customer_satisfaction",
		"technician_performance", "operational_costs", "seasonal_trends",
		"competition_analysis", "market_opportunities", "business_goals",
	})

	return chains.NewLLMChain(h.llm, prompt), nil
}

// 🚨 Emergency Agent - Emergency Response Coordination
func (h *HVACAgentChains) createEmergencyAgent() (*chains.LLMChain, error) {
	template := `You are an emergency response coordinator for HVAC services.
Assess emergency situations and coordinate rapid response.

EMERGENCY DETAILS:
- Emergency Type: {emergency_type}
- Location: {location}
- Customer: {customer_info}
- Situation: {situation_description}
- Time Reported: {time_reported}

ENVIRONMENTAL CONDITIONS:
- Weather: {weather_conditions}
- Temperature: {temperature}
- Occupancy: {building_occupancy}

RESOURCES AVAILABLE:
- Available Technicians: {available_technicians}
- Equipment Inventory: {equipment_inventory}
- Emergency Contacts: {emergency_contacts}

Coordinate emergency response:
1. SEVERITY ASSESSMENT: Emergency severity level (1-5)
2. IMMEDIATE ACTIONS: Critical first steps
3. RESOURCE ALLOCATION: Technician and equipment assignment
4. RESPONSE TIMELINE: Expected response and resolution times
5. SAFETY PROTOCOLS: Safety measures and precautions
6. CUSTOMER COMMUNICATION: Emergency communication plan
7. ESCALATION PROCEDURES: When to escalate further
8. FOLLOW-UP ACTIONS: Post-emergency procedures

Prioritize safety and rapid resolution.

EMERGENCY RESPONSE PLAN:`

	prompt := prompts.NewPromptTemplate(template, []string{
		"emergency_type", "location", "customer_info", "situation_description",
		"time_reported", "weather_conditions", "temperature", "building_occupancy",
		"available_technicians", "equipment_inventory", "emergency_contacts",
	})

	return chains.NewLLMChain(h.llm, prompt), nil
}

// 💰 Quote Agent - Intelligent Quote Generation
func (h *HVACAgentChains) createQuoteAgent() (*chains.LLMChain, error) {
	template := `You are an expert HVAC pricing specialist with comprehensive market knowledge.
Generate accurate, competitive quotes based on project requirements.

PROJECT DETAILS:
- Service Type: {service_type}
- System Requirements: {system_requirements}
- Property Details: {property_details}
- Customer Type: {customer_type}
- Timeline: {timeline}

COST FACTORS:
- Labor Requirements: {labor_requirements}
- Material Costs: {material_costs}
- Equipment Needed: {equipment_needed}
- Complexity Level: {complexity_level}
- Seasonal Factors: {seasonal_factors}

BUSINESS CONTEXT:
- Profit Margins: {profit_margins}
- Competition: {competition_pricing}
- Customer Budget: {customer_budget}

Generate comprehensive quote:
1. ITEMIZED BREAKDOWN: Detailed cost breakdown
2. LABOR COSTS: Technician time and rates
3. MATERIAL COSTS: Parts and equipment costs
4. TOTAL PRICING: Final quote with options
5. VALUE PROPOSITION: Why choose our service
6. TIMELINE: Project completion schedule
7. WARRANTY TERMS: Warranty and guarantee information
8. PAYMENT OPTIONS: Payment terms and financing

Ensure competitive pricing while maintaining profitability.

QUOTE GENERATION:`

	prompt := prompts.NewPromptTemplate(template, []string{
		"service_type", "system_requirements", "property_details", "customer_type",
		"timeline", "labor_requirements", "material_costs", "equipment_needed",
		"complexity_level", "seasonal_factors", "profit_margins",
		"competition_pricing", "customer_budget",
	})

	return chains.NewLLMChain(h.llm, prompt), nil
}

// 📅 Scheduling Agent - Smart Scheduling Optimization
func (h *HVACAgentChains) createSchedulingAgent() (*chains.LLMChain, error) {
	template := `You are an intelligent scheduling optimization specialist for HVAC services.
Optimize technician schedules for maximum efficiency and customer satisfaction.

SCHEDULING REQUEST:
- Service Type: {service_type}
- Priority Level: {priority_level}
- Estimated Duration: {estimated_duration}
- Customer Preferences: {customer_preferences}
- Location: {location}

RESOURCE AVAILABILITY:
- Available Technicians: {available_technicians}
- Technician Skills: {technician_skills}
- Equipment Availability: {equipment_availability}
- Vehicle Assignments: {vehicle_assignments}

CONSTRAINTS:
- Travel Time: {travel_time}
- Working Hours: {working_hours}
- Emergency Slots: {emergency_slots}
- Existing Schedule: {existing_schedule}

Optimize scheduling:
1. OPTIMAL TIME SLOT: Best available time
2. TECHNICIAN ASSIGNMENT: Most suitable technician
3. ROUTE OPTIMIZATION: Efficient travel routing
4. BUFFER TIME: Appropriate time buffers
5. BACKUP PLANS: Alternative scheduling options
6. CUSTOMER NOTIFICATION: Communication timeline
7. RESOURCE ALLOCATION: Equipment and vehicle assignment
8. EFFICIENCY METRICS: Schedule optimization score

Maximize efficiency while ensuring quality service.

SCHEDULING OPTIMIZATION:`

	prompt := prompts.NewPromptTemplate(template, []string{
		"service_type", "priority_level", "estimated_duration", "customer_preferences",
		"location", "available_technicians", "technician_skills", "equipment_availability",
		"vehicle_assignments", "travel_time", "working_hours", "emergency_slots",
		"existing_schedule",
	})

	return chains.NewLLMChain(h.llm, prompt), nil
}

// 🔧 DiagnoseEquipment - Execute diagnostic analysis
func (h *HVACAgentChains) DiagnoseEquipment(ctx context.Context, req *DiagnosticRequest) (*DiagnosticResult, error) {
	h.log.WithContext(ctx).Info("🔧 Executing equipment diagnostic analysis")

	input := map[string]interface{}{
		"system_type":       req.SystemType,
		"model":            req.Model,
		"age":              req.Age,
		"last_maintenance": req.LastMaintenance,
		"customer_issue":   req.CustomerIssue,
		"symptoms":         req.Symptoms,
		"temperature":      req.Temperature,
		"humidity":         req.Humidity,
		"season":           req.Season,
	}

	result, err := chains.Run(ctx, h.DiagnosticAgent, input)
	if err != nil {
		return nil, fmt.Errorf("diagnostic agent execution failed: %w", err)
	}

	return h.parseDiagnosticResult(result), nil
}

// 🔮 PredictMaintenance - Execute predictive maintenance analysis
func (h *HVACAgentChains) PredictMaintenance(ctx context.Context, req *MaintenanceRequest) (*MaintenanceResult, error) {
	h.log.WithContext(ctx).Info("🔮 Executing predictive maintenance analysis")

	input := map[string]interface{}{
		"system_type":          req.SystemType,
		"install_date":         req.InstallDate,
		"usage_hours":          req.UsageHours,
		"performance_metrics":  req.PerformanceMetrics,
		"historical_issues":    req.HistoricalIssues,
		"environmental_factors": req.EnvironmentalFactors,
		"last_service":         req.LastService,
		"current_performance":  req.CurrentPerformance,
		"sensor_readings":      req.SensorReadings,
		"customer_type":        req.CustomerType,
		"service_contract":     req.ServiceContract,
		"budget_constraints":   req.BudgetConstraints,
	}

	result, err := chains.Run(ctx, h.MaintenanceAgent, input)
	if err != nil {
		return nil, fmt.Errorf("maintenance agent execution failed: %w", err)
	}

	return h.parseMaintenanceResult(result), nil
}

// 💬 GenerateCustomerCommunication - Create customer communications
func (h *HVACAgentChains) GenerateCustomerCommunication(ctx context.Context, req *CustomerCommRequest) (*CustomerCommResult, error) {
	h.log.WithContext(ctx).Info("💬 Generating customer communication")

	input := map[string]interface{}{
		"customer_name":            req.CustomerName,
		"customer_type":            req.CustomerType,
		"customer_history":         req.CustomerHistory,
		"communication_preferences": req.CommunicationPreferences,
		"previous_issues":          req.PreviousIssues,
		"communication_purpose":    req.CommunicationPurpose,
		"urgency_level":           req.UrgencyLevel,
		"technical_details":       req.TechnicalDetails,
		"service_status":          req.ServiceStatus,
		"company_policy":          req.CompanyPolicy,
		"service_level":           req.ServiceLevel,
		"pricing_info":            req.PricingInfo,
	}

	result, err := chains.Run(ctx, h.CustomerAgent, input)
	if err != nil {
		return nil, fmt.Errorf("customer agent execution failed: %w", err)
	}

	return h.parseCustomerCommResult(result), nil
}

// Helper methods for parsing agent results
func (h *HVACAgentChains) parseDiagnosticResult(result interface{}) *DiagnosticResult {
	resultStr := fmt.Sprintf("%v", result)

	return &DiagnosticResult{
		DiagnosisID:      fmt.Sprintf("diag_%d", time.Now().Unix()),
		PrimaryDiagnosis: h.extractPrimaryDiagnosis(resultStr),
		DiagnosticSteps:  h.extractDiagnosticSteps(resultStr),
		SafetyWarnings:   h.extractSafetyWarnings(resultStr),
		PartsNeeded:      h.extractPartsNeeded(resultStr),
		UrgencyLevel:     h.extractUrgencyLevelFromText(resultStr),
		RepairTime:       h.extractRepairTime(resultStr),
		CostEstimate:     h.extractCostEstimate(resultStr),
		PreventiveMeasures: h.extractPreventiveMeasures(resultStr),
		Confidence:       h.calculateConfidence(resultStr),
		ProcessedAt:      time.Now(),
		RawResult:        resultStr,
	}
}

func (h *HVACAgentChains) parseMaintenanceResult(result interface{}) *MaintenanceResult {
	resultStr := fmt.Sprintf("%v", result)

	return &MaintenanceResult{
		MaintenanceID:        fmt.Sprintf("maint_%d", time.Now().Unix()),
		NextServiceDate:      h.extractNextServiceDate(resultStr),
		MaintenanceType:      h.extractMaintenanceType(resultStr),
		PriorityTasks:        h.extractPriorityTasks(resultStr),
		PartsToOrder:         h.extractPartsToOrder(resultStr),
		FailurePredictions:   h.extractFailurePredictions(resultStr),
		CostOptimization:     h.extractCostOptimization(resultStr),
		PerformanceImprovements: h.extractPerformanceImprovements(resultStr),
		SeasonalAdjustments:  h.extractSeasonalAdjustments(resultStr),
		Confidence:           h.calculateConfidence(resultStr),
		ProcessedAt:          time.Now(),
		RawResult:            resultStr,
	}
}

func (h *HVACAgentChains) parseCustomerCommResult(result interface{}) *CustomerCommResult {
	resultStr := fmt.Sprintf("%v", result)

	return &CustomerCommResult{
		CommunicationID: fmt.Sprintf("comm_%d", time.Now().Unix()),
		SubjectLine:     h.extractSubjectLine(resultStr),
		Greeting:        h.extractGreeting(resultStr),
		MainMessage:     h.extractMainMessage(resultStr),
		TechnicalExplanation: h.extractTechnicalExplanation(resultStr),
		NextSteps:       h.extractNextSteps(resultStr),
		Timeline:        h.extractTimeline(resultStr),
		ContactInfo:     h.extractContactInfo(resultStr),
		Closing:         h.extractClosing(resultStr),
		Tone:            h.extractTone(resultStr),
		ProcessedAt:     time.Now(),
		RawResult:       resultStr,
	}
}

// Extraction helper methods (simplified implementations)
func (h *HVACAgentChains) extractPrimaryDiagnosis(text string) string {
	lines := strings.Split(text, "\n")
	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), "diagnosis") {
			return strings.TrimSpace(line)
		}
	}
	return "Diagnosis pending analysis"
}

func (h *HVACAgentChains) extractDiagnosticSteps(text string) []string {
	var steps []string
	lines := strings.Split(text, "\n")

	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), "step") {
			steps = append(steps, strings.TrimSpace(line))
		}
	}

	if len(steps) == 0 {
		steps = append(steps, "Diagnostic steps being generated")
	}

	return steps
}

func (h *HVACAgentChains) calculateConfidence(text string) float64 {
	// Simple confidence calculation based on text analysis
	// In production, use more sophisticated methods
	if len(text) > 500 {
		return 0.85
	} else if len(text) > 200 {
		return 0.70
	}
	return 0.60
}

// Additional extraction methods would be implemented here...
func (h *HVACAgentChains) extractSafetyWarnings(text string) []string {
	return []string{"Safety analysis in progress"}
}

func (h *HVACAgentChains) extractPartsNeeded(text string) []string {
	return []string{"Parts analysis in progress"}
}

func (h *HVACAgentChains) extractRepairTime(text string) string {
	return "Time estimate in progress"
}

func (h *HVACAgentChains) extractCostEstimate(text string) string {
	return "Cost estimate in progress"
}

func (h *HVACAgentChains) extractPreventiveMeasures(text string) []string {
	return []string{"Preventive measures being analyzed"}
}

func (h *HVACAgentChains) extractNextServiceDate(text string) time.Time {
	return time.Now().AddDate(0, 3, 0) // Default 3 months
}

func (h *HVACAgentChains) extractMaintenanceType(text string) string {
	return "Routine maintenance"
}

func (h *HVACAgentChains) extractPriorityTasks(text string) []string {
	return []string{"Priority tasks being analyzed"}
}

func (h *HVACAgentChains) extractPartsToOrder(text string) []string {
	return []string{"Parts analysis in progress"}
}

func (h *HVACAgentChains) extractFailurePredictions(text string) []string {
	return []string{"Failure predictions being generated"}
}

func (h *HVACAgentChains) extractCostOptimization(text string) []string {
	return []string{"Cost optimization strategies being analyzed"}
}

func (h *HVACAgentChains) extractPerformanceImprovements(text string) []string {
	return []string{"Performance improvements being identified"}
}

func (h *HVACAgentChains) extractSeasonalAdjustments(text string) []string {
	return []string{"Seasonal adjustments being calculated"}
}

func (h *HVACAgentChains) extractSubjectLine(text string) string {
	return "Professional HVAC Service Communication"
}

func (h *HVACAgentChains) extractGreeting(text string) string {
	return "Dear Valued Customer"
}

func (h *HVACAgentChains) extractMainMessage(text string) string {
	return "Message content being generated"
}

func (h *HVACAgentChains) extractTechnicalExplanation(text string) string {
	return "Technical explanation being prepared"
}

func (h *HVACAgentChains) extractNextSteps(text string) []string {
	return []string{"Next steps being determined"}
}

func (h *HVACAgentChains) extractTimeline(text string) string {
	return "Timeline being calculated"
}

func (h *HVACAgentChains) extractContactInfo(text string) string {
	return "Contact information being prepared"
}

func (h *HVACAgentChains) extractClosing(text string) string {
	return "Professional closing being generated"
}

func (h *HVACAgentChains) extractTone(text string) string {
	return "Professional and empathetic"
}

func (h *HVACAgentChains) extractUrgencyLevelFromText(text string) string {
	lowerText := strings.ToLower(text)

	if strings.Contains(lowerText, "emergency") || strings.Contains(lowerText, "urgent") {
		return "emergency"
	}
	if strings.Contains(lowerText, "high") {
		return "high"
	}
	if strings.Contains(lowerText, "medium") {
		return "medium"
	}

	return "low"
}
