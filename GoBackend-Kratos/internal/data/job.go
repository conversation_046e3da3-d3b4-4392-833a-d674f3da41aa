package data

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gobackend-hvac-kratos/internal/biz"
)

// Job represents the database model
type Job struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CustomerID  int64     `gorm:"not null;index" json:"customer_id"`
	Title       string    `gorm:"not null" json:"title"`
	Description string    `json:"description"`
	Status      string    `gorm:"not null;index" json:"status"`
	Priority    string    `gorm:"not null" json:"priority"`
	ScheduledAt time.Time `json:"scheduled_at"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// JobRepo implements the job repository
type JobRepo struct {
	data *Data
	log  *log.Helper
}

// NewJobRepo creates a new job repository
func NewJobRepo(data *Data, logger log.Logger) biz.JobRepo {
	return &JobRepo{
		data: data,
		log:  log.<PERSON>elper(logger),
	}
}

// C<PERSON><PERSON>ob creates a new job in the database
func (r *JobRepo) CreateJob(ctx context.Context, job *biz.Job) (*biz.Job, error) {
	dbJob := &Job{
		CustomerID:  job.CustomerID,
		Title:       job.Title,
		Description: job.Description,
		Status:      job.Status,
		Priority:    job.Priority,
		ScheduledAt: job.ScheduledAt,
	}
	
	if err := r.data.db.WithContext(ctx).Create(dbJob).Error; err != nil {
		return nil, err
	}
	
	return r.convertToBiz(dbJob), nil
}// GetJob retrieves a job by ID
func (r *JobRepo) GetJob(ctx context.Context, id int64) (*biz.Job, error) {
	var job Job
	if err := r.data.db.WithContext(ctx).First(&job, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, biz.ErrJobNotFound
		}
		return nil, err
	}
	
	return r.convertToBiz(&job), nil
}

// ListJobs retrieves jobs with filtering and pagination
func (r *JobRepo) ListJobs(ctx context.Context, page, pageSize int32, customerID int64, status string) ([]*biz.Job, int32, error) {
	var jobs []Job
	var total int64
	
	query := r.data.db.WithContext(ctx).Model(&Job{})
	
	// Apply filters
	if customerID > 0 {
		query = query.Where("customer_id = ?", customerID)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	
	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// Get paginated results
	offset := (page - 1) * pageSize
	if err := query.
		Offset(int(offset)).
		Limit(int(pageSize)).
		Order("created_at DESC").
		Find(&jobs).Error; err != nil {
		return nil, 0, err
	}
	
	// Convert to business entities
	bizJobs := make([]*biz.Job, len(jobs))
	for i, job := range jobs {
		bizJobs[i] = r.convertToBiz(&job)
	}
	
	return bizJobs, int32(total), nil
}

// UpdateJob updates an existing job
func (r *JobRepo) UpdateJob(ctx context.Context, job *biz.Job) (*biz.Job, error) {
	dbJob := &Job{
		ID:          job.ID,
		CustomerID:  job.CustomerID,
		Title:       job.Title,
		Description: job.Description,
		Status:      job.Status,
		Priority:    job.Priority,
		ScheduledAt: job.ScheduledAt,
	}
	
	if err := r.data.db.WithContext(ctx).Save(dbJob).Error; err != nil {
		return nil, err
	}
	
	return r.convertToBiz(dbJob), nil
}// DeleteJob deletes a job by ID
func (r *JobRepo) DeleteJob(ctx context.Context, id int64) error {
	return r.data.db.WithContext(ctx).Delete(&Job{}, id).Error
}

// convertToBiz converts database model to business entity
func (r *JobRepo) convertToBiz(job *Job) *biz.Job {
	return &biz.Job{
		ID:          job.ID,
		CustomerID:  job.CustomerID,
		Title:       job.Title,
		Description: job.Description,
		Status:      job.Status,
		Priority:    job.Priority,
		ScheduledAt: job.ScheduledAt,
		CreatedAt:   job.CreatedAt,
		UpdatedAt:   job.UpdatedAt,
	}
}