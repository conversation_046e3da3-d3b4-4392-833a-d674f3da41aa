package data

import (
	"context"
	"encoding/json"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/biz"
)

// ⚡ Workflow Repository Implementation
// GoBackend-Kratos HVAC CRM System

// workflowRepo implements the workflow repository interface
type workflowRepo struct {
	data *Data
	log  *log.Helper
}

// NewWorkflowRepo creates a new workflow repository
func NewWorkflowRepo(data *Data, logger log.Logger) biz.WorkflowRepo {
	return &workflowRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// ============================================================================
// ⚡ RULE MANAGEMENT
// ============================================================================

// CreateWorkflowRule creates a new workflow rule
func (r *workflowRepo) CreateWorkflowRule(ctx context.Context, rule *biz.WorkflowRule) error {
	r.log.WithContext(ctx).Infof("⚡ Creating workflow rule: %s", rule.RuleName)

	// Convert conditions and actions to JSON
	conditionsJSON, err := json.Marshal(rule.TriggerConditions)
	if err != nil {
		return err
	}

	actionsJSON, err := json.Marshal(rule.Actions)
	if err != nil {
		return err
	}

	// Create database record
	dbRule := map[string]interface{}{
		"rule_name":          rule.RuleName,
		"description":        rule.Description,
		"trigger_type":       rule.TriggerType,
		"trigger_conditions": conditionsJSON,
		"actions":            actionsJSON,
		"priority":           rule.Priority,
		"is_active":          rule.IsActive,
		"created_by":         rule.CreatedBy,
		"created_at":         rule.CreatedAt,
		"updated_at":         rule.UpdatedAt,
	}

	err = r.data.db.WithContext(ctx).Table("workflow_rules").Create(&dbRule).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to create workflow rule: %v", err)
		return err
	}

	return nil
}

// GetWorkflowRules returns workflow rules with optional filtering
func (r *workflowRepo) GetWorkflowRules(ctx context.Context, triggerType string, isActive *bool) ([]*biz.WorkflowRule, error) {
	r.log.WithContext(ctx).Infof("⚡ Fetching workflow rules (type: %s, active: %v)", triggerType, isActive)

	query := r.data.db.WithContext(ctx).Table("workflow_rules")

	if triggerType != "" {
		query = query.Where("trigger_type = ?", triggerType)
	}

	if isActive != nil {
		query = query.Where("is_active = ?", *isActive)
	}

	var dbRules []map[string]interface{}
	err := query.Order("priority DESC, created_at ASC").Find(&dbRules).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to fetch workflow rules: %v", err)
		return nil, err
	}

	// Convert to business entities
	var rules []*biz.WorkflowRule
	for _, dbRule := range dbRules {
		rule, err := r.convertDBRuleToBiz(dbRule)
		if err != nil {
			r.log.WithContext(ctx).Errorf("Failed to convert rule: %v", err)
			continue
		}
		rules = append(rules, rule)
	}

	return rules, nil
}

// UpdateWorkflowRule updates a workflow rule
func (r *workflowRepo) UpdateWorkflowRule(ctx context.Context, rule *biz.WorkflowRule) error {
	r.log.WithContext(ctx).Infof("⚡ Updating workflow rule: %s", rule.RuleName)

	// Convert conditions and actions to JSON
	conditionsJSON, err := json.Marshal(rule.TriggerConditions)
	if err != nil {
		return err
	}

	actionsJSON, err := json.Marshal(rule.Actions)
	if err != nil {
		return err
	}

	updates := map[string]interface{}{
		"rule_name":          rule.RuleName,
		"description":        rule.Description,
		"trigger_conditions": conditionsJSON,
		"actions":            actionsJSON,
		"priority":           rule.Priority,
		"is_active":          rule.IsActive,
		"updated_at":         time.Now(),
	}

	err = r.data.db.WithContext(ctx).Table("workflow_rules").Where("id = ?", rule.ID).Updates(updates).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to update workflow rule: %v", err)
		return err
	}

	return nil
}

// DeleteWorkflowRule deletes a workflow rule
func (r *workflowRepo) DeleteWorkflowRule(ctx context.Context, ruleID uint) error {
	r.log.WithContext(ctx).Infof("⚡ Deleting workflow rule ID: %d", ruleID)

	err := r.data.db.WithContext(ctx).Table("workflow_rules").Where("id = ?", ruleID).Delete(nil).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to delete workflow rule: %v", err)
		return err
	}

	return nil
}

// ============================================================================
// ⚡ EXECUTION MANAGEMENT
// ============================================================================

// CreateWorkflowExecution creates a new workflow execution
func (r *workflowRepo) CreateWorkflowExecution(ctx context.Context, execution *biz.WorkflowExecution) error {
	r.log.WithContext(ctx).Infof("⚡ Creating workflow execution for rule ID: %d", execution.WorkflowRuleID)

	// Convert results to JSON
	resultsJSON, err := json.Marshal(execution.Results)
	if err != nil {
		return err
	}

	actionsJSON, err := json.Marshal(execution.ActionsExecuted)
	if err != nil {
		return err
	}

	metadataJSON, err := json.Marshal(execution.Metadata)
	if err != nil {
		return err
	}

	dbExecution := map[string]interface{}{
		"workflow_rule_id":    execution.WorkflowRuleID,
		"trigger_entity_id":   execution.TriggerEntityID,
		"trigger_entity_type": execution.TriggerEntityType,
		"execution_status":    execution.ExecutionStatus,
		"started_at":          execution.StartedAt,
		"completed_at":        execution.CompletedAt,
		"execution_time_ms":   execution.ExecutionTime,
		"actions_executed":    actionsJSON,
		"results":             resultsJSON,
		"error_message":       execution.ErrorMessage,
		"metadata":            metadataJSON,
	}

	err = r.data.db.WithContext(ctx).Table("workflow_executions").Create(&dbExecution).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to create workflow execution: %v", err)
		return err
	}

	return nil
}

// GetWorkflowExecutions returns workflow executions with filtering and pagination
func (r *workflowRepo) GetWorkflowExecutions(ctx context.Context, ruleID *uint, status string, startDate, endDate time.Time, page, pageSize int) ([]*biz.WorkflowExecution, int, error) {
	r.log.WithContext(ctx).Info("⚡ Fetching workflow executions")

	query := r.data.db.WithContext(ctx).Table("workflow_executions")

	if ruleID != nil {
		query = query.Where("workflow_rule_id = ?", *ruleID)
	}

	if status != "" {
		query = query.Where("execution_status = ?", status)
	}

	if !startDate.IsZero() {
		query = query.Where("started_at >= ?", startDate)
	}

	if !endDate.IsZero() {
		query = query.Where("started_at <= ?", endDate)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get paginated results
	var dbExecutions []map[string]interface{}
	offset := (page - 1) * pageSize
	err := query.Order("started_at DESC").Offset(offset).Limit(pageSize).Find(&dbExecutions).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to fetch workflow executions: %v", err)
		return nil, 0, err
	}

	// Convert to business entities
	var executions []*biz.WorkflowExecution
	for _, dbExecution := range dbExecutions {
		execution, err := r.convertDBExecutionToBiz(dbExecution)
		if err != nil {
			r.log.WithContext(ctx).Errorf("Failed to convert execution: %v", err)
			continue
		}
		executions = append(executions, execution)
	}

	return executions, int(total), nil
}

// UpdateWorkflowExecution updates a workflow execution
func (r *workflowRepo) UpdateWorkflowExecution(ctx context.Context, execution *biz.WorkflowExecution) error {
	r.log.WithContext(ctx).Infof("⚡ Updating workflow execution ID: %d", execution.ID)

	// Convert results to JSON
	resultsJSON, err := json.Marshal(execution.Results)
	if err != nil {
		return err
	}

	actionsJSON, err := json.Marshal(execution.ActionsExecuted)
	if err != nil {
		return err
	}

	updates := map[string]interface{}{
		"execution_status": execution.ExecutionStatus,
		"completed_at":     execution.CompletedAt,
		"execution_time_ms": execution.ExecutionTime,
		"actions_executed": actionsJSON,
		"results":          resultsJSON,
		"error_message":    execution.ErrorMessage,
	}

	err = r.data.db.WithContext(ctx).Table("workflow_executions").Where("id = ?", execution.ID).Updates(updates).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to update workflow execution: %v", err)
		return err
	}

	return nil
}

// ============================================================================
// ⚡ TEMPLATE MANAGEMENT
// ============================================================================

// GetWorkflowTemplates returns available workflow templates
func (r *workflowRepo) GetWorkflowTemplates(ctx context.Context, category string) ([]*biz.WorkflowTemplate, error) {
	r.log.WithContext(ctx).Infof("⚡ Fetching workflow templates (category: %s)", category)

	query := r.data.db.WithContext(ctx).Table("workflow_templates")
	if category != "" {
		query = query.Where("category = ?", category)
	}

	var dbTemplates []map[string]interface{}
	err := query.Order("usage_count DESC, created_at DESC").Find(&dbTemplates).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to fetch workflow templates: %v", err)
		return nil, err
	}

	// Convert to business entities
	var templates []*biz.WorkflowTemplate
	for _, dbTemplate := range dbTemplates {
		template, err := r.convertDBTemplateToBiz(dbTemplate)
		if err != nil {
			r.log.WithContext(ctx).Errorf("Failed to convert template: %v", err)
			continue
		}
		templates = append(templates, template)
	}

	return templates, nil
}

// CreateWorkflowFromTemplate creates a workflow rule from a template
func (r *workflowRepo) CreateWorkflowFromTemplate(ctx context.Context, templateID uint, ruleName string, customizations map[string]interface{}) (*biz.WorkflowRule, error) {
	r.log.WithContext(ctx).Infof("⚡ Creating workflow from template ID: %d", templateID)

	// Get template
	var dbTemplate map[string]interface{}
	err := r.data.db.WithContext(ctx).Table("workflow_templates").Where("id = ?", templateID).First(&dbTemplate).Error
	if err != nil {
		return nil, err
	}

	// Parse template
	var templateData map[string]interface{}
	if templateJSON, ok := dbTemplate["template"].(string); ok {
		err = json.Unmarshal([]byte(templateJSON), &templateData)
		if err != nil {
			return nil, err
		}
	}

	// Apply customizations
	for key, value := range customizations {
		templateData[key] = value
	}

	// Create workflow rule
	rule := &biz.WorkflowRule{
		RuleName:    ruleName,
		Description: "Created from template: " + dbTemplate["template_name"].(string),
		TriggerType: templateData["trigger_type"].(string),
		Priority:    5,
		IsActive:    true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Set conditions and actions
	if conditions, ok := templateData["conditions"]; ok {
		if conditionsBytes, err := json.Marshal(conditions); err == nil {
			json.Unmarshal(conditionsBytes, &rule.TriggerConditions)
		}
	}

	if actions, ok := templateData["actions"]; ok {
		if actionsBytes, err := json.Marshal(actions); err == nil {
			json.Unmarshal(actionsBytes, &rule.Actions)
		}
	}

	err = r.CreateWorkflowRule(ctx, rule)
	if err != nil {
		return nil, err
	}

	// Update template usage count
	r.data.db.WithContext(ctx).Table("workflow_templates").Where("id = ?", templateID).
		Update("usage_count", gorm.Expr("usage_count + 1"))

	return rule, nil
}

// ============================================================================
// ⚡ STATISTICS
// ============================================================================

// UpdateRuleStatistics updates execution statistics for a workflow rule
func (r *workflowRepo) UpdateRuleStatistics(ctx context.Context, ruleID uint, success bool) error {
	updates := map[string]interface{}{
		"execution_count": gorm.Expr("execution_count + 1"),
		"last_executed":   time.Now(),
	}

	if success {
		updates["success_count"] = gorm.Expr("success_count + 1")
	}

	err := r.data.db.WithContext(ctx).Table("workflow_rules").Where("id = ?", ruleID).Updates(updates).Error
	if err != nil {
		r.log.WithContext(ctx).Errorf("Failed to update rule statistics: %v", err)
		return err
	}

	return nil
}

// ============================================================================
// ⚡ HELPER METHODS
// ============================================================================

// convertDBRuleToBiz converts database rule to business entity
func (r *workflowRepo) convertDBRuleToBiz(dbRule map[string]interface{}) (*biz.WorkflowRule, error) {
	rule := &biz.WorkflowRule{
		ID:             uint(dbRule["id"].(int64)),
		RuleName:       dbRule["rule_name"].(string),
		Description:    dbRule["description"].(string),
		TriggerType:    dbRule["trigger_type"].(string),
		Priority:       int(dbRule["priority"].(int64)),
		IsActive:       dbRule["is_active"].(bool),
		ExecutionCount: int(dbRule["execution_count"].(int64)),
		SuccessCount:   int(dbRule["success_count"].(int64)),
		CreatedBy:      dbRule["created_by"].(string),
		CreatedAt:      dbRule["created_at"].(time.Time),
		UpdatedAt:      dbRule["updated_at"].(time.Time),
	}

	// Parse conditions
	if conditionsJSON, ok := dbRule["trigger_conditions"].(string); ok {
		json.Unmarshal([]byte(conditionsJSON), &rule.TriggerConditions)
	}

	// Parse actions
	if actionsJSON, ok := dbRule["actions"].(string); ok {
		json.Unmarshal([]byte(actionsJSON), &rule.Actions)
	}

	// Parse last executed
	if lastExecuted, ok := dbRule["last_executed"].(time.Time); ok {
		rule.LastExecuted = &lastExecuted
	}

	return rule, nil
}

// convertDBExecutionToBiz converts database execution to business entity
func (r *workflowRepo) convertDBExecutionToBiz(dbExecution map[string]interface{}) (*biz.WorkflowExecution, error) {
	execution := &biz.WorkflowExecution{
		ID:                uint(dbExecution["id"].(int64)),
		WorkflowRuleID:    uint(dbExecution["workflow_rule_id"].(int64)),
		TriggerEntityID:   uint(dbExecution["trigger_entity_id"].(int64)),
		TriggerEntityType: dbExecution["trigger_entity_type"].(string),
		ExecutionStatus:   dbExecution["execution_status"].(string),
		StartedAt:         dbExecution["started_at"].(time.Time),
		ErrorMessage:      dbExecution["error_message"].(string),
	}

	// Parse completed at
	if completedAt, ok := dbExecution["completed_at"].(time.Time); ok {
		execution.CompletedAt = &completedAt
	}

	// Parse execution time
	if executionTimeMs, ok := dbExecution["execution_time_ms"].(int64); ok {
		duration := time.Duration(executionTimeMs) * time.Millisecond
		execution.ExecutionTime = &duration
	}

	// Parse actions executed
	if actionsJSON, ok := dbExecution["actions_executed"].(string); ok {
		json.Unmarshal([]byte(actionsJSON), &execution.ActionsExecuted)
	}

	// Parse results
	if resultsJSON, ok := dbExecution["results"].(string); ok {
		json.Unmarshal([]byte(resultsJSON), &execution.Results)
	}

	// Parse metadata
	if metadataJSON, ok := dbExecution["metadata"].(string); ok {
		json.Unmarshal([]byte(metadataJSON), &execution.Metadata)
	}

	return execution, nil
}

// convertDBTemplateToBiz converts database template to business entity
func (r *workflowRepo) convertDBTemplateToBiz(dbTemplate map[string]interface{}) (*biz.WorkflowTemplate, error) {
	template := &biz.WorkflowTemplate{
		ID:           uint(dbTemplate["id"].(int64)),
		TemplateName: dbTemplate["template_name"].(string),
		Category:     dbTemplate["category"].(string),
		Description:  dbTemplate["description"].(string),
		IsPublic:     dbTemplate["is_public"].(bool),
		UsageCount:   int(dbTemplate["usage_count"].(int64)),
		CreatedBy:    dbTemplate["created_by"].(string),
		CreatedAt:    dbTemplate["created_at"].(time.Time),
	}

	// Parse template
	if templateJSON, ok := dbTemplate["template"].(string); ok {
		json.Unmarshal([]byte(templateJSON), &template.Template)
	}

	return template, nil
}