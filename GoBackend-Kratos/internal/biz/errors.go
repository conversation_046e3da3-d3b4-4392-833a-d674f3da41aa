package biz

import (
	"github.com/go-kratos/kratos/v2/errors"
)

// Customer errors
var (
	ErrCustomerNotFound      = errors.NotFound("CUSTOMER_NOT_FOUND", "customer not found")
	ErrCustomerNameRequired  = errors.BadRequest("CUSTOMER_NAME_REQUIRED", "customer name is required")
	ErrCustomerEmailRequired = errors.BadRequest("CUSTOMER_EMAIL_REQUIRED", "customer email is required")
	ErrInvalidCustomerID     = errors.BadRequest("INVALID_CUSTOMER_ID", "invalid customer ID")
)

// Job errors
var (
	ErrJobNotFound         = errors.NotFound("JOB_NOT_FOUND", "job not found")
	ErrJobTitleRequired    = errors.BadRequest("JOB_TITLE_REQUIRED", "job title is required")
	ErrInvalidJobID        = errors.BadRequest("INVALID_JOB_ID", "invalid job ID")
	ErrInvalidJobStatus    = errors.BadRequest("INVALID_JOB_STATUS", "invalid job status")
	ErrInvalidJobPriority  = errors.BadRequest("INVALID_JOB_PRIORITY", "invalid job priority")
)

// AI errors
var (
	ErrAIModelNotFound     = errors.NotFound("AI_MODEL_NOT_FOUND", "AI model not found")
	ErrAIModelUnavailable  = errors.ServiceUnavailable("AI_MODEL_UNAVAILABLE", "AI model is unavailable")
	ErrInvalidAIRequest    = errors.BadRequest("INVALID_AI_REQUEST", "invalid AI request")
	ErrAIProcessingFailed  = errors.InternalServer("AI_PROCESSING_FAILED", "AI processing failed")
)

// Email errors
var (
	ErrEmailSendFailed       = errors.InternalServer("EMAIL_SEND_FAILED", "failed to send email")
	ErrInvalidEmailAddress   = errors.BadRequest("INVALID_EMAIL_ADDRESS", "invalid email address")
	ErrEmailNotFound         = errors.NotFound("EMAIL_NOT_FOUND", "email not found")
	ErrEmailRequired         = errors.BadRequest("EMAIL_REQUIRED", "email is required")
	ErrEmailSubjectRequired  = errors.BadRequest("EMAIL_SUBJECT_REQUIRED", "email subject is required")
	ErrEmailFromRequired     = errors.BadRequest("EMAIL_FROM_REQUIRED", "email from address is required")
	ErrInvalidEmailID        = errors.BadRequest("INVALID_EMAIL_ID", "invalid email ID")
	ErrEmailAnalysisNotFound = errors.NotFound("EMAIL_ANALYSIS_NOT_FOUND", "email analysis not found")
	ErrEmailAnalysisFailed   = errors.InternalServer("EMAIL_ANALYSIS_FAILED", "email analysis failed")
	ErrAttachmentNotFound    = errors.NotFound("ATTACHMENT_NOT_FOUND", "attachment not found")
)

// MCP errors
var (
	ErrMCPServerUnavailable = errors.ServiceUnavailable("MCP_SERVER_UNAVAILABLE", "MCP server is unavailable")
	ErrMCPToolNotFound      = errors.NotFound("MCP_TOOL_NOT_FOUND", "MCP tool not found")
	ErrMCPInvalidRequest    = errors.BadRequest("MCP_INVALID_REQUEST", "invalid MCP request")
)