package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// Job represents a HVAC job entity
type Job struct {
	ID          int64     `json:"id"`
	CustomerID  int64     `json:"customer_id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Status      string    `json:"status"`
	Priority    string    `json:"priority"`
	ScheduledAt time.Time `json:"scheduled_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// JobRepo defines the interface for job data operations
type JobRepo interface {
	CreateJob(ctx context.Context, job *Job) (*Job, error)
	GetJob(ctx context.Context, id int64) (*Job, error)
	ListJobs(ctx context.Context, page, pageSize int32, customerID int64, status string) ([]*Job, int32, error)
	UpdateJob(ctx context.Context, job *Job) (*Job, error)
	DeleteJob(ctx context.Context, id int64) error
}

// JobUsecase encapsulates job business logic
type JobUsecase struct {
	repo JobRepo
	log  *log.Helper
}

// NewJobUsecase creates a new job usecase
func NewJobUsecase(repo JobRepo, logger log.Logger) *JobUsecase {
	return &JobUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// Valid job statuses
var ValidJobStatuses = map[string]bool{
	"pending":     true,
	"scheduled":   true,
	"in_progress": true,
	"completed":   true,
	"cancelled":   true,
}

// Valid job priorities
var ValidJobPriorities = map[string]bool{
	"low":    true,
	"medium": true,
	"high":   true,
	"urgent": true,
} // CreateJob creates a new job with validation
func (uc *JobUsecase) CreateJob(ctx context.Context, job *Job) (*Job, error) {
	uc.log.WithContext(ctx).Infof("Creating job: %s", job.Title)

	// Business logic validation
	if job.Title == "" {
		return nil, ErrJobTitleRequired
	}
	if job.CustomerID <= 0 {
		return nil, ErrInvalidCustomerID
	}
	if job.Status != "" && !ValidJobStatuses[job.Status] {
		return nil, ErrInvalidJobStatus
	}
	if job.Priority != "" && !ValidJobPriorities[job.Priority] {
		return nil, ErrInvalidJobPriority
	}

	// Set defaults
	if job.Status == "" {
		job.Status = "pending"
	}
	if job.Priority == "" {
		job.Priority = "medium"
	}

	// Set timestamps
	now := time.Now()
	job.CreatedAt = now
	job.UpdatedAt = now

	return uc.repo.CreateJob(ctx, job)
}

// GetJob retrieves a job by ID
func (uc *JobUsecase) GetJob(ctx context.Context, id int64) (*Job, error) {
	uc.log.WithContext(ctx).Infof("Getting job: %d", id)

	if id <= 0 {
		return nil, ErrInvalidJobID
	}

	return uc.repo.GetJob(ctx, id)
}

// ListJobs retrieves jobs with filtering and pagination
func (uc *JobUsecase) ListJobs(ctx context.Context, page, pageSize int32, customerID int64, status string) ([]*Job, int32, error) {
	uc.log.WithContext(ctx).Infof("Listing jobs: page=%d, size=%d, customer=%d, status=%s", page, pageSize, customerID, status)

	// Validate pagination parameters
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	return uc.repo.ListJobs(ctx, page, pageSize, customerID, status)
}

// UpdateJob updates an existing job with validation
func (uc *JobUsecase) UpdateJob(ctx context.Context, job *Job) (*Job, error) {
	uc.log.WithContext(ctx).Infof("Updating job: %d", job.ID)

	if job.ID <= 0 {
		return nil, ErrInvalidJobID
	}
	if job.Status != "" && !ValidJobStatuses[job.Status] {
		return nil, ErrInvalidJobStatus
	}
	if job.Priority != "" && !ValidJobPriorities[job.Priority] {
		return nil, ErrInvalidJobPriority
	}

	// Update timestamp
	job.UpdatedAt = time.Now()

	return uc.repo.UpdateJob(ctx, job)
}

// DeleteJob deletes a job by ID
func (uc *JobUsecase) DeleteJob(ctx context.Context, id int64) error {
	uc.log.WithContext(ctx).Infof("Deleting job: %d", id)

	if id <= 0 {
		return ErrInvalidJobID
	}

	return uc.repo.DeleteJob(ctx, id)
}
