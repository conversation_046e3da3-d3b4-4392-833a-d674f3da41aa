package biz

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockWorkflowRepo is a mock implementation of WorkflowRepo
type MockWorkflowRepo struct {
	mock.Mock
}

func (m *MockWorkflowRepo) CreateWorkflowRule(ctx context.Context, rule *WorkflowRule) error {
	args := m.Called(ctx, rule)
	return args.Error(0)
}

func (m *MockWorkflowRepo) GetWorkflowRules(ctx context.Context, triggerType string, isActive *bool) ([]*WorkflowRule, error) {
	args := m.Called(ctx, triggerType, isActive)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*WorkflowRule), args.Error(1)
}

func (m *MockWorkflowRepo) UpdateWorkflowRule(ctx context.Context, rule *WorkflowRule) error {
	args := m.Called(ctx, rule)
	return args.Error(0)
}

func (m *MockWorkflowRepo) DeleteWorkflowRule(ctx context.Context, ruleID uint) error {
	args := m.Called(ctx, ruleID)
	return args.Error(0)
}

func (m *MockWorkflowRepo) CreateWorkflowExecution(ctx context.Context, execution *WorkflowExecution) error {
	args := m.Called(ctx, execution)
	return args.Error(0)
}

func (m *MockWorkflowRepo) GetWorkflowExecutions(ctx context.Context, ruleID *uint, status string, startDate, endDate time.Time, page, pageSize int) ([]*WorkflowExecution, int, error) {
	args := m.Called(ctx, ruleID, status, startDate, endDate, page, pageSize)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int), args.Error(2)
	}
	return args.Get(0).([]*WorkflowExecution), args.Get(1).(int), args.Error(2)
}

func (m *MockWorkflowRepo) UpdateWorkflowExecution(ctx context.Context, execution *WorkflowExecution) error {
	args := m.Called(ctx, execution)
	return args.Error(0)
}

func (m *MockWorkflowRepo) GetWorkflowTemplates(ctx context.Context, category string) ([]*WorkflowTemplate, error) {
	args := m.Called(ctx, category)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*WorkflowTemplate), args.Error(1)
}

func (m *MockWorkflowRepo) CreateWorkflowFromTemplate(ctx context.Context, templateID uint, ruleName string, customizations map[string]interface{}) (*WorkflowRule, error) {
	args := m.Called(ctx, templateID, ruleName, customizations)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*WorkflowRule), args.Error(1)
}

func (m *MockWorkflowRepo) UpdateRuleStatistics(ctx context.Context, ruleID uint, success bool) error {
	args := m.Called(ctx, ruleID, success)
	return args.Error(0)
}

func TestNewWorkflowUsecase(t *testing.T) {
	mockRepo := new(MockWorkflowRepo)
	uc := NewWorkflowUsecase(mockRepo, log.DefaultLogger)
	assert.NotNil(t, uc)
	assert.Equal(t, mockRepo, uc.repo)
}

func TestCreateWorkflowRule(t *testing.T) {
	mockRepo := new(MockWorkflowRepo)
	uc := NewWorkflowUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	rule := &WorkflowRule{RuleName: "Test Rule", TriggerType: "email_received"}
	mockRepo.On("CreateWorkflowRule", ctx, mock.AnythingOfType("*biz.WorkflowRule")).Return(nil).Once()
	err := uc.CreateWorkflowRule(ctx, rule)
	assert.NoError(t, err)
	assert.NotZero(t, rule.CreatedAt)
	assert.NotZero(t, rule.UpdatedAt)
	mockRepo.AssertExpectations(t)

	// Error case: Repo returns error
	mockRepo.On("CreateWorkflowRule", ctx, mock.AnythingOfType("*biz.WorkflowRule")).Return(errors.New("db error")).Once()
	err = uc.CreateWorkflowRule(ctx, rule)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestGetWorkflowRules(t *testing.T) {
	mockRepo := new(MockWorkflowRepo)
	uc := NewWorkflowUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	isActive := true
	expectedRules := []*WorkflowRule{{RuleName: "Rule 1"}, {RuleName: "Rule 2"}}
	mockRepo.On("GetWorkflowRules", ctx, "email_received", &isActive).Return(expectedRules, nil).Once()
	rules, err := uc.GetWorkflowRules(ctx, "email_received", &isActive)
	assert.NoError(t, err)
	assert.Equal(t, expectedRules, rules)
	mockRepo.AssertExpectations(t)

	// Error case: Repo returns error
	mockRepo.On("GetWorkflowRules", ctx, "email_received", &isActive).Return(nil, errors.New("db error")).Once()
	rules, err = uc.GetWorkflowRules(ctx, "email_received", &isActive)
	assert.Error(t, err)
	assert.Nil(t, rules)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestExecuteWorkflowsForTrigger(t *testing.T) {
	tests := []struct {
		name                    string
		triggerType             string
		entity                  interface{}
		entityID                uint
		mockRules               []*WorkflowRule
		mockGetRulesErr         error
		expectedResultsLen      int
		expectedErr             error
		expectedSuccess         bool
		expectedActionsExecuted int
		expectedActionSuccess   bool
		expectedActionError     string
	}{
		{
			name:        "Success case with two actions",
			triggerType: "customer_created",
			entity:      map[string]interface{}{"name": "John Doe"},
			entityID:    123,
			mockRules: []*WorkflowRule{
				{
					ID:          1,
					RuleName:    "Test Rule",
					TriggerType: "customer_created",
					IsActive:    true,
					Actions: []WorkflowAction{
						{Type: "email", Parameters: map[string]interface{}{"to": "<EMAIL>"}},
						{Type: "notification"},
					},
				},
			},
			expectedResultsLen:      1,
			expectedErr:             nil,
			expectedSuccess:         true,
			expectedActionsExecuted: 2,
			expectedActionSuccess:   true,
		},
		{
			name:               "Error case: GetWorkflowRules returns error",
			triggerType:        "customer_created",
			entity:             map[string]interface{}{"name": "John Doe"},
			entityID:           123,
			mockRules:          nil,
			mockGetRulesErr:    errors.New("db error"),
			expectedResultsLen: 0,
			expectedErr:        errors.New("db error"),
		},
		{
			name:        "Test case: Conditions not met",
			triggerType: "customer_created",
			entity:      map[string]interface{}{"name": "John Doe"},
			entityID:    123,
			mockRules: []*WorkflowRule{
				{
					ID:          2,
					RuleName:    "Conditional Rule",
					TriggerType: "customer_created",
					IsActive:    true,
					TriggerConditions: []WorkflowCondition{
						{Field: "name", Operator: "equals", Value: "Jane Doe"},
					},
					Actions: []WorkflowAction{
						{Type: "email"},
					},
				},
			},
			expectedResultsLen: 0,
			expectedErr:        nil,
		},
		{
			name:        "Test case: Action fails",
			triggerType: "customer_created",
			entity:      map[string]interface{}{"name": "John Doe"},
			entityID:    123,
			mockRules: []*WorkflowRule{
				{
					ID:          3,
					RuleName:    "Failing Action Rule",
					TriggerType: "customer_created",
					IsActive:    true,
					Actions: []WorkflowAction{
						{Type: "unknown_action"},
					},
				},
			},
			expectedResultsLen:      1,
			expectedErr:             nil,
			expectedSuccess:         false,
			expectedActionsExecuted: 1,
			expectedActionSuccess:   false,
			expectedActionError:     "Unknown action type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := new(MockWorkflowRepo)
			uc := NewWorkflowUsecase(mockRepo, log.DefaultLogger)
			ctx := context.Background()

			isActive := true
			mockRepo.On("GetWorkflowRules", ctx, tt.triggerType, &isActive).Return(tt.mockRules, tt.mockGetRulesErr).Once()

			if tt.expectedResultsLen > 0 {
				if tt.expectedSuccess {
					mockRepo.On("CreateWorkflowExecution", ctx, mock.AnythingOfType("*biz.WorkflowExecution")).Return(nil).Once()
					mockRepo.On("UpdateWorkflowExecution", ctx, mock.AnythingOfType("*biz.WorkflowExecution")).Return(nil).Once()
					mockRepo.On("UpdateRuleStatistics", ctx, mock.AnythingOfType("uint"), true).Return(nil).Once()
				} else {
					mockRepo.On("CreateWorkflowExecution", ctx, mock.AnythingOfType("*biz.WorkflowExecution")).Return(nil).Once()
					mockRepo.On("UpdateWorkflowExecution", ctx, mock.AnythingOfType("*biz.WorkflowExecution")).Return(nil).Once()
					mockRepo.On("UpdateRuleStatistics", ctx, mock.AnythingOfType("uint"), false).Return(nil).Once()
				}
			}

			results, err := uc.ExecuteWorkflowsForTrigger(ctx, tt.triggerType, tt.entity, tt.entityID)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedErr.Error())
				assert.Nil(t, results)
			} else {
				assert.NoError(t, err)
				assert.Len(t, results, tt.expectedResultsLen)
				if tt.expectedResultsLen > 0 {
					assert.Equal(t, tt.expectedSuccess, results[0].Success)
					assert.Len(t, results[0].ActionsExecuted, tt.expectedActionsExecuted)
					if tt.expectedActionsExecuted > 0 {
						assert.Equal(t, tt.expectedActionSuccess, results[0].ActionsExecuted[0].Success)
						if !tt.expectedActionSuccess {
							assert.Contains(t, results[0].ActionsExecuted[0].Error, tt.expectedActionError)
						}
					}
				}
			}
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestGetWorkflowTemplates(t *testing.T) {
	mockRepo := new(MockWorkflowRepo)
	uc := NewWorkflowUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	expectedTemplates := []*WorkflowTemplate{{TemplateName: "Template 1"}, {TemplateName: "Template 2"}}
	mockRepo.On("GetWorkflowTemplates", ctx, "general").Return(expectedTemplates, nil).Once()
	templates, err := uc.GetWorkflowTemplates(ctx, "general")
	assert.NoError(t, err)
	assert.Equal(t, expectedTemplates, templates)
	mockRepo.AssertExpectations(t)

	// Error case: Repo returns error
	mockRepo.On("GetWorkflowTemplates", ctx, "general").Return(nil, errors.New("db error")).Once()
	templates, err = uc.GetWorkflowTemplates(ctx, "general")
	assert.Error(t, err)
	assert.Nil(t, templates)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

func TestCreateWorkflowFromTemplate(t *testing.T) {
	mockRepo := new(MockWorkflowRepo)
	uc := NewWorkflowUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Success case
	templateID := uint(1)
	ruleName := "New Rule From Template"
	customizations := map[string]interface{}{"key": "value"}
	expectedRule := &WorkflowRule{ID: 10, RuleName: ruleName}
	mockRepo.On("CreateWorkflowFromTemplate", ctx, templateID, ruleName, customizations).Return(expectedRule, nil).Once()

	rule, err := uc.CreateWorkflowFromTemplate(ctx, templateID, ruleName, customizations)
	assert.NoError(t, err)
	assert.Equal(t, expectedRule, rule)
	mockRepo.AssertExpectations(t)

	// Error case: Repo returns error
	mockRepo.On("CreateWorkflowFromTemplate", ctx, templateID, ruleName, customizations).Return(nil, errors.New("db error")).Once()
	rule, err = uc.CreateWorkflowFromTemplate(ctx, templateID, ruleName, customizations)
	assert.Error(t, err)
	assert.Nil(t, rule)
	assert.Contains(t, err.Error(), "db error")
	mockRepo.AssertExpectations(t)
}

// Benchmarks
func BenchmarkExecuteWorkflowsForTrigger(b *testing.B) {
	mockRepo := new(MockWorkflowRepo)
	uc := NewWorkflowUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	rule := &WorkflowRule{
		ID:          1,
		RuleName:    "Benchmark Rule",
		TriggerType: "customer_created",
		IsActive:    true,
		Actions: []WorkflowAction{
			{Type: "email"},
			{Type: "notification"},
		},
	}
	isActive := true
	mockRepo.On("GetWorkflowRules", ctx, "customer_created", &isActive).Return([]*WorkflowRule{rule}, nil)
	mockRepo.On("CreateWorkflowExecution", ctx, mock.AnythingOfType("*biz.WorkflowExecution")).Return(nil)
	mockRepo.On("UpdateWorkflowExecution", ctx, mock.AnythingOfType("*biz.WorkflowExecution")).Return(nil)
	mockRepo.On("UpdateRuleStatistics", ctx, uint(1), true).Return(nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = uc.ExecuteWorkflowsForTrigger(ctx, "customer_created", map[string]interface{}{"name": "John Doe"}, 123)
	}
}
