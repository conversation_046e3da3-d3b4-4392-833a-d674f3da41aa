package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// Customer represents a HVAC customer entity
type Customer struct {
	ID        int64     `json:"id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	Address   string    `json:"address"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CustomerRepo defines the interface for customer data operations
type CustomerRepo interface {
	CreateCustomer(ctx context.Context, customer *Customer) (*Customer, error)
	GetCustomer(ctx context.Context, id int64) (*Customer, error)
	ListCustomers(ctx context.Context, page, pageSize int32) ([]*Customer, int32, error)
	UpdateCustomer(ctx context.Context, customer *Customer) (*Customer, error)
	DeleteCustomer(ctx context.Context, id int64) error
}

// CustomerUsecase encapsulates customer business logic
type CustomerUsecase struct {
	repo CustomerRepo
	log  *log.Helper
}

// NewCustomerUsecase creates a new customer usecase
func NewCustomerUsecase(repo CustomerRepo, logger log.Logger) *CustomerUsecase {
	return &CustomerUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// CreateCustomer creates a new customer with validation
func (uc *CustomerUsecase) CreateCustomer(ctx context.Context, customer *Customer) (*Customer, error) {
	uc.log.WithContext(ctx).Infof("Creating customer: %s", customer.Name)

	// Business logic validation
	if customer.Name == "" {
		return nil, ErrCustomerNameRequired
	}
	if customer.Email == "" {
		return nil, ErrCustomerEmailRequired
	}

	// Set timestamps
	now := time.Now()
	customer.CreatedAt = now
	customer.UpdatedAt = now

	return uc.repo.CreateCustomer(ctx, customer)
} // GetCustomer retrieves a customer by ID
func (uc *CustomerUsecase) GetCustomer(ctx context.Context, id int64) (*Customer, error) {
	uc.log.WithContext(ctx).Infof("Getting customer: %d", id)

	if id <= 0 {
		return nil, ErrInvalidCustomerID
	}

	return uc.repo.GetCustomer(ctx, id)
}

// ListCustomers retrieves customers with pagination
func (uc *CustomerUsecase) ListCustomers(ctx context.Context, page, pageSize int32) ([]*Customer, int32, error) {
	uc.log.WithContext(ctx).Infof("Listing customers: page=%d, size=%d", page, pageSize)

	// Validate pagination parameters
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	return uc.repo.ListCustomers(ctx, page, pageSize)
}

// UpdateCustomer updates an existing customer
func (uc *CustomerUsecase) UpdateCustomer(ctx context.Context, customer *Customer) (*Customer, error) {
	uc.log.WithContext(ctx).Infof("Updating customer: %d", customer.ID)

	if customer.ID <= 0 {
		return nil, ErrInvalidCustomerID
	}

	// Update timestamp
	customer.UpdatedAt = time.Now()

	return uc.repo.UpdateCustomer(ctx, customer)
}

// DeleteCustomer deletes a customer by ID
func (uc *CustomerUsecase) DeleteCustomer(ctx context.Context, id int64) error {
	uc.log.WithContext(ctx).Infof("Deleting customer: %d", id)

	if id <= 0 {
		return ErrInvalidCustomerID
	}

	return uc.repo.DeleteCustomer(ctx, id)
}
