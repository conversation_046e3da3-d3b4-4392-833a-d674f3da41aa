package server

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/mux"
	"go.uber.org/zap"

	aiv1 "gobackend-hvac-kratos/api/ai/v1"
	analyticsv1 "gobackend-hvac-kratos/api/analytics/v1"
	hvacv1 "gobackend-hvac-kratos/api/hvac/v1"
	workflowv1 "gobackend-hvac-kratos/api/workflow/v1"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/service"
)

// TRPCAdapter bridges gRPC services to tRPC-compatible HTTP endpoints
type TRPCAdapter struct {
	hvacService      *service.HVACService
	aiService        *service.AIService
	analyticsService *service.AnalyticsService
	workflowService  *service.WorkflowService
	logger           *log.Helper
	zapLogger        *zap.Logger
	validator        *ValidationService
}

// TRPCRequest represents a tRPC request structure
type TRPCRequest struct {
	ID     interface{} `json:"id"`
	Method string      `json:"method"`
	Params interface{} `json:"params"`
}

// TRPCResponse represents a tRPC response structure
type TRPCResponse struct {
	ID     interface{} `json:"id"`
	Result interface{} `json:"result,omitempty"`
	Error  *TRPCError  `json:"error,omitempty"`
}

// TRPCError represents a tRPC error structure
type TRPCError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// NewTRPCAdapter creates a new tRPC adapter with enhanced error handling and validation
func NewTRPCAdapter(
	hvacService *service.HVACService,
	aiService *service.AIService,
	analyticsService *service.AnalyticsService,
	workflowService *service.WorkflowService,
	logger log.Logger,
) *TRPCAdapter {
	// Initialize zap logger
	zapLogger, _ := zap.NewProduction()

	// Initialize validation service
	validator := NewValidationService(logger)

	return &TRPCAdapter{
		hvacService:      hvacService,
		aiService:        aiService,
		analyticsService: analyticsService,
		workflowService:  workflowService,
		logger:           log.NewHelper(logger),
		zapLogger:        zapLogger,
		validator:        validator,
	}
}

// RegisterRoutes registers tRPC routes with the HTTP router
func (t *TRPCAdapter) RegisterRoutes(router *mux.Router) {
	// Main tRPC endpoint
	router.HandleFunc("/api/trpc", t.handleTRPCRequest).Methods("POST")
	router.HandleFunc("/api/trpc/{procedure}", t.handleTRPCProcedure).Methods("GET", "POST")

	// Health check endpoint
	router.HandleFunc("/api/trpc/health", t.handleHealth).Methods("GET")

	// Enable CORS for tRPC endpoints
	router.Use(t.corsMiddleware)

	t.logger.Info("🔗 tRPC adapter routes registered successfully")
}

// corsMiddleware adds CORS headers for frontend integration
func (t *TRPCAdapter) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// handleHealth provides a health check endpoint
func (t *TRPCAdapter) handleHealth(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"status":    "healthy",
		"service":   "trpc-adapter",
		"timestamp": "2024-01-01T00:00:00Z",
		"version":   "1.0.0",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleTRPCRequest handles batch tRPC requests
func (t *TRPCAdapter) handleTRPCRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Parse request body
	var requests []TRPCRequest
	if err := json.NewDecoder(r.Body).Decode(&requests); err != nil {
		// Try single request format
		var singleRequest TRPCRequest
		r.Body.Close()
		if err := json.NewDecoder(r.Body).Decode(&singleRequest); err != nil {
			customErr := biz.NewCustomError(biz.ErrCodeParseError, "Parse error", err.Error())
			t.sendError(w, nil, customErr)
			return
		}
		requests = []TRPCRequest{singleRequest}
	}

	// Process each request
	responses := make([]TRPCResponse, len(requests))
	for i, req := range requests {
		responses[i] = t.processRequest(ctx, req)
	}

	// Send response
	w.Header().Set("Content-Type", "application/json")
	if len(responses) == 1 {
		json.NewEncoder(w).Encode(responses[0])
	} else {
		json.NewEncoder(w).Encode(responses)
	}
}

// handleTRPCProcedure handles individual procedure calls
func (t *TRPCAdapter) handleTRPCProcedure(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)
	procedure := vars["procedure"]

	// Create request from URL parameters
	req := TRPCRequest{
		ID:     "1",
		Method: procedure,
		Params: t.parseQueryParams(r),
	}

	response := t.processRequest(ctx, req)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// processRequest processes a single tRPC request
func (t *TRPCAdapter) processRequest(ctx context.Context, req TRPCRequest) TRPCResponse {
	t.logger.Infof("🔄 Processing tRPC request: %s", req.Method)

	// Route to appropriate service based on method
	parts := strings.Split(req.Method, ".")
	if len(parts) < 2 {
		return TRPCResponse{
			ID:    req.ID,
			Error: &TRPCError{Code: -32601, Message: "Method not found"},
		}
	}

	service := parts[0]
	method := parts[1]

	switch service {
	case "customer":
		return t.handleCustomerRequest(ctx, req.ID, method, req.Params)
	case "job":
		return t.handleJobRequest(ctx, req.ID, method, req.Params)
	case "ai":
		return t.handleAIRequest(ctx, req.ID, method, req.Params)
	case "analytics":
		return t.handleAnalyticsRequest(ctx, req.ID, method, req.Params)
	case "workflow":
		return t.handleWorkflowRequest(ctx, req.ID, method, req.Params)
	case "health":
		return t.handleHealthRequest(ctx, req.ID, method, req.Params)
	default:
		return TRPCResponse{
			ID:    req.ID,
			Error: &TRPCError{Code: -32601, Message: fmt.Sprintf("Service not found: %s", service)},
		}
	}
}

// handleCustomerRequest handles customer-related tRPC requests
func (t *TRPCAdapter) handleCustomerRequest(ctx context.Context, id interface{}, method string, params interface{}) TRPCResponse {
	switch method {
	case "list":
		req := &hvacv1.ListCustomersRequest{}
		if params != nil {
			// Parse params into request
			t.parseParams(params, req)
		}

		resp, err := t.hvacService.ListCustomers(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	case "get":
		req := &hvacv1.GetCustomerRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.hvacService.GetCustomer(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	case "create":
		req := &hvacv1.CreateCustomerRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.hvacService.CreateCustomer(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	default:
		return TRPCResponse{
			ID:    id,
			Error: &TRPCError{Code: -32601, Message: fmt.Sprintf("Customer method not found: %s", method)},
		}
	}
}

// handleJobRequest handles job-related tRPC requests
func (t *TRPCAdapter) handleJobRequest(ctx context.Context, id interface{}, method string, params interface{}) TRPCResponse {
	switch method {
	case "list":
		req := &hvacv1.ListJobsRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.hvacService.ListJobs(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	case "get":
		req := &hvacv1.GetJobRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.hvacService.GetJob(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	case "create":
		req := &hvacv1.CreateJobRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.hvacService.CreateJob(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	default:
		return TRPCResponse{
			ID:    id,
			Error: &TRPCError{Code: -32601, Message: fmt.Sprintf("Job method not found: %s", method)},
		}
	}
}

// handleAIRequest handles AI-related tRPC requests
func (t *TRPCAdapter) handleAIRequest(ctx context.Context, id interface{}, method string, params interface{}) TRPCResponse {
	switch method {
	case "chat":
		req := &aiv1.ChatRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.aiService.Chat(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	case "analyze":
		req := &aiv1.AnalyzeRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.aiService.Analyze(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	case "listModels":
		req := &aiv1.ListModelsRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.aiService.ListModels(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	default:
		return TRPCResponse{
			ID:    id,
			Error: &TRPCError{Code: -32601, Message: fmt.Sprintf("AI method not found: %s", method)},
		}
	}
}

// handleAnalyticsRequest handles analytics-related tRPC requests
func (t *TRPCAdapter) handleAnalyticsRequest(ctx context.Context, id interface{}, method string, params interface{}) TRPCResponse {
	switch method {
	case "getMetrics":
		req := &analyticsv1.GetMetricsRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.analyticsService.GetMetrics(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	case "generateReport":
		req := &analyticsv1.GenerateReportRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.analyticsService.GenerateReport(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	default:
		return TRPCResponse{
			ID:    id,
			Error: &TRPCError{Code: -32601, Message: fmt.Sprintf("Analytics method not found: %s", method)},
		}
	}
}

// handleWorkflowRequest handles workflow-related tRPC requests
func (t *TRPCAdapter) handleWorkflowRequest(ctx context.Context, id interface{}, method string, params interface{}) TRPCResponse {
	switch method {
	case "execute":
		req := &workflowv1.ExecuteWorkflowRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.workflowService.ExecuteWorkflow(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	case "getStatus":
		req := &workflowv1.GetWorkflowStatusRequest{}
		if params != nil {
			t.parseParams(params, req)
		}

		resp, err := t.workflowService.GetWorkflowStatus(ctx, req)
		if err != nil {
			return TRPCResponse{
				ID:    id,
				Error: &TRPCError{Code: -32000, Message: err.Error()},
			}
		}

		return TRPCResponse{ID: id, Result: resp}

	default:
		return TRPCResponse{
			ID:    id,
			Error: &TRPCError{Code: -32601, Message: fmt.Sprintf("Workflow method not found: %s", method)},
		}
	}
}

// handleHealthRequest handles health check requests
func (t *TRPCAdapter) handleHealthRequest(ctx context.Context, id interface{}, method string, params interface{}) TRPCResponse {
	return TRPCResponse{
		ID: id,
		Result: map[string]interface{}{
			"status":    "healthy",
			"service":   "gobackend-kratos",
			"timestamp": "2024-01-01T00:00:00Z",
			"version":   "1.0.0",
		},
	}
}

// parseParams parses interface{} params into a protobuf message
func (t *TRPCAdapter) parseParams(params interface{}, target interface{}) error {
	// Convert params to JSON and then unmarshal into target
	jsonData, err := json.Marshal(params)
	if err != nil {
		return err
	}

	return json.Unmarshal(jsonData, target)
}

// parseQueryParams parses URL query parameters into a map
func (t *TRPCAdapter) parseQueryParams(r *http.Request) map[string]interface{} {
	params := make(map[string]interface{})

	for key, values := range r.URL.Query() {
		if len(values) == 1 {
			// Try to parse as number
			if intVal, err := strconv.Atoi(values[0]); err == nil {
				params[key] = intVal
			} else if floatVal, err := strconv.ParseFloat(values[0], 64); err == nil {
				params[key] = floatVal
			} else {
				params[key] = values[0]
			}
		} else {
			params[key] = values
		}
	}

	return params
}

// sendError sends a tRPC error response with enhanced logging and error tracking
func (t *TRPCAdapter) sendError(w http.ResponseWriter, id interface{}, customErr *biz.CustomError) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusBadRequest)

	resp := TRPCResponse{
		ID: id,
		Error: &TRPCError{
			Code:    customErr.Code,
			Message: customErr.Message,
			Data:    fmt.Sprintf("%v", customErr.Data), // Convert data to string for TRPCError
		},
	}

	jsonResp, err := json.Marshal(resp)
	if err != nil {
		t.logError(context.Background(), err, "failed to marshal error response")
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}

	_, err = w.Write(jsonResp)
	if err != nil {
		t.logError(context.Background(), err, "failed to write error response")
	}

	// Enhanced logging with zap
	t.zapLogger.Error("tRPC error response sent",
		zap.Int("code", customErr.Code),
		zap.String("message", customErr.Message),
		zap.Any("data", customErr.Data),
		zap.String("request_id", customErr.RequestID),
		zap.Time("timestamp", customErr.Timestamp),
	)
}

// logError logs errors with appropriate context and level
func (t *TRPCAdapter) logError(ctx context.Context, err error, message string, keyvals ...interface{}) {
	keyvals = append(keyvals, "err", err)
	t.logger.WithContext(ctx).Error(message, keyvals...)

	// Also log with zap for structured logging
	t.zapLogger.Error(message,
		zap.Error(err),
		zap.Any("context", keyvals),
		zap.Time("timestamp", time.Now()),
	)
}

// sendErrorLegacy maintains backward compatibility with old sendError signature
func (t *TRPCAdapter) sendErrorLegacy(w http.ResponseWriter, id interface{}, code int, message, data string) {
	customErr := biz.NewCustomError(code, message, data)
	t.sendError(w, id, customErr)
}
